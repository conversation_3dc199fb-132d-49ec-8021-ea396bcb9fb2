<template>
  <page-layout>
    <table-component
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :parentData="params"
      :pageName="pageName"
    ></table-component>
  </page-layout>
</template>

<script>
import { institutionsMixin } from '../mixins/institutionsMixin';
import tableComponent from '@/views/basicData/components/tableComponent';
import moment from 'moment';

export default {
  name: 'majorProgramInstitutions',
  components: { tableComponent },
  mixins: [institutionsMixin],

  data() {
    return {
      pageName: 'majorProgramInstitutions',
      projectLevelList: [],
      params: {
        rateTime: undefined,
        planName: undefined,
        operatingUnit: undefined,
        level: undefined,
      },
      tableColumn: [
        {
          field: '',
          title: '',
          type: 'checkbox',
          fixed: 'left',
          width: 70,
        },
        {
          field: '',
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: 70,
        },
        {
          field: 'rateTime',
          title: '日期',
          width: 200,
          formatter: ({ cellValue }) => {
            return cellValue ? moment(cellValue).format('YYYY') : '';
          },
        },
        {
          field: 'planName',
          title: '项目名称',
          width: 200,
        },
        {
          field: 'operatingUnit',
          title: '建设单位',
          width: 200,
        },
        {
          field: 'floorSpace',
          title: '占地面积（亩）',
          width: 200,
        },
        {
          field: 'floorArea',
          title: '建筑面积（平方米）',
          width: 200,
        },
        {
          field: 'investment',
          title: '计划总投资（万元）',
          width: 200,
        },
        {
          field: 'completeInvestment',
          title: '已完成投资（万元）',
          width: 200,
        },
        {
          field: 'level',
          title: '项目级别',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.projectLevelList);
          },
        },
        {
          field: 'industrialField',
          title: '产业领域',
          width: 200,
        },
        {
          field: 'beginningEnding',
          title: '建设起止年限',
          width: 200,
        },
        {
          field: 'functionalOrientation',
          title: '项目功能定位',
          width: 200,
        },
        {
          field: 'progress',
          title: '项目进展情况',
          width: 200,
        },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        //筛选控件配置
        config: [
          {
            field: 'planName',
            title: '项目名称',
          },
          {
            field: 'operatingUnit',
            title: '建设单位',
          },
          {
            field: 'level',
            title: '项目级别',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.projectLevelList,
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
          {
            field: 'rateTime',
            title: '日期',
            element: 'slot',
            slotName: 'dateYear',
            rules: [{ required: true, message: '请选择年份' }],
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        submitBtn: true,
        okBtn: false,
        addBtn: false,
        viewBtn: true,
        editBtn: true,
        delBtn: false,
        menu: true,
        menuWidth: 200,
        menuFixed: 'right',
        formConfig: [
          {
            field: 'rateTime',
            title: '年份',
            element: 'slot',
            slotName: 'dateYearForPop',
            rules: [{ required: true, message: '请选择年份' }],
          },
          {
            field: 'operatingUnit',
            title: '建设单位',
            rules: [{ required: true, message: '请输入建设单位' }],
          },
          {
            field: 'planName',
            title: '项目名称',
            rules: [{ required: true, message: '请输入项目名称' }],
          },
          {
            field: 'floorSpace',
            title: '占地面积',
            props: {
              suffix: '亩',
            },
            rules: [
              { required: true, validator: this.checkNum, trigger: 'change' },
            ],
          },
          {
            field: 'floorArea',
            title: '建筑面积',
            props: {
              suffix: '平方米',
            },
            rules: [{ required: true, message: '请输入建筑面积' }],
          },
          {
            field: 'investment',
            title: '计划总投资',
            props: {
              suffix: '万元',
            },
            rules: [
              { required: true, validator: this.checkNum, trigger: 'change' },
            ],
          },
          {
            field: 'completeInvestment',
            title: '已完成投资',
            props: {
              suffix: '万元',
            },
            rules: [
              { required: true, validator: this.checkNum, trigger: 'change' },
            ],
          },
          {
            field: 'level',
            title: '项目级别',
            element: 'a-select',
            rules: [{ required: true, message: '请输入项目级别' }],
            props: {
              options: this.projectLevelList,
              showSearch: true,
              optionFilterProp: 'children',
            },
            previewFormatter: (value) => {
              return this.translateValue(value, this.projectLevelList);
            },
          },
          {
            field: 'industrialField',
            title: '产业领域',
          },
          {
            field: 'beginningEnding',
            title: '建设起止年限',
          },
          {
            field: 'functionalOrientation',
            title: '项目功能定位',
          },
          {
            field: 'progress',
            title: '项目进展情况',
          },
        ],
      };
    },
  },
  created() {
    this.getCodeByType('PROJECT_LEVEL').then((res) => {
      this.projectLevelList = res;
    });
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.mr-10 {
  margin-right: 10px;
}
</style>
