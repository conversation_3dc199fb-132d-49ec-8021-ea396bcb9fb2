<template>
  <page-layout>
    <PageWrapper
      title="人物名单"
      createText="创建人物"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      @handleCreate="handleCreate"
    >
      <!-- 创建按钮区域插槽 -->
      <template #defaultHeader>
        <a-button
          type="primary"
          style="margin-right: 8px"
          @click="onClickDetail"
        >
          查看详情
        </a-button>
      </template>
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
      </template>
      <!-- 编辑弹窗 -->
      <ListModal
        :visible="visible"
        :detail="modalData"
        @handleCancel="handleCancel"
      />
    </PageWrapper>
  </page-layout>
</template>

<script>
import { defineComponent, onActivated, reactive, toRefs, computed } from 'vue';
import { useRouter } from 'vue-router/composables';
import moment from 'moment';
import { defaultTableColumn, defaultFilterConfig } from './constant';
import { queryList } from './mock';
import ListModal from './components/ListModal.vue';
import { getCurrentInstance } from 'vue';
import { useStore } from '@/utils/hooks/vueApi';
export default defineComponent({
  components: { ListModal },
  setup() {
    const router = useRouter();
    const state = reactive({
      loading: false,
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
        params: {
          blog: '',
          name: 'test',
          state: '',
          identity: '',
          birthday: '',
          birthdayRange: ['', ''],
        }, // 筛选器结果数据
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: defaultTableColumn(),
      tableData: [],
      visible: false,
      modalData: null,
    });

    const { proxy } = getCurrentInstance();
    const { my_notify_rule } = proxy.useDict('my_notify_rule');

    const store = useStore();
    const dictStore = computed(() => store.state.dict);
    // 请求接口数据
    const loadData = async () => {
      state.loading = true;
      const params = state.filterOptions.params;
      const [res, err] = await queryList({
        limit: state.tablePage.pageSize,
        pageNum: state.tablePage.currentPage,
        ...params,
        birthdayRange: undefined, // 对时间范围按照后端要求处理
        timeStart: moment(params.birthdayRange[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        timeEnd: moment(params.birthdayRange[1]).format('YYYY-MM-DD HH:mm:ss'),
      });
      state.loading = false;

      if (err) return;
      // 设置数据
      state.tablePage.total = res.total;
      state.tableData = res.data;
    };
    // 创建按钮点击事件
    const handleCreate = () => {
      state.visible = true;
    };
    // 编辑按钮点击事件
    const onClickEdit = (row) => {
      state.visible = true;
      state.modalData = { ...row };
    };
    // 关闭弹窗
    const handleCancel = (update) => {
      if (update) {
        loadData();
      }
      state.visible = false;
      state.modalData = null;
    };
    // 查看详情
    const onClickDetail = () => {
      router.push('./second/detail');
    };
    onActivated(() => {
      console.log('onActivated');
      state.filterOptions.config[1].props.options = my_notify_rule;
      loadData();
    });
    return {
      ...toRefs(state),
      loadData,
      my_notify_rule,
      dictStore,
      onClickDetail,
      handleCancel,
      onClickEdit,
      handleCreate,
    };
  },
});
</script>
<style scoped>
/deep/.page-wrapper-container {
  margin: 0 !important;
}
</style>
