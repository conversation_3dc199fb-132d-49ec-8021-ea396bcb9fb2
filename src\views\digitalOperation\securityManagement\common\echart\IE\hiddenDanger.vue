<template>
  <div class="action-echart-box">
    <h2>历史检查隐患类型数量统计</h2>
    <BaseChart
      class="chart-box"
      v-if="actionsResultData.axisData.length > 0"
      :axisData="actionsResultData.axisData"
      :seriesData="actionsResultData.seriesData"
      :yAxis="yAxis"
      :tooltipFormatter="tooltipFormatter"
      unit="符合率"
    />
    <echartEmptyBox v-else />
  </div>
</template>

<script>
import echartEmptyBox from '@/components/echartEmptyBox/index.vue';
import BaseChart from '@/components/chart/lineChart.vue';
import { getTemplateBase } from '../echart';
import { industryHistoryDanger } from '@/api/digitalOperation/securityManagement/parkSafety/echart/IE.js';
export default {
  name: 'ManagementTkActions',
  components: {
    BaseChart,
    echartEmptyBox,
  },

  data() {
    return {
      total: 0,
      actionsResultData: {
        axisData: [],
        seriesData: [
          {
            name: '重大隐患',
            type: 'bar',
            data: [],
          },
          {
            name: '一般隐患',
            type: 'bar',
            data: [],
          },
        ],
      },
    };
  },
  created() {
    this.yAxis = {
      type: 'value',
      nameGap: 40,
      nameTextStyle: {
        // 字体样式
        padding: [0, -60, 0, 0],
        color: 'rgba(0,0,0,0.45)',
        fontSize: 14, // 字体大小
      },
      axisLabel: {
        formatter: '{value}%',
      },
    };
  },

  mounted() {
    this.getActionsData();
  },

  methods: {
    async getActionsData() {
      const [res, err] = await industryHistoryDanger();
      if (err) return;
      console.log(res.data, '9999909.0');
      this.total = res.total;
      let axisData = [];
      let seriesData0 = [];
      let seriesData1 = [];
      res.data.forEach((item) => {
        axisData.push(item.parkName);
        seriesData0.push(item.major);
        seriesData1.push(item.general);
      });
      setTimeout(() => {
        this.actionsResultData.axisData = axisData;
        this.actionsResultData.seriesData[0].data = seriesData0;
        this.actionsResultData.seriesData[1].data = seriesData1;
      }, 500);
      //TODO:获取近期和历史符合率数据
    },
    tooltipFormatter(info) {
      let str = `<div style="text-align: left; color:#1D2129;" >${info[0].name}</div>`;
      info.forEach((item) => {
        str += getTemplateBase(item.marker, item.seriesName, item.value + '个');
      });
      return str;
    },
  },
};
</script>

<style lang="less" scoped>
.action-echart-box {
  margin: 16px;
}
</style>
