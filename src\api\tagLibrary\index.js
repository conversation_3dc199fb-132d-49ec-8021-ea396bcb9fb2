import { request } from '@/utils/request/requestTkb';

// 标签库目录查询
export function queryLabelDir(query) {
  return request({
    url: '/label/dir/queryLabelDir',
    method: 'GET',
    params: query,
  });
}

// 标签库目录保存/更新
export function saveLabelDir(query) {
  return request({
    url: '/label/dir/saveLabelDir',
    method: 'POST',
    data: query,
  });
}

// 标签库目录保存/更新
export function getListLabelWareHouse(query) {
  return request({
    url: '/label/warehouse/listLabelWareHouse',
    method: 'POST',
    data: query,
  });
}

// 标签库目录保存/更新
export function updateLabelWareHouse(query) {
  return request({
    url: '/label/warehouse/updateLabelWareHouse',
    method: 'POST',
    data: query,
  });
}

export function searchLabelNames(query) {
  return request({
    url: '/label/warehouse/searchLabelNames',
    method: 'POST',
    data: query,
    // params: query,
  });
}
export function saveLabelWareHouse(query) {
  return request({
    url: '/label/warehouse/saveLabelWareHouse',
    method: 'POST',
    data: query,
  });
}
export function getByLabel(query) {
  return request({
    url: '/label/warehouse/getByLabel',
    method: 'POST',
    data: query,
  });
}
export function downloadAllInformation(query) {
  return request({
    url: '/label/warehouse/downloadAllInformation',
    method: 'POST',
    data: query,
    responseType: 'blob',
  });
}

// 标签删除
export function deleteLabelWareHouse(query) {
  return request({
    url: '/label/warehouse/deleteLabelWareHouse',
    method: 'POST',
    data: query,
  });
}
