<template>
  <div>
    <template v-if="btn.children && btn.children.length">
      <a-popover>
        <template slot="content">
          <div
            v-for="(btn, index) in btn.children"
            :key="index"
            class="btn-ml12"
          >
            <PopoverButton
              :loading="loading"
              :btn="btn"
              :row="row"
              class="in-row"
            />
          </div>
        </template>
        <Button :loading="loading" :btn="btn" :row="row">
          <template v-for="(_, name) in $scopedSlots" v-slot:[name]="data">
            <slot :name="name" v-bind="data" />
          </template>
        </Button>
      </a-popover>
    </template>
    <template v-else>
      <Button :loading="loading" :btn="btn" :row="row">
        <template v-for="(_, name) in $scopedSlots" v-slot:[name]="data">
          <slot :name="name" v-bind="data" />
        </template>
      </Button>
    </template>
  </div>
</template>
<script>
export default {
  name: 'PopoverButton',
  components: {
    Button: {
      functional: true,
      render(_, { data, props, listeners, scopedSlots }) {
        const classArr = ['popover-button'];
        if (data?.class) {
          if (typeof data.class === 'string') {
            classArr.push(data.class);
          } else if (Array.isArray(data.class)) {
            classArr.push(...data.class);
          } else if (typeof data.class === 'object') {
            classArr.push(
              ...(Object.keys(data.class)?.filter((k) => data.class[k]) || [])
            );
          }
        }
        const className = classArr.join(' ');
        return (
          <a-button
            on={{
              ...listeners,
              ...(props.btn?.on || {}),
              ...(props.btn?.['event'] instanceof Function
                ? {
                    click: () => props.btn['event'](props.row),
                  }
                : {}),
            }}
            props={{
              ...data.attrs,
              ...(props.btn?.props || {}),
              loading: props.btn?.props?.loading || props.loading,
            }}
            class={className}
            style={data.style}
          >
            {props.btn?.label}
            {scopedSlots?.default && scopedSlots.default()}
          </a-button>
        );
      },
    },
  },
  props: {
    btn: {
      type: Object,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    row: {
      type: Object,
      default: () => {},
    },
  },
};
</script>
<style lang="less" scoped>
.popover-button.ant-btn-link {
  padding: 0;
}
</style>
