<template>
  <page-layout>
    <TableLayout v-bind="tableProps" ref="page" :params.sync="searchForm">
      <template #operation="{ row }">
        <BtnGroup :row="row" :btns="columnButtons" :maxCount="5" />
      </template>
      <template #tableTags="{ row }">
        <a-tag
          v-for="(tag, index) in row.label"
          :key="index"
          :color="
            ['#6A5ECC', '#F79D00', '#00B42A', '#1890FF', '#FF7B00'][index % 5]
          "
          >{{ tag }}</a-tag
        >
      </template>
    </TableLayout>
    <a-modal
      :visible="visible"
      title="画像详情"
      width="100%"
      wrapClassName="full-modal"
      :footer="null"
      @cancel="visible = false"
    >
      <portraits-detail
        v-if="visible"
        :pageName="pageName"
        :pictureId="pictureId"
        @close="handleModalClose"
      ></portraits-detail>
    </a-modal>
  </page-layout>
</template>

<script>
// import TagTree from '@/views/tagLibrary/tagManage/components/tagTree';
import portraitsDetail from '@/views/portraits/components/portraitsDetail';

import * as api from '@/api/tagLibrary';
import TableLayout from '@/layouts/TableLayout.vue';
// import BuseModal from '@/layouts/BuseModal.vue';
import BtnGroup from '@/components/BtnGroup';
import { confirmFunc, getDictByApi } from '@/utils';
import { saveAs } from 'file-saver';

export default {
  name: 'index',
  components: {
    // TagTree,
    TableLayout,
    BtnGroup,
    portraitsDetail,
  },
  data() {
    return {
      limit: 10,
      pageNum: 1,
      currentPage: 1,
      total: 0,
      pages: 0,
      catalogInfo: {},
      loading: false,
      cardList: [],
      labelCol: {
        span: 2,
      },
      wrapperCol: {
        span: 14,
      },
      searchForm: {
        labelClass: '3',
        labelId: [],
      },
      typeList: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '0',
          label: '初始化标签',
        },
        {
          value: '1',
          label: '自定义标签',
        },
      ],
      dict: {
        labelClass: [
          {
            value: '3',
            label: '全部',
          },
          {
            value: '1',
            label: '园区',
          },
          {
            value: '2',
            label: '企业',
          },
        ],
        labelId: [],
      },
      visible: false,
      pageName: 'businessPortraits',
      pictureId: '',
    };
  },
  computed: {
    tableProps() {
      const _this = this;
      return {
        tableApiList: [
          {
            api: api.getByLabel,
          },
        ],
        filterConfig: [
          {
            field: 'labelClass',
            title: '标签属性',
            element: 'a-select',
            defaultValue: '3',
            props: {
              options: this.dict.labelClass,
            },
            on: {
              change() {
                _this.searchForm.labelId = [];
                _this.getLabelId();
              },
            },
          },
          {
            field: 'labelId',
            title: '标签名称',
            element: 'a-select',
            props: {
              options: this.dict.labelId,
              defaultValue: ['20240204152903541000006021502049'],
              mode: 'multiple',
              maxCount: 5,
              maxTagCount: 1,
              showSearch: true,
              filterOption(inputValue, option) {
                return option?.componentOptions?.children[0]?.text?.includes(
                  inputValue
                );
              },
            },
          },
        ],
        tableColumn: [
          {
            field: 'name',
            title: '园区/企业名称',
            minWidth: 200,
          },
          {
            field: 'label',
            title: '标签',
            slots: { default: 'tableTags' },
            minWidth: 400,
          },
          {
            field: 'operation',
            title: '操作',
            width: 140,
            fixed: 'right',
            slots: { default: 'operation' },
          },
        ],
        // dataHandler,
        tableButtons: this.creatTableButtons,
      };
    },
  },

  async mounted() {
    // await this.getLabelId();

    this.searchForm.labelClass = '3';
    await this.getLabelId();
    if (this.dict.labelId?.length) {
      // this.$set(this.searchForm, 'labelId', [this.dict.labelId[0].value]);
      this.searchForm.labelId = [this.dict.labelId[0].value];
    }
    this.$nextTick(() => {
      this.getData();
    });
  },

  methods: {
    handleModalClose() {
      this.visible = false;
    },
    async getLabelId() {
      const res = await getDictByApi({
        api: api.searchLabelNames,
        target: this.dict.labelId,
        params: {
          labelClass: this.searchForm.labelClass,
          labelName: this.searchForm.labelName,
        },
        label: 'labelName',
        value: 'id',
      });
    },
    creatTableButtons() {
      return [
        {
          label: '导出',
          event: this.handleExport,
          props: {
            type: 'primary',
          },
          show: () => true,
        },
      ];
    },
    columnButtons() {
      return [
        {
          label: '',
          event: this.goDetail,
          props: {
            icon: 'search',
          },
          show: () => true,
        },
      ];
    },
    goDetail(row) {
      console.log(row);
      this.pageName = row?.type === '2' ? 'businessPortraits' : 'parkPortraits';
      if (!row?.id) {
        this.$message.warn('暂无对应画像');
        return;
      }
      this.pictureId = row.id;
      this.visible = true;
    },
    async handleExport() {
      const res = await confirmFunc('确定导出吗？');
      if (!res) return;
      const [res2, err2] = await api.downloadAllInformation(this.searchForm);
      if (err2) return;
      saveAs(res2, '标签.xlsx');
    },
    handleEdit() {},
    async handleDelete() {
      const res = await confirmFunc('确定要删除吗？');
      if (!res) return;
      this.$message.success('删除成功');
    },
    // 查询数据
    getData() {
      this.$refs.page.loadData();
    },
  },
};
</script>

<style scoped lang="scss">
.tag-manage {
  display: flex;
  flex-direction: row;
  height: 80vh;
  background: white;
  padding: 10px;

  .tag-manage-left {
    width: 20%;
    height: 100%;
    border-right: #d9d9d9 1px solid;
    padding: 10px;
  }

  .tag-manage-right {
    width: 80%;
    padding: 20px;

    .catalog-content {
      margin-top: 30px;
      /*max-height: 60vh;*/
      overflow: auto;

      .catalog-name {
        padding: 5px;
        background: #1677ffdb;
        font-size: 15px;
        color: white;
        border-radius: 5px;
      }

      .tag-card {
        margin-bottom: 10px;
        height: 100px;
        border: #d9d9d9 1px solid;
        border-radius: 5px;
        display: flex;
        flex-direction: row;
        align-items: center;
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);

        p {
          margin-bottom: unset;
          color: #898989;
        }

        .line {
          border-color: #7cb305;
          height: 80px;
        }

        .icon {
          width: 10%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          p {
            /*color: blue;*/
          }
        }

        .details {
          width: 70%;
          display: flex;
          flex-direction: row;

          .details-left,
          .details-right {
            .upper {
              padding: 3px;

              .code {
                font-weight: 700;
                color: #0000ff;
              }

              .title {
                color: #545353;
                font-weight: 700;
              }
            }

            .lower {
              padding: 3px;

              .title {
                color: #545353;
                font-weight: 700;
              }
            }
          }

          .details-left {
            width: 60%;

            .upper {
              display: flex;
              flex-direction: row;

              .code {
                width: 30%;
                font-weight: 700;
                color: #0000ff;
              }

              .tag-name {
                display: flex;
                width: 70%;

                .title {
                  width: 30%;
                  text-align: right;
                }

                .info {
                  width: 70%;
                  color: #ff9900;
                  font-weight: 700;
                }
              }
            }
          }

          .details-right {
            width: 40%;
          }
        }

        .online {
          width: 20%;
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          cursor: pointer;

          p {
            text-align: center;
            font-weight: 700;
            margin-top: 5px;
          }

          img {
            width: 40px;
            height: 40px;
            max-width: 100%;
            height: auto;
          }

          .picture:hover {
            transition-duration: 0.5s;
            transform: scale(1.2);
            transition-duration: 0.5s;
          }
        }
      }

      .more-button {
        margin: 20px 0;
        text-align: center;
        cursor: pointer;
      }

      .more-button:hover {
        color: #1994ff;
      }

      .more-loading {
        text-align: center;
        border-radius: 4px;
        margin-bottom: 20px;
        padding: 30px 50px;
        margin: 20px 0;
      }
    }
  }
}
</style>
