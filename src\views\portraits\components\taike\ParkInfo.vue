<template>
  <PortraitCard :span="24" title="园区信息" :canExpand="true">
    <a-row :gutter="12">
      <a-col :span="24" class="icon-title">
        <img
          :src="require('@/assets/images/portraits/1-2012.png')"
          alt=""
        />园区数量

        <span v-if="!pictureInfo.parkCount" class="detail-num detail-num-null"
          >暂无数据</span
        >
        <template v-else>
          <span class="detail-num">{{ pictureInfo.parkCount }}</span>
        </template>
      </a-col>
      <a-col :span="12">
        <ChartsCard chartTitle="园区类别统计" :options="charts1Options" />
      </a-col>
      <a-col :span="12">
        <ScrollTable
          tableTitle="园区标签统计"
          height="160px"
          :columns="table2.columns"
          :tableData="table2.data"
          :scrollSpeed="1"
          :scrollInterval="80"
          :visibleRowCount="4"
          :autoScroll="true"
        />
      </a-col>
      <a-col :span="12">
        <ParkList
          :parkList="pictureInfo.parkList || []"
          @openModal="openModal"
        />
      </a-col>
      <a-col :span="12">
        <ScrollTable
          tableTitle="园区拥有企业排名TOP10"
          height="374px"
          :columns="table1.columns"
          :tableData="table1.data"
          :scrollSpeed="1"
          :scrollInterval="60"
          :visibleRowCount="8"
          :autoScroll="true"
        />
      </a-col>
    </a-row>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
import ChartsCard from '../ChartsCard.vue';
import ScrollTable from '../ScrollTable.vue';
import ParkList from './ParkList.vue';

import { TaikeAPI } from '@/api/portraits-new/index.js';
import { usePieCharts } from '../chartHooks';

export default {
  components: {
    PortraitCard,
    ChartsCard,
    ScrollTable,
    ParkList,
  },
  props: {
    pictureId: {
      type: String,
      default: '',
    },
    pictureInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      charts1Options: {},
      charts2Options: {},
      table1: {
        columns: [
          {
            title: '排名',
            field: 'rank',
            width: 90,
            type: 'html',
            formatter: ({ cellValue }) =>
              `<div class='top-title top-title-${+cellValue}' ">TOP${+cellValue}</div>`,
          },
          {
            title: '园区名称',
            field: 'parkName',
            minWidth: 80,
          },
          {
            title: '企业数量',
            field: 'count',
            width: 80,
            type: 'html',
            headerAlign: 'right',
            align: 'right',
            formatter: ({ cellValue }) =>
              `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
          },
        ],
        data: [],
        total: 0,
      },
      table2: {
        columns: [
          {
            title: '标签名称',
            field: 'labelName',
            overflow: 'tooltip',
            minWidth: 80,
          },
          {
            title: '园区数量',
            field: 'count',
            width: 80,
            type: 'html',
            headerAlign: 'right',
            align: 'right',
            formatter: ({ cellValue }) =>
              `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
          },
          {
            title: '园区占比',
            field: 'account',
            width: 90,
            type: 'html',
            headerAlign: 'right',
            align: 'right',
            formatter: ({ cellValue }) =>
              `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
          },
        ],
        data: [],
        total: 0,
      },
    };
  },
  watch: {
    pictureInfo: {
      deep: true,
      handler() {
        this.handleInitCharts1();
        this.handleInitCharts2();
        this.table1.data = (this.pictureInfo?.epHaveList || []).slice(0, 10);
      },
    },
  },
  mounted() {
    this.loadData2();
  },
  methods: {
    openModal(...args) {
      this.$emit('openModal', ...args);
    },
    async loadData2(args) {
      this.loading = true;
      const [res, err] = await TaikeAPI.getParkLabelAysInfo({
        ...this.tablePage,
        ...args,
        parkId: this.pictureId,
      });
      this.loading = false;
      if (err || typeof res.data !== 'object') return;
      let list = [];
      if (Array.isArray(res.data)) {
        list = res.data;
      } else {
        list = Object.values(res.data).find((item) => Array.isArray(item));
      }
      list.forEach((item) => {
        this.table2.data.push({
          ...item,
          index: this.table2.data.length,
        });
      });
      this.table2.total = res.total;
    },
    handleInitCharts1() {
      const { parkTypeList = [] } = this.pictureInfo;
      this.charts1Options = usePieCharts(
        {
          data: parkTypeList,
        },
        true
      );
    },
    handleInitCharts2() {
      const { regulateList = [] } = this.pictureInfo;
      this.charts2Options = usePieCharts({
        data: regulateList,
      });
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;
  .title {
    width: 70px;
    text-align: right;
    color: #999999;
  }
  .info {
    width: calc(100% - 70px);
    .fold-button {
      color: #86bded;
      float: right;
    }
    .num {
      font-family: D-DIN;
      font-size: 20px;
      font-weight: bold;
      line-height: 20px;
      letter-spacing: 0px;
      color: #333333;
      z-index: 0;
    }
    .unit {
      margin-left: 4px;
    }
  }
  &-inline {
    display: inline-block;
  }
  &-picture {
    &-3 {
      background-image: url('@/assets/images/portraits/1-003.png');
    }
  }
}
.ant-col {
  margin-top: 24px;
}
.icon-title {
  margin-top: 9px;
  font-weight: normal;
  font-size: 14px;
  img {
    width: 48px;
    height: 48px;
    margin-right: 16px;
  }
  .detail-num {
    font-family: D-DIN;
    font-size: 26px;
    font-weight: bold;
    display: inline-block;
    color: #00173a;
    margin-left: 15px;
    position: relative;
    top: 3px;
    &-null {
      font-size: 16px;
    }
  }
  .unit {
    font-weight: normal;
    margin-left: 4px;
    font-size: 12px;
  }
}
</style>
