<template>
  <PortraitCard :span="24" title="基本信息" :canExpand="true">
    <template #default="{ showAll }">
      <a-row v-if="!showAll">
        <a-col :span="8" class="detail">
          <div class="title">园区名称：</div>
          <div class="info">
            {{ pictureInfo.parkName || '-' }}
          </div>
        </a-col>
        <a-col :span="4" class="detail">
          <div class="title" style="width: 50px">电话：</div>
          <div class="info" style="width: 120px">
            {{ pictureInfo.phone || '-' }}
          </div>
        </a-col>
        <a-col :span="8" class="detail">
          <div class="title" style="width: 150px">地址：</div>
          <div class="info" style="width: 180px">
            {{ pictureInfo.address || '-' }}
          </div>
        </a-col>
        <a-col :span="24" class="detail" style="margin-top: 16px">
          <div class="title">
            <p>简介：</p>
          </div>
          <div class="info">
            <div class="three-lines">{{ pictureInfo.intro || '-' }}</div>
          </div>
        </a-col>
      </a-row>
      <a-row v-else>
        <a-col
          v-for="(item, index) in baseList"
          :key="index"
          class="detail"
          :span="item.field !== 'intro' ? 6 : 24"
          style="margin-bottom: 8px"
        >
          <div class="title">{{ item.title }}：</div>
          <div class="info">
            {{ pictureInfo[item.field] || '-' }} {{ item.unit || '' }}
          </div>
        </a-col>
      </a-row>
      <a-modal
        :visible="updateHistory.visible"
        :title="null"
        :closable="false"
        :footer="null"
        width="1000px"
        @cancel="updateHistory.visible = false"
      >
        <updateRecord
          :dataDict="dataDict"
          :rowData="{ unifiedCreditCode: pictureInfo.pictureId }"
        ></updateRecord>
      </a-modal>
    </template>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
import UpdateRecord from '@/views/basicData/enterpriseArchives/components/updateRecord.vue';
export default {
  components: {
    PortraitCard,
    UpdateRecord,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    dataDict: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      showAll: false,
      updateHistory: { visible: false },
      baseList: [
        { title: '园区名称', field: 'parkName' },
        { title: '园区类别', field: 'typeName' },
        { title: '主要地址', field: 'address' },
        { title: '详细园区名称', field: 'parkNameDetail' },
        { title: '详细地址', field: 'addressDetail' },
        { title: '总规划面积', field: 'totalArea', unit: 'm²' },
        { title: '总建成面积', field: 'completedArea', unit: 'm²' },
        { title: '建成状况', field: 'isCompletedName' },
        { title: '电话', field: 'phone' },
        { title: '荣誉', field: 'honor' },
        { title: '经营范围', field: 'businessRange' },
        { title: '简介', field: 'intro' },
        { title: '出租率', field: 'ratingPre', unit: '%' },
        { title: '绿化率', field: 'greenPre', unit: '%' },
        { title: '占地面积', field: 'floorSpace', unit: 'm²' },
        { title: '就业人口数量', field: 'population' },
      ],
    };
  },
  methods: {
    toDetail(path) {
      if (path) {
        this.$router.push({
          path,
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;

  .title {
    width: 98px;
    text-align: right;
    color: #999999;
  }

  .info {
    width: calc(100% - 98px);
  }
  ::v-deep .three-lines {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
