<template>
  <page-layout>
    <div class="container">
      <PageWrapper
        style="margin: 0"
        ref="vxeRef"
        title="文件列表"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        @loadData="documentManageList"
        @handleReset="handleReset"
      >
        <!-- 创建按钮区域插槽 -->
        <template #defaultHeader>
          <a-button
            type="primary"
            style="margin-right: 8px"
            @click="batchDeletion"
          >
            批量删除
          </a-button>
          <a-button
            type="primary"
            style="margin-right: 8px"
            @click="uploadFile"
          >
            上传文件
          </a-button>
        </template>
        <!-- filter插槽 -->
        <template #identity="{ item }">
          <a-input
            v-model="filterOptions.params[item.field]"
            placeholder="AutoFilter插槽"
          />
        </template>

        <!-- table插槽 -->
        <template #operate="{ row }">
          <span
            class="operate-button"
            v-clipboard:copy="row.url"
            v-clipboard:success="onCopy"
            v-clipboard:error="onError"
            >链接</span
          >
          <span class="operate-button" @click="onClickDownload(row)">下载</span>
          <span class="operate-button" @click="onClickDelete(row)">删除</span>
        </template>
        <!-- 编辑弹窗 -->
        <ModalData
          @success="success"
          @handleCreateSum="handleCancel"
          :visible="visible"
          @handleCancel="handleCancel"
        />
      </PageWrapper>
    </div>
  </page-layout>
</template>

<script>
import ModalData from './components/Modal.vue';
import { Modal } from 'ant-design-vue';
import { filterOptions, tableColumn } from './constant';
import {
  documentManageList,
  documentManageRemove,
  documentManageRemoveBatch,
} from '@/api/system/documentCenter/fileManagement';
import { changeByte } from '@/utils/index';
import { saveAs } from 'file-saver';
export default {
  components: { ModalData },
  data() {
    return {
      disabled: true,
      loading: false,
      filterOptions,
      // 表头
      tableColumn: tableColumn(),
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      visible: false,
      center: 'center',
    };
  },
  created() {
    this.documentManageList();
  },
  methods: {
    success() {
      this.documentManageList();
    },
    // 重置
    handleReset() {
      for (let key in this.filterOptions.params) {
        this.filterOptions.params[key] = undefined;
      }
      this.filterOptions.params.fileType = '';
      this.tablePage.currentPage = 1;
      this.documentManageList();
    },
    // 下载
    onClickDownload(row) {
      saveAs(row.url, row.fileName);
    },
    // 单行删除
    onClickDelete(row) {
      Modal.confirm({
        title: '警告',
        content: '删除后云存储中文件将会被同步删除，请谨慎操作！',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.documentManageRemove(row.recordId);
        },
      });
    },
    handleCancel(update) {
      if (update) {
        this.documentManageList();
      }
      this.visible = false;
    },
    // 批量删除
    batchDeletion() {
      const recordIdList = [];
      const deleteList = this.$refs.vxeRef.getCheckboxRecords();
      if (deleteList.length === 0)
        return this.$message.warn('请选择需要删除的记录');
      deleteList.forEach((element) => {
        recordIdList.push(element.recordId);
      });
      Modal.confirm({
        title: '警告',
        content: '删除后云存储中文件将会被同步删除，请谨慎操作！',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.documentManageRemoveBatch(recordIdList);
        },
      });
    },
    uploadFile() {
      this.visible = true;
    },
    // 复制成功
    onCopy() {
      this.$message.success('复制成功');
    },
    onError() {
      this.$message.error('复制失败');
    },
    /**
     * 接口数据处理Start
     */
    // 查询列表
    async documentManageList() {
      this.loading = true;
      const {
        fileName = '',
        fileType = '',
        uploadTime = ['', ''],
      } = this.filterOptions.params;
      const paramsObject = {
        fileName,
        fileType,
        uploadTimeBegin: uploadTime[0],
        uploadTimeEnd: uploadTime[1],
      };

      const getData = {
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
        ...paramsObject,
      };
      const [result, error] = await documentManageList(getData);
      this.loading = false;
      if (error) return;
      const { data, count } = result;
      this.tableData = data;
      this.tableData.forEach((element) => {
        element.fileSize = changeByte(Number(element.fileSize));
      });
      this.tablePage.total = count;
    },
    // 删除
    async documentManageRemove(recordId) {
      const [result, error] = await documentManageRemove(recordId);
      if (error) return;
      if (result) {
        this.$message.success('删除成功');
        this.documentManageList();
      }
    },
    // 批量删除
    async documentManageRemoveBatch(data) {
      const [result, error] = await documentManageRemoveBatch(data);
      if (error) return;
      if (result) {
        this.$message.success('删除成功');
        this.documentManageList();
      }
    },
    /**
     * 接口数据处理End
     */
  },
};
</script>

<style lang="less" scoped>
.container {
  background-color: #f4f4f4;
}
/deep/.ant-select-dropdown-menu-item {
  text-align: left;
}
// table操作按钮
.operate-button {
  display: inline-block;
  margin-right: 16px;
  padding: 8px 0;
  color: #1677ff;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  cursor: pointer;
}
</style>
