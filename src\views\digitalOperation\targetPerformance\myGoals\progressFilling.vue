<template>
  <div>
    <div class="objective-overview">
      <h2>目标总览</h2>
      <a-row type="flex">
        <a-col :span="6">
          <p><span class="title">目标发布者：</span>建设局</p>
        </a-col>
        <a-col :span="6">
          <p><span class="title">我的目标：</span>80%</p>
        </a-col>
        <a-col :span="6">
          <p><span class="title">当前完成情况：</span>30%</p>
        </a-col>
        <a-col :span="6">
          <p><span class="title">完成进度：</span>30%</p>
        </a-col>
        <a-col :span="6">
          <p><span class="title">目标年份：</span>2023</p>
        </a-col>
        <a-col :span="6">
          <p><span class="title">接收时间：</span>2023-02-03</p>
        </a-col>
        <a-col :span="6">
          <p><span class="title">一级指标：</span>国资综合运营能力</p>
        </a-col>
        <a-col :span="6">
          <p><span class="title">二级指标：</span>资产使用率</p>
        </a-col>
      </a-row>
    </div>
    <div class="forms-wrapper">
      <baseForm
        class="base-form-box"
        v-for="(formData, index) in formsData"
        :formData="formData"
        :key="index"
      />
    </div>
  </div>
</template>

<script>
import baseForm from './components/progressFillForm.vue';
export default {
  name: 'ManagementTkProgressFilling',
  components: {
    baseForm,
  },
  data() {
    return {
      formsData: [
        {
          stageDate: '2023-02-03',
          recentProgress: undefined,
          completeStatus: undefined,
          supplementary: '',
          unit: '%',
        },
        {
          stageDate: '2023-03-03',
          recentProgress: '2023-03-02',
          completeStatus: 10,
          supplementary: '补充说明啊啊啊',
          unit: '个',
        },
      ],
    };
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="less" scoped>
.forms-wrapper {
  margin: 24px;
  padding: 24px;
  background: #fff;
}
.base-form-box {
  margin-top: 40px;
}
</style>
