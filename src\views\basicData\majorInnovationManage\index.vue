<template>
  <page-layout>
    <table-component
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :parentData="params"
      :pageName="pageName"
    ></table-component>
  </page-layout>
</template>

<script>
import { institutionsMixin } from '../mixins/institutionsMixin';
import tableComponent from '@/views/basicData/components/tableComponent';
import moment from 'moment';

export default {
  name: 'majorInnovationManage',
  components: { tableComponent },
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'majorInnovationManage',
      projectStatusList: [],
      domainsList: [],
      params: {
        name: undefined,
        code: undefined,
        rateTime: undefined,
      },
      tableColumn: [
        {
          field: '',
          title: '',
          type: 'checkbox',
          fixed: 'left',
          width: 70,
        },
        {
          field: '',
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: 70,
        },
        {
          field: 'rateTime',
          title: '日期',
          width: 200,
          formatter: ({ cellValue }) => {
            return cellValue ? moment(cellValue).format('YYYY') : '';
          },
        },
        {
          field: 'projectName',
          title: '项目名称',
          width: 200,
        },
        {
          field: 'developmentOrganization',
          title: '建设单位',
          width: 200,
        },
        {
          field: 'projectStatus',
          title: '项目状态',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.projectStatusList);
          },
        },
        {
          field: 'totalInvestment',
          title: '计划总投资（亿元）',
          width: 200,
        },
        {
          field: 'yearPlannedInvestment',
          title: '年度计划投资（亿元）',
          width: 200,
        },
        {
          field: 'yearCompleteInvestment',
          title: '年度投资（亿元）',
          width: 200,
        },
        {
          field: 'investmentVolume',
          title: '已完成投资（亿元）',
          width: 200,
        },
        {
          field: 'domains',
          title: '领域',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.domainsList);
          },
        },
        {
          field: 'constructionContent',
          title: '主要建设内容',
          width: 800,
        },
        {
          field: 'schedule',
          title: '至目前项目形象进度',
          width: 400,
        },
        {
          field: 'startStopYear',
          title: '建设起止年限',
          width: 200,
        },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        //筛选控件配置
        config: [
          {
            field: 'rateTime',
            title: '日期',
            element: 'slot',
            slotName: 'dateYear',
            rules: [{ required: true, message: '请选择年份' }],
          },
          {
            field: 'name',
            title: '项目名称',
          },
          {
            field: 'code',
            title: '项目状态',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.projectStatusList,
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        submitBtn: true,
        okBtn: false,
        addBtn: false,
        viewBtn: true,
        editBtn: true,
        delBtn: false,
        menu: true,
        menuWidth: 200,
        menuFixed: 'right',
        formConfig: [
          {
            field: 'rateTime',
            title: '年份',
            element: 'slot',
            slotName: 'dateYearForPop',
            rules: [{ required: true, message: '请选择年份' }],
          },
          {
            field: 'developmentOrganization',
            title: '建设单位',
            rules: [{ required: true, message: '请输入建设单位' }],
          },
          {
            field: 'projectName',
            title: '项目名称',
            rules: [{ required: true, message: '请输入项目名称' }],
          },
          {
            field: 'projectStatus',
            title: '项目状态',
            element: 'a-select',
            props: {
              options: this.projectStatusList,
              showSearch: true,
              optionFilterProp: 'children',
            },
            previewFormatter: (value) => {
              return this.translateValue(value, this.projectStatusList);
            },
            rules: [{ required: true, message: '请选择项目状态' }],
          },
          {
            field: 'totalInvestment',
            title: '计划总投资',
            props: {
              suffix: '亿元',
            },
            rules: [
              { required: true, validator: this.checkNum, trigger: 'change' },
            ],
          },
          {
            field: 'yearPlannedInvestment',
            title: '年度计划投资',
            props: {
              suffix: '亿元',
            },
            rules: [
              { required: true, validator: this.checkNum, trigger: 'change' },
            ],
          },
          {
            field: 'yearCompleteInvestment',
            title: '年度投资',
            props: {
              suffix: '亿元',
            },
            rules: [
              { required: true, validator: this.checkNum, trigger: 'change' },
            ],
          },
          {
            field: 'investmentVolume',
            title: '已完成投资',
            props: {
              suffix: '亿元',
            },
            rules: [
              { required: true, validator: this.checkNum, trigger: 'change' },
            ],
          },
          {
            field: 'domains',
            title: '领域',
            element: 'a-select',
            props: {
              options: this.domainsList,
              showSearch: true,
              optionFilterProp: 'children',
            },
            previewFormatter: (value) => {
              return this.translateValue(value, this.domainsList);
            },
            rules: [{ required: true, message: '请输入领域' }],
          },
          {
            field: 'constructionContent',
            title: '主要建设内容',
            element: 'a-textarea',
          },
          {
            field: 'schedule',
            title: '至目前项目形象进度',
            element: 'a-textarea',
          },
          {
            field: 'startStopYear',
            title: '建设起止年限',
            rules: [{ required: true, message: '请输入建设起止年限' }],
          },
        ],
      };
    },
  },
  created() {
    this.getCodeByType('project_status').then((res) => {
      this.projectStatusList = res;
    });
    this.getCodeByType('belong_domains').then((res) => {
      this.domainsList = res;
    });
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
