<template>
  <div class="full-screen"><screen :url="url"></screen></div>
</template>

<script>
import screen from '@/components/bigScreen';
import getBigScreenUrl from '@/global/bigScreen/bigScreenUrl';

export default {
  name: 'economicDevelopmentHeatMap',
  components: { screen },
  data() {
    return {
      url: getBigScreenUrl.call(this),
    };
  },
  mounted() {
    document.title = '经济科创一张图';
  },
};
</script>

<style scoped></style>
