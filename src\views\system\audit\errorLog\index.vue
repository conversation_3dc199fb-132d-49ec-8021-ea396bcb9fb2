<template>
  <page-layout>
    <PageWrapper
      style="margin: 0"
      title="异常日志"
      createText=""
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      @loadData="loadData"
      @handleCreate="handleCreate"
    >
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- 版本 -->
      <template #time="{ row }">{{ row.createTime }}</template>
      <!-- 状态 -->
      <!-- <template #state="{ row }">
          <a-switch
            checked-children="开"
            un-checked-children="关"
            v-model="row.state"
          />
        </template> -->
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row, 'edit')"
          >查看</span
        >
      </template>
    </PageWrapper>
    <LogDetail :visible.sync="infoVisible" :infoDetail="infoDetail"></LogDetail>
  </page-layout>
</template>

<script>
import LogDetail from './LogDetail';
import {
  // getFormPage,
  workflowStart,
  deleteWorkflow,
} from '@/api/system/workflow';
import { filterOptions, tableColumn } from './constant';
export default {
  components: { LogDetail },
  data() {
    return {
      loading: false,
      filterOptions,
      tableColumn,
      infoVisible: false,
      infoDetail: {},
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      visible: false,
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    // 请求接口数据
    async loadData() {
      this.loading = true;
      // const params = this.filterOptions.params;
      // const [res, err] = await getFormPage({
      //   limit: this.tablePage.pageSize,
      //   page: this.tablePage.currentPage,
      //   ...params,
      // });
      this.loading = false;
      // if (err) return;
      // // 设置数据
      // this.tablePage.total = res.count;
      // this.tableData = res.data;
    },
    // 创建bpmn流程图
    handleCreate() {
      this.$router.push({
        name: 'BpmnBlock',
      });
    },
    // 编辑数据
    onClickEdit(data, type) {
      if (type === 'edit') {
        this.$router.push({
          name: 'BpmnBlock',
          query: {
            id: data.configId,
          },
        });
      } else if (type === 'test') {
        // 复制流水线
        this.$confirm({
          title: '提示',
          content: () => <div>确认开启流水线么</div>,
          async onOk() {
            const [, err] = await workflowStart({
              itemId: 'HD66270922712159',
              itemName: '小练的测试活动',
              processKey: 'ACTIVITY_ONLINE',
              extParams: { remark: '测试' },
            });
            if (!err) {
              this.$message.success('发布成功');
              return;
            }

            // 刷新数据
            this.loadData();
          },
        });
      } else if (type === 'copy') {
        // 复制流水线
        this.$confirm({
          title: '提示',
          content: () => <div>确认复制:""流水线么</div>,
          async onOk() {
            // const [, err] = await workflowStart({
            //   itemId: "HD66270922712159",
            //   itemName: "小练的测试活动",
            //   processKey: "ACTIVITY_ONLINE",
            //   extParams: { remark: "测试" },
            // });
            // if (!err) {
            //   this.$message.success("发布成功");
            //   return;
            // }

            // 刷新数据
            this.loadData();
          },
        });
      } else if (type === 'publish') {
        // 部署
        this.$confirm({
          title: '提示',
          content: () => <div>部署后流程将升级为新版本，请确认操作！！</div>,
          onOk() {
            // const [res,err]=await deleteItem({
            //   id:'1'
            // })
            // if(!err){
            //   this.$message.success('发布成功')
            // return
            // }
            // 刷新数据
            this.loadData();
          },
        });
      } else if (type === 'delete') {
        this.$confirm({
          title: '提示',
          content: () => (
            <div>删除流程后，将无法按照该流程新建工作流任务，请谨慎操作！</div>
          ),
          async onOk() {
            const [, err] = await deleteWorkflow({ configId: data.configId }); // 刷新数据
            this.loadData();
            if (!err) {
              this.$message.success('删除成功');
              return;
            }
          },
        });
      }
    },
    // 取消业务
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
    },
    // 点击详情
    onClickDetail() {
      console.log('onClickDetail');
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  padding: 24px;
  background-color: #f4f4f4;
}
// table操作按钮
.operate-button {
  display: inline-block;
  margin-right: 16px;
  padding: 8px 0;
  color: #1677ff;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  cursor: pointer;
}
</style>
