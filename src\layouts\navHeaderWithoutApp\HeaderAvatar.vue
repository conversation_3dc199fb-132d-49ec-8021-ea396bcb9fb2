<template>
  <div class="header-avatar-wrap">
    <a-popover
      placement="bottomRight"
      overlayClassName="ant-popover-inner-content-no-padding"
    >
      <div class="header-avatar">
        <img class="avatar" :src="avatar" />
        <div class="user-info">
          <div class="main-line one-line-ellipsis">
            <!-- {{ merchantName || '商户名' }} -->
            {{ nickName || '用户名' }}
          </div>
          <!-- <div class="sub-line one-line-ellipsis">
            {{ username || '用户名' }}
          </div> -->
        </div>
        <a-icon class="arrow-down" type="down" />
      </div>
      <!-- overlay -->
      <div slot="content">
        <div class="pop-header-avatar">
          <img class="avatar" :src="avatar" />
          <div class="user-info">
            <div class="main-line">
              <!-- {{ merchantName || '商户名' }} -->
              {{ nickName || '用户名' }}
            </div>
            <!-- <div class="sub-line">
              {{ username || '用户名' }}
            </div> -->
          </div>
          <a-icon class="arrow-down" type="down" />
        </div>
        <a-menu>
          <a-menu-item
            v-if="merchantList && merchantList.length > 1"
            @click="showChangeMerchantModal"
          >
            <a-icon :style="{ marginRight: '8px' }" type="retweet" />
            <span>切换商户</span>
          </a-menu-item>
          <a-menu-item @click="() => (showDetailVisible = true)">
            <a-icon :style="{ marginRight: '8px' }" type="user" />
            <span>个人信息</span>
          </a-menu-item>
          <a-menu-item @click="logout">
            <a-icon :style="{ marginRight: '8px' }" type="logout" />
            <span>退出登录</span>
          </a-menu-item>
        </a-menu>
      </div>
    </a-popover>
    <UserDetail :visible.sync="showDetailVisible" :userId="user.userId" />
    <a-modal
      title="选择商户"
      :visible="changeMerchantModalVisible"
      okText="确定"
      cancelText="取消"
      @ok="showChangeMerchantModal"
      @cancel="closeChangeMerchantModal"
    >
    </a-modal>
  </div>
</template>

<script>
import { Modal } from 'ant-design-vue';
import UserDetail from './UserDetail';
import { mapState } from 'vuex';
export default {
  name: 'HeaderAvatar',
  components: { UserDetail },
  props: {
    avatar: {
      type: String,
      default: require('@/assets/images/logo/userLogo.png'),
    },
    username: {
      type: String,
      default: '',
    },
    nickName: {
      type: String,
      default: '',
    },
    merchantName: {
      type: String,
      default: '',
    },
    // 是否存在消息中心
    hasMessageAppInfo: {
      type: Object,
      default: () => {},
    },
    // 是否存在系统管理
    hasSystemAppInfo: {
      type: Object,
      default: () => {},
    },
    // 消息列表
    newsTipList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    ...mapState('base', ['user']),
  },
  mounted() {
    console.log(this.$attrs, 'attr');
  },
  data() {
    return {
      changeMerchantModalVisible: false,
      merchantList: [], // 系统管理logo
      systemLogo: require('@/assets/images/logo/systemLogo.png'),
      // 消息管理logo
      messageLogo: require('@/assets/images/logo/messageLogo.png'),
      showDetailVisible: false,
    };
  },
  methods: {
    /**
     * 切换商户
     */
    showChangeMerchantModal() {
      this.changeMerchantModalVisible = true;
    },
    closeChangeMerchantModal() {
      this.changeMerchantModalVisible = false;
    },
    /**
     * 退出登录
     */
    async logout() {
      Modal.confirm({
        title: '系统提示',
        content: '确定注销并退出系统吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.$emit('logout');
        },
      });
    },
    /**
     * 悬浮刷新列表
     */
    async handleMessageHoverChange(e) {
      this.$emit('handleMessageHoverChange', e);
    },
    /**
     * 去系统管理
     */
    async goToSystem() {
      window.location.href =
        this.hasSystemAppInfo.appPath || `${this.getBaseUrl()}/#/home`;
    },
    /**
     * 去消息管理
     */
    async goToMessage() {
      window.location.href =
        this.hasMessageAppInfo.appPath ||
        `${this.getBaseUrl()}/#/notify/notification`;
    },
    /**
     * 去消息管理
     */
    async checkMore() {
      window.location.href =
        this.hasMessageAppInfo.appPath ||
        `${this.getBaseUrl()}/#/notify/notification`;
    },
    // 获取路由
    getBaseUrl() {
      const firstName = window.location.pathname.replace(/^\/([^/]*).*$/, '$1');
      const baseURL = `${window.location.origin}${
        firstName ? '/' + firstName : ''
      }`;
      return baseURL;
    },
  },
};
</script>

<style lang="less" scoped>
.header-avatar-wrap {
  display: flex;
  height: 100%;
}
.header-extra-icon {
  padding: 0 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;
  .app-logo {
    flex: 0 0 auto;
    height: 34px;
    width: 34px;
  }
  .drop {
    width: 5px;
    height: 5px;
    background-color: rgb(255, 254, 84);
    position: absolute;
    top: 15px;
    right: 12px;
    border-radius: 50%;
  }
}
.pop-header-content {
  width: 350px;
  min-height: 200px;
  .top-title {
    padding: 8px 8px;
    border-bottom: 1px solid #f3f3f3;
    display: flex;
    justify-content: space-between;
    .top-title-right {
      cursor: pointer;
    }
  }
  .main-content {
    .main-content-item {
      padding: 8px 8px;
      border-bottom: 1px solid #f3f3f3;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行
      .content-item-time {
        margin-top: 2px;
      }
    }
    .main-content-item:last-child {
      border-bottom: none;
    }
  }
}
.pop-header-avatar,
.header-avatar {
  padding: 0 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 100%;
  color: #333;
  &:hover {
    background-color: rgba(255, 255, 255, 0.075);
  }
  &.pop-header-avatar {
    align-items: flex-start;
    padding: 16px 12px;
    border-bottom: 1px solid #e1e1e1;
    .avatar {
      margin-top: 4px;
    }
  }
  .avatar {
    flex: 0 0 auto;
    height: 27px;
    width: 27px;
    margin-right: 10px;
    border-radius: 4px;
  }
  .user-info {
    flex: 0 0 auto;
    max-width: 154px;
    min-width: 60px;
    font-size: 12px;
    line-height: 17px;
    color: #333;
    .main-line {
      font-weight: 700;
      font-size: 18px;
      line-height: 30px;
    }
    .sub-line {
      font-weight: 300;
    }
    .one-line-ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
      word-wrap: break-word;
    }
  }
  .arrow-down {
    margin-left: 8px;
    color: #fff;
  }
}
.pop-header-avatar {
  .main-line {
    font-weight: 700;
    font-size: 18px;
    line-height: 30px;
    max-width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px !important;
  }
}
</style>
<style lang="less">
.ant-popover-inner-content-no-padding {
  .ant-popover-inner-content {
    padding: 0;
    width: 180px;
    overflow: hidden;
    border-radius: 4px;
  }
  .ant-menu-vertical .ant-menu-item {
    height: 40px;
    line-height: 40px;
    margin: 0 !important;
    text-align: center;
  }
  .ant-menu-vertical .ant-menu-item:not(:last-child) {
    border-bottom: 1px solid #e8e8e8;
  }
}
</style>
