<template>
  <div class="layout-wrap">
    <PortraitsDetail page-name="taikePortraits"></PortraitsDetail>
  </div>
</template>

<script setup>
import PortraitsDetail from '../components/portraitsDetail.vue';
</script>

<style scoped lang="scss">
.layout-wrap {
  width: 100%;
  // height: 800px;
  height: calc(100vh - 100px);
}
::v-deep .portrait-cnt-header {
  margin-bottom: 0;
}
::v-deep .portrait-cnt-bottom {
  width: 1800px !important;
  height: 900px !important;
  margin-right: 0;
}
::v-deep .right-cnt {
  width: 100% !important;
}
</style>
