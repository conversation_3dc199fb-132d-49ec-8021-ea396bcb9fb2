<template>
  <a-form
    layout="horizontal"
    :labelCol="{ span: 6 }"
    :wrapperCol="{ span: 14 }"
    :form="form"
    @submit="handleSubmit"
  >
    <a-row type="flex">
      <a-col :span="8">
        <a-form-item label="阶段性反馈规定日期">
          <a-date-picker
            :disabled="true"
            v-decorator="[
              'stageDate',
              {
                rules: [
                  {
                    type: 'object',
                  },
                ],
                initialValue: formData.stageDate,
              },
            ]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="8">
        <a-form-item label="最近进度填报日期">
          <a-date-picker
            v-decorator="[
              'recentProgress',
              {
                rules: [
                  {
                    type: 'object',
                  },
                ],
                initialValue: formData.recentProgress,
              },
            ]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="8">
        <a-form-item label="当前完成情况">
          <a-input
            :suffix="formData.unit || '%'"
            v-decorator="[
              'completeStatus',
              {
                rules: [
                  {
                    type: 'string',
                    required: true,
                    message: '请填写当前完成情况',
                  },
                ],
                initialValue: formData.completeStatus,
              },
            ]"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item
          label="补充说明"
          :labelCol="{ span: 2 }"
          :wrapperCol="{ span: 20 }"
        >
          <a-textarea
            :auto-size="{ minRows: 3, maxRows: 6 }"
            v-decorator="[
              'supplementary',
              {
                rules: [
                  {
                    type: 'string',
                    required: true,
                    message: '请输入补充说明',
                  },
                ],
                initialValue: formData.supplementary,
              },
            ]"
          />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row type="flex" justify="end">
      <a-button type="default" class="mr10" @click="save"> 保存 </a-button>
      <a-button type="primary" html-type="submit"> 提交 </a-button>
    </a-row>
  </a-form>
</template>
<script>
export default {
  props: {
    formData: {
      type: Object,
      default() {
        return {
          stageDate: undefined,
          recentProgress: undefined,
          completeStatus: undefined,
          supplementary: '',
          //完成情况的单位
          unit: '个',
        };
      },
    },
  },
  data() {
    return {};
  },
  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'progressFillForm' });
  },
  methods: {
    save() {
      const formValues = this.form.getFieldsValue();
      console.log(
        '🚀 ~ file: progressFillForm.vue:102 ~ save ~ formValues:',
        formValues
      );
    },
    handleSubmit(e) {
      e.preventDefault();
      this.form.validateFields((err, fieldsValue) => {
        console.log(
          '🚀 ~ file: progressFillForm.vue:102 ~ this.form.validateFields ~ fieldsValue:',
          fieldsValue
        );
        if (err) {
          return;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-calendar-picker {
  display: block;
}
</style>
