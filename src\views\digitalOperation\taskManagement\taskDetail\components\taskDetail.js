//操作列表表头

function getTableColumn() {
  const _this = this;
  return [
    {
      title: '序号',
      type: 'seq',
    },
    {
      title: '操作人员',
      field: 'creatorName',
    },
    {
      title: '操作名称',
      field: 'status',
      formatter({ row, cellValue }) {
        console.log(row.remark, 'row----');
        const currentBiz = _this.operationStatus.filter((item) => {
          return item.value == cellValue;
        });
        let remark = '';
        cellValue === '5' ? (remark = `(${row.remark})`) : (remark = '');
        return currentBiz[0]
          ? `${currentBiz[0].label}${remark}`
          : `${cellValue}${remark}`;
      },
    },
    {
      title: '操作时间',
      field: 'createTime',
    },
  ];
}

export { getTableColumn };
