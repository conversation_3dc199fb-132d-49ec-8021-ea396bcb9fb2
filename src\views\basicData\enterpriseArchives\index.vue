<template>
  <page-layout>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tableColumn="tableColumn"
      :tablePage="tablePage"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirm"
      @loadData="loadData"
      @rowDel="rowDel"
      @rowEdit="rowEdit"
      @rowView="handlerView"
    >
      <template slot="defaultTitle">
        <span></span>
      </template>
      <template slot="defaultHeader">
        <div class="flex-row-10">
          <a-button type="primary" @click="handleCreate">新增</a-button>
          <a-button @click="toBatch">批量导入</a-button>
          <a-button @click="exportItem" :loading="exportLoading">导出</a-button>
          <a-button @click="batchDel" :loading="delAllLoading"
            >批量删除</a-button
          >
          <uploadBtn text="税号校验"></uploadBtn>
        </div>
      </template>
      <template #modalDefault="{ row }">
        <commonModel
          ref="commonModel"
          :parkList="parkList"
          :rowData="row"
          :operationType="operationType"
          :dataDict="dict.type"
          :labelList="labelList"
        ></commonModel>
      </template>
      <template #updateRecord="{ row }">
        <updateRecord :dataDict="dict.type" :rowData="row"></updateRecord>
      </template>
    </BuseCrud>
  </page-layout>
</template>

<script>
import { filterOption, disabledEndDate, disabledStartDate } from '@/utils';
import commonModel from './components/commonModel.vue';
import updateRecord from './components/updateRecord.vue';
import { resolveBlob } from '@/utils/common/fileDownload';
import uploadBtn from './components/uploadBtn.vue';
import {
  pageInfoEnterprise,
  updateEnterprise,
  addEnterprise,
  delEnterprise,
  exportEnterprise,
  getParkList,
  getLabelList,
} from '@/api/basicData/index.js';
import moment from 'moment';
export default {
  components: { commonModel, updateRecord, uploadBtn },
  dicts: [
    'enterprise_regulate_status',
    'enterprise_nature_status',
    'enterprise_register_status',
    'qcc_field_mapping',
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      filterParams: {
        name: '',
        unifiedCreditCode: '',
        address: '',
        status: undefined,
        parkId: undefined,
        regulate: undefined,
        nature: undefined,
        startTime: '',
        endTime: '',
      },
      operationType: '',
      dataDict: {},
      parkList: [],
      labelList: [],
      exportLoading: false,
      delAllLoading: false,
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'name',
            title: '企业名称',
            props: {
              placeholder: '请输入企业名称',
            },
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              placeholder: '请输入统一社会信用代码',
            },
            itemProps: {
              labelCol: { span: 10 },
              wrapperCol: { span: 14 },
            },
          },
          {
            field: 'address',
            title: '企业地址',
            props: {
              maxLength: 30,
              placeholder: '请输入企业地址',
            },
          },
          {
            field: 'status',
            element: 'a-select',
            title: '登记状态',
            props: {
              placeholder: '请选择登记状态',
              options: this.dict.type.enterprise_register_status,
            },
          },
          {
            field: 'parkId',
            element: 'a-select',
            title: '所属园区',
            props: {
              placeholder: '请选择所属园区',
              options: this.parkList,
              showSearch: true,
              filterOption: filterOption,
            },
          },
          {
            field: 'regulate',
            element: 'a-select',
            title: '是否规上',
            props: {
              placeholder: '请选择是否规上',
              options: this.dict.type.enterprise_regulate_status,
              showSearch: true,
              filterOption: filterOption,
            },
          },
          {
            field: 'nature',
            element: 'a-select',
            title: '企业性质',
            props: {
              placeholder: '请选择企业性质',
              options: this.dict.type.enterprise_nature_status,
              showSearch: true,
              filterOption: filterOption,
            },
          },
          {
            field: 'startTime',
            title: '更新开始时间',
            element: 'a-date-picker',
            props: {
              placeholder: '请选择更新开始时间',
              format: 'YYYY-MM-DD',
              valueFormat: 'YYYY-MM-DD',
              disabledDate: this.disabledStartDate,
            },
          },
          {
            field: 'endTime',
            title: '更新结束时间',
            element: 'a-date-picker',
            props: {
              placeholder: '请选择更新结束时间',
              format: 'YYYY-MM-DD',
              valueFormat: 'YYYY-MM-DD',
              disabledDate: this.disabledEndDate,
            },
          },
        ],
        params: this.filterParams,
        // showCount: 7,
      };
    },
    tableColumn() {
      return [
        {
          type: 'checkbox',
          fixed: 'left',
          width: 80,
        },
        {
          type: 'seq',
          title: '序号',
          width: 80,
          fixed: 'left',
        },
        {
          field: 'name',
          title: '企业名称',
          minWidth: 120,
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          minWidth: 140,
        },
        {
          field: 'address',
          title: '企业地址',
          minWidth: 120,
        },
        {
          field: 'synopsis',
          title: '企业简介',
          minWidth: 120,
        },
        {
          field: 'status',
          title: '登记状态',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.dict?.type?.enterprise_register_status?.find(
              (q) => q.value == cellValue
            )?.label;
          },
        },
        {
          field: 'parkId',
          title: '所属园区',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.parkList?.find((q) => q.value == cellValue)?.label;
          },
        },
        {
          field: 'industryCategory',
          title: '国标行业门类',
          minWidth: 120,
        },
        {
          field: 'industryBigCategory',
          title: '国标行业大类',
          minWidth: 120,
        },
        {
          field: 'industryMiddleCategory',
          title: '国标行业中类',
          minWidth: 120,
        },
        {
          field: 'industrySmallCategory',
          title: '国标行业小类',
          minWidth: 120,
        },
        {
          field: 'regulate',
          title: '是否规上',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.dict?.type?.enterprise_regulate_status?.find(
              (q) => q.value == cellValue
            )?.label;
          },
        },
        {
          field: 'nature',
          title: '企业性质',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.dict?.type?.enterprise_nature_status?.find(
              (q) => q.value == cellValue
            )?.label;
          },
        },
        {
          field: 'updateBy',
          title: '更新人',
          minWidth: 120,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          minWidth: 160,
        },
      ];
    },
    modalConfig() {
      return {
        addBtn: false,
        addBtnText: '测试',
        submitBtn: false,
        editBtn: true,
        delBtn: true,
        menuFixed: 'right',
        menuWidth: 250,
        // modalWith: this.operationType == 'VIEW' ? 1000 : 800,
        customOperationTypes: [
          {
            title: '更新记录',
            showForm: false,
            typeName: 'updateRecord',
            slotName: 'updateRecord',
            showDefault: false,
            okBtn: false,
            modalWith: 1000,
            event: (row) => {
              this.btnClickHandler('updateRecord', row);
            },
          },
        ],
      };
    },
  },
  watch: {},
  created() {
    this.getParkList();
    this.getLabelList();
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      const [res] = await pageInfoEnterprise({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...this.filterParams,
        startTime: this.filterParams.startTime
          ? this.filterParams.startTime + ' 00:00:00'
          : '',
        endTime: this.filterParams.endTime
          ? this.filterParams.endTime + ' 23:59:59'
          : '',
      });
      this.loading = false;
      if (res && res.data) {
        this.tableData = res.data;
        this.tablePage.total = res.total;
      }
    },
    disabledStartDate(val) {
      return disabledStartDate(val, this.filterParams.endTime);
    },
    disabledEndDate(val) {
      return disabledEndDate(val, this.filterParams.startTime);
    },
    // 打开弹窗
    btnClickHandler(operationType, row) {
      this.operationType = operationType;
      this.$refs.crud.switchModalView(true, operationType, row);
    },
    // 保存
    modalConfirm(row) {
      return new Promise((resolve, reject) => {
        this.$refs.commonModel?.$refs?.baseForm?.validate(async (err) => {
          if (!err) {
            resolve(false);
            return;
          }
          const params = {
            ...this.$refs.commonModel?.params,
            labelId: this.$refs.commonModel?.params?.labelId.join(','),
          };
          const { crudOperationType } = row;
          if (crudOperationType == 'add') {
            const [res] = await addEnterprise(params);
            if (res && res.code == '10000') {
              this.loadData();
              resolve();
            }
          } else {
            const [res] = await updateEnterprise(params);
            if (res && res.code == '10000') {
              this.loadData();
              resolve();
            }
          }
          resolve(false);
        });
      });
    },
    // 新增
    handleCreate() {
      this.btnClickHandler('ADD');
    },
    rowEdit(row) {
      this.btnClickHandler('UPDATE', row);
    },
    handlerView(row) {
      this.btnClickHandler('VIEW', row);
    },
    rowDel(row) {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [res] = await delEnterprise({ idList: [row.id] });
          if (res && res.code == '10000') {
            that.$message.success('删除成功');
            that.loadData();
          }
        },
      });
    },
    // 获得所属园区
    async getParkList() {
      const [res] = await getParkList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.parkList = res.data;
      }
    },
    // 获得企业标签
    async getLabelList() {
      const [res] = await getLabelList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.labelList = res.data;
      }
    },
    async exportItem() {
      this.exportLoading = true;
      const [res] = await exportEnterprise({
        ...this.filterParams,
        idList: this.$refs.crud.getCheckboxRecords().map((q) => q.id),
      });
      this.exportLoading = false;
      if (res) {
        const mimeMap = {
          xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
        };
        resolveBlob(res, mimeMap.xlsx, '企业档案', '.xlsx');
        this.$message.success('导出成功');
      }
    },
    async batchDel() {
      const list = this.$refs.crud.getCheckboxRecords();
      const that = this;
      if (Array.isArray(list) && list.length > 0) {
        const that = this;
        this.$confirm({
          title: '确认删除',
          content: () => '确认删除当前选中数据？',
          cancelText: '取消',
          okText: '确定',
          async onOk() {
            that.delAllLoading = true;
            const [res] = await delEnterprise({
              idList: list.map((q) => q.id),
            });
            that.delAllLoading = false;
            if (res && res.code == '10000') {
              that.$message.success('删除成功');
              that.loadData();
            }
          },
        });
      } else {
        this.$message.info('请选择要删除的内容');
      }
    },
    toBatch() {
      this.$router.push({ path: '/basicData/enterpriseArchivesBatchImport' });
    },
  },
};
</script>

<style scoped lang="less"></style>
