<template>
  <div class="task-detail">
    <a-descriptions title="基本信息" bordered :column="2">
      <a-descriptions-item label="创建人">{{
        taskDetail.creatorName
      }}</a-descriptions-item>
      <a-descriptions-item label="创建部门">
        {{ taskDetail.departmentName }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">
        {{ taskDetail.createTime }}
      </a-descriptions-item>
      <a-descriptions-item label="办理时限">
        {{ taskDetail.transactorTime }}
      </a-descriptions-item>
      <a-descriptions-item label="任务名称">
        {{ taskDetail.taskName }}
      </a-descriptions-item>
      <a-descriptions-item label="任务内容" :span="2">
        {{ taskDetail.content }}
      </a-descriptions-item>
      <a-descriptions-item label="工作要求" :span="2">
        {{ taskDetail.jobRequirement }}
      </a-descriptions-item>
      <a-descriptions-item label="办理人" :span="2">
        {{ taskDetail.transactorName }}
      </a-descriptions-item>
      <a-descriptions-item label="相关附件" :span="2">
        <downLoadTemplate :attachment="taskDetail.attachment" />
      </a-descriptions-item>
    </a-descriptions>
    <div class="instruction-detail">
      <a-descriptions title="批示情况" bordered :column="2">
        <a-descriptions-item label="批示人">
          {{ taskDetail.instructionName }}</a-descriptions-item
        >
        <a-descriptions-item label="批示">
          {{
            !taskDetail.instructionFlag
              ? ''
              : taskDetail.instructionFlag == '2'
              ? '不参加'
              : '参加'
          }}
        </a-descriptions-item>
        <a-descriptions-item label="批示时间" :span="2">
          {{ taskDetail.instructionTime }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="2">
          {{ taskDetail.instructionRemark }}</a-descriptions-item
        >
      </a-descriptions>
    </div>
    <div class="read-detail">
      <h3 class="read-detail-title">阅读情况</h3>
      <vxe-table :data="taskDetail.taskCcPeopleInfoResList">
        <vxe-column field="ccPeopleName" title="抄送人"></vxe-column>
        <vxe-column field="ifRead" title="阅读状态"
          ><template #default="{ row }">
            <span>{{ row.ifRead == '1' ? '已读' : '未读' }}</span></template
          ></vxe-column
        >
        <vxe-column field="readTime" title="阅读时间"></vxe-column>
      </vxe-table>
    </div>
    <a-form
      v-if="
        $route.query.detailPageType == 'myInstruction' &&
        !taskDetail.instructionFlag
      "
      :form="form"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 12 }"
      @submit="handleSubmit"
    >
      <h2 class="instruction-title">批示</h2>
      <a-form-item
        label="批示"
        :labelCol="{ span: 2 }"
        :wrapperCol="{ span: 12 }"
      >
        <a-radio-group
          v-decorator="[
            'status',
            {
              rules: [
                { type: 'string', required: true, message: '请选择批示' },
              ],
            },
          ]"
        >
          <a-radio value="1"> 参加 </a-radio>
          <a-radio value="2"> 不参加 </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="备注"
        :labelCol="{ span: 2 }"
        :wrapperCol="{ span: 12 }"
      >
        <a-textarea
          v-decorator="[
            'remark',
            {
              rules: [
                { type: 'string', required: true, message: '请填写备注' },
              ],
            },
          ]"
        ></a-textarea>
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 12, offset: 5 }">
        <a-button type="primary" html-type="submit"> 提交 </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { instruction } from '@/api/digitalOperation/taskManagement/myInstruction.js';
import downLoadTemplate from '@/components/downLoadTemplate/index.vue';
export default {
  name: 'ManagementTkTaskDetail',
  components: { downLoadTemplate },
  props: {
    taskDetail: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      remark: '',
      participate: '',
      // readDetailTableData: [
      //   {
      //     name: '张三',
      //     readState: '已读',
      //     readTime: '2020-01-21',
      //   },
      // ],
    };
  },
  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'dynamic_rule' });
  },
  mounted() {},

  methods: {
    async instruction(p) {
      const [, err] = await instruction(p);
      if (err) return;
      this.$message.success('批示成功');
      this.instructionSuccessJump();
    },
    //
    instructionSuccessJump() {
      this.$router.push({
        path: '/taskTarget/taskManagement/instruction',
      });
    },
    handleSubmit(e) {
      e.preventDefault();
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('Received values of form: ', values);
          let p = { ...values, id: this.taskDetail.id };
          this.instruction(p);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.task-detail {
  background: #fff;
  padding: 24px;
  /deep/.ant-descriptions-bordered {
    .ant-descriptions-item-label {
      width: 200px;
    }
  }
  .instruction-title {
    margin: 24px 0 16px 0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
    font-size: 16px;
    line-height: 1.5;
  }
  .instruction-detail {
    margin: 20px 0;
  }
  .read-detail-title {
    margin: 20px 0;
  }
}
</style>
