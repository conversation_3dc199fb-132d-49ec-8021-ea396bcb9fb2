<template>
  <a-modal
    width="600px"
    :title="
      modelTitle === 'add' ? '新增' : modelTitle === 'see' ? '详情' : '编辑'
    "
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="ruleForm"
        :config="formConfig"
        :params="formValue"
        :preview="preview"
      >
        <!-- 年份插槽 -->
        <template #year>
          <BuseRangePicker
            type="year"
            :needShowSecondPicker="() => false"
            v-model="formValue.year"
            format="YYYY"
            placeholder="请选择年份"
            :disableDateFunc="disableDateFunc"
          />
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment';
import { initFormValue } from '../constant';
import { addCompany, updateCompany } from '@/api/basicData';
import { institutionsMixin } from '../../mixins/institutionsMixin';
import FuzzySelect from '@/components/FuzzySelect/index.vue';
import { initParams } from '@/utils';

export default {
  props: ['visible', 'detail', 'preview', 'isLook', 'modelTitle'],
  mixins: [institutionsMixin],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.companyList = [];
          if (!this.detail) {
            this.formValue = initFormValue();
            return;
          }
          this.formValue.enterpriseName = this.detail.enterpriseName;
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
            year: {
              endOpen: false,
              endValue: null,
              startOpen: true,
              startValue: this.detail.year ? moment(this.detail.year) : '',
            },
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: {},
    };
  },
  computed: {
    formConfig() {
      return [
        {
          field: 'year',
          title: '年份',
          element: 'slot',
          slotName: 'year',
          // echoFormatter: (value) => {
          //   return { startValue: moment(value) };
          // },
          previewFormatter: (value) => {
            return value.startValue.format('YYYY');
          },
          rules: [
            { required: true, message: '请选择年份' },
            {
              validator: (rule, value, callback) => {
                console.log(value, 'value');
                if (value && value.startValue) {
                  callback();
                } else {
                  callback('请选择年份');
                }
              },
              trigger: 'blur',
            },
          ],
        },
        {
          field: 'enterpriseName',
          element: (h, item, params) => {
            return (
              <FuzzySelect
                value={params?.enterpriseName}
                onChangeSelect={(val) => {
                  if (val) {
                    this.formValue = {
                      ...this.formValue,
                      enterpriseName: val.name,
                      unifiedCreditCode: val.unifiedCreditCode,
                    };
                  } else {
                    this.formValue = {
                      ...this.formValue,
                      enterpriseName: '',
                      unifiedCreditCode: '',
                    };
                  }
                }}
                disabled={this.modelTitle == 'see'}
              />
            );
          },
          title: '企业名称',
          rules: [{ required: true, message: '请选择企业名称' }],
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          props: {
            placeholder: '请输入统一社会信用代码',
            disabled: true,
          },
        },
      ];
    },
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      // this.formConfig[0].props.options = this.stateSelect;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.formValue,
            year: this.formValue.year?.startValue
              ? moment(this.formValue.year?.startValue).format('YYYY')
              : '',
          };
          if (this.modelTitle === 'add') {
            this.addCompany(params);
          } else {
            this.updateCompany(params);
          }
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 调用父组件方法
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    // 新增
    async addCompany(params) {
      const [, err] = await addCompany(params);
      if (err) return;
      this.$message.success('新增成功!');
      this.handleCancel();
      this.$emit('loadData');
    },
    // 编辑
    async updateCompany(params) {
      const [, err] = await updateCompany(params);
      if (err) return;
      this.$message.success('编辑成功!');
      this.handleCancel();
      this.$emit('loadData');
    },
  },
  mounted() {},
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
