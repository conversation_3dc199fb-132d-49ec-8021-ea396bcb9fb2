<template>
  <div class="operation-buttons">
    <a-row v-bind="layout">
      <a-col
        v-for="(btn, index) in visibleButtons"
        :key="index"
        :span="buttonSpan ? buttonSpan : undefined"
        :class="[!buttonSpan ? 'btn-ml12' : '']"
      >
        <PopoverButton :loading="loading" :btn="btn" :row="row"></PopoverButton>
      </a-col>
      <a-col v-if="popoverButtons.length">
        <PopoverButton
          :loading="loading"
          :btn="{
            props: {
              type: 'link',
            },
            children: popoverButtons,
          }"
          :row="row"
          class="ellipsis-button"
        >
          <div class="ellipsis-button-dot"></div
        ></PopoverButton>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import PopoverButton from './PopoverButton';

/**
 * @name BtnGroup
 * @description 按钮组组件，用于展示一组按钮的组件
 * @param {Array|Function} btns - <required>按钮组数据，用以渲染按钮
 * @param {Object} btns.props - 按钮属性，参考 a-button 组件文档
 * @param {Object} btns.on - 按钮事件，参考 a-button 和原生 button 组件，不常用
 * @param {Array} btns.children - 若设置了此属性，button group 将被渲染为浮动气泡按钮组。数组内部结构参照 `btns.props`
 * @param {String} btns.event - 按钮点击事件，第一个参数是 props.row，建议使用异步函数或返回 Promise，以便控制按钮加载状态
 * @param {Function} btns.show - 按钮是否显示？如果第一个参数为 props.row 并返回值为 false，则该按钮不会被显示
 * @param {Object} row - 按钮所在模块的数据，如行数据，将会作为 event 和 show 函数的第一个参数传递
 * @param {Object} layout - 布局配置，参考 a-row 组件
 * @param {Number} buttonSpan - 一行中按钮所占用的网格数
 * @param {Number} maxCount - 最多显示几个按钮，超出的将会被隐藏
 * @param {Boolean} loading - 按钮组内各按钮是否处于加载状态，并维护或计算各自加载状态的关系
 */
export default {
  name: 'BtnGroup',
  components: {
    PopoverButton,
  },
  props: {
    btns: {
      type: [Array, Function],
      required: true,
      default: () => [],
    },
    row: {
      type: Object,
      // required: true,
      default: () => {},
    },
    layout: {
      type: Object,
      default: () => ({
        type: 'flex',
        justify: 'start',
      }),
    },
    buttonSpan: {
      type: Number,
      default: 0,
    },
    maxCount: {
      type: Number,
      default: 5,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      computedBtns: [],
      visibleButtons: [],
      popoverButtons: [],
    };
  },
  watch: {
    row: {
      immediate: true,
      handler() {
        // console.log('row', this.row);
        this.handlerInit();
      },
    },
    // btns: {
    //   handler() {
    //     this.handlerInit();
    //   },
    // },
  },
  methods: {
    handlerInit() {
      this.computedBtns = (
        typeof this.btns === 'function' ? this.btns(this.row) : this.btns
      )
        ?.map((item) => ({
          ...item,
          props: {
            type: 'link',
            loading: false,
            ...(item?.props || {}),
          },
        }))
        .map((item) => {
          const { event, show, props } = item;
          item.event = async function (row) {
            props.loading = true;
            await event(row);
            props.loading = false;
          };
          item.show =
            (typeof show === 'function' && show.bind(null, this.row)) ||
            function () {
              return true;
            };
          return item;
        })
        .filter((btn) => {
          return !Object.hasOwn(btn, 'show') || typeof btn.show === 'function'
            ? btn['show']()
            : !!btn.show;
        });
      this.visibleButtons = this.computedBtns.slice(0, this.maxCount);
      this.popoverButtons = this.computedBtns.slice(this.maxCount);
    },
  },
};
</script>
<style lang="less">
.in-row {
  width: 100%;
  text-align: left;
}

.btn-ml12 {
  & + .btn-ml12 {
    margin-top: 0;

    &:has(.in-row) {
      margin-top: 7px;
    }
  }

  &:not(:last-child) {
    margin-right: 12px;
  }
}
.ellipsis-button {
  display: flex;
  &-dot {
    width: 3px;
    height: 3px;
    background-color: #666666;
    border-radius: 50%;
    position: relative;
    top: 0.05em;
    &::after,
    &::before {
      content: '';
      display: block;
      width: 3px;
      height: 3px;
      background-color: #666666;
      border-radius: 50%;
      position: absolute;
      left: 0;
    }
    &::after {
      top: 0.4em;
    }

    &::before {
      top: -0.4em;
    }
  }
}
</style>
