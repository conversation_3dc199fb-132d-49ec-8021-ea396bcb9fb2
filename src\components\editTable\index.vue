<template>
  <div>
    <BuseCrud
      ref="crud"
      :class="className"
      :loading="loading"
      :tableData="useTableData"
      :modalConfig="modalConfig"
      :tableColumn="tableColumn"
      :tableProps="{
        headerAlign: 'center',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'center',
        highlightHoverRow: true,
        'max-height': 500,
        'show-footer': true,
        'min-height': 100,
        editConfig: preview ? {} : { trigger: 'click', mode: 'cell' },
        showFooter: showFooter,
        footerMethod: footerMethod,
        spanMethod: spanMethod,
        cellClassName: preview ? 'cell-auto' : '',
        editRules: editRules || [],
      }"
      @rowDel="rowDel"
    >
      <template v-for="(_, name) in $slots || []" #[name]="slotData">
        <slot :name="name" v-bind="slotData || {}"></slot>
      </template>
      <template slot="defaultTitle">
        <div class="text-wrap" v-show="title">
          <div class="h4-title">{{ title }}</div>
        </div>
      </template>

      <template v-if="addBtn">
        <template slot="defaultHeader">
          <a-button @click="addItem" v-show="addBtn && !preview"
            >添加行</a-button
          >
        </template>
      </template>
      <slot />
    </BuseCrud>
  </div>
</template>

<script>
import { initParams } from '@/utils';
import moment from 'moment';
export default {
  props: {
    editRules: {
      type: Object,
      default: () => {},
    },
    className: {
      type: String,
      default: '',
    },
    spanMethod: {
      type: Function,
      default: () => {
        return () => {
          return { rowspan: 1, colspan: 1 };
        };
      },
    },
    loading: {
      type: Boolean,
      default: () => false,
    },
    tableColumn: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
    },
    maxLength: {
      type: Number,
      default: 1000,
    },
    preview: {
      type: Boolean,
      default: () => false,
    },
    addBtn: {
      type: Boolean,
      default: () => true,
    },
    delBtn: {
      type: Boolean,
      default: () => true,
    },
    maxShowLength: {
      type: Number,
      default: 9999,
    },
    showFooter: {
      type: Boolean,
      default: false,
    },
    mergeFooterKeys: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
  data() {
    return {
      data: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      useTableData: [],
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        editBtn: false,
        viewBtn: false,
        menuFixed: 'right',
        menuWidth: 80,
        menu: !this.preview && this.delBtn,
      };
    },
    // useTableData: {
    //   get() {
    //     return this.value.map((q) => {
    //       return {
    //         ...q,
    //         keyId: uniqueId('key-'),
    //       }
    //     })
    //   },
    //   set(val) {
    //     console.log(val, 'val')
    //     this.$emit('input', val)
    //   },
    // },
  },
  watch: {
    value: {
      handler(n, o) {
        if (n.find((q) => typeof q.time == 'number')) {
          this.useTableData = n.map((q) => {
            return {
              ...q,
              time: q.time + '',
            };
          });
        } else {
          this.useTableData = n;
        }
      },
      deep: true,
      immediate: true,
    },
    useTableData: {
      handler(n, o) {
        this.$emit('input', n);
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    footerMethod({ columns, data }) {
      const keys = this.mergeFooterKeys;
      const sums = [];
      columns.forEach((column, columnIndex) => {
        if (columnIndex === 0) {
          sums.push('合计');
        } else {
          if (column.field) {
            const findData = keys.find((q) => q === column.field);
            if (findData) {
              // const computed = findData?.formatter
              const num = data.reduce((n, c) => {
                // return (n += computed ? computed(c) : +(c[column.field] ? c[column.field] : 0))
                n = (n + (+c[column.field] || 0)).toFixed(2);
                return n;
              }, 0);
              sums.push(num);
            } else {
              sums.push('');
            }
          }
        }
      });
      // 返回一个二维数组的表尾合计
      return [sums];
    },
    panelChange(val, index, row) {
      console.log(index, 'index');
      row.constructionPeriod = val;
    },
    getCalendarContainer(trigger) {
      return document.body;
    },
    changeSwitch(val, row) {
      if (
        this.useTableData.filter((q) => q.isDisplay).length >=
          this.maxShowLength &&
        val
      ) {
        this.$message.info(`最多展示${this.maxShowLength}行`);
        return;
      }
      row.isDisplay = val ? 1 : 0;
    },
    rowDel(row) {
      this.useTableData = this.useTableData.filter((q) => {
        return q['_X_ROW_KEY'] != row['_X_ROW_KEY'];
      });
      this.$forceUpdate();
    },
    addItem() {
      if (this.useTableData.length >= this.maxLength) {
        this.$message.info(`最多添加${this.maxLength}行数据`);
        return;
      }
      this.useTableData = [
        { ...initParams(this.tableColumn) },
        ...this.useTableData,
      ];
      this.$forceUpdate();
    },
    disabledStartMethod({ date, viewType }, row) {
      if (row.endYear) {
        let end = moment(row.endYear).format('yyyy');
        let target = moment(date).format('yyyy');
        return end < target;
      }
      return false;
    },
    disabledEndMethod({ date, viewType }, row) {
      if (row.endYear) {
        let start = moment(row.startYear).format('yyyy');
        let target = moment(date).format('yyyy');
        return start >= target;
      }
      return false;
    },
  },
};
</script>

<style scoped lang="less">
.text-wrap {
  height: 100%;
  display: grid;
  height: 58px;
  place-content: center;
}
</style>
