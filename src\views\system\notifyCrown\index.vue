<template>
  <page-layout>
    <PageWrapper
      style="margin: 0"
      title="报警器列表"
      createText="新增"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumns"
      :tableData="tableData"
      @loadData="loadData"
      @handleCreate="handleCreate"
    >
      <template #operation="{ row }">
        <a-button
          icon="edit"
          type="link"
          style="padding: 0; margin-right: 8px"
          @click="handleUpdate(row)"
        >
          编辑
        </a-button>
        <a-button
          icon="delete"
          type="link"
          style="padding: 0; margin-right: 8px"
          @click="handleDelete(row)"
        >
          删除
        </a-button>
      </template>
    </PageWrapper>
    <EditModal
      :visible.sync="editModalVisible"
      :contractForm="contractForm"
      @ok="loadData"
    />
  </page-layout>
</template>
<script>
import { dingTalkList, deleteDingTalk } from '@/api/system/dingTalk';
import EditModal from './EditModal';
import { Modal } from 'ant-design-vue';
import { filterOptions, tableColumns } from './constant.js';
export default {
  components: { EditModal },
  data() {
    return {
      loading: false,
      filterOptions,
      tableColumns,
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      // 弹窗编辑数据
      editModalVisible: false,
      contractForm: {},
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    /**
     * 列表查询
     */
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [result, error] = await dingTalkList({
        ...params,
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
      });
      this.loading = false;
      if (error) return;
      this.tableData = result.data;
      this.tablePage.total = result.count;
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.loading) return;
      const { groupName, dingNotifyId } = row;
      Modal.confirm({
        title: '警告',
        content: '是否确认删除报警群名为"' + groupName + '"的数据项?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.loading = true;
          const [, error] = await deleteDingTalk({ dingTalkId: dingNotifyId });
          this.loading = false;
          if (error) return;
          this.$message.success('删除成功');
          this.loadData();
        },
      });
    },
    /** 新增按钮操作 */
    handleCreate() {
      this.editModalVisible = true;
      this.contractForm = {};
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.editModalVisible = true;
      this.contractForm = row;
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .ant-form-item {
  margin-bottom: 0;
}
</style>
