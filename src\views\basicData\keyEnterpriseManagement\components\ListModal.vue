<template>
  <a-modal
    width="600px"
    :title="modelTitle === 'add' ? '新增' : '编辑'"
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm ref="ruleForm" :config="formConfig" :params="formValue">
        <template #areaSlot>
          <a-input
            placeholder="请输入数字"
            v-model="formValue.area"
            class="model-unit"
            type="number"
          />
          <span class="unit">m²</span>
        </template>
        <!-- 企业 -->
        <template #nameSlot>
          <a-select
            :allowClear="true"
            :filterOption="filterOption"
            :showSearch="true"
            placeholder="请选择"
            v-model="formValue.enterpriseId"
            :notFoundContent="null"
            :disabled="!formValue.parkId"
          >
            <a-select-option
              v-for="item in companyList"
              :key="item.id"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </template>
        <!-- 园区 -->
        <template #enterSlot>
          <a-select
            :allowClear="true"
            :filterOption="filterOption"
            :showSearch="true"
            placeholder="请选择"
            @change="parkChange"
            v-model="formValue.parkId"
          >
            <a-select-option
              v-for="item in affiliatedPark"
              :key="item.id"
              :value="item.id"
            >
              {{ item.parkName }}
            </a-select-option>
          </a-select>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import { initFormValue } from '../constant';
import { saveEmphasis, editEmphasis } from '@/api/basicData';
import * as api from '@/api/basicData/index';

export default {
  props: ['visible', 'detail', 'isLook', 'modelTitle', 'affiliatedPark'],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          this.parkId = this.detail?.parkId;
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
          };
        } else {
          this.formValue = initFormValue();
        }
        this.companyNameList();
      },
    },
  },
  data() {
    return {
      loading: false,
      companyList: [],
      parkId: '',
      formValue: initFormValue(),
      formConfig: [
        {
          field: 'parkId',
          title: '重点园区',
          element: 'slot',
          slotName: 'enterSlot',
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'enterpriseId',
          title: '重点企业',
          element: 'slot',
          slotName: 'nameSlot',
          rules: [{ required: true, message: '请输入重点企业' }],
        },
        {
          field: 'area',
          title: '载体面积',
          element: 'slot',
          slotName: 'areaSlot',
          rules: [
            { required: true, validator: this.checkNum, trigger: 'change' },
          ],
        },
      ],
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  mounted() {},
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      this.formConfig[0].props.options = this.stateSelect;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.modelTitle === 'add') {
            this.saveEmphasis();
          } else {
            this.editEmphasis();
          }
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 新增
    async saveEmphasis() {
      const [, err] = await saveEmphasis(this.formValue);
      if (err) return;
      this.$message.success('新增成功!');
      this.$emit('loadData');
    },
    // 编辑
    async editEmphasis() {
      const [, err] = await editEmphasis(this.formValue);
      if (err) return;
      this.$message.success('编辑成功!');
      this.$emit('loadData');
    },
    // 园区选择
    parkChange(value, option) {
      console.log('当前选择值', value, option);
      this.parkId = value;
      this.formValue.enterpriseId = '';
      this.companyNameList();
    },
    // 重点企业下拉
    async companyNameList() {
      const [res, err] = await api.companyNameList({
        parkId: this.parkId,
      });
      if (err) return;
      this.companyList = res.data.map((item) => {
        item.label = item.name;
        item.value = item.id;
        return item;
      });
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
