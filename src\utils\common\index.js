// 数据合并
export function mergeRecursive(source, target) {
  for (var p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p]);
      } else {
        source[p] = target[p];
      }
    } catch (e) {
      source[p] = target[p];
    }
  }
  return source;
}
/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]';
  }
  return Array.isArray(arg);
}

// 页面统计数据 保留两位小数 为0时展示0 为null时展示‘-’
export function isNullShowText(val) {
  if (
    val == null ||
    val == undefined ||
    val === '' ||
    isNaN(val) ||
    val === '-' ||
    val == 'null' ||
    val == 'undefined'
  ) {
    return '-';
  } else if (val === 0 || val === '0') {
    return 0;
  } else {
    // return val.toString()
    const parsedVal = parseFloat(val);
    if (Number.isInteger(parsedVal)) {
      return parsedVal.toLocaleString();
    } else {
      // return val.toString()
      return parseFloat(parsedVal.toFixed(4).toLocaleString());
    }
  }
}
