import { request } from '@/utils/request/requestTkb';

//工业企业 符合率排名
export function industryCoincidenceRate() {
  return request({
    url: `/safety/statistics/industryHistoryNotCompliant`,
    method: 'GET',
  });
}
//工业企业 每月检查情况
export function industryMonthly() {
  return request({
    url: `/safety/statistics/industryMonthly`,
    method: 'GET',
  });
}
//工业企业 各类行动检查情况
export function industryVariousAct() {
  return request({
    url: `/safety/statistics/industryVariousAct`,
    method: 'GET',
  });
}

//工业企业 历史检查隐患类型数量统计
export function industryHistoryDanger() {
  return request({
    url: `/safety/statistics/industryHistoryDanger`,
    method: 'GET',
  });
}
