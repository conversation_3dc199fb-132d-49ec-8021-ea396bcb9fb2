import moment from 'moment';
// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    fixed: 'left',
    width: 60,
  },
  {
    type: 'seq',
    title: '序号',
    width: 80,
  },
  {
    field: 'year',
    title: '年份',
    minWidth: 150,
  },
  {
    field: 'enterpriseName',
    title: '企业名称',
    minWidth: 150,
  },
  {
    field: 'unifiedCreditCode',
    title: '统一社会信用代码',
    width: 200,
  },
  {
    field: 'updateBy',
    title: '更新人',
    minWidth: 150,
  },
  {
    field: 'updateTime',
    title: '更新时间',
    minWidth: 180,
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 160,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'year',
    title: '年份',
    element: 'slot',
    slotName: 'year',
    // rules: [{ required: true, message: '请输入日期' }],
  },
  {
    field: 'name',
    title: '企业名称',
    element: 'a-input',
    // slotName: 'enterSlot',
  },
  {
    field: 'unifiedCreditCode',
    title: '统一社会信用代码',
    element: 'a-input',
    itemProps: {
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
    },
    // slotName: 'enterSlot',
  },
];

export const initFormValue = () => {
  return {
    rateTime: undefined,
    enterpriseId: '',
    enterprise: {},
    enterpriseName: '',
  };
};
