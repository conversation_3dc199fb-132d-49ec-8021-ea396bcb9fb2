import enquireJs from 'enquire.js';
import moment from 'moment';
import { isArray } from './validate';

/**
 * Remove an item from an array.
 */
export function remove(arr, item) {
  if (arr.length) {
    const index = arr.indexOf(item);
    if (index > -1) {
      return arr.splice(index, 1);
    }
  }
}

export function enquireScreen(call) {
  const handler = {
    match: function () {
      call && call(true);
    },
    unmatch: function () {
      call && call(false);
    },
  };
  enquireJs.register('only screen and (max-width: 767.99px)', handler);
}

/**
 * @desc 从一个对象通过操作序列来拿里面的值，做了基本防空措施
 * @param {object} state - 需要获取的数据源
 * @param {array} path - 操作路径
 * @param {any} initial - 默认值，当没有内容的时候
 * @example <caption>Example usage of getIn.</caption>
 * // testcase
 * {%common%}
 * // getIn
 * {%getIn%}
 * @returns {any} expected - 获取的值
 */
export function getIn(state, path, initial = null) {
  let obj = Object.assign({}, state);
  // path 数组判空
  if (!path || !isArray(path)) {
    return null;
  }
  for (const i of path) {
    // when is undefined return init immediately
    if (typeof obj !== 'object' || obj === null) {
      return initial;
    }

    const prop = i;

    obj = obj[prop];
  }
  if (obj === undefined || obj === null) {
    return initial;
  }

  return obj;
}

export function extend(target) {
  for (let i = 1, l = arguments.length; i < l; i++) {
    let arg = arguments[i];
    for (var key in arg) {
      Object.prototype.hasOwnProperty.call(arg, key) &&
        (target[key] = arg[key]);
    }
  }
  return target;
}

export function recursionData(data) {
  if (!data) return [];
  data.forEach((item) => {
    item.title = item.label;
    if (item.children && item.children.length) {
      recursionData(item.children);
    }
  });
  return data;
}

// 添加日期范围
export function addDateRange(params, dateRange) {
  var search = params;
  search.beginTime = '';
  search.endTime = '';
  if (dateRange != null && '' != dateRange) {
    search.beginTime = this.dateRange[0].format('YYYY-MM-DD');
    search.endTime = this.dateRange[1].format('YYYY-MM-DD');
  }
  return search;
}
export function getBaseUrl() {
  const firstName = window.location.pathname.replace(/^\/([^/]*).*$/, '$1');
  const baseURL = `${window.location.origin}${
    firstName ? '/' + firstName : ''
  }`;
  return baseURL;
}
// 通用下载方法
export function download(fileName) {
  const baseURL = process.env.VUE_APP_USE_BUILD_TYPE
    ? getBaseUrl()
    : process.env.VUE_APP_BASE_API;
  window.location.href =
    baseURL +
    '/common/download?fileName=' +
    encodeURI(fileName) +
    '&delete=' +
    true;
}

/**
 * 时间戳转换工具
 * @param {*} timestampNum 时间戳
 * @param {*} digitNum 小数位数
 * @param {*} sourceUnit 源时间戳单位，默认 ms
 * @param {*} targetUnit 转换时间戳单位，默认 h
 * @returns
 */
export function timestampTransform(
  timestampNum,
  digitNum = 0,
  sourceUnit = 'ms',
  targetUnit = 'h'
) {
  const hourNum = moment
    .duration(timestampNum, sourceUnit)
    .as(targetUnit)
    .toFixed(digitNum);
  return hourNum;
}
export function getUUID() {
  var data = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
  var nums = '';
  for (var i = 0; i < 9; i++) {
    var r = parseInt(Math.random() * 9 + 1);
    nums += data[r];
  }
  return nums + new Date().getTime();
}

/**
 * list转map
 * @param {[]} list
 * @param {String} keyName 转map作为key的字段名
 * @returns
 */
export function list2Map(list, keyName) {
  if (!isArray(list)) return {};
  let map = {};
  let item;
  for (let i = 0, len = list.length; i < len; i++) {
    item = list[i];
    map[item[keyName]] = item;
  }
  return map;
}
// 万，十万，百万级保留两位小数，千万保留一位小数，鼠标移入显示详细数据
export function formatNum(num) {
  if (typeof num != 'number') return num;
  const w = 10000;
  // const sw = 10 * w;
  // const bw = 100 * w;
  const qw = 1000 * w;
  const y = 10000 * w;

  switch (true) {
    case num < w:
      // 小于万
      return num;
    // case sw < w:
    //   return (num / w).toFixed(2) + '万';
    case num < qw:
      // 万到1千万之前
      return (num / w).toFixed(2) + '万';
    case num < y:
      // 千万到1亿之前
      return (num / w).toFixed(1) + '万';
    default:
      return (num / y).toFixed(2) + '亿';
  }

  // 一位或三位数字，后边是 有三位数组一组的(1个或多个), 然后匹配结束或者. ,?=,?:参与格式匹配，不参与结果匹配,$1为 (\d{1,3})的匹配结果
  // return String(num).replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, '$1,');
}
// 日期转换
// 数字补0操作
function addZero(num) {
  return num < 10 ? '0' + num : num;
}
export function formatDateTime(date) {
  const time = new Date(Date.parse(date));
  time.setTime(time.setHours(time.getHours() + 8));
  const Y = time.getFullYear() + '-';
  const M = addZero(time.getMonth() + 1) + '-';
  const D = addZero(time.getDate()) + ' ';
  const h = addZero(time.getHours()) + ':';
  const m = addZero(time.getMinutes()) + ':';
  const s = addZero(time.getSeconds());
  return Y + M + D + h + m + s;
}

/**
 *
 * @param {*} url // 图片路径
 * @param {*} name // 图片名称
 */
export function urlDownLoad(url, name) {
  let image = new Image();
  image.setAttribute('crossOrigin', 'anonymous');
  image.src = url;
  image.onload = () => {
    let canvas = document.createElement('canvas');
    canvas.width = image.width;
    canvas.height = image.height;
    let ctx = canvas.getContext('2d');
    ctx.drawImage(image, 0, 0, image.width, image.height);
    canvas.toBlob((blob) => {
      let href = URL.createObjectURL(blob);
      let Link = document.createElement('a');
      Link.download = name;
      Link.href = href;
      Link.click();
      Link.remove();
      // 用完释放URL对象
      URL.revokeObjectURL(href);
    });
  };
}

export function debounce(func, wait) {
  let timeout;

  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
