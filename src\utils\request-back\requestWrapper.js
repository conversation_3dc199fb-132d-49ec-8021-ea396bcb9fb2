import {
  handleCommonServiceError,
  handleCommonHttpError,
} from './errorHandler';
/**
 * 请求包装函数
 * @param {*} requestInstance 请求类
 * @param {*} handleError 统一错误处理
 * @param  {...any} args [请求参数,自定义错误处理函数]
 */
const requestWrapper = (requestInstance, ...args) => {
  const [params, customError] = args;
  return new Promise((resolve) => {
    requestInstance(params)
      .then((response) => {
        // 采取网关的接口，要在这里处理业务错误码。
        const hasServiceError = handleCommonServiceError(response);
        if (hasServiceError) {
          resolve([undefined, response.data]);
        } else {
          resolve([response.data, undefined]);
        }
      })
      .catch((error) => {
        // 走到这里，是 http 请求失败。所以一般走向是兜底错误页，并不是业务错误码。
        if (customError) {
          // 如果需要自定义错误处理
          customError(error, params);
        } else {
          // 统一错误处理
          handleCommonHttpError(error, params);
        }
        resolve([undefined, error]);
      });
  });
};

export default requestWrapper;
