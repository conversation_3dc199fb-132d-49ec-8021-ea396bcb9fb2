import { request } from '@/utils/request/requestTkb';

//消防安全 符合率排名
export function fireSafetyCoincidenceRate() {
  return request({
    url: `/safety/statistics/fireSafetyCoincidenceRate`,
    method: 'GET',
  });
}
//消防安全 每月检查情况
export function fireSafetyMonthly() {
  return request({
    url: `/safety/statistics/fireSafetyMonthly`,
    method: 'GET',
  });
}

//消防安全 各单位
export function fireSafetyUnit(params) {
  return request({
    url: `/safety/statistics/fireSafetyUnit`,
    method: 'GET',
    params,
  });
}
