<template>
  <div>
    <BuseCrud
      ref="crud"
      title="资源池管理"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      :tableOn="{
        'checkbox-change': handleTableSelect,
        'checkbox-all': handleTableSelect,
      }"
      :tableProps="{
        'checkbox-config': {
          checkMethod,
        },
      }"
      @loadData="loadData"
      @handleCreate="rowAdd"
    >
      <template slot="defaultTitle">
        <TabRadio
          v-model="shelfStatus"
          :radioList="statusRadioList"
          @change="onStatusChange"
          class="mb10"
        >
        </TabRadio>
      </template>
      <template #defaultHeader>
        <BtnGroup :btns="createBtn" :row="{ selectRows }" class="mr10" />
      </template>
      <template slot="pushResource" slot-scope="{ row }">
        <pushTransfer ref="pushResource" :params="pushParams" :rowInfo="row" />
      </template>
    </BuseCrud>
    <BuseModal
      ref="modal"
      :modalConfig="modal.modalConfig"
      :formConfig="formConfig"
      :submit="handleModalSubmit"
      v-model="modal.formParams"
      :type="modalType"
    >
      <template #batchPush="{ row }">
        <pushTransfer
          ref="batchPush"
          :params="batchPushParams"
          :rowInfo="row"
        />
      </template>
    </BuseModal>
  </div>
</template>

<script>
import pushTransfer from '@/components/pushTransfer/index.vue';
import BuseModal from '@/components/BuseModal/index.vue';
import BtnGroup from '@/components/BtnGroup/index.vue';
import { confirmFunc, getDictByApi } from '@/utils';
import { searchLabelNames } from '@/api/tagLibrary';

import TabRadio from '@/components/tabRadio/TabRadio.vue';
import { getTableColumn } from './resourceManagement';
import {
  getAllResType,
  getResList,
  onShelf,
  offShelf,
  push,
  removeResource,
  pushPublicBatch,
  batchPush,
  pushResByLabel,
} from '@/api/digitalOperation/resourcePool/resourceManagement/index.js';
import { getOrganize } from '@/views/digitalOperation/taskManagement/utils/index.js';
export default {
  name: 'resourceManagement',
  components: {
    TabRadio,
    pushTransfer,
    BuseModal,
    BtnGroup,
  },

  data() {
    return {
      pushParams: {},
      batchPushParams: {},
      tableData: [],
      shelfStatus: '1', //1在架，2下架
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      params: {
        publishTime: undefined,
        resType: undefined,
        resName: undefined,
      },
      resourceType: [],
      organizeAllList: [],
      modal: {
        modalConfig: [
          {
            type: 'push',
            props: {
              title: '推送',
              width: '400px',
              okText: '确定',
            },
            formProps: {
              //  layout: 'vertical',
              defaultColSpan: 24,
            },
          },
          {
            type: 'batchPush',
            props: {
              title: '定向推送',
              width: '800px',
              okText: '确定',
            },
            formProps: {
              defaultColSpan: 24,
            },
          },
        ],
        formParams: {},
      },
      pushType: [
        { label: '公开推送', value: '2' },
        { label: '按标签推送', value: '1' },
        { label: '定向推送', value: '0' },
        // { label: '按标签推送、定向推送', value: '3' },
      ],
      selectRows: [],
      enterpriseLabels: [],
      parkLabels: [],
      modalType: 'push',
    };
  },
  created() {
    this.statusRadioList = [
      {
        label: '在架资源',
        value: '1',
      },
      {
        label: '下架资源',
        value: '0',
      },
    ];
    this.getAllResType();
    this.tableColumn = getTableColumn.call(this);
  },
  computed: {
    formConfig() {
      return [
        {
          field: 'pushType',
          title: '推送方式',
          element: 'a-radio-group',
          props: {
            options: this.pushType,
          },
          itemProps: {
            labelCol: {
              span: 24,
            },
            wrapperCol: {
              span: 24,
            },
          },
          // on: {
          //   change: this.handlePushTypeChange,
          // },
          rules: [
            { required: true, message: '请选择推送方式', trigger: 'change' },
          ],
        },
        {
          field: 'pushType_1',
          title: '按标签推送到园区',
          element: 'a-checkbox-group',
          props: {
            options: [
              {
                label: '',
                value: '1',
              },
            ],
          },
          itemProps: {
            labelCol: {
              span: 10,
            },
            wrapperCol: {
              span: 14,
            },
            labelAlign: 'left',
          },
          show: this.modal.formParams?.pushType === '1',
        },
        {
          field: 'parkLabels',
          title: '',
          element: 'a-select',
          props: {
            options: this.parkLabels,
            mode: 'multiple',
            placeholder: '请选择园区标签',
            maxTagCount: 1,
            showSearch: true,
            filterOption(inputValue, option) {
              return option?.componentOptions?.children[0]?.text?.includes(
                inputValue
              );
            },
            getPopupContainer: (triggerNode) =>
              triggerNode.parentNode.parentNode.parentNode.parentNode.parentNode
                .parentNode.parentNode.parentNode,
          },
          itemProps: {
            wrapperCol: {
              span: 24,
            },
          },
          rules: [
            {
              required: !!(
                this.modal.formParams?.pushType === '1' &&
                this.modal.formParams?.pushType_1?.includes('1')
              ),
              type: 'array',
              message: '请选择园区标签',
              trigger: 'change',
            },
          ],
          show: !!(
            this.modal.formParams?.pushType === '1' &&
            this.modal.formParams?.pushType_1?.includes('1')
          ),
        },

        {
          field: 'pushType_2',
          title: '按标签推送到企业',
          element: 'a-checkbox-group',
          props: {
            options: [
              {
                label: '',
                value: '1',
              },
            ],
          },
          itemProps: {
            labelCol: {
              span: 10,
            },
            wrapperCol: {
              span: 14,
            },
            labelAlign: 'left',
          },
          show: this.modal.formParams?.pushType === '1',
        },
        {
          field: 'enterpriseLabels',
          title: '',
          element: 'a-select',
          props: {
            options: this.enterpriseLabels,
            mode: 'multiple',
            placeholder: '请选择企业标签',
            maxTagCount: 1,
            showSearch: true,
            getPopupContainer: (triggerNode) =>
              triggerNode.parentNode.parentNode.parentNode.parentNode.parentNode
                .parentNode.parentNode.parentNode,
            filterOption(inputValue, option) {
              return option?.componentOptions?.children[0]?.text?.includes(
                inputValue
              );
            },
          },
          itemProps: {
            wrapperCol: {
              span: 24,
            },
          },
          show: !!(
            this.modal.formParams?.pushType === '1' &&
            this.modal.formParams?.pushType_2?.includes('1')
          ),
          rules: [
            {
              required: !!(
                this.modal.formParams?.pushType === '1' &&
                this.modal.formParams?.pushType_2?.includes('1')
              ),
              type: 'array',
              message: '请选择企业标签',
              trigger: 'change',
            },
          ],
        },
      ];
    },
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            title: '发布时间',
            field: 'publishTime',
            element: 'a-range-picker',
          },
          {
            title: '资源类型',
            field: 'resType',
            element: 'a-select',
            props: {
              options: this.resourceType,
            },
          },
          {
            title: '资源名称',
            field: 'resName',
          },
        ],
      };
    },
    modalConfig() {
      return {
        editBtn: false,
        delBtn: false,
        addBtn: true,
        menuWidth: 300,
        viewBtn: false,
        modalWith: 1000,
        addBtnText: '发布资源',
        customOperationTypes: [
          {
            title: '查看推送详情',
            typeName: 'pushDetail',
            condition: (row) => {
              return !!(row.pushStatus == 1 && row.pushType !== '2');
            },
            event: (row) => {
              this.$router.push({
                path: '/policyResource/resourcePool/pushManagement',
                query: {
                  id: row.id,
                },
              });
            },
          },
          // {
          //   title: '推送资源',
          //   typeName: 'pushResource',
          //   slotName: 'pushResource',
          //   event: (row) => {
          //     return this.$refs.crud.switchModalView(true, 'pushResource', row);
          //     // this.pushResource(row);
          //   },
          //   condition: (row) => {
          //     return this.shelfStatus == '1' && row.pushStatus == 0;
          //   },
          // },
          {
            title: '编辑资源',
            typeName: 'editResource',
            event: (row) => {
              this.$router.push({
                path: '/policyResource/resourcePool/pushResource',
                query: { id: row.id, type: 'edit' },
              });
            },
            condition: (row) => {
              return this.shelfStatus == '0' && row.pushStatus == 0;
            },
          },
          {
            title: '查看资源详情',
            typeName: 'resourceDetail',
            event: (row) => {
              this.$router.push({
                path: '/policyResource/resourcePool/pushResource',
                query: { id: row.id, type: 'view' },
              });
            },

            condition: () => {
              return this.shelfStatus === '1';
            },
          },
          {
            title: '重新上架',
            typeName: 'reUp',
            event: (row) => {
              this.$confirm({
                title: '上架',
                content: `是否重新上架资源名称为‘${row.resName}’的资源？`,
                onOk: () => {
                  this.onShelf(row.id);
                },
                onCancel() {},
              });
            },
            condition: () => {
              return this.shelfStatus == '0';
            },
          },

          {
            title: '下架',
            typeName: 'down',
            event: (row) => {
              this.$confirm({
                title: '下架',
                content: `是否下架资源名称为‘${row.resName}’的资源？`,
                onOk: () => {
                  this.offShelf(row.id);
                },
                onCancel() {},
              });
            },
            condition: () => {
              return this.shelfStatus === '1';
            },
          },
          {
            title: '删除',
            typeName: 'del',
            event: (row) => {
              this.$confirm({
                title: '删除',
                content: `是否删除资源名称为‘${row.resName}’的资源？`,
                onOk: () => {
                  this.removeResource(row.id);
                },
                onCancel() {},
              });
            },
            condition: () => {
              return this.shelfStatus == '0';
            },
          },
        ],
      };
    },
  },

  mounted() {
    getOrganize().then((res) => {
      this.organizeAllList = res;
    });
    this.getLabelId({ labelClass: 1 });
    this.getLabelId({ labelClass: 2 });

    this.loadData();
  },

  methods: {
    checkMethod({ row }) {
      return row.pushType !== '2';
    },
    /**
     * @description:
     * @param {*} labelClass 1 园区 2 企业
     * @return {*}
     */
    async getLabelId({ labelClass = 1, labelName }) {
      const res = await getDictByApi({
        api: searchLabelNames,
        target: labelClass === 1 ? this.parkLabels : this.enterpriseLabels,
        params: {
          labelClass,
          labelName,
        },
        label: 'labelName',
        value: 'id',
      });
    },
    handleTableSelect({ records }) {
      this.selectRows = records;
    },
    async handleModalSubmit(val) {
      console.log('val', val);
      if (
        val?.pushType === '1' &&
        !val?.pushType_1?.length &&
        !val?.pushType_2?.length
      ) {
        this.$message.error('请选择推送标签');
        return false;
      }
      let params = {};
      let api = async function () {};
      let flag;
      switch (val.pushType) {
        case '2':
          params = this.selectRows.map((x) => x.id);
          api = pushPublicBatch;
          flag = await confirmFunc('确定要公开推送该资源吗？');
          if (!flag) return false;
          break;
        case '1':
          params = {
            resList: this.selectRows.map((x) => x.id),
            enterpriseLabels: val?.enterpriseLabels || [],
            parkLabels: val?.parkLabels || [],
          };
          api = pushResByLabel;
          break;
        case '0':
          this.modalType = 'batchPush';
          this.$refs.modal.open();
          return false;
        default:
          api = batchPush;
          if (!this.$refs.batchPush?.paramsSelectedRowKeys.length) {
            this.$message.error('请选择推送人员');
            //返回false，阻止弹窗关闭
            return false;
          } else {
            const { dataSource, paramsSelectedRowKeys } = this.$refs.batchPush;
            let parkUserIds = []; //B端用户id
            let govUserIds = []; //A端用户id
            dataSource.forEach((item1) => {
              paramsSelectedRowKeys.forEach((item2) => {
                if (item1.key === item2) {
                  if (item1.type == '1') {
                    govUserIds.push(item2);
                  } else {
                    parkUserIds.push(item2);
                  }
                }
              });
            });
            //推送接口
            params = {
              resList: this.selectRows.map((x) => x.id),
              parkUserIds,
              govUserIds,
              enterpriseUserIds: [],
            };
          }
      }
      console.log('params', params, 'api', api);
      const [, err] = await api(params);
      if (err) return;
      this.$message.success('推送成功');
      this.loadData();
    },
    createBtn() {
      return [
        {
          label: '推送',
          event: () => {
            // if (
            //   this.selectRows?.length === 1 &&
            //   this.selectRows[0].pushType !== null
            // ) {
            //   let row = this.selectRows[0];
            //   let arr = [+row.pushType];
            //   if (+row.pushType === 3) arr = [1, 2];
            //   this.pushType = this.pushType.map((x) => ({
            //     ...x,
            //     disabled: arr.includes(+x.value),
            //   }));
            // }
            this.modalType = 'push';
            this.$refs.modal.open();
          },
          props: {
            type: 'primary',
            disabled: !this.selectRows?.length,
          },
        },
      ];
    },
    async loadData() {
      this.loading = true;
      let p = this.paramsHandle();

      const [res, err] = await getResList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data.records;
      this.tablePage.total = res.data.total;
    },
    paramsHandle() {
      let p = {
        ...this.params,
        shelfStatus: this.shelfStatus,
        startTime: this.params.publishTime?.[0]?.format('YYYY-MM-DD'),
        endTime: this.params.publishTime?.[1]?.format('YYYY-MM-DD'),
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      };
      delete p.publishTime;
      return p;
    },
    //查询所有资源类型
    async getAllResType() {
      const [res, err] = await getAllResType();
      if (err) return;
      this.resourceType = res.data.map((item) => {
        return {
          value: item.id,
          label: item.resType,
        };
      });
    },
    //上架
    async onShelf(id) {
      const [, err] = await onShelf({ id });
      if (err) return;
      this.$message.success('上架成功');
      this.loadData();
    },
    //下架
    async offShelf(id) {
      const [, err] = await offShelf({ id });
      if (err) return;
      this.$message.success('下架成功');
      this.loadData();
    },
    //删除
    async removeResource(id) {
      const [, err] = await removeResource({ id });
      if (err) return;
      this.$message.success('删除成功');
      this.loadData();
    },

    //推送
    async push(p) {
      const [, err] = await push(p);
      if (err) return;
      this.$message.success('推送成功');
      this.loadData();
    },

    //推送
    pushConfirmHandler(value) {
      return new Promise((resolve) => {
        if (!this.$refs.pushResource?.paramsSelectedRowKeys.length) {
          this.$message.error('请选择推送人员');
          //返回false，阻止弹窗关闭
          resolve(false);
        } else {
          //推送接口
          let p = {
            resId: value.id,
            userIds: this.$refs.pushResource.paramsSelectedRowKeys,
          };
          this.push(p);

          resolve();
        }
      });
    },
    //弹窗确认按钮事件
    async modalConfirmHandler(value) {
      console.log('value', value);
      const { crudOperationType } = value;
      if (crudOperationType === 'pushResource') {
        const res = await this.pushConfirmHandler(value);
        return res;
      }
    },

    handleAdd() {
      //TODO:添加推送人
    },
    onStatusChange(value) {
      this.tableColumn = getTableColumn.call(this, value);
      this.loadData();
    },
    rowAdd() {
      this.$router.push({
        path: '/policyResource/resourcePool/pushResource',
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
