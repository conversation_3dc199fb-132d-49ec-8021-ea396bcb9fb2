<template>
  <BuseCrud
    ref="crud"
    title="归档任务"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @rowView="rowView"
  >
    <template slot="attachment" slot-scope="{ row }">
      <downLoadTemplate :attachment="row.attachment" />
    </template>
    <template slot="pageCenter">
      <div class="table-left archive-wrap">
        <div class="policy-dynamics-content-left">
          <div class="policy-dynamics-content-left-top">
            <span class="text">档案目录</span>
          </div>
          <div
            class="policy-dynamics-content-left-detail"
            :style="
              isMore
                ? 'max-height:58vh;overflow: scroll;'
                : ' overflow: hidden;'
            "
          >
            <div
              v-for="(item, index) in archiveListTemp"
              :key="index"
              class="detail-item"
            >
              <div class="item-header">
                <h6>{{ item.label }}</h6>
                <a
                  :style="{ marginLeft: '8px', fontSize: '12px' }"
                  @click="toggle(item)"
                >
                  {{ item.expand ? '收起' : '展开' }}
                  <a-icon :type="item.expand ? 'up' : 'down'" />
                </a>
              </div>
              <div
                class="item-tags"
                :style="item.expand ? '' : 'max-height: 76px;min-height:38px'"
              >
                <a-tag
                  v-for="(tag, tagIndex) in item.children"
                  :key="tagIndex"
                  color="#f7f8fa"
                  @click="tagClick(tag)"
                  :class="[tag.checked && 'tag-checked']"
                >
                  {{ tag.label }}
                </a-tag>
              </div>
            </div>
          </div>
          <div :class="['more', isMore && 'is-more']">
            <a @click="moreHander()">
              更多
              <a-icon type="double-left" />
            </a>
          </div>
        </div>
      </div>
    </template>
  </BuseCrud>
</template>

<script>
import FileImgs from '@/assets/config/art_detail.js';
import { getFileName, getFileType } from '@/utils/index';
import { getTableColumn } from './pigeonhole';
import {
  archiveList,
  catalogue,
  revocation,
} from '@/api/digitalOperation/taskManagement/pigeonhole.js';
import downLoadTemplate from '@/components/downLoadTemplate/index.vue';
export default {
  name: 'Pigeonhole',
  components: { downLoadTemplate },
  data() {
    return {
      FileImgs,
      getFileName,
      getFileType,
      tableData: [],
      isMore: false, //是否展示更多
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      archiveList: [],
      archiveListTemp: [],
      params: {
        archiveName: '',
        archiveCatalogue: '',
      },
      year: '', //档案名称
      taskTypeName: '', //档案目录
    };
  },
  created() {
    this.tableColumn = getTableColumn();
  },
  computed: {
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            title: '档案',
            field: 'archiveName',
          },
          {
            title: '目录',
            field: 'archiveCatalogue',
          },
        ],
      };
    },
    modalConfig() {
      return {
        editBtn: false,
        delBtn: false,
        viewBtn: true,
        addBtn: false,
        customOperationTypes: [
          {
            title: '撤档',
            typeName: 'revocation',
            event: (row) => {
              let that = this;
              this.$confirm({
                content: `确认要撤档吗？`,
                onOk() {
                  return new Promise((resolve) => {
                    that.revocation(row);
                    resolve();
                  });
                },
                cancelText: '取消',
              });
            },
            condition: (row) => {
              return row.ifRevocation != '1';
            },
          },
        ],
      };
    },
  },

  mounted() {
    this.loadData();
    this.catalogue();
  },

  methods: {
    async loadData(flag) {
      this.loading = true;
      let p = this.paramsHandle(flag);
      const [res, err] = await archiveList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    paramsHandle(flag) {
      let p = {
        archiveCatalogue:
          flag === 1
            ? `${this.year}/${this.taskTypeName}`
            : this.params.archiveCatalogue, //档案目录
        archiveName: this.params.archiveName,
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      };

      return p;
    },
    async revocation(row) {
      const [, err] = await revocation(row.id);
      if (err) return;
      this.$message.success('撤档成功');
      this.loadData();
    },
    //档案目录查询
    async catalogue() {
      const [res, err] = await catalogue();
      if (err) return;
      let data = res?.data;
      data = JSON.parse(
        JSON.stringify(data)
          .replace(/year/g, 'label')
          .replace(/filingCatalogueResList/g, 'children')
          .replace(/taskTypeName/g, 'label')
          .replace(/taskTypeValue/g, 'value')
      );
      let arr = this.addExpand(data);
      this.archiveList = arr;
      this.archiveListTemp = this.archiveList.slice(0, 3);
    },
    //左边数据是否展开expand字段
    addExpand(data) {
      data.forEach((element) => {
        element.expand = false;
        element.children.forEach((item) => {
          item.checked = false;
        });
      });
      return data;
    },
    tagClick(tag) {
      let flag = tag.checked;
      this.archiveListTemp.forEach((element) => {
        element.children.forEach((item) => {
          if (item == tag) {
            this.year = element.label;
          }
          item.checked = false;
        });
      });

      if (!flag) {
        tag.checked = true;
        this.taskTypeName = tag.label;
      } else {
        this.taskTypeName = '';
        this.year = '';
      }
      this.loadData(1);
    },
    toggle(item) {
      item.expand = !item.expand;
    },
    moreHander() {
      this.isMore = !this.isMore;
      this.isMore
        ? (this.archiveListTemp = this.archiveList)
        : (this.archiveListTemp = this.archiveList.slice(0, 3));
    },
    rowView(row) {
      this.$router.push({
        path: '/taskTarget/taskManagement/instructionDetail',
        query: {
          id: row.taskId,
          detailPageType: 'pigeonhole', //归档
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.bd3001-table-wrap {
  display: flex;
  flex-direction: row;
  .table-left {
    flex: 2.5;
  }
  .bd3001-content {
    flex: 8;
  }
}
.policy-dynamics-content-left {
  // background: url('@/assets/images/knowledeg-bg.png') no-repeat;
  // background-size: 100% 282px;
  background-color: #fff;
  width: 505px;
  // min-height: 65vh;
  margin-right: 12px;
  padding: 12px;

  .policy-dynamics-content-left-top {
    display: flex;
    align-items: center;
    height: 29px;
    img {
      margin-right: 6.5px;
      width: 17.43px;
      height: 20.31px;
    }
    .text {
      font-family: Alimama ShuHeiTi;
      font-size: 20px;
      font-weight: bold;
      line-height: 29px;
      letter-spacing: 0px;
      color: #333333;
    }
  }

  .policy-dynamics-content-left-detail {
    position: relative;
    margin-top: 12px;
    width: 100%;
    border-radius: 12px 12px 0px 0px;
    padding: 12px;
    background: #fff;

    a {
      color: #1890ff;
    }
    .detail-item:not(:first-child) {
      margin-top: 20px;
    }
    .item-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      h6 {
        font-family: HarmonyOS Sans SC;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: #333333;
        margin-bottom: 0;
      }
    }
    .item-tags {
      margin-bottom: 8px;
      overflow: hidden;

      .tag-checked {
        background-color: #edf7ff !important;
        color: #1890ff;
      }
      span {
        margin: 8px 0 0 4px;
        color: #767676;
        padding: 2px 6px;
        font-size: 14px;
      }
    }
  }
  .more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    padding: 32px 0 12px;
    bottom: 12px;
    background-color: #fff;
    a {
      color: #1890ff;
    }
    i {
      transform: rotate(-90deg);
    }
  }
  .is-more {
    i {
      transform: rotate(90deg);
    }
  }
}
</style>
