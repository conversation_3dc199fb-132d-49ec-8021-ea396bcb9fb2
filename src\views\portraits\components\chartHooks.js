// import echarts from 'echarts';
export function useLineCharts({
  xAxis = [],
  unit = '万元',
  series = [],
  grid = {
    left: 35,
    right: 10,
    top: 40,
    bottom: 20,
  },
  showDataZoom = false, // 新增参数控制是否显示dataZoom
  dataZoomOptions = {}, // 新增参数允许自定义dataZoom配置
  formatDate = false, // 新增参数控制是否格式化日期
  dateFormat = 'YY-MM', // 新增参数控制日期格式化的格式
}) {
  if (!series?.length) return {};

  const legend =
    series?.length > 1
      ? {
          data: series.map((x) => x.name),
          textStyle: {
            color: '#666666',
            fontSize: 12,
          },
          icon: 'rect',
          right: 0,
          top: 8,
          itemWidth: 8,
          itemHeight: 8,
        }
      : undefined;

  // 默认dataZoom配置
  const defaultDataZoom = {
    show: true,
    type: 'slider',
    realtime: true,
    startValue: 0,
    endValue: 10,
    xAxisIndex: [0],
    bottom: '10',
    left: '30',
    height: 10,
    handleIcon:
      'path://M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
    handleSize: 16,
    borderColor: 'rgba(0,0,0,0)',
    textStyle: {
      color: '#86909C',
    },
  };

  // 合并自定义配置
  const finalDataZoom = { ...defaultDataZoom, ...dataZoomOptions };

  // 如果需要格式化日期，使用moment处理xAxis数据
  let formattedXAxis = xAxis;
  if (formatDate && xAxis && xAxis.length > 0) {
    // 引入moment
    const moment = require('moment');
    formattedXAxis = xAxis.map((date) =>
      moment(date, 'YYYY-MM').format(dateFormat)
    );
  }

  const options = {
    legend,
    grid: showDataZoom
      ? { ...grid, bottom: Math.max(grid.bottom, 40) } // 如果显示dataZoom，增加底部空间
      : grid,
    tooltip: {
      show: true,
      // 触发类型：坐标轴触发
      trigger: 'axis',
      backgroundColor: 'rgba(229,237,250,0.5)', // 通过设置rgba调节背景颜色与透明度
      borderWidth: '0',
      textStyle: {
        color: '#999999',
      },
      formatter: function (info) {
        let str = `<div style="text-align: left; color:#1D2129;" >${info[0].name}</div>`;
        info.forEach((item, index) => {
          str += `<div style="
				background-color: rgba(255, 255, 255, 0.8);
				height: 32px;
				display: flex;
				justify-content: space-between;
				padding: 8px;
				border-radius: 4px;
				margin: 4px 0;"
      >
      <span style="color: #4e5969; font-size: 12px">${item.marker}${
            item.seriesName
          }</span>
      <span style="color: #1d2129; font-size: 13px;margin-left:30px">${Number(
        item.value
      )
        ?.toFixed(2)
        .toLocaleString()}</span></div>`;
        });
        return str;
      },
      axisPointer: {
        //坐标轴指示器，坐标轴触发有效，
        type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
        shadowStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(7,185,185,0)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(0,181,120,0.12)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    },
    xAxis: {
      type: 'category',
      data: formattedXAxis || [],
      interval: 1,
      axisLabel: {
        color: '#999999',
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.06)',
        },
      },
    },
    yAxis: {
      name: '单位：' + unit,
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.06)',
          // 虚线
          type: 'dashed',
        },
      },
      nameTextStyle: {
        color: '#999999',
      },
      axisLabel: {
        color: '#999999',
      },
    },
    // 条件性添加dataZoom
    ...(showDataZoom ? { dataZoom: [finalDataZoom] } : {}),
    series: series.map(
      ({ name = '税收', data = [], color = '#61DDAA', type = 'bar' }) =>
        type === 'bar'
          ? {
              name,
              type,
              data,
              symbol: 'circle',
              symbolSize: 6,
              barWidth: '10px',
              showSymbol: false,
              itemStyle: {
                normal: {
                  color,
                },
              },
            }
          : {
              name,
              type,
              data,
              symbol: 'circle',
              symbolSize: 6,
              showSymbol: false,
              itemStyle: {
                normal: {
                  color,
                },
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color, // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(0, 0, 0, 0)', // 100% 处的颜色
                    },
                  ],
                },
              },
            }
    ),
  };

  return options;
}

export function usePieCharts(
  { data = [], direction = 'right', legendType = 'scroll' },
  isLog
) {
  let chartData = data;
  if (chartData.length) {
    if (!chartData[0].name) {
      const nameLKey = Object.keys(chartData[0]).find((key) =>
        isNaN(+chartData[0][key])
      );
      const valueKey = Object.keys(chartData[0]).find(
        (key) => !isNaN(+chartData[0][key])
      );
      chartData = chartData.map((item) => {
        return {
          name: item[nameLKey] || '',
          value: item[valueKey],
        };
      });
      // console.log('🚀 ~ usePieCharts ~ nameLKey:', nameLKey, valueKey);
    }
  } else {
    return {};
  }
  const total = chartData.reduce((acc, cur) => acc + cur.value, 0);
  const center = direction === 'right' ? ['80px', '50%'] : ['50%', '65px'];

  let colorList = [
    '#3868fc',
    '#cb20f2',
    '#01b8b9',
    '#f68a00',
    '#f82525',
    '#24cf86',
    '#fc7948',
    '#e771c5',
    '#349867',
    '#f9c14e',
    '#8f55ab',
    '#68b8d9',
  ];

  const option = {
    toolbox: {
      show: false,
    },
    legend: {
      type: legendType,
      pageIconSize: 8,
      pageButtonItemCount: 3,
      pageButtonGap: 5,
      pageIconColor: '#333333',
      pageTextStyle: {
        color: '#333333',
        fontSize: 12,
      },
      ...(direction === 'right'
        ? {
            right: '5%',
            top: '10%',
          }
        : {
            left: '0',
            bottom: '0',
          }),
      orient: direction === 'right' ? 'vertical' : 'horizontal',
      icon: 'roundRect',
      itemGap: 10,
      itemWidth: 5,
      itemHeight: 10,
      height: direction === 'right' ? 'auto' : 60,
      formatter: function (name) {
        const item = chartData.filter((ele) => {
          if (ele.name == name) {
            return true;
          }
          return false;
        });
        let total = 0;
        for (let i = 0; i < chartData.length; i++) {
          total += chartData[i].value;
        }
        let per = ((item[0].value / total) * 100).toFixed(2);
        if (isNaN(per)) {
          per = '0';
        }
        const arr = ['{b|' + item[0].name + '}', '{d|' + per + '%}'];
        return arr.join(' ');
      },
      data: chartData.map((ele, index) => {
        return {
          name: ele.name,
          textStyle: {
            rich: {
              b: {
                color: '#333333',
                padding: 6,
                fontSize: 10,
              },
              d: {
                color: '#333333',
                padding: 6,
                fontSize: 10,
                fontWeight: 600,
              },
            },
          },
        };
      }),
    },

    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(229,237,250,0.5)', // 通过设置rgba调节背景颜色与透明度
      borderWidth: '0',
      textStyle: {
        color: '#333333',
      },
      formatter: function (info) {
        console.log(
          '🚀 ~ file: chartHooks.js ~ line 109 ~ formatter ~ info',
          info
        );
        if (info.seriesName === '外圈' && info.data.name) {
          // 只对外圈系列显示 tooltip
          let str = `<div style="text-align: left; color:#000;font-weight:700" >${info.data.name}</div>`;
          const arr = [
            {
              label: '数量',
              value: Number(info.value).toFixed(2).toLocaleString(),
            },
            { label: '占比', value: info.percent + '%' },
          ];
          arr.forEach((item) => {
            str += `<div style="
background-color: rgba(255, 255, 255, 0.8);
height: 32px;
display: flex;
justify-content: space-between;
padding: 8px;
border-radius: 4px;
margin: 4px 0;"
>
<span style="color: #4e5969; font-size: 12px">${item.label}</span>
<span style="color: #1d2129; font-size: 13px;margin-left:30px">${item.value}</span></div>`;
          });
          return str;
        } else {
          return ''; // 对于其他系列返回空，使得不显示 tooltip
        }
      },
      axisPointer: {
        //坐标轴指示器，坐标轴触发有效，
        type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
        shadowStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(7,185,185,0)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(0,181,120,0.12)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    },
    series: [
      {
        name: '中心背景',
        type: 'pie',
        startAngle: 90,
        center,
        hoverAnimation: false,
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },

        labelLine: {
          show: false,
        },
        itemStyle: {
          labelLine: {
            show: false,
          },
          normal: {
            color: 'rgba(239, 242, 250)',
          },
        },
        radius: direction === 'right' ? ['45px', 0] : [0, '38px'],
        data: [
          {
            value: 100,
          },
        ],
      },
      {
        name: '内圈',
        type: 'pie',
        center,
        radius: direction === 'right' ? ['72px', '65px'] : ['58px', '52px'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },

        labelLine: {
          show: false,
        },
        itemStyle: {
          normal: {
            borderRadius: '50%',
          },
        },
        // hoverAnimation: false,
        data: chartData.flatMap((data, i) => [
          {
            value: data.value,
            name: data.name,
            itemStyle: {
              normal: {
                color: colorList[i],
              },
            },
          },
          {
            value: total / 100,
            itemStyle: {
              normal: {
                color: 'rgba(0, 0, 0, 0)',
              },
            },
          },
        ]),
      },
      {
        name: '外圈',
        type: 'pie',
        center,
        radius: direction === 'right' ? ['80px', '68px'] : ['65px', '55px'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
        itemStyle: {
          normal: {
            borderRadius: '50%',
          },
        },
        hoverAnimation: false,
        data: chartData.flatMap((data, i) => [
          {
            value: data.value,
            name: data.name,
            itemStyle: {
              normal: {
                color: hexToRgba(colorList[i], 0.35),
              },
            },
          },
          {
            value: total / 100,
            itemStyle: {
              normal: {
                color: 'rgba(0, 0, 0, 0)',
              },
            },
          },
        ]),
      },
    ],
  };

  return option;
}

function hexToRgba(hex, alpha = 1) {
  if (!hex) return '';
  // 移除可能存在的哈希符号
  hex = hex.replace('#', '');
  // 处理短格式（#RGB）
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }
  // 解析十六进制值
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}
