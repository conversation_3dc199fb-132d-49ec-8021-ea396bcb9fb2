// 重置初始样式

main {
  padding: 0px;
}
.flex-row-10 {
  display: flex;
  flex-direction: row;
  gap: 10px;
}
.vxe-table {
  .vxe-header--column:first-child {
    .vxe-cell {
      padding-left: 16px;
    }
  }
  .vxe-body--column:first-child {
    .vxe-cell {
      padding-left: 16px;
    }
  }
  .vxe-cell {
    .operate-button {
      color: @primary-color;
    }
  }
  .bd3001-btns-container .bd3001-link {
    color: @primary-color;
  }
  .vxe-table--fixed-right-wrapper.scrolling--middle {
    box-shadow: -8px 0px 4px -4px rgba(0, 0, 0, 0.12);
  }
}
.full-width {
  width: 100% !important;
  min-width: 150px;
}
.ant-input-search {
  &.without-search-icon {
    .ant-input-suffix {
      display: none;
    }
  }
}
.info-card {
  margin: 0 0 16px !important;
}
.detail-header {
  background-color: @g-background-white !important;
  padding: 16px !important;
  /deep/ .ant-page-header-heading-title {
    font-size: 18px !important;
  }
}

// antd按钮渐变背景
.ant-btn-primary {
  background: linear-gradient(to right, @primary-color, @primary-color);
  background-color: @primary-color;
}
.ant-btn-primary:focus,
.ant-btn-primary:hover {
  background-color: @primary-color;
}
.ant-menu-item > a:hover {
  color: @menu-color;
}
// .ant-menu-item-selected > a:hover{
//   color: #fff;
// }
.ant-menu-submenu-title:hover {
  color: @menu-color !important;
}
.full-screen {
  position: fixed;
  inset: 0;
  z-index: 999999999999;
  background: #fff;
  overflow: auto;
}
.input-unit {
  display: inline-block;
  width: 30px;
  text-align: center;
}
