<template>
  <BuseCrud
    ref="crud"
    title="账号列表"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @handleCreate="rowAdd"
    @rowEdit="rowEdit"
    @modalConfirm="modalConfirmHandler"
  >
  </BuseCrud>
</template>

<script>
import {
  parkUserList,
  saveOrEdit,
  changeState,
} from '@/api/digitalOperation/accountManagement/index.js';

import {
  getAccountState,
  getAccountType,
  getOrganize,
} from '@/views/digitalOperation/taskManagement/utils/index.js';
export default {
  name: 'ToDoList',

  data() {
    return {
      tableData: [
        {
          name: '无锡太湖湾科创城管理服务中心有限公司',
          type: '企业',
          account: 'zj3322',
          password: '123456',
          status: 1,
        },
      ],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      params: { parkName: undefined, state: undefined },
      accountState: [],
      organizeAll: [], //园区
      accountType: [], //账号角色
      parkName: '', //园区名称
    };
  },
  created() {
    this.tableColumn = [
      {
        title: '序号',
        type: 'seq',
      },
      {
        title: '园区名称',
        field: 'parkName',
      },
      {
        title: '用户名',
        field: 'userName',
      },
      {
        title: '账号',
        field: 'account',
      },
      {
        title: '密码',
        field: 'password',
      },
      {
        title: '状态',
        field: 'state',
        formatter({ cellValue }) {
          return cellValue == '1' ? '禁用' : '启用';
        },
      },
      {
        title: '类型',
        field: 'accountType',
        formatter({ cellValue }) {
          return cellValue == 'admin' ? '园区管理员' : '物业管理员';
        },
      },
    ];
  },
  computed: {
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            title: '园区名称',
            field: 'parkName',
          },
          {
            title: '状态',
            field: 'state',
            element: 'a-select',
            props: {
              options: this.accountState,
            },
          },
        ],
      };
    },

    modalConfig() {
      return {
        addBtn: true,
        addTitle: '新增账号',
        editBtn: true,
        viewBtn: false,
        delBtn: false,
        editTitle: '编辑账号',
        formConfig: [
          {
            title: '园区名称',
            field: 'parkId',
            element: 'a-select',
            rules: [{ required: true, message: '请选择园区名称' }],
            props: {
              options: this.organizeAll,
            },
            on: { change: this.organizeAllChange },
          },
          {
            title: '用户名',
            field: 'userName',
            rules: [{ required: true, message: '请填写用户名' }],
          },
          {
            title: '账号',
            field: 'account',
            rules: [
              {
                required: true,
                validator: (rule, value, callback) => {
                  const phoneReg = /^1[3456789]\d{9}$/;
                  if (phoneReg.test(value)) {
                    callback();
                  } else {
                    callback('请输入正确的手机号');
                  }
                },
              },
            ],
          },
          {
            title: '密码',
            field: 'password',
            element: 'a-input-password',
            rules: [{ required: true, message: '请填写密码' }],
          },
          {
            title: '类型',
            field: 'state',
            element: 'a-select',
            rules: [{ required: true, message: '请选择类型' }],
            props: {
              options: this.accountState,
            },
          },
          {
            title: '账号角色',
            field: 'accountType',
            element: 'a-select',
            rules: [{ required: true, message: '请选择账号角色' }],
            props: {
              options: this.accountType,
            },
          },
        ],
        customOperationTypes: [
          {
            title: '启用',
            typeName: 'enabled',
            event: (row) => {
              this.changeStateHandle(row);
            },
            condition(row) {
              return row.state == '1';
            },
          },
          {
            title: '禁用',
            typeName: 'disabled',
            event: (row) => {
              this.changeStateHandle(row);
            },
            condition(row) {
              return row.state == '0';
            },
          },
        ],
      };
    },
  },

  mounted() {
    this.getBaseInfo();
    this.loadData();
  },

  methods: {
    async loadData() {
      this.loading = true;
      let p = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      };
      const [res, err] = await parkUserList(p);
      if (err) return;
      this.loading = false;
      console.log('用户管理', res);
      // 设置数据
      this.tablePage.total = res.total;
      this.tableData = res.data;
    },
    getBaseInfo() {
      getAccountState().then((res) => {
        this.accountState = res;
      });
      getAccountType().then((res) => {
        this.accountType = res;
      });
      getOrganize().then((res) => {
        this.organizeAll = res;
      });
    },
    changeStateHandle(row) {
      let that = this;
      let str = row.state == '0' ? '禁用' : '启用';
      let stateParams = row.state == '0' ? '1' : '0';
      this.$confirm({
        content: `确认要${str}账户名称为‘${row.account}’的账户吗？`,
        onOk() {
          return new Promise((resolve) => {
            that.changeState(row.id, stateParams);
            resolve();
          });
        },
        cancelText: '取消',
      });
    },
    //启用/禁用
    async changeState(id, state) {
      const [, err] = await changeState({ id, state });
      if (err) return;
      this.$message.success(state == '0' ? '启用成功' : '禁用成功');
      this.loadData();
    },
    //园区change
    organizeAllChange(val) {
      const currentBiz = this.organizeAll.filter((item) => {
        return item.value === val;
      });
      this.parkName = currentBiz[0] ? currentBiz[0].label : val;
    },
    handleHasRead() {},
    handleUnRead() {},
    rowAdd() {
      this.$refs.crud.switchModalView(true, 'ADD');
    },
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, 'UPDATE', row);
    },
    //编辑新增
    async saveOrEdit(value) {},
    //弹窗提交按钮事件
    async modalConfirmHandler(value) {
      return new Promise(async (resolve, reject) => {
        const [res, err] = await saveOrEdit({
          ...value,
          parkName: this.parkName || value.parkName,
        });
        if (err){
          reject()
          return
        }
        let str = value.crudOperationType === 'update' ? '编辑' : '新增';
        this.$message.success(`${str}成功`);
        this.loadData();
        resolve();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.mr-10 {
  margin-right: 10px;
}
</style>
