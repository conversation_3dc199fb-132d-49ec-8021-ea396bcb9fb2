<template>
  <a-modal
    title="岗位成员"
    :visible="visible"
    :loading="loading"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
    width="700px"
    :footer="false"
    @cancel="closeModal"
  >
    <a-spin :spinning="baseLoading">
      <a-alert
        style="margin-bottom: 12px"
        message="用户加入到岗位后，将拥有该组所有权限"
        banner
      />
      <a-descriptions title="岗位信息">
        <a-descriptions-item label="租户名称">
          {{ baseInfo.merchantName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="组织名称">
          {{ baseInfo.organizeName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="岗位名称">
          {{ baseInfo.positionName || '-' }}
        </a-descriptions-item>
      </a-descriptions>
      <a-descriptions title="岗位成员列表">
        <div class="choose-user-block">
          选择用户:
          <a-select
            v-model="userChoose"
            mode="multiple"
            style="width: 400px; margin-right: 10px"
            :filter-option="filterOption"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            placeholder="请选择用户"
          >
            <a-select-option
              v-for="item in userOptionsList"
              :key="item.userId"
              :value="item.userId"
              :disabled="!!dataList.find((user) => user.userId === item.userId)"
            >
              {{ item.userName }}
            </a-select-option>
          </a-select>
          <a-button type="primary" v-if="userChoose.length" @click="submitForm">
            添加用户
          </a-button>
        </div>
      </a-descriptions>
      <a-table
        :columns="tableUserColumns"
        :data-source="dataList"
        :loading="loading"
        rowKey="userId"
        :toolbarConfig="null"
        :pagination="false"
      >
        <template slot="order" slot-scope="text, record, index">{{
          index + 1
        }}</template>
        <template slot="operation" slot-scope="text, record">
          <a-button
            icon="delete"
            type="link"
            style="padding: 0; margin-right: 8px"
            @click="handleDelete(record.userId, record.userName)"
          >
            删除
          </a-button>
        </template>
      </a-table>
    </a-spin>
  </a-modal>
</template>

<script>
import {
  getPositionUserList,
  addPositionUsers,
  getOrganizeUserList,
  getPoitionDetail,
  removePositionUser,
} from '@/api/system/position';
import { tableUserColumns } from '../constant.js';
import { Modal } from 'ant-design-vue';

export default {
  components: {},
  props: {
    visible: Boolean,
    positionId: String, //岗位ID
    positionName: String, //岗位ID
    organizeId: String, //组织架构ID
    organizeName: String, //组织架构名称
  },
  data() {
    return {
      // 表格
      tableUserColumns,
      dataList: [],
      baseInfo: {},
      // 菜单树
      userOptionsList: [],
      userChoose: [],
      loading: false,
      baseLoading: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.userChoose = [];
        this.getUserList();
        if (this.positionId) {
          this.getDetail();
          this.getPositionUser();
        }
      }
    },
  },
  computed: {
    merchantId() {
      return this.$store.state?.base?.merchant?.merchantId;
    },
    merchantName() {
      return this.$store?.state?.base?.merchant?.merchantName;
    },
  },
  methods: {
    /**
     * 获取用户列表
     */
    async getUserList() {
      this.loading = true;
      const [result] = await getOrganizeUserList(this.organizeId);
      this.loading = false;
      this.userOptionsList = result?.data || [];
    },
    /**
     * 获取详情
     */
    async getDetail() {
      this.baseLoading = true;
      const [result] = await getPoitionDetail(this.positionId);
      this.baseLoading = false;
      this.baseInfo = result?.data;
    },
    // 获取管理的用户账号列表
    async getPositionUser() {
      this.loading = true;
      const [result] = await getPositionUserList(this.positionId);
      this.loading = false;
      this.dataList = result?.data || [];
    },
    // 筛选列表
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    /**
     * 提交按钮
     */
    async submitForm() {
      if (this.loading) return;
      this.loading = true;
      const [, error] = await addPositionUsers({
        positionId: this.positionId,
        userIds: this.userChoose,
      });
      this.loading = false;
      if (error) return;
      this.userChoose = [];
      this.$message.success(`添加成功`);
      this.getPositionUser();
    },
    async handleDelete(userId, userName) {
      if (this.loading) return;
      Modal.confirm({
        title: '警告',
        content: `请确认将:${userName || ''}成员移出${
          this.baseInfo.positionName
        }岗位!`,
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.loading = true;
          const [, error] = await removePositionUser({
            positionId: this.positionId,
            userId,
          });
          this.loading = false;
          if (error) return;
          this.$message.success(`删除成功`);
          this.getPositionUser();
        },
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
<style lang="less" scoped>
.choose-user-block {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>
