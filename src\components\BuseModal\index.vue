<template>
  <a-modal
    :maskClosable="!!config.props.maskClosable"
    :visible="visible"
    :class="['buse-modal', loading ? 'hidden' : '']"
    :width="config.props.width || '750px'"
    @ok="handleOk"
    @cancel="close"
    :confirmLoading="loading"
    :destroyOnClose="true"
    v-bind="config.props"
    v-on="config.on"
  >
    <template #footer>
      <BtnGroup
        :row="params"
        :btns="cpModalBtns"
        :layout="btnsLayout"
        :buttonSpan="0"
      />
    </template>
    <a-spin :spinning="loading">
      <slot name="modalTop" />
      <template v-for="name in Object.keys(modalSlots)" v-slot:[name]="data">
        <slot :name="name" v-bind="data" />
      </template>
      <template v-if="mainSlotName">
        <slot :name="mainSlotName" />
      </template>
      <template v-else>
        <components
          :is="formTypeName"
          ref="dynamicForm"
          :config="config.formConfig"
          :params="params"
          class="buse-modal-main"
          v-bind="config.formProps"
        >
          <template v-for="(_, name) in formSlots" v-slot:[name]="data">
            <slot :name="name" v-bind="data" />
          </template>
        </components>
      </template>
      <slot name="modalBottom" />
    </a-spin>
  </a-modal>
</template>
<script>
import { initParams } from '@/utils';
import { clone, isEqual } from 'xe-utils';
import BtnGroup from '@/components/BtnGroup/index.vue';

const cloneDeep = function (obj) {
  return clone(obj, true);
};
const camelize = function camelize(str) {
  return str.replace(/-(\w)/g, function (_, c) {
    return c ? c.toUpperCase() : '';
  });
};
export default {
  name: 'BuseModal', // 读作 biu si Modal
  components: { BtnGroup },
  model: {
    prop: 'formParams',
    event: 'change',
  },
  props: {
    modalConfig: {
      type: [Object, Array],
      default() {
        return {
          type: 'default',
          formType: 'DynamicForm',
          props: {
            title: 'add',
          },
          formProps: {},
        };
      },
    },
    formConfig: {
      type: Array,
      default() {
        return [];
      },
    },
    submit: {
      type: Function,
      default() {
        return function () {};
      },
    },
    formParams: Object,
    type: {
      default: 'default',
    },
    modalBtns: {
      type: [Function, Array],
      default: () => [],
    },
    btnsLayout: {
      type: Object,
      default: () => ({
        type: 'flex',
        align: 'middle',
        justify: 'end',
      }),
    },
  },
  data() {
    return {
      visible: false,
      params: {},
      modalDefaultSlots: [
        'footer',
        'title',
        'cancelText',
        'closeIcon',
        'footer',
        'okText',
      ],
      loading: false,
      cpModalBtns: [],
      activeKey: 'en-US',
    };
  },
  computed: {
    formTypeName() {
      return this.config.formType || 'DynamicForm';
    },
    mainSlotName() {
      return this.modalSlots.default
        ? 'default'
        : this.modalSlots[this.type]
        ? this.type
        : '';
    },
    config() {
      let config = this.modalConfigMap[this.type || 'default'] || {};
      let props = config?.props || {};
      config.props = Object.keys(props).reduce((acc, key) => {
        acc[camelize(key)] = props[key];
        return acc;
      }, {});
      if (this.mainSlotName) {
        config.formConfig = [];
      } else {
        if (config?.formProps?.disabled) {
          config.formConfig.forEach((item) => {
            if (!item?.props) {
              item.props = { disabled: true };
            } else {
              item.props = {
                visible: this.visible,
                ...item.props,
                disabled: true,
              };
            }
            item.rules = [];
          });
        }
      }
      return config;
    },
    modalConfigMap() {
      let configArr = this.modalConfig;
      if (!Array.isArray(configArr)) {
        configArr = [
          {
            type: 'default',
            props: {
              title: 'add ',
            },
            ...cloneDeep(this.modalConfig),
          },
        ];
      }
      return configArr
        .map((x) => x.type)
        .reduce((acc, item) => {
          let findObj = configArr.find((x) => x.type === item);
          acc[item] = {
            formConfig: this.formConfig || [],
            ...cloneDeep(findObj),
          };
          return acc;
        }, {});
    },
    modalSlots() {
      let slotNames = Object.values(this.modalConfigMap).map(
        (x) => x.slotName || x.type
      );
      return Object.keys(this.$scopedSlots)
        .filter(
          (key) =>
            slotNames.includes(key) || this.modalDefaultSlots.includes(key)
        )
        .reduce((acc, item) => {
          acc[item] = this.$scopedSlots[item];
          return acc;
        }, {});
    },
    formSlots() {
      let slotNames = Object.values(this.modalConfigMap).map(
        (x) => x.slotName || x.type
      );
      return Object.keys(this.$scopedSlots)
        .filter((key) => !slotNames.includes(key))
        .filter((key) => !this.modalDefaultSlots.includes(key))
        .reduce((acc, item) => {
          acc[item] = this.$scopedSlots[item];
          return acc;
        }, {});
    },
  },
  watch: {
    formParams: {
      immediate: true,
      deep: true,
      handler(nVal) {
        if (!isEqual(nVal, this.params)) {
          this.params = nVal;
        }
      },
    },
    params: {
      immediate: true,
      deep: true,
      handler(val) {
        if (!val) return;
        this.$emit('change', val);
      },
    },
  },
  methods: {
    async open({ params = {}, openFn } = {}) {
      // let repeatSlots = Object.keys(this.$scopedSlots)?.filter((key) =>
      //   this.modalDefaultSlots.includes(key)
      // );
      if (openFn && typeof openFn === 'function') {
        this.visible = true;
        this.loading = true;
        const res = await openFn();
        this.loading = false;
        this.params = {
          ...initParams(this.config.formConfig, res),
        };
        setTimeout(() => {
          if (this.$refs?.dynamicForm) {
            this.$refs?.dynamicForm?.clearValidate();
          }
        }, 100);
        return;
      }

      this.cpModalBtns =
        typeof this.modalBtns === 'function'
          ? this.modalBtns()
          : this.modalBtns;
      if (!this.cpModalBtns?.length) {
        this.cpModalBtns = [
          {
            label: '取消',
            event: this.close,
            props: {
              type: 'default',
            },
          },
          {
            label: '确定',
            event: this.handleOk,
            color: '#fff',
            props: {
              type: 'primary',
            },
          },
        ];
      }
      // if (openFn && typeof openFn === 'function') {
      //   this.visible = true;
      //   this.loading = true;
      //   const res = await openFn();
      //   this.loading = false;
      //   this.params = {
      //     ...initParams(this.config.formConfig, res),
      //   };
      //   setTimeout(() => {
      //     if (this.$refs?.dynamicForm) {
      //       this.$refs?.dynamicForm?.clearValidate();
      //     }
      //   }, 1);
      //   return;
      // }
      // this.params = {
      //   ...initParams(
      //     this.config.formConfig,
      //     !(params instanceof PointerEvent) ? params : {}
      //   ),
      // };
      this.visible = true;
      setTimeout(() => {
        this.$refs?.dynamicForm?.clearValidate();
        this.params = {
          ...initParams(
            this.config.formConfig,
            !(params instanceof PointerEvent) ? params : {}
          ),
        };
      }, 1);
    },

    async handleOk({
      submitFn = undefined,
      isValid = true,
      extendParams = {},
    } = {}) {
      let valid;
      try {
        if (isValid && !this.mainSlotName) {
          valid = await this.$refs.dynamicForm.validate();
          if (!valid) {
            return false;
          }
        }
        this.loading = true;
        const res = await (submitFn || this.submit)({
          ...this.params,
          ...extendParams,
        });
        this.loading = false;
        if (res !== false) {
          this.close();
        }
      } catch (e) {
        if (e === false) {
          setTimeout(() => {
            const firstErrorChild =
              document.getElementsByClassName('has-error')[0].parentNode
                .parentNode.parentNode;
            const parent = document.querySelector(
              `.buse-modal-main > .ant-form > div`
            );
            parent?.scrollTo({
              top: firstErrorChild.offsetTop,
              behavior: 'smooth',
            });
          }, 1);
        }
        return false;
      }
    },
    async close() {
      this.visible = false;
      this.loading = false;
      this.params = initParams(this.config.formConfig, {});
      this.$nextTick(() => {
        this.$refs?.dynamicForm?.clearValidate();
      });
      return;
    },
  },
};
</script>
<style lang="less" scoped>
::v-deep .buse-modal-main > .ant-form > div {
  position: relative;
  max-height: 550px;
  padding-right: 12px;
  overflow: hidden auto;
}

::v-deep .ant-row {
  margin-bottom: 0px;
}

.hidden {
  ::v-deep .ant-modal-body {
    overflow: hidden;
  }
}
</style>
