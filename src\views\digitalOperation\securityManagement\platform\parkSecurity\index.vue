<template>
  <div class="park-sec-wrapper">
    <a-tabs class="tab-box" v-model="activeKey">
      <a-tab-pane key="1" tab="企业安全生产">
        <IndustrialEnterprise class="mr0" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="消防安全检查" class="mr0">
        <FireSafety />
      </a-tab-pane>
      <a-tab-pane key="3" tab="电梯安全" class="mr0"
        ><ElevatorSafety />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import IndustrialEnterprise from './components/industrialEnterprise.vue';
import FireSafety from './components/fireSafety.vue';
import ElevatorSafety from './components/elevatorSafety.vue';
export default {
  name: 'ManagementTkIndex',
  components: {
    IndustrialEnterprise,
    FireSafety,
    ElevatorSafety,
  },

  data() {
    return {
      activeKey: '1',
    };
  },

  mounted() {
    if (this.$route.query.type == 'FS') {
      this.activeKey = '2';
    }
  },

  methods: {},
};
</script>

<style lang="less" scoped>
.park-sec-wrapper {
  margin: 24px;
  /deep/.ant-tabs-bar {
    margin-bottom: 0;
  }
  .mr0 {
    margin: 0;
  }
  /deep/.bd3001-page-wrapper-container {
    margin: 0;
  }
}
</style>
