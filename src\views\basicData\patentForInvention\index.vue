<template>
  <page-layout>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tableColumn="tableColumn"
      :tablePage="tablePage"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
      @rowView="rowView"
      :tableOn="{
        'checkbox-change': selectChangeEvent,
        'checkbox-all': selectChangeEvent,
      }"
    >
      <!--    日期筛选器（年）-->
      <template slot="dateYear">
        <BuseRangePicker
          type="year"
          v-model="filterParams.reportYear"
          :needShowSecondPicker="() => false"
          format="YYYY"
          placeholder="请选择年份"
          :disableDateFunc="disableDateFunc"
        />
      </template>
      <template slot="defaultTitle">
        <span></span>
      </template>
      <template slot="defaultHeader">
        <div class="flex-row-10">
          <!-- <a-button type="primary" @click="handleCreate">新增</a-button> -->
          <a-button @click="onClickImport">导入</a-button>
          <a-button :loading="exportLoading" @click="exportData">导出</a-button>
          <a-button
            :loading="delLoading"
            @click="delAll"
            :disabled="!checkItems.length"
            type="danger"
            >删除</a-button
          >
        </div>
      </template>
    </BuseCrud>
  </page-layout>
</template>

<script>
import moment from 'moment';
import { resolveBlob } from '@/utils/common/fileDownload';
import {
  getParkList,
  getPatentList,
  delPatentList,
  downloadInformation,
} from '@/api/basicData/index.js';
import { filterOption } from '@/utils';
export default {
  props: {},
  components: {},
  dicts: ['enterprise_listing_type'],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      filterParams: {
        reportYear: { startValue: '' },
        enterpriseName: '',
        unifiedCreditCode: '',
        inventName: '',
        applyCode: '',
      },
      exportLoading: false,
      delLoading: false,
      parkList: [],
      checkItems: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'reportYear',
            title: '年份',
            element: 'slot',
            slotName: 'dateYear',
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
            props: {
              placeholder: '请输入企业名称',
            },
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              placeholder: '请输入统一社会信用代码',
            },
            itemProps: {
              labelCol: { span: 10 },
              wrapperCol: { span: 14 },
            },
          },
          {
            field: 'inventName',
            title: '发明名称',
            props: {
              placeholder: '请输入发明名称',
            },
          },
          {
            field: 'applyCode',
            title: '申请号',
            props: {
              placeholder: '请输入申请号',
            },
          },
        ],
        params: this.filterParams,
      };
    },
    tableColumn() {
      return [
        {
          type: 'checkbox',
          width: 80,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          width: 80,
          fixed: 'left',
        },
        {
          field: 'reportYear',
          title: '年份',
          minWidth: 120,
        },
        {
          field: 'primaryCategory',
          title: '主分类号',
          minWidth: 140,
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          minWidth: 160,
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          minWidth: 120,
        },
        {
          field: 'address',
          title: '企业地址',
          minWidth: 120,
        },
        {
          field: 'patentAgency',
          title: '专利机构',
          minWidth: 120,
        },
        {
          field: 'authTime',
          title: '授权日期',
          minWidth: 120,
        },
        {
          field: 'inventName',
          title: '发明名称',
          minWidth: 120,
        },
        {
          field: 'applyCode',
          title: '申请号',
          minWidth: 120,
        },
        {
          field: 'applyTime',
          title: '申请日期',
          minWidth: 120,
        },
        {
          field: 'updateBy',
          title: '更新人',
          minWidth: 120,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          minWidth: 180,
        },
      ];
    },
    modalConfig() {
      return {
        addBtn: false,
        menuFixed: 'right',
        editBtn: false,
        delBtn: false,
        formConfig: [
          {
            field: 'reportYear',
            title: '年份',
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              placeholder: '请输入统一社会信用代码',
              disabled: true,
            },
          },
          {
            field: 'primaryCategory',
            title: '主分类号',
            props: {
              placeholder: '请输入主分类号',
              disabled: true,
            },
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
            props: {
              placeholder: '请输入企业名称',
              disabled: true,
            },
          },
          {
            field: 'address',
            title: '企业地址',
            props: {
              placeholder: '请输入企业地址',
              maxLength: 30,
            },
          },
          {
            field: 'patentAgency',
            title: '专利机构',
            props: {
              placeholder: '请输入专利机构',
              maxLength: 30,
            },
          },
          {
            field: 'authTime',
            title: '授权日期',
            props: {
              placeholder: '请输入授权日期',
            },
          },
          {
            field: 'inventName',
            title: '发明名称',
            props: {
              placeholder: '请输入发明名称',
              maxLength: 30,
            },
          },
          {
            field: 'applyCode',
            title: '申请号',
            props: {
              placeholder: '请输入申请号',
            },
          },
          {
            field: 'applyTime',
            title: '申请日期',
            props: {
              placeholder: '请选择申请日期',
            },
          },
          {
            field: 'authStoreDate',
            title: '授权入库日',
            props: {
              placeholder: '请输入授权入库日',
            },
          },
          {
            field: 'patentOwnerCode',
            title: '专利权人邮编',
            props: {
              placeholder: '请输入专利权人邮编',
            },
          },
          {
            field: 'patentType',
            title: '专利类型',
            props: {
              placeholder: '请输入专利类型',
            },
          },
          {
            field: 'applyType',
            title: '申请人类型',
            props: {
              placeholder: '请输入申请人类型',
            },
          },
          {
            field: 'importMonth',
            title: '导入月份',
            props: {
              placeholder: '请输入导入月份',
            },
          },
        ],
        formLayoutConfig: {
          layout: 'vertical',
        },
      };
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.getParkList();
    this.loadData();
  },
  methods: {
    selectChangeEvent({ records }) {
      this.checkItems = records.map((item) => {
        return item.id;
      });
    },
    async loadData() {
      this.loading = true;
      const [res] = await getPatentList({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...this.filterParams,
        reportYear: this.filterParams.reportYear?.startValue
          ? moment(this.filterParams.reportYear?.startValue).format('yyyy')
          : '',
      });
      this.loading = false;
      if (res && res.data) {
        this.checkItems = [];
        this.tableData = res.data;
        this.tablePage.total = res.total;
      }
    },
    rowDel(row) {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [res] = await delPatentList({
            ids: [row.id],
          });
          if (res && res.code == '10000') {
            that.$message.success('删除成功!');
            that.loadData();
          }
        },
      });
    },
    // 打开弹窗
    btnClickHandler(operationType, row) {
      this.operationType = operationType;
      if (operationType !== 'ADD') {
        this.$refs.crud.switchModalView(true, operationType, {
          ...row,
          enterpriseId: row.name,
        });
        return;
      }
      this.$refs.crud.switchModalView(true, operationType, row);
    },
    // 获取园区列表
    async getParkList() {
      const [res] = await getParkList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.parkList = res.data;
      }
    },
    handlerSearchEnterprise(val) {
      console.log(val);
      return [];
    },
    async exportData() {
      this.exportLoading = true;
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
      };
      const [res] = await downloadInformation(
        {
          ids: this.$refs.crud.getCheckboxRecords().map((q) => q.id),
          ...this.filterParams,
          reportYear: this.filterParams.reportYear?.startValue
            ? moment(this.filterParams.reportYear?.startValue).format('yyyy')
            : '',
        },
        'patentForInvention'
      );
      this.exportLoading = false;
      resolveBlob(res, mimeMap.xlsx, '发明专利', '.xlsx');
    },
    rowView(row) {
      this.btnClickHandler('VIEW', row);
    },
    delAll() {
      const list = this.$refs.crud.getCheckboxRecords();
      console.log(list, 'list');
      if (list && list.length > 0) {
        const ids = list.map((q) => q.id);
        const that = this;
        this.$confirm({
          title: '确认删除',
          content: () => '确认删除当前选中数据？',
          cancelText: '取消',
          okText: '确定',
          async onOk() {
            that.delLoading = true;
            const [res] = await delPatentList({
              ids: ids,
            });
            that.delLoading = false;
            if (res && res.code == '10000') {
              that.$message.success('删除成功!');
              that.loadData();
            }
          },
        });
      } else {
        this.$message.info('请选择需要删除的数据');
      }
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: 'patentForInvention',
        },
      });
    },
    // 日期格式化
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
  },
};
</script>

<style scoped lang="less"></style>
