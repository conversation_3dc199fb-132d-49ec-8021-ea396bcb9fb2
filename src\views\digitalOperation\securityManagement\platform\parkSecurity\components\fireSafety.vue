<template>
  <div>
    <div v-if="isListTab">
      <BuseCrud
        ref="crud"
        title="消防安全检查"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :modalConfig="modalConfig"
        @modalConfirm="modalConfirmHandler"
        @handleCreate="rowAdd"
        @loadData="loadData"
        :tableProps="{
          headerAlign: 'left',
          border: 'none',
          columnConfig: { resizable: true },
          showOverflow: 'tooltip',
          align: 'left',
        }"
      >
        <template slot="checkHistory" slot-scope="{ row }">
          <HistoryDetail historyType="FS" :row="row" />
        </template>
        <template slot="defaultHeader">
          <a-button type="default" class="mr10" @click="goEchartPage"
            >图表</a-button
          >
        </template>
      </BuseCrud>
    </div>
    <div v-else>
      <echartFS @goListPage="goListPage"></echartFS>
    </div>
  </div>
</template>

<script>
import echartFS from '@/views/digitalOperation/securityManagement/common/echart/echartFS.vue';
import {
  firePageList,
  fireAdd,
  fireDelete,
} from '@/api/digitalOperation/securityManagement/parkSafety/fireSafety.js';
import {
  getOrganize,
  enterpriseBasicAllList,
} from '@/views/digitalOperation/taskManagement/utils/index.js';
import HistoryDetail from './history.vue';
export default {
  name: 'FireSafety',
  components: {
    HistoryDetail,
    echartFS,
  },
  data() {
    return {
      menuShow: true,
      tableData: [],
      tableColumn: [],
      params: { parkId: undefined, reviewedOrganizeId: undefined },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      showCompanyList: false,
      parkFilterList: [], //筛选园区数据下拉框
      parkModelList: [], //model园区数据下拉框
      isListTab: true,
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          // {
          //   title: '所属园区',
          //   element: 'a-select',
          //   field: 'parkId',
          //   props: {
          //     options: this.parkFilterList,
          //     showSearch: true,
          //     filterOption: false,
          //     notFoundContent: null,
          //     labelInValue: true,
          //   },
          //   on: {
          //     change: this.parkFilterChange,
          //     search: this.parkFilterSearch,
          //   },
          //   previewFormatter: (value) => {
          //     return value.label;
          //   },
          // },
          {
            field: 'reviewedOrganizeId',
            title: '被检查单位',
            element: 'a-select',
            props: {
              options: this.parkFilterList,
              showSearch: true,
              filterOption: false,
              notFoundContent: null,
              labelInValue: true,
            },
            on: {
              change: this.parkFilterChange,
              search: this.parkFilterSearch,
            },
            previewFormatter: (value) => {
              return value.label;
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: true,
        editBtn: false,
        viewBtn: false,
        delBtn: false,
        menuWidth: 300,
        addBtnText: '新增被检查单位',
        addTitle: '新增被检查单位',
        formConfig: [
          // {
          //   title: '所属园区',
          //   element: 'a-select',
          //   field: 'parkId',
          //   rules: [{ required: true, message: '请选择所属园区' }],
          //   props: {
          //     options: this.parkModelList,
          //     showSearch: true,
          //     filterOption: false,
          //     notFoundContent: null,
          //     labelInValue: true,
          //   },
          //   on: {
          //     change: this.parkModelChange,
          //     search: this.parkModelSearch,
          //   },
          //   previewFormatter: (value) => {
          //     return value.label;
          //   },
          // },
          {
            field: 'reviewedOrganizeId',
            title: '被检查单位',
            rules: [{ required: true, message: '请选择被检查单位' }],
            element: 'a-select',
            props: {
              options: this.parkModelList,
              showSearch: true,
              filterOption: false,
              notFoundContent: null,
              labelInValue: true,
            },
            on: {
              change: this.parkModelChange,
              search: this.parkModelSearch,
            },
            previewFormatter: (value) => {
              return value.label;
            },
          },
        ],
        customOperationTypes: [
          {
            title: '查看详情',
            typeName: 'checkDetail',
            event: (row) => {
              localStorage.setItem('addForFSInfo', JSON.stringify(row));
              this.$router.push({
                name: 'addForFS',
                query: {
                  id: row.id,
                  type: 'view',
                },
              });
            },
            condition: (row) => {
              //TODO:如果最近检查时间没有填写，则不显示查看详情按钮
              return row.inspectTime ? true : false;
            },
          },
          {
            title: '新增检查记录',
            typeName: 'addCheckRecord',
            event: (row) => {
              console.error(
                '🚀 ~ file: industrialEnterprise.vue:151 ~ modalConfig ~ row:',
                row
              );
              localStorage.setItem('addForFSInfo', JSON.stringify(row));
              this.$router.push({
                name: 'addForFS',
                query: {
                  id: row.id,
                  type: 'add',
                },
              });
            },
            condition: () => {
              return true;
            },
          },
          {
            title: '查看历史记录',
            typeName: 'checkHistory',
            slotName: 'checkHistory',
            showForm: false,
            modalProps: {
              footer: null,
            },
            event: (row) => {
              return this.$refs.crud.switchModalView(true, 'checkHistory', row);
            },
            condition: (row) => {
              //TODO:如果最近检查时间没有填写，则不显示查看详情按钮
              return row.inspectTime ? true : false;
            },
          },
          {
            title: '删除被检查单位',
            typeName: 'delCompany',
            event: (row) => {
              let that = this;
              this.$confirm({
                title: '提醒',
                content: '确认要删除被检查单位吗？',
                okText: '确认',
                cancelText: '取消',
                onOk() {
                  console.log('OK');
                  that.fireDelete(row.id);
                },
                onCancel() {
                  console.log('Cancel');
                },
              });
            },
            condition: (row) => {
              //TODO:如果最近检查时间没有填写，则不显示查看详情按钮
              return row.inspectTime ? false : true;
            },
          },
        ],
      };
    },
  },
  created() {
    this.tableColumn = [
      {
        title: '被检查单位',
        field: 'reviewedOrganizeName',
      },
      {
        title: '最近检查时间',
        field: 'inspectTime',
      },
      {
        title: '检查人员',
        field: 'inspectName',
      },
      {
        title: '责任人',
        field: 'principalName',
      },
      {
        title: '检查结果',
        field: 'inspectionResult',
        formatter({ cellValue }) {
          return cellValue === '1'
            ? '合格'
            : cellValue === '0'
            ? '不合格'
            : null;
        },
      },
      {
        title: '最近信息录入时间',
        field: 'informationEntryTime',
      },
    ];
    this.getParkFilterList();
    this.getParkModelList();
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      const p = {
        // parkId: this.params.parkId?.key || '',
        // parkName: this.params.parkId?.label || '',
        reviewedOrganizeId: this.params.reviewedOrganizeId?.key || '',
        reviewedOrganizeName: this.params.reviewedOrganizeId?.label || '',
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      };
      const [res, err] = await firePageList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    //park -- filter事件处理开始
    getParkFilterList(name) {
      getOrganize(name).then((res) => {
        this.parkFilterList = res;
      });
    },
    parkFilterSearch(value) {
      this.getParkFilterList(value);
    },
    // parkFilterChange(val) {
    //   console.log(val, 'filter');
    //   this.params.reviewedOrganizeId = undefined;
    //   enterpriseBasicAllList(val.key).then((res) => {
    //     this.reviewedOrganizeFilterList = res;
    //   });
    // },
    //park -- filter事件处理结束
    //被检查单位--filter事件处理开始
    reviewedOrganizeFilterSearch(value) {
      enterpriseBasicAllList(this.params.parkId?.key, value).then((res) => {
        this.reviewedOrganizeFilterList = res;
      });
    },
    //被检查单位--filter事件处理结束
    //park -- model事件处开始
    getParkModelList(name) {
      getOrganize(name).then((res) => {
        this.parkModelList = res;
      });
    },
    parkModelSearch(value) {
      this.getParkModelList(value);
    },
    // parkModelChange(val) {
    //   console.log(val, 'model');
    //   this.parkIdModel = val.key;
    //   this.parkNameModel = val.label;
    //   this.$refs.crud.$refs.modalView.formParams.reviewedOrganizeId = undefined;
    //   enterpriseBasicAllList(val.key).then((res) => {
    //     this.reviewedOrganizeModelList = res;
    //   });
    // },
    //park -- model事件处结束
    //被检查单位--model事件处理开始
    reviewedOrganizeModelSearch(value) {
      enterpriseBasicAllList(this.parkIdModel, value).then((res) => {
        this.reviewedOrganizeModelList = res;
      });
    },
    //被检查单位--model事件处理结束
    //新增
    async fireAdd(params) {
      const [, err] = await fireAdd(params);
      if (err) return this.$message.error('新增失败');
      this.$message.success('新增成功');
      this.loadData();
    },
    //删除
    async fireDelete(id) {
      const [, err] = await fireDelete(id);
      if (err) return this.$message.error('删除失败');
      this.$message.success('删除成功');
      this.loadData();
    },

    modalConfirmHandler(value) {
      const { reviewedOrganizeId } = value;
      let p = {
        // parkId: parkId.key,
        // parkName: parkId.label,
        reviewedOrganizeId: reviewedOrganizeId.key,
        reviewedOrganizeName: reviewedOrganizeId.label,
      };
      console.log('modalConfirmHandler', p);
      this.fireAdd(p);
    },
    deleteRowHandler() {},
    rowAdd() {
      this.showCompanyList = false;
      return this.$refs.crud.switchModalView(true);
    },
    goListPage() {
      this.isListTab = true;
    },
    goEchartPage() {
      this.isListTab = false;
      // const targetRoute = {
      //   name: 'echartIE',
      // };
      // const routeInfo = this.$router.resolve(targetRoute);
      // const url = routeInfo.href;
      // window.open(url, '_blank');
    },
  },
};
</script>

<style lang="less" scoped></style>
