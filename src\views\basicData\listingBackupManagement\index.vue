<template>
  <page-layout>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tableColumn="tableColumn"
      :tablePage="tablePage"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
      :tableOn="{
        'checkbox-change': selectChangeEvent,
        'checkbox-all': selectChangeEvent,
      }"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
      @rowDel="rowDel"
      @modalConfirm="modalConfirm"
      @rowEdit="rowEdit"
      @rowView="rowView"
    >
      <template slot="defaultTitle">
        <span></span>
      </template>
      <template slot="defaultHeader">
        <div class="flex-row-10">
          <a-button type="primary" @click="handleCreate">新增</a-button>
          <a-button @click="onClickImport">导入</a-button>
          <a-button :loading="exportLoading" @click="exportData">导出</a-button>
          <a-button
            :loading="delLoading"
            type="danger"
            @click="delAll"
            :disabled="!checkItems.length"
            >删除</a-button
          >
        </div>
      </template>
      <template #enterpriseId="{ params }">
        <FuzzySelect
          v-model="params.enterpriseId"
          @changeSelect="handlerChange"
          :disabled="operationType == 'VIEW'"
        ></FuzzySelect>
      </template>
    </BuseCrud>
  </page-layout>
</template>

<script>
import FuzzySelect from '@/components/FuzzySelect/index.vue';
import moment from 'moment';
import { resolveBlob } from '@/utils/common/fileDownload';
import {
  getParkList,
  getLabelList,
  addEditOReserveUpdate,
  retryEnterprise,
  delReserve,
  exportReserveList,
} from '@/api/basicData/index.js';
import { filterOption } from '@/utils';
export default {
  props: {},
  components: { FuzzySelect },
  dicts: ['enterprise_listing_type'],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      filterParams: {
        type: undefined,
        enterpriseName: '',
        unifiedCreditCode: '',
        parkId: undefined,
      },
      exportLoading: false,
      delLoading: false,
      parkList: [],
      checkItems: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'type',
            element: 'a-select',
            title: '所属类别',
            props: {
              placeholder: '请选择所属类别',
              options: this.dict.type.enterprise_listing_type,
              showSearch: true,
              filterOption: filterOption,
            },
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
            props: {
              placeholder: '请输入企业名称',
            },
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              placeholder: '请输入统一社会信用代码',
            },
            itemProps: {
              labelCol: { span: 10 },
              wrapperCol: { span: 14 },
            },
          },
          {
            field: 'parkId',
            element: 'a-select',
            title: '所属园区',
            props: {
              placeholder: '请选择所属园区',
              options: this.parkList,
              showSearch: true,
              filterOption: filterOption,
            },
          },
        ],
        params: this.filterParams,
      };
    },
    tableColumn() {
      return [
        {
          type: 'checkbox',
          width: 80,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          width: 80,
        },
        {
          field: 'name',
          title: '企业名称',
          minWidth: 120,
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          minWidth: 140,
        },
        {
          field: 'address',
          title: '企业地址',
          minWidth: 120,
        },
        {
          field: 'parkName',
          title: '所属园区',
          minWidth: 120,
        },
        {
          field: 'speed',
          title: '上市进度',
          minWidth: 120,
        },
        {
          field: 'plate',
          title: '拟上市板块',
          minWidth: 120,
        },
        {
          field: 'valuation',
          title: '估值(亿元)',
          minWidth: 120,
        },
        {
          field: 'sponsorAgency',
          title: '保荐机构',
          minWidth: 120,
        },
        {
          field: 'operate',
          title: '经营情况',
          minWidth: 120,
        },
        {
          field: 'type',
          title: '所属类别',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.dict?.type?.enterprise_listing_type?.find(
              (q) => q.value == cellValue
            )?.label;
          },
        },
        {
          field: 'remark',
          title: '备注',
          minWidth: 120,
        },
        {
          field: 'updateBy',
          title: '更新人',
          minWidth: 120,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          minWidth: 180,
        },
      ];
    },
    modalConfig() {
      return {
        addBtn: false,
        menuFixed: 'right',
        delBtn: false,
        viewBtn: false,
        menuWidth: 100,
        formConfig: [
          {
            field: 'enterpriseId',
            element: 'slot',
            slotName: 'enterpriseId',
            title: '企业名称',
            rules: [{ required: true, message: '请选择企业名称' }],
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              placeholder: '请输入统一社会信用代码',
              disabled: true,
            },
          },
          {
            field: 'address',
            title: '企业地址',
            props: {
              placeholder: '请输入企业地址',
              disabled: true,
              maxLength: 50,
            },
          },
          {
            field: 'parkName',
            title: '所属园区',
            props: {
              placeholder: '请输入所属园区',
              disabled: true,
            },
          },
          {
            field: 'speed',
            title: '上市进度',
            props: {
              placeholder: '请输入上市进度',
              maxLength: 30,
            },
            rules: [{ required: true, message: '请输入上市进度' }],
          },
          {
            field: 'plate',
            title: '拟上市板块',
            props: {
              placeholder: '请输入拟上市板块',
              maxLength: 30,
            },
            rules: [{ required: true, message: '请输入拟上市板块' }],
          },
          {
            field: 'valuation',
            title: '估值(亿元)',
            props: {
              placeholder: '请输入估值',
              min: 0,
              max: 99999999999,
              step: 0.11,
            },
            defaultValue: 0,
            element: 'a-input-number',
            rules: [
              { required: true, message: '请输入估值' },
              {
                pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
                trigger: 'blur',
                message: '请输入大于或等于零的数字，最多保留两位小数。',
              },
            ],
          },
          {
            field: 'sponsorAgency',
            title: '保荐机构',
            props: {
              placeholder: '请输入保荐机构',
              maxLength: 30,
            },
            rules: [{ required: true, message: '请输入保荐机构' }],
          },
          {
            field: 'operate',
            title: '经营情况',
            element: 'a-textarea',
            props: {
              placeholder: '请输入经营情况',
              maxLength: 30,
              autosize: {
                minRows: 2,
                maxRows: 4,
              },
            },
            rules: [{ required: true, message: '请输入经营情况' }],
          },
          {
            field: 'type',
            element: 'a-select',
            title: '所属类别',
            props: {
              placeholder: '请选择所属类别',
              options: this.dict.type.enterprise_listing_type,
              showSearch: true,
              filterOption: filterOption,
            },
            rules: [{ required: true, message: '请选择所属类别' }],
            previewFormatter: (val) => {
              return this.dict?.type?.enterprise_listing_type?.find(
                (q) => q.value == val
              )?.label;
            },
          },
          {
            field: 'remark',
            title: '备注',
            element: 'a-textarea',
            props: {
              placeholder: '请输入备注',
              maxLength: 30,
              autosize: {
                minRows: 2,
                maxRows: 4,
              },
            },
          },
        ],
        formLayoutConfig: {
          // layout: 'vertical',
        },
      };
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.getParkList();
    this.loadData();
  },
  methods: {
    selectChangeEvent({ records }) {
      this.checkItems = records.map((item) => {
        return item.id;
      });
    },
    async loadData() {
      this.checkItems = [];
      this.loading = true;
      const [res] = await retryEnterprise({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...this.filterParams,
      });
      this.loading = false;
      if (res && res.data) {
        this.tableData = res.data;
        this.tablePage.total = res.total;
      }
    },
    rowDel(row) {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [res] = await delReserve({
            ids: [row.id],
          });
          if (res && res.code == '10000') {
            that.$message.success('删除成功!');
            that.loadData();
          }
        },
      });
    },
    // 打开弹窗
    btnClickHandler(operationType, row) {
      this.operationType = operationType;
      if (operationType !== 'ADD') {
        this.$refs.crud.switchModalView(true, operationType, {
          ...row,
          enterpriseId: row.name,
        });
        return;
      }
      this.$refs.crud.switchModalView(true, operationType, row);
    },
    // 获取园区列表
    async getParkList() {
      const [res] = await getParkList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.parkList = res.data;
      }
    },
    handlerSearchEnterprise(val) {
      console.log(val);
      return [];
    },
    async exportData() {
      this.exportLoading = true;
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
      };
      const [res] = await exportReserveList({
        ids: this.$refs.crud.getCheckboxRecords().map((q) => q.id),
        ...this.filterOptions.params,
      });
      this.exportLoading = false;
      if (res) {
        resolveBlob(res, mimeMap.xlsx, '上市后备企业管理', '.xlsx');
      }
    },
    handleCreate() {
      this.btnClickHandler('ADD');
    },
    rowEdit(row) {
      this.btnClickHandler('UPDATE', row);
    },
    rowView(row) {
      this.btnClickHandler('VIEW', row);
    },
    modalConfirm(value) {
      return new Promise((resolve) => {
        const handleOperation = async () => {
          if (this.operationType == 'ADD') {
            const [res] = await addEditOReserveUpdate(value);
            if (res && res.code == '10000') {
              this.$message.success('新增成功');
              resolve();
              this.loadData();
            }
          } else {
            const [res] = await addEditOReserveUpdate(value);
            if (res && res.code == '10000') {
              this.$message.success('编辑成功');
              resolve();
              this.loadData();
            }
          }
          resolve(false);
        };

        handleOperation();
      });
    },
    handlerChange(val) {
      if (val) {
        this.$refs.crud.setFormFields({
          name: val.name,
          unifiedCreditCode: val.unifiedCreditCode,
          address: val.address,
          parkName: val.parkName,
          parkId: val.parkId,
        });
      } else {
        this.$refs.crud.setFormFields({
          name: '',
          unifiedCreditCode: '',
          address: '',
          parkName: '',
          parkId: '',
        });
      }
    },
    delAll() {
      const list = this.$refs.crud.getCheckboxRecords();
      console.log(list, 'list');
      if (list && list.length > 0) {
        const ids = list.map((q) => q.id);
        const that = this;
        this.$confirm({
          title: '确认删除',
          content: () => '确认删除当前选中数据？',
          cancelText: '取消',
          okText: '确定',
          async onOk() {
            that.delLoading = true;
            const [res] = await delReserve({
              ids: ids,
            });
            that.delLoading = false;
            if (res && res.code == '10000') {
              that.$message.success('删除成功!');
              that.loadData();
            }
          },
        });
      } else {
        this.$message.info('请选择需要删除的数据');
      }
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: 'listingBackupManagement',
        },
      });
    },
  },
};
</script>

<style scoped lang="less"></style>
