import { request } from '@/utils/request/requestTkb';
/**
 * 目标管理分页查询
 */
export function pageList(data) {
  return request({
    url: '/bpm/management/page/list',
    method: 'post',
    data,
  });
}

/**
 * 目标管理新增
 */
export function managementAdd(data) {
  return request({
    url: '/bpm/management/add',
    method: 'post',
    data,
  });
}
/**
 * 目标管理分配
 */
export function cenaAdd(data) {
  return request({
    url: '/bpm/cena/add',
    method: 'post',
    data,
  });
}

/**
 * 目标管理导出
 */
export function downloadInformation(data) {
  return request({
    url: '/bpm/management/downloadInformation',
    method: 'post',
    data,
  });
}

//查询草稿
export function cenaInfo(id) {
  return request({
    url: `/bpm/cena/info/${id}`,
    method: 'post',
  });
}

//查询草稿
export function getLastTargetEnum(data) {
  return request({
    url: `/bpm/management/getLastTargetEnum`,
    method: 'post',
    data,
  });
}
