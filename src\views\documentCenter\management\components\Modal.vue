<template>
  <a-modal
    :footer="null"
    width="800px"
    title="上传文件"
    :visible="visible"
    cancelText="取消"
    @ok="handleCreate"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <Upload
        :visible="visible"
        :uploadFileList="uploadFileList"
        @success="success"
        :data="{
          source: 'background',
        }"
      ></Upload>
    </a-spin>
  </a-modal>
</template>

<script>
import Upload from './upload';
export default {
  components: { Upload },
  props: ['visible'],
  data() {
    return {
      loading: false,
      layout: {
        labelCol: { span: 4 },
        wrapperCol: { span: 14 },
      },
      uploadFileList: [],
    };
  },
  watch: {
    visible: {
      deep: true,
      handler(val) {
        if (val) {
          this.init();
        }
      },
    },
  },
  methods: {
    async init() {
      this.uploadFileList = [];
    },
    /**
     * 新增、编辑
     */
    async handleCreate() {
      this.$emit('handleCreateSum');
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    success() {
      this.$emit('success');
    },
  },
};
</script>

<style lang="less" scoped></style>
