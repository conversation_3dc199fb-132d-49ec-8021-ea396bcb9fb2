<template>
  <page-layout>
    <table-component
      ref="tableComponent"
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :parentData="params"
      :pageName="pageName"
      @getEnterprise="getEnterprise"
      :notExportAll="true"
    ></table-component>
  </page-layout>
</template>

<script>
import { institutionsMixin } from '../mixins/institutionsMixin';
import tableComponent from '@/views/basicData/components/tableComponent';
import { filterOption } from '@/utils';
import FuzzySelect from '@/components/FuzzySelect';
import moment from 'moment';

export default {
  name: 'overseasManage',
  components: { tableComponent },
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'overseasManage',
      params: {
        year: undefined,
        enterpriseName: undefined,
        parkId: undefined,
        unifiedCreditCode: this.$route.query.pictureId,
      },
      tableColumn: [
        {
          field: '',
          title: '',
          type: 'checkbox',
          fixed: 'left',
          width: 70,
        },
        {
          field: '',
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: 70,
        },
        {
          field: 'year',
          title: '年份',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return cellValue ? moment(cellValue).format('YYYY') : '';
          },
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          minWidth: 120,
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          minWidth: 150,
        },
        {
          field: 'parkName',
          title: '所属园区',
          minWidth: 150,
        },
        {
          field: 'overseasName',
          title: '设立的境外研发机构名称',
          minWidth: 180,
        },
        {
          field: 'enterpriseHoldingSituation',
          title: '企业持股情况',
          minWidth: 120,
        },
        {
          field: 'updateBy',
          title: '更新人',
          minWidth: 120,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          minWidth: 180,
        },
      ],
      aaa: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        //筛选控件配置
        config: [
          {
            field: 'year',
            title: '年份',
            element: 'slot',
            slotName: 'year',
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            itemProps: {
              labelCol: { span: 10 },
              wrapperCol: { span: 14 },
            },
          },
          {
            field: 'parkId',
            title: '所属园区',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.parkList,
              showSearch: true,
              filterOption: filterOption,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        submitBtn: true,
        okBtn: false,
        addBtn: false,
        viewBtn: true,
        editBtn: true,
        delBtn: false,
        menu: true,
        menuWidth: 200,
        menuFixed: 'right',
        formConfig: [
          {
            field: 'year',
            title: '年份',
            element: (h, item, params) => {
              return (
                <BuseRangePicker
                  type="year"
                  needShowSecondPicker={() => false}
                  value={params.year}
                  onChange={(val) => {
                    params.year = val;
                  }}
                  placeholder="请选择年份"
                  format="YYYY"
                  disableDateFunc={(val) => val.isAfter(moment())}
                />
              );
            },
            rules: [
              { required: true, message: '请选择年份' },
              {
                message: '请选择年份',
                validator: (rule, val, callback) => {
                  if (!val) callback('请选择年份');
                  if (!val.startValue) callback('请选择年份');
                  callback();
                },
              },
            ],
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
            rules: [{ required: true, message: '请选择企业名称' }],
            element: (h, item, params) => {
              return (
                <FuzzySelect
                  value={params?.enterpriseName}
                  onChangeSelect={(val) => {
                    if (val) {
                      this.$refs.tableComponent.setForm({
                        enterpriseName: val.name,
                        enterpriseId: val.unifiedCreditCode,
                        unifiedCreditCode: val.unifiedCreditCode,
                        parkName: val.parkName,
                      });
                    } else {
                      this.$refs.tableComponent.setForm({
                        enterpriseName: '',
                        enterpriseId: '',
                        unifiedCreditCode: '',
                        parkName: '',
                      });
                    }
                  }}
                  disabled={this.modelTitle == 'see'}
                />
              );
            },
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              disabled: true,
              placeholder: '请输入统一社会信用代码',
              maxLength: 30,
            },
          },
          {
            field: 'parkName',
            title: '所属园区',
            props: {
              disabled: true,
              placeholder: '请输入所属园区',
              maxLength: 30,
            },
          },
          {
            field: 'overseasName',
            title: '设立的境外研发机构名称',
            props: {
              maxLength: 30,
            },
            rules: [
              { required: true, message: '请输入设立的境外研发机构名称' },
            ],
          },
          {
            field: 'enterpriseHoldingSituation',
            title: '企业持股情况',
            props: {
              maxLength: 30,
            },
            rules: [{ required: true, message: '请输入企业持股情况' }],
          },
        ],
      };
    },
  },
  created() {},
  methods: {},
};
</script>

<style lang="scss" scoped></style>
