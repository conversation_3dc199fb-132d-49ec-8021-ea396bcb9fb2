<template>
  <div
    class="bd3001-page-wrapper-container"
    :style="{ margin: config.noMargin ? 0 : '' }"
  >
    <a-spin :tip="loading ? '加载中...' : ''" :spinning="loading">
      <template v-if="config.onlyMargin">
        <slot></slot>
      </template>
      <template v-else>
        <slot></slot>
        <AutoFilters
          v-if="filterOptions"
          ref="filterRef"
          class="bd3001-filter"
          :tablePage="tablePage"
          v-bind="filterOptions"
          v-on="$listeners"
        >
          <template
            v-for="name in Object.keys(getFilterSlots())"
            v-slot:[name]="data"
          >
            <slot :name="name" v-bind="data" />
          </template>
        </AutoFilters>

        <div class="bd3001-content">
          <div
            v-if="
              !createText &&
              !title &&
              !$slots.defaultTitle &&
              !$slots.defaultHeader
            "
          ></div>
          <div v-else class="bd3001-header">
            <span v-if="$slots.defaultTitle" class="bd3001-title">
              <slot name="defaultTitle"></slot>
            </span>
            <span v-else class="bd3001-title">{{ title }}</span>
            <div class="bd3001-button">
              <slot name="defaultHeader"></slot>
              <template v-if="createText">
                <a-button
                  v-if="role"
                  v-has-permi="[role]"
                  type="primary"
                  @click="$listeners.handleCreate"
                  >{{ createText }}</a-button
                >
                <a-button
                  v-else
                  type="primary"
                  @click="$listeners.handleCreate"
                  >{{ createText }}</a-button
                >
              </template>
            </div>
          </div>
          <vxe-grid
            ref="vxeRef"
            :auto-resize="true"
            :columns="getTableColumn()"
            :data="tableData"
            v-bind="tableProps"
            v-on="tableOn"
          >
            <template
              v-for="name in Object.keys(getTableSlots())"
              v-slot:[name]="data"
            >
              <slot :name="name" v-bind="data" />
            </template>
            <template v-if="tablePage" v-slot:pager>
              <vxe-pager
                :border="true"
                :layouts="[
                  'Total',
                  'PrevPage',
                  'JumpNumber',
                  'PageCount',
                  'NextPage',
                  'Sizes',
                  'FullJump',
                ]"
                :current-page="tablePage.currentPage"
                :page-size="tablePage.pageSize"
                :total="tablePage.total"
                v-bind="pagerProps"
                @page-change="handlePageChange"
                @...="paperOn"
              ></vxe-pager>
            </template>
          </vxe-grid>
        </div>
      </template>
    </a-spin>
  </div>
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    createText: {
      type: String,
      default: '',
    },
    role: {
      type: String,
      default: '',
    },
    filterOptions: {
      type: [Object, Function],
      default: () => ({}),
    },
    tablePage: {
      type: Object,
      default: () => ({}),
    },
    tableColumn: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    tableProps: {
      type: Object,
      default: () => ({}),
    },
    tableOn: {
      type: Object,
      default: () => ({}),
    },
    pagerProps: {
      type: Object,
      default: () => ({}),
    },
    paperOn: {
      type: Object,
      default: () => ({}),
    },
    config: {
      type: Object,
      default: () => ({
        minWidth: '',
        noMargin: false,
        onlyMargin: false,
      }),
    },
  },
  methods: {
    getAutoFiltersRef() {
      return this.$refs.filterRef;
    },
    getVxeTableRef() {
      return this.$refs.vxeRef;
    },
    getCheckboxRecords() {
      return this.$refs.vxeRef.getCheckboxRecords();
    },
    getCheckboxReserveRecords() {
      return this.$refs.vxeRef.getCheckboxReserveRecords();
    },
    getCheckboxIndeterminateRecords() {
      return this.$refs.vxeRef.getCheckboxIndeterminateRecords();
    },
    removeCheckboxRow() {
      return this.$refs.vxeRef.removeCheckboxRow();
    },
    clearCheckboxRow() {
      return this.$refs.vxeRef.clearCheckboxRow();
    },
    clearCheckboxReserve() {
      return this.$refs.vxeRef.clearCheckboxReserve();
    },
    /* eslint-disable vue/no-mutating-props */
    handlePageChange({ currentPage, pageSize }) {
      if (this.tablePage.pageSize !== pageSize) {
        this.tablePage.currentPage = 1;
      } else {
        this.tablePage.currentPage = Math.trunc(currentPage);
      }
      this.tablePage.pageSize = pageSize;
      const loadData = this.$listeners['loadData'];
      loadData();
    },
    /* eslint-enable vue/no-mutating-props */
    getFilterSlots() {
      const slots = {};
      this.filterOptions.config.forEach((item) => {
        if (!item.slotName) return;
        if (this.$scopedSlots[item.slotName]) {
          slots[item.slotName] = this.$scopedSlots[item.slotName];
        } else {
          console.error(
            `[AutoFilter] \u63D2\u69FD "${item.slotName}" \u4E0D\u5B58\u5728`
          );
        }
      });
      return slots;
    },
    getTableSlots() {
      const slots = {};
      const tableSlotsName = Object.keys(this.$scopedSlots).filter((item) => {
        var _a;
        if (item === '_normalized') return false;
        const find =
          (_a = this.filterOptions) == null
            ? void 0
            : _a.config.find((filter) => filter.slotName === item);
        if (find) return false;
        return true;
      });
      tableSlotsName.forEach((slotName) => {
        slots[slotName] = this.$scopedSlots[slotName];
      });
      return slots;
    },
    getTableColumn() {
      return this.tableColumn.map((item) => {
        return {
          minWidth: this.config.minWidth || void 0,
          ...item,
        };
      });
    },
  },
};
</script>
