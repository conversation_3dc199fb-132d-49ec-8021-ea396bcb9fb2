<!-- B端数据填报 -->
<template>
  <BuseCrud
    ref="crud"
    title="数据列表"
    :loading="loading"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalCancel="modalCancelHandler"
    @modalConfirm="modalConfirmHandler"
    @rowEdit="rowEditHandler"
    @loadData="loadData"
    @handleCreate="rowAdd"
  >
    <template slot="modalDefault" slot-scope="{ operationType, row }">
      <ActivityDetail
        ref="activityDetail"
        :operationType="operationType"
        :row="row"
      />
    </template>
  </BuseCrud>
</template>

<script>
import ActivityDetail from './activityDetail.vue';
export default {
  name: 'DataFilling',
  components: {
    ActivityDetail,
  },
  data() {
    return {
      tableData: [],
      tableColumn: [],
      params: { name: '', sex: '', age: '' },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: true,
        viewBtn: true,
        editBtn: true,
        viewBtnText: '查看详情',
        delBtn: false,
      };
    },
  },
  created() {
    this.tableColumn = [
      {
        title: '活动名称',
        field: 'activityName',
      },
      {
        title: '开始统计时间',
        field: 'startTime',
      },
      {
        title: '结束统计时间',
        field: 'endTime',
      },
      {
        title: '二氧化碳排放量（减排量）',
        field: 'co2',
      },
    ];
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      const result = await new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: 200,
            data: [
              {
                activityName: '活动1',
                startTime: '2020-01-01',
                endTime: '2020-01-02',
                co2: 100,
                number: 20,
              },
              {
                activityName: '活动2',
                startTime: '2020-01-01',
                endTime: '2020-01-02',
                co2: 100,
                number: 22,
              },
            ],
          });
        }, 1000);
      });
      this.loading = false;
      this.tableData = result.data;
      this.tablePage.total = result.total;
    },
    modalCancelHandler() {},
    deleteRowHandler() {},
    async modalConfirmHandler() {
      const activityDetailInstance = this.$refs.activityDetail;
      const ruleForm = activityDetailInstance.getFormInstance();
      const res = await ruleForm.validate().catch(() => {
        return false;
      });
      if (res) {
        //获取弹窗数据数据
        const modalData = activityDetailInstance.getModalData();
        console.log(
          '🚀 ~ file: index.vue:95 ~ modalConfirmHandler ~ modalData:',
          modalData
        );
      } else {
        return false;
      }
      console.log('🚀 ~ file: index.vue:89 ~ modalConfirmHandler ~ res:', res);
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true);
    },
    rowEditHandler(row) {
      this.$refs.crud.switchModalView(true, 'UPDATE', row);
    },
  },
};
</script>

<style lang="less" scoped></style>
