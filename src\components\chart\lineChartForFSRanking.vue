<template>
  <div class="line-emissions">
    <div ref="chart" class="carbon-echart"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
  props: {
    //x轴数据
    axisData: {
      type: Array,
      default: () => [],
      require: true,
    },
    //y轴数据
    seriesData: {
      type: Array,
      default: () => [],
      require: true,
    },
    yAxis: {
      type: Array,
      default: null,
    },
    tooltipFormatter: {
      typeof: Function,
      default: null,
    },
  },
  data() {
    return {
      textColor: 'rgba(0,0,0,0.45)',
    };
  },
  computed: {
    showZoom() {
      return this.seriesData[0]?.data.length > 12 ? true : false;
    },
  },
  watch: {
    axisData: {
      handler(val) {
        if (val && val.length) {
          this.$nextTick(() => {
            this.setOption();
          });
        }
      },
      immediate: true, // 立即执行
    },
    seriesData: {
      handler(val) {
        if (val && val.length) {
          this.$nextTick(() => {
            this.setOption();
          });
        }
      },
      immediate: true, // 立即执行
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initObj.resize);
  },
  methods: {
    setOption() {
      const that = this;
      that.initObj.setOption({
        dataZoom: [
          {
            show: this.showZoom,
            orient: 'vertical',
            type: 'slider',
            realtime: true,
            handleIcon:
              'path://M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: 16,
            borderColor: 'rgba(0,0,0,0)',
            textStyle: {
              color: '#86909C',
            },
          },
        ],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          name: '合格率（合格数量/总检查数量）',
          nameLocation: 'middle',
          nameGap: 30,
          axisLabel: {
            formatter: '{value}%',
          },
          boundaryGap: [0, 0.01],
        },
        yAxis: {
          type: 'category',
          data: this.yAxis,
        },
        tooltip: {
          show: true,
          // 触发类型：坐标轴触发
          trigger: 'axis',
          backgroundColor: 'rgba(229,237,250,0.5)', // 通过设置rgba调节背景颜色与透明度
          borderWidth: '0',
          textStyle: {
            color: that.textColor,
          },
          formatter:
            this.tooltipFormatter ||
            function (info) {
              let str = `<div style="text-align: left; color:#1D2129;" >${info[0].name}</div>`;
              info.forEach((item) => {
                str += `<div style="
				background-color: rgba(255, 255, 255, 0.8);
				height: 32px;
				display: flex;
				justify-content: space-between;
				padding: 8px;
				border-radius: 4px;
				margin: 4px 0;"
      >
      <span style="color: #4e5969; font-size: 12px">${item.marker}${item.seriesName}</span>
      <span style="color: #1d2129; font-size: 13px;margin-left:10px">${item.value}%</span></div>`;
              });
              return str;
            },
          axisPointer: {
            //坐标轴指示器，坐标轴触发有效，
            type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
            shadowStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(7,185,185,0)', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(0,181,120,0.12)', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        },
        series: this.seriesData,
      });
    },
    initChart() {
      this.initObj = echarts.init(this.$refs.chart, null, { height: 720 });
      window.addEventListener('resize', this.initObj.resize);
    },
  },
};
</script>

<style lang="less" scoped>
.line-emissions {
  .carbon-echart {
    width: 100%;
    height: 350px;
    display: flex;
    text-align: center;
  }
}
</style>
