<template>
  <a-layout class="layout">
    <FNCommonTopNav
      class="layout-header"
      v-bind="topNavOptions"
      :currentNavMenu="[currentNavMenu]"
      :navMenuListShow="navMenuListShow"
      @logout="logout"
    />
    <a-layout>
      <SideMenu
        class="fixed-side"
        :style="`width: ${sideWidth}`"
        :menuData="sideMenuData"
        :collapsed="collapsed"
        :collapsible="true"
        :loading="false"
        @toggleCollapse="toggleCollapse"
      />
      <a-layout-content
        id="layoutContent"
        class="beauty-scroll"
        style="overflow: auto"
      >
        <div class="layout-content">
          <!-- key 用于解决vue-router replace同一页面不刷新的问题 -->
          <keep-alive>
            <router-view
              v-if="$route.meta.keepAlive"
              :key="$route.fullPath"
            ></router-view>
          </keep-alive>
          <router-view
            v-if="!$route.meta.keepAlive"
            :key="$route.fullPath"
          ></router-view>
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script>
import FNCommonTopNav from './navHeaderWithoutApp/HeaderContent.vue';
import SideMenu from './menu/SideMenu';
import { redirectLogin } from '@/router/guards';
import { mapState, mapMutations } from 'vuex';
import { mailFormworkTips } from '@/api/system/mailFormworkTe';
export default {
  name: 'Layout',
  components: { FNCommonTopNav, SideMenu },
  dicts: ['my_authority_app_category', 'platform_logo_url'],
  data() {
    return {
      newsTipList: [], //消息中心列表
      logoUrl: '',
    };
  },
  computed: {
    ...mapState('setting', ['collapsed', 'menuData']),
    ...mapState({
      username: (state) => state.base.username,
      merchantName: (state) => state.base?.merchant?.merchantName || '',
      merchantLogo: (state) => state.base?.merchant?.icon || undefined,
      appList: (state) => state.base?.sysApps || [],
    }),
    sideWidth() {
      return this.collapsed ? 80 : 208;
    },
    // 默认展示高亮的选中的菜单
    currentNavMenu() {
      const matchedObject = this.$route?.matched[0] || {};
      const parentName = matchedObject?.name;
      return parentName;
    },
    navMenuListShow() {
      return (
        this.menuData
          ?.filter((item) => item.visible === '1')
          .map((item) => {
            return {
              path: '/#' + item.fullPath,
              title: item?.meta?.title || item.name,
              uuid: item.name,
              key: item.name,
            };
          }) || []
      );
    },
    topNavOptions() {
      return {
        logo:
          this.logoUrl || require('@/assets/images/logo/userLogo.png') || '',
        userInfo: {
          avatar: this.merchantLogo,
          username: this.username,
          merchantName: this.merchantName,
        },
      };
    },
    sideMenuData() {
      return (
        this.menuData.find((item) => item.name === this.currentNavMenu)
          .children || []
      );
    },
  },
  methods: {
    ...mapMutations('setting', ['setSideMenuCollapsed']),
    // 字典加载完成
    onDictReady() {
      // 获取系统字典：应用类型
      this.logoUrl = this.dict.type.platform_logo_url?.[0]?.value || '';
    },
    // 刷新信息数据
    async handleMessageHoverChange(visible) {
      if (visible) {
        const [result, error] = await mailFormworkTips();
        if (error) return;
        if (result) {
          const { data } = result;
          this.newsTipList = data;
        }
      }
    },
    toggleCollapse() {
      this.setSideMenuCollapsed(!this.collapsed);
    },
    logout() {
      this.$store.dispatch('base/LogOut').finally(() => {
        this.$router.push(redirectLogin());
      });
    },
  },
};
</script>

<style scoped lang="less">
.layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
  .layout-header {
    z-index: 1;
    flex: 0 0 auto;
  }
  .layout-content {
    position: relative;
    min-width: 1240px; // 1280
    max-width: 2557px; // 2557 // 1920
    margin: 0 auto;
  }
  /deep/ .ant-layout {
    // padding-top: 60px;
    flex: 1;
    background-color: #f4f4f4;
  }
  /deep/ .ant-layout-header {
    overflow-x: auto;
    overflow-y: hidden;
    height: 60px;
    line-height: 60px;
    padding: 0 40px;
    background-image: linear-gradient(
      135deg,
      #ffffff 0%,
      #f9fcff 50%,
      #53a7ed 100%,
      #03c1fe 100%
    );
    // position: absolute;
    width: 100%;
    z-index: 100;
    box-shadow: 0 10px 10px -10px rgba(174, 183, 207, 0.25);
  }
  /deep/ .ant-layout.ant-layout-has-sider > .ant-layout-content {
    overflow-x: auto;
  }
}
</style>
