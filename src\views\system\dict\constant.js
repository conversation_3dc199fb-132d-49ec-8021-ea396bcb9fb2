import moment from 'moment';
import {
  statusOptions,
  getStatusLabel,
  statusEnum2String,
} from '@/views/system/constant/system';

// 字典类型
export const dictTypeEnum = {
  SYSTEM: 'SYSTEM',
  CUSTOM: 'CUSTOM',
  DATASOURCE: 'DATASOURCE',
  WORKFLOW: 'WORKFLOW',
};
export const dictTypeOptions = [
  {
    label: '系统配置',
    value: dictTypeEnum.SYSTEM,
  },
  {
    label: '我的配置',
    value: dictTypeEnum.CUSTOM,
  },
  {
    label: '数据权限字典',
    value: dictTypeEnum.DATASOURCE,
  },
  {
    label: '审批流字典',
    value: dictTypeEnum.WORKFLOW,
  },
];
export const getDictTypeLabel = (value) => {
  return dictTypeOptions.find((item) => item.value === value)?.label;
};
export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'dictName',
      title: '字典名称',
      props: {
        placeholder: '请输入字典名称',
      },
    },
    {
      field: 'dictCode',
      title: '字典编码',
      props: {
        placeholder: '请输入字典编码',
      },
    },
    {
      field: 'status',
      title: '字典状态',
      element: 'a-select',
      props: {
        options: [
          {
            label: '全部',
            value: '',
          },
          ...statusOptions,
        ],
        showSearch: true,
        optionFilterProp: 'children',
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { dictName: '', dictCode: '', status: '' },
};
// 列表查询的分页默认参数
export const getQueryPageParams = () => {
  return {
    page: 1,
    pageSize: 12,
  };
};

export const tableColumns = [
  {
    title: '编码',
    field: 'dictCode',
    key: 'dictCode',
    slots: { default: 'dictCode' },
  },
  {
    title: '名称',
    field: 'dictName',
    key: 'dictName',
  },
  {
    title: '类型',
    field: 'dictType',
    key: 'dictType',
    width: 80,

    formatter: ({ cellValue }) => {
      return getDictTypeLabel(cellValue) || '--';
    },
  },
  {
    title: '状态',
    field: 'status',
    key: 'status',
    width: 80,
    formatter: ({ cellValue }) => {
      return getStatusLabel(cellValue) || '--';
    },
  },
  {
    title: '备注',
    field: 'remark',
    key: 'remark',
  },
  {
    title: '创建时间',
    field: 'createTime',
    key: 'createTime',
    width: 180,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '操作',
    field: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 150,
    slots: { default: 'operation' },
    // scopedSlots: { customRender: 'operation' },
  },
];

export const initForm = ({ dictType } = {}) => {
  return {
    dictId: undefined,
    dictName: '',
    dictType,
    status: statusEnum2String.ENABLED,
    remark: '',
  };
};

/**
 * 字典code相关
 */
export const tableColumns2dictCode = [
  {
    title: '字典Value',
    dataIndex: 'dictValue',
    key: 'dictValue',
    width: 200,
  },
  {
    title: '字典Label',
    dataIndex: 'dictLabel',
    key: 'dictLabel',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 100,
    scopedSlots: { customRender: 'operation' },
  },
];

export const formRules2dictCode = {
  dictLabel: [
    { required: true, message: '不能为空', trigger: 'blur', whitespace: true },
  ],
  dictValue: [
    { required: true, message: '不能为空', trigger: 'blur', whitespace: true },
  ],
};

export const initForm2dictCode = ({ dictCode } = {}) => {
  return {
    dictCode,
    dictLabel: undefined,
    dictValue: undefined,
  };
};
