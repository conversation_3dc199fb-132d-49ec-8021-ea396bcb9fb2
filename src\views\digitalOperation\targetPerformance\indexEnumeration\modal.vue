<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div>
    <a-modal
      :visible="visible"
      :title="visibleTitle"
      @ok="submitHandler"
      @cancel="cancelHandler"
    >
      <a-form-model ref="formRef" :model="formState">
        <a-form-model-item
          prop="targetName"
          label="指标名称"
          :required="true"
          :rules="[{ required: true, message: '请输入指标名称' }]"
        >
          <a-input
            v-model.trim="formState.targetName"
            placeholder="请输入指标名称"
            allow-clear
          />
        </a-form-model-item>
        <a-form-model-item
          v-if="operationType > 2"
          prop="targetUnit"
          label="指标单位"
          :required="true"
          :rules="[{ required: true, message: '请输入指标单位' }]"
        >
          <a-input
            v-model.trim="formState.targetUnit"
            placeholder="请输入指标单位,如”个“、”亿元“、”%“等"
            allow-clear
          />
        </a-form-model-item>
        <a-form-model-item
          prop="initValue"
          label="年度目标初始值"
          v-if="operationType > 2"
        >
          <a-input-number
            v-model.trim="formState.initialValue"
            placeholder="请输入年度目标初始值"
            allow-clear
            :min="0"
            style="width: 100%"
          />
        </a-form-model-item>
        <a-form-model-item
          v-if="operationType > 2"
          prop="rule"
          label="通过分配完成量统计总完成量规则"
          :required="true"
          :rules="[{ required: true, message: '请选择规则' }]"
        >
          <a-radio-group v-model="formState.rule">
            <a-radio value="1"> 取分配量平均值 </a-radio>
            <a-radio value="2"> 取分配量总值 </a-radio>
            <a-radio value="3"> 不按分配量统计</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
const modalTitle = new Map([
  [1, '新增一级指标'],
  [2, '编辑一级指标'],
  [3, '新增二级指标'],
  [4, '编辑二级指标'],
]);
export default {
  name: 'ManagementTkModal',
  props: {
    modalType: {
      type: String,
      default: '1',
    },
    operationType: {
      type: Number,
      default: 1,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    formState: {
      type: Object,
      default: () => {
        return {
          targetName: '',
          targetUnit: '',
          rule: '',
          initialValue: undefined,
        };
      },
    },
  },

  data() {
    return {
      modalTitle,
    };
  },
  computed: {
    visibleTitle() {
      return this.modalTitle.get(this.operationType);
    },
  },

  mounted() {},

  methods: {
    submitHandler() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.$refs.formRef.clearValidate();
          this.$emit('submitHandler');
        }
      });
    },
    cancelHandler() {
      this.$refs.formRef.clearValidate();
      this.$emit('cancel');
    },
  },
};
</script>

<style lang="less" scoped></style>
