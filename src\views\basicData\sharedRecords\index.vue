<template>
  <page-layout>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tableColumn="tableColumn"
      :tablePage="tablePage"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
    >
      <template slot="defaultTitle">
        <span></span>
      </template>
      <template slot="defaultHeader">
        <div class="flex-row-10">
          <a-button type="primary" :loading="hanLoading" @click="handMovement"
            >手动共享</a-button
          >
          <a-button :loading="resetLoading" @click="resetMovement"
            >刷新档案信息</a-button
          >
        </div>
      </template>
    </BuseCrud>
  </page-layout>
</template>

<script>
import { filterOption } from '@/utils';
import {
  syncLogList,
  syncLogExecute,
  refreshLogExecute,
} from '@/api/basicData';
import { saveAs } from 'file-saver';
export default {
  props: {},
  components: {},
  dicts: ['sync_log_share_type'],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      filterParams: {
        status: undefined,
        createBy: '',
        type: undefined,
      },
      hanLoading: false,
      resetLoading: false,
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'type',
            title: '共享类型',
            element: 'a-select',
            props: {
              options: this.dict?.type?.sync_log_share_type || [],
              placeholder: '请选择共享类型',
              showSearch: true,
              filterOption: filterOption,
            },
          },
          {
            field: 'createBy',
            title: '操作人',
            props: {
              placeholder: '请选择操作人',
            },
          },
          {
            field: 'status',
            title: '状态',
            element: 'a-select',
            props: {
              options: [
                {
                  label: '成功',
                  value: '1',
                },
                {
                  label: '失败',
                  value: '0',
                },
              ],
              placeholder: '请选择状态',
              showSearch: true,
            },
          },
        ],
        params: this.filterParams,
      };
    },
    tableColumn() {
      return [
        {
          type: 'seq',
          title: '序号',
          width: 80,
        },
        {
          field: 'type',
          title: '共享类型',
          minWidth: 120,
          // formatter: ({ cellValue }) => {
          //   return this.dict?.type?.sync_log_share_type?.find(
          //     (q) => q.value == cellValue
          //   )?.label;
          // },
        },
        {
          field: 'createTime',
          title: '操作时间',
          minWidth: 150,
        },
        {
          field: 'createBy',
          title: '操作人',
          minWidth: 120,
        },
        {
          field: 'status',
          title: '状态',
          minWidth: 120,
        },
        {
          field: 'fileName',
          title: '共享文件',
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return (
                <a-button
                  type="link"
                  onClick={() => {
                    if (row.filePath) {
                      saveAs(row.filePath, '共享文件.xlsx');
                    }
                  }}
                >
                  下载
                </a-button>
              );
            },
          },
        },
      ];
    },
    modalConfig() {
      return {
        addBtn: false,
        menu: false,
      };
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      const [res] = await syncLogList({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...this.filterParams,
      });
      this.loading = false;
      if (res && res.data) {
        this.tableData = res.data;
        this.tablePage.total = res.total;
      }
    },
    async handMovement() {
      this.hanLoading = true;
      const [res] = await syncLogExecute();
      this.hanLoading = false;
      if (res) {
        this.$message.success('手动共享成功');
      }
    },
    async resetMovement() {
      this.resetLoading = true;
      const [res] = await refreshLogExecute();
      this.resetLoading = false;
      if (res) {
        this.$message.success('刷新档案信息成功');
      }
    },
  },
};
</script>

<style scoped lang="less"></style>
