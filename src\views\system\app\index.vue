<template>
  <page-layout>
    <div>
      <PageWrapper
        style="margin: 0"
        title="应用列表"
        createText=""
        :loading="loading"
        :filterOptions="filterOption"
        :tablePage="tablePage"
        :tableColumn="tableColumns"
        :tableData="tableData"
        @loadData="loadData"
        @handleReset="handleReset"
        @handleCreate="handleCreate"
      >
        <template #defaultHeader>
          <a-button
            v-hasPermi="['system:app:addApp']"
            style="margin-right: 8px"
            type="primary"
            :loading="loading"
            @click="handleCreate"
          >
            添加应用
          </a-button>
        </template>
        <template #appLogo="{ row }">
          <div style="display: flex; justify-content: center">
            <img
              :src="row.appLogo"
              v-if="row.appLogo"
              style="width: 30px; height: 30px; display: block"
            />
            <a-icon v-else type="file-image" style="font-size: 25px" />
          </div>
        </template>
        <template #appCategory="{ row }">
          <a-tag :color="appCategoryTagColor[row.appCategory] || ''">
            {{
              (
                (appCategoryOptions || []).find(
                  (item) => item.value === text
                ) || {}
              ).label || '--'
            }}
          </a-tag>
        </template>
        <template #visible="{ row }">
          <a-badge v-bind="getVisibleBadgeInfo(row.visible)" />
        </template>
        <!-- table插槽 -->
        <template #operate="{ row }">
          <a-button
            icon="bars"
            type="link"
            style="padding: 0; margin-right: 8px"
            @click="goToMenu(row)"
          >
            配置菜单
          </a-button>
          <a-button
            v-hasPermi="['system:app:editApp']"
            icon="edit"
            type="link"
            style="padding: 0; margin-right: 8px"
            @click="handelUpdate(row)"
          >
            编辑
          </a-button>
          <a-button
            v-hasPermi="['system:app:deleteApp']"
            icon="delete"
            type="link"
            style="padding: 0; margin-right: 8px"
            @click="handelDelete(row)"
          >
            删除
          </a-button>
        </template>
      </PageWrapper>
    </div>
    <!-- 编辑app -->
    <EditModal
      :visible.sync="editModalVisible"
      :appId="editModalAppId"
      :appCategoryOptions="appCategoryOptions"
      @ok="loadData"
    />
  </page-layout>
</template>

<script>
import { getList, deleteApp } from '@/api/system/apps';
import { Modal } from 'ant-design-vue';
import EditModal from './EditApp.vue';
import { filterOption, getTableColumns, appCategoryTagColor } from './constant';
import { getVisibleBadgeInfo } from '@/views/system/constant/system';

export default {
  components: {
    EditModal,
  },
  dicts: ['my_authority_app_category'],
  data() {
    return {
      // 遮罩层
      loading: true,
      filterOption,
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],

      appCategoryOptions: [], // 应用分类下拉框选项
      appCategoryTagColor,
      // 编辑应用弹窗相关
      editModalVisible: false,
      editModalAppId: '',
    };
  },
  computed: {
    tableColumns() {
      return getTableColumns({
        appCategoryOptions: this.dict.type.my_authority_app_category,
      });
    },
  },
  async created() {
    this.loadData();
  },
  methods: {
    getVisibleBadgeInfo,
    // 字典加载完成
    onDictReady() {
      // 获取系统字典：应用类型
      this.filterOption.config[1].props.options = [
        { value: '', label: '全部' },
        ...this.dict.type.my_authority_app_category,
      ];
    },
    /** 请求接口数据 */
    async loadData() {
      this.loading = true;
      const params = this.filterOption.params;
      const [result, error] = await getList({
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
        ...params,
      });
      this.loading = false;
      if (error) return;
      this.tableData = result.data;
      this.tablePage.total = result.count;
    },
    handleReset() {
      this.filterOptions.params = { appName: '', appCategory: '' };
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      this.loadData();
    },
    /** 点击按钮 */
    goToMenu(data) {
      const { appId } = data;
      this.$router.push({
        name: 'MenuManage',
        params: {
          id: appId,
        },
      });
    },
    /** 新增按钮操作 */
    handleCreate() {
      this.editModalAppId = '';
      this.editModalVisible = true;
    },
    /** 修改按钮操作 */
    handelUpdate(row) {
      this.editModalAppId = row?.appId;
      this.editModalVisible = true;
    },
    /** 删除按钮操作 */
    handelDelete(row) {
      if (this.loading) return;
      Modal.confirm({
        title: '警告',
        content: '是否确认删除应用名称为"' + row.appName + '"的数据项?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.loading = true;
          const [, error] = await deleteApp({
            appId: row.appId,
          });
          this.loading = false;
          if (error) return;
          this.$message.success('删除成功');
          this.loadData();
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .ant-form-item {
  margin-bottom: 0;
}
</style>
