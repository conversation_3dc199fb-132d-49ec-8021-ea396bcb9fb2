<template>
  <a-drawer
    width="600px"
    title=" 操作日志详情"
    :visible="visible"
    cancelText="取消"
    :footer="null"
    @close="handleCancel"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <a-descriptions title="">
        <a-descriptions-item label="日志内容" :span="3"
          >系统操作
        </a-descriptions-item>
        <a-descriptions-item label="操作模块" :span="3">
          {{ infoDetail && infoDetail.appName }}
        </a-descriptions-item>
        <a-descriptions-item label="操作信息" :span="3">
          {{ infoDetail && infoDetail.menuName }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="操作内容" :span="3">
          {{ infoDetail && infoDetail.appName }}
        </a-descriptions-item> -->
        <a-descriptions-item label="请求地址" :span="3">
          {{ infoDetail && infoDetail.accessAddress }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="操作方法" :span="3">
          {{ infoDetail && infoDetail.appName }}
        </a-descriptions-item>
        <a-descriptions-item label="请求参数" :span="3">
          {{ infoDetail && infoDetail.appName }} -->
        <!-- </a-descriptions-item> -->
        <a-descriptions-item label="操作结果" :span="3">
          {{ infoDetail && infoDetail.remark }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="操作说明" :span="3">
          {{ infoDetail && infoDetail.appName }}
        </a-descriptions-item> -->
      </a-descriptions>
    </a-spin>
  </a-drawer>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    infoDetail: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      form: {},
    };
  },
  methods: {
    // 关闭弹窗
    handleCancel() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
