const TableColumns = [
  {
    title: '园区',
    field: 'parkName',
  },
  {
    title: '总数',
    field: 'totalCount',
  },
  {
    title: '危化品隐患',
    children: [
      {
        title: '数量(个)',
        field: 'dangerousGoodsCount',
      },
      {
        title: '占比(%)',
        field: 'dangerousGoodsPercentage',
      },
    ],
  },
  {
    title: '易燃易爆隐患',
    children: [
      {
        title: '数量(个)',
        field: 'fireExplosiveCount',
      },
      {
        title: '占比(%)',
        field: 'fireExplosivePercentage',
      },
    ],
  },
  {
    title: '易制毒隐患',
    children: [
      {
        title: '数量(个)',
        field: 'poisonCount',
      },
      {
        title: '占比(%)',
        field: 'poisonPercentage',
      },
    ],
  },
  {
    title: '电气安全隐患',
    children: [
      {
        title: '数量(个)',
        field: 'electricalCount',
      },
      {
        title: '占比(%)',
        field: 'electricalPercentage',
      },
    ],
  },
  {
    title: '生物安全隐患',
    children: [
      {
        title: '数量(个)',
        field: 'biologicalCount',
      },
      {
        title: '占比(%)',
        field: 'biologicalPercentage',
      },
    ],
  },
  {
    title: '消防安全隐患',
    children: [
      {
        title: '数量(个)',
        field: 'fireSafetyCount',
      },
      {
        title: '占比(%)',
        field: 'fireSafetyPercentage',
      },
    ],
  },
  {
    title: '特种设备隐患',
    children: [
      {
        title: '数量(个)',
        field: 'specialEquipmentCount',
      },
      {
        title: '占比(%)',
        field: 'specialEquipmentPercentage',
      },
    ],
  },
  {
    title: '其他隐患',
    children: [
      {
        title: '数量(个)',
        field: 'otherCount',
      },
      {
        title: '占比(%)',
        field: 'otherPercentage',
      },
    ],
  },
];
export { TableColumns };
