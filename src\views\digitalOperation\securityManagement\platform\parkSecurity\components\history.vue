<template>
  <div class="history-detail-wrapper">
    <a-row v-if="historyType === 'IE'" type="flex" justify="start">
      <a-col :span="12"
        ><b>被检查单位：</b>{{ row.reviewedOrganizeName }}</a-col
      >
      <a-col :span="12"
        ><b>当前单位消防安全责任人：</b>{{ row.fireSafetyName }}</a-col
      >
    </a-row>
    <a-row v-if="historyType === 'FS'" type="flex" justify="start">
      <a-col :span="8"><b>被检查单位：</b>{{ row.reviewedOrganizeName }}</a-col>
      <a-col :span="8"><b>当前责任人：</b>{{ row.principalName }}</a-col>
      <a-col :span="8"><b>责任人电话：</b>{{ row.principalPhone }}</a-col>
    </a-row>
    <a-row v-if="historyType === 'ELE'" type="flex" justify="start">
      <a-col :span="8"><b>电梯所属园区：</b>{{ row.parkName }}</a-col>
      <a-col :span="8"><b>使用单位：</b>{{ row.userComName }}</a-col>
      <a-col :span="8"><b>设备使用地点：</b>{{ row.devAddress }}</a-col>
    </a-row>
    <BuseCrud
      ref="crud"
      title="历史记录"
      :loading="loading"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :modalConfig="modalConfig"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
      @rowDel="deleteRowHandler"
      @rowView="rowView"
      @loadData="loadData"
    >
    </BuseCrud>
  </div>
</template>

<script>
import { getHistoryTableColumn } from './history.js';
import {
  enterpriseHistory,
  enterpriseDelete,
} from '@/api/digitalOperation/securityManagement/parkSafety/industrialEnterprise.js';
import {
  fireHistory,
  fireDelete,
} from '@/api/digitalOperation/securityManagement/parkSafety/fireSafety.js';
import { elevatorSafetyHistory } from '@/api/digitalOperation/securityManagement/parkSafety/elevatorSafety.js';
export default {
  name: 'HistoryDetail',
  props: {
    //类型：IE:工业企业消防安全，FS：消防安全检查，ELE：电梯安全
    historyType: {
      type: String,
      default: 'IE',
    },
    row: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      menuShow: true,
      tableData: [],
      tableColumn: [],
      params: {},
      loading: false,
    };
  },
  created() {
    this.tableColumn = getHistoryTableColumn(this.historyType);
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: true,
        viewBtnText: '查看详情',
        editBtn: false,
        delBtn: true,
        menuWidth: 150,
        menu: this.historyType === 'ELE' ? false : true,
        modalProps: {
          footer: null,
        },
      };
    },
  },
  mounted() {
    console.log('历史row', this.row);
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      let result = [];

      switch (this.historyType) {
        case 'IE':
          result = await enterpriseHistory(this.row.reviewedOrganizeId);
          break;
        case 'FS':
          result = await fireHistory(this.row.reviewedOrganizeId);
          break;
        case 'ELE':
          result = await elevatorSafetyHistory({ id: this.row.id });
          break;
      }
      const [res, err] = result;
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      console.log(this.tableData, 'this.tableData');
    },
    async delete(id) {
      let result = [];
      switch (this.historyType) {
        case 'IE':
          result = await enterpriseDelete(id);
          break;
        case 'FS':
          result = await fireDelete(id);
          break;
      }
      const [, err] = result;
      if (err) return this.$message.error('删除失败');
      this.$message.success('删除成功');
      this.loadData();
    },
    deleteRowHandler(row) {
      let that = this;
      this.$confirm({
        title: '提醒',
        content: '确定要删除这条历史数据吗?',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          console.log('OK');
          that.delete(row.id);
        },
        onCancel() {
          console.log('Cancel');
        },
      });
    },

    rowView(row) {
      switch (this.historyType) {
        case 'IE':
          localStorage.setItem('addForIEInfo', JSON.stringify(row));
          this.$router.push({
            name: 'addForIE',
            query: {
              id: row.id,
              type: 'view',
            },
          });
          break;
        case 'FS':
          localStorage.setItem('addForFSInfo', JSON.stringify(row));
          this.$router.push({
            name: 'addForFS',
            query: {
              id: row.id,
              type: 'view',
            },
          });
          break;
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.bd3001-page-wrapper-container {
  margin: 16px 0;
}
/deep/.bd3001-content {
  padding: 0;
}
</style>
