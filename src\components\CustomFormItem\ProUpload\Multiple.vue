<template>
  <div class="multiple-upload">
    <Upload
      v-bind="$attrs"
      :maxNum="1"
      v-for="index in Array.from({ length: maxNum })"
      :key="index"
    />
  </div>
</template>
<script>
// 未完成
import Upload from './index.vue';
export default {
  components: {
    Upload,
  },
  props: {
    maxNum: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      fileList: [],
    };
  },
  created() {
    if (this.value) {
      if (!Array.isArray(this.value)) {
        this.fileList = [
          {
            uid: -1,
            status: 'done',
            name: this.value.split('/').at(-1),
            url: this.value,
          },
        ];
      }
    }
  },
};
</script>
