// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    field: 'park',
    title: '重点园区',
    minWidth: 200,
  },
  {
    field: 'enterprise',
    title: '重点企业',
    width: 420,
  },
  {
    field: 'area',
    title: '载体面积(m²)',
    width: 320,
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 100,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'enterprise',
    title: '企业名称',
    // element: 'slot',
    // slotName: 'nameSlot',
    element: 'a-input',
  },
  {
    field: 'park',
    title: '所属园区',
    element: 'slot',
    slotName: 'enterSlot',
  },
];

export const initFormValue = () => {
  return {
    parkId: '',
    enterpriseId: '',
    area: '',
  };
};

export const formConfig = [
  {
    field: 'name',
    title: '重点园区',
    element: 'a-select',
    props: {
      options: [
        { value: '1', label: '应税收入' },
        { value: '2', label: '工业产值' },
      ],
    },
    rules: [{ required: true, message: '请输入重点园区' }],
  },
  {
    field: 'state',
    title: '重点企业',
    // element: 'a-select',
    // props: {
    //   options: [
    //     { value: '1', label: '应税收入' },
    //     { value: '2', label: '工业产值' },
    //     { value: '3', label: '规上服务业营收' },
    //     { value: '4', label: '盈利性服务业' },
    //     { value: '5', label: '合作基金' },
    //     { value: '6', label: '招商企业' },
    //     { value: '7', label: '招入人才' },
    //   ],
    // },
    element: 'slot',
    slotName: 'enterSlot',
    rules: [{ required: true, message: '请输入重点企业' }],
  },
];
