<template>
  <div>
    <a-row v-if="operationType !== 'editTarget'">
      <a-form-item label="分配对象" :required="true">
        <a-select
          mode="multiple"
          :default-value="[]"
          style="width: 100%"
          placeholder="请先选择分配对象"
          @change="handleAreaChange"
          :options="organizeAll"
          v-model="organizelist"
        >
        </a-select>
      </a-form-item>
    </a-row>
    <AddTimeNodes
      ref="AddTimeNodes"
      v-bind="AddTimeNodesData"
      :operationType="operationType"
    />
    <AllotForm
      ref="AllotForm"
      :areaKeys="areaKeys"
      :areaList="organizeAll"
      :areaInitData="areaInitData"
      :targetUnit="row.targetUnit"
    />
  </div>
</template>

<script>
import AddTimeNodes from './addTimeNodes.vue';
import AllotForm from './allotForm.vue';
import moment from 'moment';
import { getOrganize } from '@/views/digitalOperation/taskManagement/utils/index.js';
export default {
  name: 'ManagementTkTargetAssignment',
  props: {
    row: {
      type: Object,
      default: () => {
        return {};
      },
    },
    operationType: {
      type: String,
      default: '',
    },
    // cenaInfo: {
    //   type: Object,
    //   default: () => {
    //     return {};
    //   },
    // },
  },
  components: {
    AddTimeNodes,
    AllotForm,
  },
  data() {
    return {
      areaKeys: [],
      organizeAll: [], //园区
      organizelist: [],
      /**
       * AddTimeNodesData：时间节点组件数据
       * selectType: 0:1:自定义
       * customTimeNodes: 自定义时间节点数组，传Moment类型
       */
      AddTimeNodesData: {
        selectType: '0',
        customTimeNodes: null,
      },
      /**
       * 分配园区具体数据，包括园区id,分配数量，分配单位,补充说明
       * *** 编辑时，需要回显的字段 ***
       * areaKey: 园区ID
       * number: 分配数量
       * unit: 分配单位
       * detail: 补充说明
       * **/
      areaInitData: [],
      // targetUnit: '', //单位
      // achieveTarget: '', //目标
    };
  },

  created() {
    console.log('回显啊回显', this.row, '回显啊回显');
    getOrganize().then((res) => {
      this.organizeAll = res;
    });
    console.log('========', this.operationType, this.row);
    /**
     * TODO:如果是编辑数据，进行回显，需要初始化数据.
     * =========字段名和后端保持一致，联调需要调整========
     * */
    // this.getAchieveTarget(this.row);
    if (this.operationType === 'editTarget') {
      console.log(this.row, 'this.row');
      //传入园区id
      this.areaKeys.push(this.row.parkId);
      //传入阶段性反馈日期选择类型
      this.AddTimeNodesData.selectType = this.row.dateSelection || '0';
      //如果是反馈日期选择类型自定义的，需要初始化自定义数据
      if (this.row.dateSelection == '1') {
        this.AddTimeNodesData.customTimeNodes =
          this.row.customTime.split(',').map((item) => moment(item)) || [];
      }

      //回显分配园区具体数据
      this.areaInitData.push({
        areaKey: this.row.parkId,
        number: this.row.assignedAmount,
        unit: this.row.targetUnit,
        detail: this.row.remark,
      });
    }
    //目标分配的草稿回显数据
    if (this.operationType === 'targetAssignment' && this.row.cenaInfoAll) {
      let arr = [];
      let areaArr = [];
      this.row.cenaInfoAll.infoList.forEach((item) => {
        arr.push(item.parkId);
        areaArr.push({
          areaKey: item.parkId,
          number: item.assignedAmount,
          unit: this.row.targetUnit || '',
          detail: item.remark,
        });
      });

      //传入园区id
      this.organizelist = arr;
      this.areaKeys = arr;
      //传入阶段性反馈日期选择类型
      this.AddTimeNodesData.selectType =
        this.row.cenaInfoAll.dateSelection || '0';
      //如果是反馈日期选择类型自定义的，需要初始化自定义数据
      if (this.row.cenaInfoAll.dateSelection == '1') {
        this.AddTimeNodesData.customTimeNodes =
          this.row.cenaInfoAll.customTime
            .split(',')
            .map((item) => moment(item)) || [];
      }

      //回显分配园区具体数据
      this.areaInitData = areaArr;
    }
  },
  mounted() {},

  methods: {
    //总计划完成目标回显
    achieveTargetHandle(value) {
      const { achieveTarget } = value;
      let index = null;
      if (achieveTarget.indexOf('个') > -1) {
        return (index = achieveTarget.indexOf('个'));
      } else if (achieveTarget.indexOf('%') > -1) {
        return (index = achieveTarget.indexOf('%'));
      } else if (achieveTarget.indexOf('亿元') > -1) {
        return (index = achieveTarget.indexOf('亿元'));
      }
      return index;
    },
    getAchieveTarget(value) {
      let index = this.achieveTargetHandle(value);
      this.targetUnit = value.achieveTarget.slice(
        index,
        value.achieveTarget.length
      );
      index > -1
        ? (this.achieveTarget = value.achieveTarget.slice(0, index))
        : '';
    },
    handleAreaChange(value) {
      console.log(value, 'value44');
      this.areaKeys = value;
    },

    getAllFormData() {
      const AllotForm = this.$refs.AllotForm;
      const addTimeNodeForm = this.$refs.AddTimeNodes;
      const AddTimeNodesFrom = addTimeNodeForm.form;
      const { form, resolveData } = AllotForm;
      if (
        this.operationType !== 'editTarget' &&
        this.organizelist.length === 0
      ) {
        this.$message.error('请选择分配对象');
        return false;
      }

      return Promise.all([
        AddTimeNodesFrom.validateFields(),
        form.validateFields(),
      ])
        .then((res) => {
          // debugger;
          //阶段性反馈日期选择数据
          const timeNodeData = res[0];
          //阶段性反馈日期选择数据中自定义数据--没有空数据
          const timeNodeDataForCustom =
            res[0].times?.filter((item) => item) || [];
          console.log(
            'Received values of form1: ',
            timeNodeData,
            timeNodeDataForCustom
          );
          //分配对象数据
          const areaData = resolveData(res[1]);
          // console.log('Received values of form2222: ', areaData);
          let areaDataParams = [];
          areaData.forEach((item) => {
            this.organizeAll.forEach((it) => {
              if (it.value === item.area) {
                areaDataParams.push({
                  parkId: it.value,
                  parkName: it.label,
                  assignedAmount: item.number,
                  remark: item.detail || '',
                });
              }
            });
          });
          console.log(areaDataParams, 'areaDataParams');
          return {
            timeNodeData,
            timeNodeDataForCustom,
            areaDataParams,
          };
        })
        .catch((error) => {
          error && this.$message.error('表单填写不完整');
          return false;
        });
    },
  },
};
</script>

<style lang="less" scoped></style>
