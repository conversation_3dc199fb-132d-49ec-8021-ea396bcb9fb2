<template>
  <div class="action-echart-box">
    <h2>各单位历史合格率情况</h2>
    <a-form-model :model="form" layout="horizontal">
      <a-row>
        <a-col :span="11" class="mr10">
          <a-form-model-item label="所属园区">
            <a-select
              v-model="form.parkId"
              placeholder="请选择园区"
              @change="parkChangeHandler"
              @search="parkSearchHandler"
              :options="parkList"
              :showSearch="true"
              :filterOption="false"
              :notFoundContent="null"
            >
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12" class="mr10">
          <p class="pass-tip">
            被检查单位：<b>{{ echartDataLength }}个</b>
          </p>
        </a-col>
      </a-row>
    </a-form-model>
    <BaseChart
      class="chart-box"
      v-if="resultData.axisData.length > 0"
      :axisData="resultData.axisData"
      :seriesData="resultData.seriesData"
      :yAxis="yAxis"
      :tooltipFormatter="tooltipFormatter"
      unit="符合率"
    />
    <echartEmptyBox v-else />
  </div>
</template>

<script>
import echartEmptyBox from '@/components/echartEmptyBox/index.vue';
import BaseChart from '@/components/chart/lineChart.vue';
import { getTemplateBase } from '../echart.js';
// import { mockDataForFSHistory } from '../mockData';
import { getOrganize } from '@/views/digitalOperation/taskManagement/utils/index.js';
import { fireSafetyUnit } from '@/api/digitalOperation/securityManagement/parkSafety/echart/FS.js';
export default {
  name: 'ManagementTkActions',
  components: {
    BaseChart,
    echartEmptyBox,
  },

  data() {
    return {
      echartData: null,
      echartDataLength: 0,
      parkList: [],
      form: {
        parkId: undefined,
      },
      resultData: {
        axisData: [],
        seriesData: [
          {
            name: '合格率',
            type: 'bar',
            data: [],
          },
        ],
      },
    };
  },
  created() {
    this.yAxis = {
      type: 'value',
      nameGap: 40,
      nameTextStyle: {
        // 字体样式
        padding: [0, -60, 0, 0],
        color: 'rgba(0,0,0,0.45)',
        fontSize: 14, // 字体大小
      },
      axisLabel: {
        formatter: '{value}%',
      },
    };
  },

  mounted() {
    getOrganize().then((res) => {
      this.parkList = res;
      this.form.parkId = this.parkList[0].value;
      this.getActionsData();
    });
  },

  methods: {
    async getActionsData() {
      const [res, err] = await fireSafetyUnit({ parkId: this.form.parkId });
      if (err) return;
      let arr = [];
      res.data.forEach((item) => {
        arr.push({
          name: item.unitName,
          historyCheck: item.checkNums,
          checkPass: item.compliantNums,
          checkRate: item.rate,
        });
      });
      this.echartDataLength = arr.length;
      setTimeout(() => {
        this.echartData = arr;
        this.resultData.axisData = this.echartData.map((item) => item.name);
        this.resultData.seriesData[0].data = this.echartData.map(
          (item) => item.checkRate
        );
      }, 500);
      //TODO:获取近期和历史符合率数据
    },
    tooltipFormatter(info) {
      let str = `<div style="text-align: left; color:#1D2129;" >${info[0].name}</div>`;

      const itemInfo = this.echartData.filter(
        (item) => item.name === info[0].name
      )[0];
      str += getTemplateBase('', '历史检查次数', itemInfo.historyCheck);
      str += getTemplateBase('', '合格次数', itemInfo.checkPass);
      info.forEach((item) => {
        str += getTemplateBase(item.marker, item.seriesName, item.value + '%');
      });
      return str;
    },
    parkSearchHandler(val) {
      getOrganize(val).then((res) => {
        this.parkList = res;
      });
    },
    parkChangeHandler(value) {
      console.log(
        '🚀 ~ file: actions.vue:124 ~ parkChangeHandler ~ value:',
        value
      );
      //重新获取数据
      this.getActionsData();
    },
  },
};
</script>

<style lang="less" scoped>
.action-echart-box {
  margin: 16px;
}
</style>
