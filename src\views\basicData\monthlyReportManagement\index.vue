<template>
  <page-layout>
    <BuseCrud
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
      :modalConfig="modalConfig"
      :tableOn="{
        'checkbox-change': selectChangeEvent,
        'checkbox-all': selectChangeEvent,
      }"
    >
      <template #defaultTitle>
        <span></span>
      </template>
      <!-- 创建按钮区域插槽 -->
      <template #defaultHeader>
        <a-button
          type="primary"
          style="margin-right: 8px"
          @click="handleCreate"
        >
          新增
        </a-button>
        <a-button style="margin-right: 8px" @click="onClickImport">
          导入
        </a-button>
        <a-button
          :loading="exportLoading"
          style="margin-right: 8px"
          @click="exportItem"
        >
          导出
        </a-button>
        <a-button
          type="danger"
          style="margin-right: 8px"
          :disabled="!checkItems.length"
          @click="handelDelete"
        >
          删除
        </a-button>
      </template>
      <!-- 年插槽 -->
      <template #year>
        <BuseRangePicker
          type="month"
          v-model="filterOptions.params.rateTime"
          format="YYYY-MM"
          :placeholder="['请选择开始月份', '请选择结束月份']"
          :disableDateFunc="disableDateFunc"
        />
      </template>
      <!-- 企业 -->
      <template #enterSlot>
        <a-select
          :allowClear="true"
          :filterOption="filterOption"
          :showSearch="true"
          placeholder="请选择"
          @change="onEnterChange"
        >
          <a-select-option
            v-for="item in stateSelect"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </template>
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
        <span class="operate-button" @click="onClickDetail(row)">详情</span>
      </template>
      <!-- 编辑弹窗 -->
      <ListModal
        :visible="visible"
        :detail="modalData"
        :modelTitle="modelTitle"
        :preview="preview"
        @loadData="loadData"
        @handleCancel="handleCancel"
      />
    </BuseCrud>
  </page-layout>
</template>

<script>
import moment from 'moment';
import { institutionsMixin } from '../mixins/institutionsMixin';
import { defaultTableColumn, defaultFilterConfig } from './constant';
import ListModal from './components/ListModal.vue';
import { resolveBlob } from '@/utils/common/fileDownload';
import {
  taxablePage,
  deleteTaxable,
  downloadInformation,
} from '@/api/basicData';

export default {
  components: { ListModal },
  dicts: ['my_notify_rule'],
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'monthlyReportManagement',
      loading: false,
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
        params: {
          rateTime: { startValue: '', endValue: '' },
          enterpriseName: '',
          scc: this.$route.query.pictureId,
          unifiedCreditCode: this.$route.query.pictureId,
        }, // 筛选器结果数据
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: defaultTableColumn(),
      tableData: [],
      visible: false,
      modalData: null,
      checkItems: [],
      modelTitle: 'add',
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
      detail: {},
      preview: false,
      url: '',
      exportLoading: false,
    };
  },
  mounted() {},
  created() {
    this.loadData();
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        menu: false,
      };
    },
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {},
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 下拉联动
    onEnterChange() {
      console.log(222);
    },
    // 请求接口数据
    async loadData() {
      this.checkItems = [];
      this.loading = true;
      const [res, err] = await taxablePage({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        enterpriseName: this.filterOptions.params.enterpriseName,
        scc: this.filterOptions.params.scc,
        startRateTime: this.filterOptions.params.rateTime?.startValue
          ? moment(this.filterOptions.params.rateTime?.startValue).format(
              'YYYY-MM'
            )
          : '',
        endRateTime: this.filterOptions.params.rateTime?.endValue
          ? moment(this.filterOptions.params.rateTime?.endValue).format(
              'YYYY-MM'
            )
          : '',
      });
      this.loading = false;
      if (err) return;
      // 设置数据
      this.tablePage.total = res.total;
      this.tableData = res.data;
    },
    // 创建按钮点击事件
    handleCreate() {
      this.modelTitle = 'add';
      this.visible = true;
    },
    // 编辑按钮点击事件
    onClickEdit(row) {
      this.modelTitle = 'edit';
      this.visible = true;
      this.modalData = row;
    },
    onClickDetail(row) {
      this.modelTitle = 'see';
      this.modalData = row;
      this.preview = true;
      this.visible = true;
    },
    // 关闭弹窗
    handleCancel(update) {
      this.visible = false;
      this.modalData = null;
      this.manageVisible = false;
      this.preview = false;
      if (update) {
        this.loadData();
      }
    },
    // 管理按钮点击事件
    onClickManage() {
      this.manageVisible = true;
    },
    // 删除
    handelDelete() {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [, err] = await deleteTaxable({
            list: that.checkItems,
          });
          if (!err) {
            that.$message.success('删除成功!');
            that.checkItems = [];
            // 刷新数据
            that.loadData();
            return;
          }
        },
      });
    },
    // 园区类别
    stateChange(value) {
      console.log('选中值', value);
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: this.pageName,
          // url: 'downloadTaxTemplate', // 导出传参下载模板的url
          // apiUrl: '/base/taxableMarket/excelAdd', // 导入传参上传请求url
        },
      });
    },
    // 年份选择
    yearChange(date, dateString) {
      console.log('月份选择回调', date, dateString);
    },
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    async exportItem() {
      this.exportLoading = true;
      const params = {
        ids: this.checkItems,
        ...this.filterOptions.params,
        rateTime: this.filterOptions?.params?.rateTime?.startValue
          ? moment(this.filterOptions?.params?.rateTime?.startValue).format(
              'yyyy'
            )
          : '',
      };
      const [res] = await downloadInformation(
        params,
        'monthlyReportManagement'
      );
      this.exportLoading = false;
      console.log(res, 'res');
      if (res) {
        resolveBlob(res, this.mimeMap, '导出', '.xlsx');
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.page-wrapper-container {
  margin: 0 !important;
}
/deep/.bd3001-auto-filters-container {
  .ant-row-flex {
    .ant-col {
      &:nth-child(3) {
        width: 500px;
      }
    }
  }
}
</style>
