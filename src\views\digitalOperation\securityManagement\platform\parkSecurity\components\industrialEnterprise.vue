<template>
  <div>
    <div v-if="isListTab">
      <BuseCrud
        ref="crud"
        title="企业安全生产"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :tableProps="{
          headerAlign: 'left',
          border: 'none',
          columnConfig: { resizable: true },
          showOverflow: 'tooltip',
          align: 'left',
        }"
        :modalConfig="modalConfig"
        @modalConfirm="modalConfirmHandler"
        @handleCreate="rowAdd"
        @loadData="loadData"
      >
        <template slot="checkHistory" slot-scope="{ row }">
          <HistoryDetail historyType="IE" :row="row" />
        </template>
        <template slot="defaultHeader">
          <a-button type="default" class="mr10" @click="goEchartPage"
            >图表</a-button
          >
        </template>
      </BuseCrud>
    </div>
    <div v-else>
      <echartIE @goListPage="goListPage"></echartIE>
    </div>
  </div>
</template>

<script>
import HistoryDetail from './history.vue';
import echartIE from '@/views/digitalOperation/securityManagement/common/echart/echartIE.vue';
import {
  pageList,
  enterpriseAdd,
  enterpriseDelete,
} from '@/api/digitalOperation/securityManagement/parkSafety/industrialEnterprise.js';
import {
  getOrganize,
  enterpriseBasicAllList,
} from '@/views/digitalOperation/taskManagement/utils/index.js';
// eslint-disable-next-line vue/no-unused-components
import FuzzySelect from '@/components/FuzzySelect/index.vue';
export default {
  name: 'IndustrialEnterprise',
  // eslint-disable-next-line vue/no-unused-components
  components: { HistoryDetail, echartIE, FuzzySelect },
  data() {
    return {
      menuShow: true,
      tableData: [],
      tableColumn: [],
      params: {
        parkId: undefined,
        reviewedOrganizeName: '',
        unifiedCreditCode: this.$route.query.pictureId,
      },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      companyListForSearch: [],
      parkFilterList: [], //筛选园区数据下拉框
      parkModelList: [], //model园区数据下拉框
      reviewedOrganizeFilterList: [], //筛选企业数据下拉框
      reviewedOrganizeModelList: [], //model企业数据下拉框
      isListTab: true,
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            title: '所属园区',
            element: 'a-select',
            field: 'parkId',
            props: {
              options: this.parkFilterList,
              showSearch: true,
              filterOption: false,
              notFoundContent: null,
              labelInValue: true,
            },
            on: {
              change: this.parkFilterChange,
              search: this.parkFilterSearch,
            },
            previewFormatter: (value) => {
              return value.label;
            },
          },
          {
            field: 'reviewedOrganizeName',
            title: '被检查单位',
            props: {
              placeholder: '请输入被检查单位',
            },
          },
          {
            title: '统一社会信用代码',
            field: 'unifiedCreditCode',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: true,
        editBtn: false,
        viewBtn: false,
        delBtn: false,
        menuWidth: 300,
        addBtnText: '新增被检查单位',
        addTitle: '新增被检查单位',
        formConfig: [
          {
            title: '所属园区',
            field: 'parkName',
            rules: [{ required: true, message: '请选择所属园区' }],
            props: {
              disabled: true,
            },
          },
          {
            field: 'reviewedOrganizeId',
            title: '被检查单位',
            rules: [{ required: true, message: '请选择被检查单位' }],
            element: (h, item, params) => {
              return (
                <FuzzySelect
                  value={params?.reviewedOrganizeId}
                  onChangeSelect={(val) => {
                    if (val) {
                      this.$refs.crud.setFormFields({
                        ...this.formValue,
                        reviewedOrganizeId: val.unifiedCreditCode,
                        reviewedOrganizeName: val.name,
                        unifiedCreditCode: val.unifiedCreditCode,
                        parkName: val.parkName,
                        parkId: val.parkId,
                      });
                    } else {
                      this.$refs.crud.setFormFields({
                        ...this.formValue,
                        reviewedOrganizeId: '',
                        reviewedOrganizeName: '',
                        unifiedCreditCode: '',
                        parkName: '',
                        parkId: '',
                      });
                    }
                  }}
                />
              );
            },
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            // rules: [{ required: true, message: '请输入统一社会信用代码' }],
            props: {
              placeholder: '请输入统一社会信用代码',
              disabled: true,
            },
          },
        ],
        customOperationTypes: [
          {
            title: '查看详情',
            typeName: 'checkDetail',
            event: (row) => {
              localStorage.setItem('addForIEInfo', JSON.stringify(row));
              this.$router.push({
                name: 'addForIE',
                query: {
                  id: row.id,
                  type: 'view',
                },
              });
            },
            condition: (row) => {
              //如果最近检查时间没有填写，则不显示查看详情按钮
              return row.inspectTime ? true : false;
            },
          },
          {
            title: '新增检查记录',
            typeName: 'addCheckRecord',
            event: (row) => {
              this.reviewedOrganizeModelList = [];
              localStorage.setItem('addForIEInfo', JSON.stringify(row));
              this.$router.push({
                name: 'addForIE',
                query: {
                  id: row.id,
                  type: 'add',
                },
              });
            },
            condition: () => {
              return true;
            },
          },
          {
            title: '查看历史记录',
            typeName: 'checkHistory',
            slotName: 'checkHistory',
            showForm: false,
            modalProps: {
              footer: null,
            },
            event: (row) => {
              return this.$refs.crud.switchModalView(true, 'checkHistory', row);
            },
            condition: (row) => {
              //TODO:如果最近检查时间没有填写，则不显示查看详情按钮
              return row.inspectTime ? true : false;
            },
          },
          {
            title: '删除被检查单位',
            typeName: 'delCompany',
            event: (row) => {
              let that = this;
              this.$confirm({
                title: '提醒',
                content: '确认要删除被检查单位吗？',
                okText: '确认',
                cancelText: '取消',
                onOk() {
                  console.log('OK');
                  that.enterpriseDelete(row.id);
                },
                onCancel() {
                  console.log('Cancel');
                },
              });
            },
            condition: (row) => {
              //TODO:如果最近检查时间没有填写，则不显示查看详情按钮
              return row.inspectTime ? false : true;
            },
          },
        ],
      };
    },
  },
  created() {
    this.tableColumn = [
      {
        title: '所属园区',
        field: 'parkName',
        minWidth: 120,
      },
      {
        title: '统一社会信用代码',
        field: 'unifiedCreditCode',
        minWidth: 140,
      },
      {
        title: '被检查单位',
        field: 'reviewedOrganizeName',
        minWidth: 120,
      },
      {
        title: '最近检查时间',
        field: 'inspectTime',
        minWidth: 120,
      },
      {
        title: '检查人员',
        field: 'inspectName',
        minWidth: 120,
      },
      {
        title: '单位消防安全负责人',
        field: 'fireSafetyName',
        minWidth: 160,
      },
      {
        title: '最近信息录入时间',
        field: 'informationEntryTime',
        minWidth: 140,
      },
    ];
    this.getParkFilterList();
    this.getParkModelList();
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      const p = {
        unifiedCreditCode: this.params.unifiedCreditCode || '',
        parkId: this.params.parkId?.key || '',
        parkName: this.params.parkId?.label || '',
        reviewedOrganizeName: this.params.reviewedOrganizeName || '',
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      };

      const [res, err] = await pageList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data.records;
      this.tablePage.total = res.data.total;
    },
    //park -- filter事件处理开始
    getParkFilterList(name) {
      getOrganize(name).then((res) => {
        this.parkFilterList = res;
      });
    },
    parkFilterSearch(value) {
      this.getParkFilterList(value);
    },
    parkFilterChange(val) {
      console.log(val, 'filter');
      this.params.reviewedOrganizeId = undefined;
      enterpriseBasicAllList(val.key).then((res) => {
        this.reviewedOrganizeFilterList = res;
      });
    },
    //park -- filter事件处理结束
    //被检查单位--filter事件处理开始
    reviewedOrganizeFilterSearch(value) {
      enterpriseBasicAllList(this.params.parkId?.key, value).then((res) => {
        this.reviewedOrganizeFilterList = res;
      });
    },
    //被检查单位--filter事件处理结束
    //park -- model事件处开始
    getParkModelList(name) {
      getOrganize(name).then((res) => {
        this.parkModelList = res;
      });
    },
    parkModelSearch(value) {
      this.getParkModelList(value);
    },
    parkModelChange(val) {
      this.parkIdModel = val.key;
      this.parkNameModel = val.label;
      this.$refs.crud.$refs.modalView.formParams.reviewedOrganizeId = undefined;
      this.$refs.crud.$refs.modalView.formParams.unifiedCreditCode = '';
      enterpriseBasicAllList(val.key).then((res) => {
        this.reviewedOrganizeModelList = res;
      });
    },
    //park -- model事件处结束
    //被检查单位--model事件处理开始
    reviewedOrganizeModelSearch(value) {
      enterpriseBasicAllList(this.parkIdModel, value).then((res) => {
        this.reviewedOrganizeModelList = res;
      });
    },
    //被检查单位--model事件处理结束
    //新增
    async enterpriseAdd(params) {
      const [, err] = await enterpriseAdd(params);
      if (err) return this.$message.error('新增失败');
      this.$message.success('新增成功');
      this.loadData();
    },
    //删除
    async enterpriseDelete(id) {
      const [, err] = await enterpriseDelete(id);
      if (err) return this.$message.error('删除失败');
      this.$message.success('删除成功');
      this.loadData();
    },

    modalConfirmHandler(value) {
      return new Promise((resolve) => {
        const {
          parkId,
          parkName,
          reviewedOrganizeId,
          reviewedOrganizeName,
          unifiedCreditCode,
        } = value;
        console.log(value, 'value');
        enterpriseAdd({
          parkId: parkId,
          parkName: parkName,
          reviewedOrganizeId: reviewedOrganizeId,
          reviewedOrganizeName: reviewedOrganizeName,
          unifiedCreditCode: unifiedCreditCode,
        })
          .then(([, err]) => {
            if (err) {
              this.$message.error('新增失败');
              resolve(false);
            } else {
              resolve(true);
              this.$message.success('新增成功');
              this.loadData();
            }
          })
          .finally(() => {
            resolve(false);
          });
      });
    },
    deleteRowHandler() {},
    rowAdd() {
      // this.showCompanyList = false;
      return this.$refs.crud.switchModalView(true);
    },
    goListPage() {
      this.isListTab = true;
    },
    goEchartPage() {
      this.isListTab = false;
      // const targetRoute = {
      //   name: 'echartIE',
      // };
      // const routeInfo = this.$router.resolve(targetRoute);
      // const url = routeInfo.href;
      // window.open(url, '_blank');
    },
  },
};
</script>

<style lang="less" scoped></style>
