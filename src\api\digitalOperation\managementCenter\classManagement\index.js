import { request } from '@/utils/request/requestTkb';

// 类别配置查询
export function categoryList(data) {
  return request({
    url: '/category/list',
    method: 'get',
    params: data,
  });
}

// 类别配置新增
export function categoryAdd(data) {
  return request({
    url: '/category/add',
    method: 'post',
    data,
  });
}

// 类别配置删除
export function categoryDelete(data) {
  return request({
    url: '/category/delete/{id}'.replace('{id}', data.id),
    method: 'get',
    params: data,
  });
}
