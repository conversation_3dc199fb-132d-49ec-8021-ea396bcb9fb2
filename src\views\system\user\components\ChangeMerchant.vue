<template>
  <a-modal
    :title="`选择租户`"
    okText="确定"
    cancelText="取消"
    width="500px"
    :visible="visible"
    @ok="chooseMerchant"
    @cancel="closeModal"
    :bodyStyle="{ padding: '24px 48px' }"
  >
    <MerchantTree
      :merchantId.sync="merchantId"
      :merchantName.sync="merchantName"
      @afterChooseMerchant="afterChooseMerchant"
    ></MerchantTree>
  </a-modal>
</template>

<script>
import MerchantTree from '@/components/SystemTreeSelect/MerchantTree.vue';
export default {
  components: { MerchantTree },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    chooseMerchantId: {
      type: String,
      default: '',
    },
    chooseMerchantName: {
      type: String,
      default: '',
    },
  },

  watch: {
    visible(val) {
      if (val) {
        this.merchantId = this.chooseMerchantId;
      }
    },
  },
  data() {
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      userDetail: {},
      loading: false,

      merchantId: '',
      merchantName: '',
    };
  },
  methods: {
    afterChooseMerchant() {
      console.log('afterChooseMerchant');
    },
    chooseMerchant() {
      this.$emit('update:chooseMerchantId', this.merchantId);
      this.$emit('update:chooseMerchantName', this.merchantName);
      this.$emit('update:visible', false);
      this.$emit('refresh');
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped></style>
