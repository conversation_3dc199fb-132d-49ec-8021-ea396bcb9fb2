<template>
  <div>
    <a-modal
      width="600px"
      :visible="visible"
      title="终止"
      @cancel="cancelHandler"
      @ok="okHandler"
    >
      <a-form :form="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="终止原因">
          <a-textarea
            placeholder="请输入终止原因！"
            :auto-size="{ minRows: 3, maxRows: 5 }"
            v-decorator="[
              'terminateRemark',
              {
                rules: [{ required: true, message: '请输入终止原因！' }],
              },
            ]"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { operation } from '@/api/digitalOperation/taskManagement/todoList.js';
export default {
  name: 'ManagementTkTermination',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    taskDetail: {
      type: Object,
      default() {
        return {};
      },
    },
  },

  data() {
    return {
      form: this.$form.createForm(this, { name: 'termination' }),
    };
  },

  mounted() {},

  methods: {
    async operation(value) {
      let p = {
        ...value,
        taskId: this.taskDetail.id,
        departmentName: this.taskDetail.departmentName,
        tag: '5', //操作按钮标识: 1:发起任务  2:提交结果  3:确认办结  4:归档  5:终止
      };
      console.log('p', p);
      const [, err] = await operation(p);
      if (err) return;
      this.$message.success('终止成功');
      this.$emit('refresh');
      this.$emit('close');
    },

    cancelHandler() {
      this.$emit('close');
    },
    okHandler() {
      this.form.validateFields((err, values) => {
        if (err) return;
        const reason = values.reason;
        console.log(
          '🚀 ~ file: termination.vue:55 ~ this.form.validateFields ~ reason:',
          reason
        );
        //终止请求
        this.operation(values);
        //this.$emit('close');
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
