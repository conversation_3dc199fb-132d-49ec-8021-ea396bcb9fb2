import { request } from '@/utils/request/requestTkb';

// 查询企业画像经营数据
export function queryBusinessPictureOperate(query) {
  return request({
    url: '/business/picture/queryBusinessPictureOperate',
    method: 'POST',
    data: query,
  });
}
// 营收增长率查询
export function getGrowRateAnalysisPage(query) {
  return request({
    url: '/business/picture/getGrowRateAnalysisPage',
    method: 'POST',
    data: query,
  });
}
// 下载营收增长率分析
export function downloadGrowRateAnalysis(query) {
  return request({
    url: '/business/picture/downloadGrowRateAnalysis',
    method: 'POST',
    data: query,
    responseType: 'blob',
  });
}
