import store from '@/store';
// 判断是否有权限
export function hasPermissions(value) {
  const all_permission = '*:*:*';
  const permissions = store.state.base.permissions;

  if (value && value instanceof Array && value.length > 0) {
    const permissionFlag = value;
    const hasPermissions = permissions.some((permission) => {
      return (
        all_permission === permission || permissionFlag.includes(permission)
      );
    });
    // 无权限
    if (!hasPermissions) {
      return true;
    }
  } else {
    throw new Error('请设置操作权限标签值');
  }
}
