<template>
  <div class="uploadImg">
    <a-upload
      :file-list="fileList"
      :remove="handleRemove"
      :before-upload="beforeUpload"
      @change="handleChange"
    >
      <div :style="{ display: 'flex' }" v-if="fileList.length < 1">
        <a-button class="ant-upload-text">选择文件</a-button>
      </div>
    </a-upload>
  </div>
</template>
<script>
export default {
  data() {
    return {
      previewVisible: false,
      previewImage: '',
      fileList: [], //控制缩略图展示
      fileZiped: null, // 压缩后的图片数据
    };
  },
  methods: {
    // 监听fileList是否有值，会影响缩略图展示
    handleChange({ fileList }) {
      // 手动上传，此处需要将文件列表状态置为已上传，否则鼠标浮上会展示404信息
      fileList.forEach((item) => {
        item.status = 'done';
      });
      if (this.fileZiped) {
        this.fileList = fileList;
      } else {
        this.fileList = [];
      }
    },
    // 将需要上传的文件放入列表，传给上传函数使用
    beforeUpload(file) {
      // 素材管理部分上传不进行压缩
      this.fileZiped = file;
      this.$emit('change', file);
      return false;
    },
    // 图片移除
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      const newFileList = this.fileList.slice();
      newFileList.splice(index, 1);
      this.fileList = newFileList;
      // 删除图片后，更新外部图片文件数据
      this.fileZiped = null;
      this.$emit('change', null);
    },
    // 预览弹窗关闭
    previewClose() {
      this.previewVisible = false;
    },
    emptyFile() {
      this.fileList = [];
    },
  },
};
</script>
<style lang="less" scoped>
.uploadImg {
  width: 400px;
  height: 50px;
}
// 图片上传组件，只获取上传文件内容，拦截上传逻辑，原上传拦截的标红状态需要去除
/deep/ .ant-upload-list-picture .ant-upload-list-item-error,
/deep/ .ant-upload-list-picture-card .ant-upload-list-item-error {
  border-color: #d9d9d9;
}
</style>
