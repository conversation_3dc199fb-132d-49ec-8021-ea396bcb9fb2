import moment from 'moment';

export function formatDate(value, formatSrt = 'YYYY-MM-DD HH:mm:ss') {
  if (!value) return '--';
  return moment(value).format(formatSrt);
}

export function formatNumber(value, defaultValue = '--') {
  return typeof value === 'undefined' || value === null ? defaultValue : value;
}

/**
 * 按万、亿单位格式化数字
 * @param {Number} num
 * @returns
 */
export function formatNum2WY(num) {
  if (isNaN(num))
    return {
      num: '--',
      unit: '',
    };
  const w = 10000;
  // const sw = 10 * w;
  const bw = 100 * w;
  const qw = 1000 * w;
  const y = 10000 * w;
  const getNumToFixed = (value, num) => {
    return (Math.floor(value * 100) / 100).toFixed(num);
  };
  if (num < w) {
    // 小于万
    return {
      num,
      unit: '',
    };
  } else if (num < bw) {
    // 百万，保留两位小数
    return {
      num: getNumToFixed(num / w, 2),
      unit: '万',
    };
  } else if (num < qw) {
    // 千万，保留一位小数
    return {
      num: getNumToFixed(num / w, 1),
      unit: '万',
    };
  } else {
    return {
      num: getNumToFixed(num / y, 2),
      unit: '亿',
    };
  }
}

/**
 * 数字格式化千分位
 * @param {Number} num
 * @returns
 */
export function formatNum2Thousandth(num) {
  if (isNaN(num)) return num || '--';
  // 一位或三位数字，后边是 有三位数组一组的(1个或多个), 然后匹配结束或者. ,?=,?:参与格式匹配，不参与结果匹配,$1为 (\d{1,3})的匹配结果
  return String(num).replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, '$1,');
}

/**
 * 把对象按照 js配置文件的格式进行格式化
 * @param obj 格式化的对象
 * @param dep 层级，此项无需传值
 * @returns {string}
 */
export function formatConfig(obj, dep) {
  dep = dep || 1;
  const LN = '\n',
    TAB = '  ';
  let indent = '';
  for (let i = 0; i < dep; i++) {
    indent += TAB;
  }
  let isArray = false,
    arrayLastIsObj = false;
  let str = '',
    prefix = '{',
    subfix = '}';
  if (Array.isArray(obj)) {
    isArray = true;
    prefix = '[';
    subfix = ']';
    str = obj
      .map((item, index) => {
        let format = '';
        if (typeof item == 'function') {
          //
        } else if (typeof item == 'object') {
          arrayLastIsObj = true;
          format = `${LN}${indent}${formatConfig(item, dep + 1)},`;
        } else if (
          (typeof item == 'number' && !isNaN(item)) ||
          typeof item == 'boolean'
        ) {
          format = `${item},`;
        } else if (typeof item == 'string') {
          format = `'${item}',`;
        }
        if (index == obj.length - 1) {
          format = format.substring(0, format.length - 1);
        } else {
          arrayLastIsObj = false;
        }
        return format;
      })
      .join('');
  } else if (typeof obj != 'function' && typeof obj == 'object') {
    str = Object.keys(obj)
      .map((key, index, keys) => {
        const val = obj[key];
        let format = '';
        if (typeof val == 'function') {
          //
        } else if (typeof val == 'object') {
          format = `${LN}${indent}${key}: ${formatConfig(val, dep + 1)},`;
        } else if (
          (typeof val == 'number' && !isNaN(val)) ||
          typeof val == 'boolean'
        ) {
          format = `${LN}${indent}${key}: ${val},`;
        } else if (typeof val == 'string') {
          format = `${LN}${indent}${key}: '${val}',`;
        }
        if (index == keys.length - 1) {
          format = format.substring(0, format.length - 1);
        }
        return format;
      })
      .join('');
  }
  const len = TAB.length;
  if (indent.length >= len) {
    indent = indent.substring(0, indent.length - len);
  }
  if (!isArray || arrayLastIsObj) {
    subfix = LN + indent + subfix;
  }
  return `${prefix}${str}${subfix}`;
}

// 格式化数据 分转换为元 保留两位小数 不够补零
// 注意：分为单位金额 不存在小数情况 所以未对小数情况做处理
export function fen2yuan(val) {
  if (val === '') {
    val = 0;
  }
  // 转换为元
  const value = Math.round(parseFloat(val)) / 100;
  // 补零
  const xsd = value.toString().split('.');
  if (xsd.length === 1) {
    return value.toString() + '.00';
  }
  if (xsd.length > 1) {
    if (xsd[1].length < 2) {
      return value.toString() + '0';
    }
    return value + '';
  }
  return value + '';
}
