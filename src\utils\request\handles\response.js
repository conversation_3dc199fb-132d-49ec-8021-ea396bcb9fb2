import { notification } from 'ant-design-vue';
import { throttledConfirmModal } from './reLogin';

// 请求响应处理
export const responseHandle = (response, extend = { errorCustom: false }) => {
  const { errorCustom } = extend;
  const { data = {}, config = {}, status, msg } = response;
  if (status !== 200) {
    commonErrorHandle(errorCustom, msg);
    return [
      undefined,
      {
        code: status,
        message: msg,
      },
    ];
  }

  const { code, subCode } = data;
  if (
    subCode === 'auth-check-error' &&
    config.url !== '/api/authority/admin/logout'
  ) {
    // 登录过期或者不合法
    throttledConfirmModal();
    // TODO:
    return [undefined, { code: subCode, message: '登录过期或者不合法' }];
  } else if (
    data &&
    typeof data === 'object' &&
    data.toString() === '[object Blob]'
  ) {
    // 非业务接口，并有数据返回的
    return [data, undefined];
  } else if (code !== '10000') {
    commonErrorHandle(errorCustom, data.subMsg || data.msg);
    return [
      undefined,
      {
        code: code,
        message: data.subMsg || data.msg,
        subCode: data.subCode,
      },
    ];
  }
  return [data, undefined];
};

// 通用异常处理
function commonErrorHandle(errorCustom, message) {
  if (!errorCustom) {
    notification.error({
      message,
    });
  }
}
