<template>
  <page-layout>
    <PageWrapper
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      :tableOn="{
        'checkbox-change': selectChangeEvent,
        'checkbox-all': selectChangeEvent,
      }"
    >
      <!-- 创建按钮区域插槽 -->
      <template #defaultHeader>
        <a-button
          type="primary"
          style="margin-right: 8px"
          @click="handleCreate"
        >
          新增
        </a-button>
        <a-button style="margin-right: 8px" @click="onClickImport">
          导入
        </a-button>
        <a-button
          type="button"
          style="margin-right: 8px"
          :disabled="!checkItems.length"
          @click="onClickDetail"
        >
          导出
        </a-button>
        <a-button
          type="danger"
          style="margin-right: 8px"
          :disabled="!checkItems.length"
          @click="handelDelete"
        >
          删除
        </a-button>
      </template>
      <!-- 月份插槽 -->
      <template #year>
        <BuseRangePicker
          type="year"
          :needShowSecondPicker="() => false"
          v-model="filterOptions.params.date"
          format="YYYY"
          placeholder="请选择年份"
          :disableDateFunc="disableDateFunc"
        />
      </template>
      <!-- 企业 -->
      <template #enterSlot>
        <a-select
          :allowClear="true"
          :filterOption="filterOption"
          :showSearch="true"
          placeholder="请选择"
          @change="onEnterChange"
        >
          <a-select-option
            v-for="item in stateSelect"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </template>
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
        <span class="operate-button" @click="onClickDetail(row)">详情</span>
      </template>
      <!-- 编辑弹窗 -->
      <ListModal
        :visible="visible"
        :detail="modalData"
        :modelTitle="modelTitle"
        :preview="preview"
        @handleCancel="handleCancel"
      />
    </PageWrapper>
  </page-layout>
</template>

<script>
import moment from 'moment';
import { defaultTableColumn, defaultFilterConfig } from './constant';
import { queryList } from './mock';
import ListModal from './components/ListModal.vue';
export default {
  components: { ListModal },
  dicts: ['my_notify_rule'],
  data() {
    return {
      loading: false,
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
        params: {
          date: undefined,
          name: '',
          state: '',
          category: '',
          level: '',
          buildType: '',
        }, // 筛选器结果数据
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: defaultTableColumn(),
      tableData: [],
      visible: false,
      modalData: null,
      checkItems: [],
      modelTitle: 'add',
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
      // 载体类别
      carrierCategory: [
        {
          value: '',
          label: '全部',
        },
        {
          value: 1,
          label: '众创空间',
        },
        {
          value: 2,
          label: '科技企业孵化器',
        },
        {
          value: 3,
          label: '科技企业加速器',
        },
        {
          value: 4,
          label: '大学科技园',
        },
        {
          value: 5,
          label: '科创飞地',
        },
      ],
      // 载体级别
      carrierLevel: [
        {
          value: '',
          label: '全部',
        },
        {
          value: 1,
          label: '市级',
        },
        {
          value: 2,
          label: '省级',
        },
        {
          value: 3,
          label: '国家级',
        },
      ],
      detail: {},
      preview: false,
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      this.filterOptions.config[2].props.options = this.carrierCategory;
      this.filterOptions.config[3].props.options = this.carrierLevel;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 下拉联动
    onEnterChange() {
      console.log(222);
    },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [res, err] = await queryList({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...params,
      });
      this.loading = false;

      if (err) return;
      // 设置数据
      this.tablePage.total = res.total;
      this.tableData = res.data;
    },
    // 创建按钮点击事件
    handleCreate() {
      this.modelTitle = 'add';
      this.visible = true;
    },
    // 编辑按钮点击事件
    onClickEdit(row) {
      this.modelTitle = 'edit';
      this.visible = true;
      this.modalData = row;
    },
    // 关闭弹窗
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
      this.modalData = null;
      this.manageVisible = false;
      this.preview = false;
    },
    // 管理按钮点击事件
    onClickManage() {
      this.manageVisible = true;
    },
    // 查看详情
    onClickDetail(row) {
      this.modelTitle = 'see';
      this.modalData = row;
      this.preview = true;
      this.visible = true;
    },
    // 删除
    handelDelete() {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          // const [, err] = await workflowStart({
          //   itemId: 'HD66270922712159',
          //   itemName: '小练的测试活动',
          //   processKey: 'ACTIVITY_ONLINE',
          //   extParams: { remark: '测试' },
          // });
          // if (!err) {
          //   that.$message.success('发布成功');
          //   return;
          // }
          // 刷新数据
          that.loadData();
        },
      });
    },
    // 单选
    selectChangeEvent({ records }) {
      console.log('单选事件', records);
      this.checkItems = records;
    },
    // 园区类别
    stateChange(value) {
      console.log('选中值', value);
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: this.pageName,
        },
      });
    },
    // 年份选择
    yearChange(date, dateString) {
      console.log('月份选择回调', date, dateString);
    },
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
  },
};
</script>
<style scoped>
/deep/.page-wrapper-container {
  margin: 0 !important;
}
</style>
