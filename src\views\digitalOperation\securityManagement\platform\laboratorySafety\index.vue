<template>
  <BuseCrud
    ref="crud"
    title="检查列表"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalConfirm="modalConfirmHandler"
    @rowDel="deleteRowHandler"
    @handleCreate="rowAdd"
    @loadData="loadData"
  >
    <template slot="uploadFile" slot-scope="{ params }">
      <uploadFiles
        v-model="params.attachments"
        accept=".pdf, .ppt, .pptx, .doc, .docx, .png, .jpg,"
        @setAnnexList="(fileLists) => setAnnexList(fileLists, params)"
        :showDefaultUploadList="true"
        :maxSize="100000"
      ></uploadFiles>
    </template>
  </BuseCrud>
</template>

<script>
import uploadFiles from '@/components/Uploads/uploadFilesNew.vue';
import {
  laboratorySafetyList,
  laboratorySafetySave,
  laboratorySafetyDelete,
} from '@/api/digitalOperation/securityManagement/laboratorySafety';
import { getCheckObject } from '@/views/digitalOperation/taskManagement/utils/index.js';
export default {
  name: 'LaboratorySafety',
  components: {
    uploadFiles,
  },
  data() {
    return {
      files: [],
      menuShow: true,
      tableData: [],
      tableColumn: [],
      params: { checkTime: undefined, isRectify: '0' },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      checkObjectList: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'checkTime',
            title: '检查时间',
            element: 'a-range-picker',
          },
          {
            title: '是否有整改项',
            field: 'isRectify',
            element: 'a-select',
            props: {
              options: [
                {
                  label: '全部',
                  value: '0',
                },
                {
                  label: '是',
                  value: '1',
                },
                {
                  label: '否',
                  value: '2',
                },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: true,
        editBtn: false,
        menu: true,
        viewBtnText: '查看详情',
        addTitle: '实验室安全检查情况录入',
        formConfig: [
          {
            title: '检查对象',
            field: 'checkObject',
            element: 'a-select',
            props: {
              options: this.checkObjectList,
            },
            rules: [
              {
                required: true,
                message: '请选择检查对象',
              },
            ],
          },
          {
            title: '检查时间',
            field: 'checkTime',
            element: 'a-date-picker',
            rules: [
              {
                required: true,
                message: '请选择检查时间',
              },
            ],
          },
          {
            title: '是否有整改项',
            field: 'isRectify',
            element: 'a-select',
            props: {
              options: [
                {
                  label: '是',
                  value: '1',
                },
                {
                  label: '否',
                  value: '2',
                },
              ],
            },
            previewFormatter: (value) => {
              return value ? '是' : '否';
            },
            rules: [
              {
                required: true,
                message: '请选择是否有整改项',
              },
            ],
          },
          {
            title: '上传附件',
            field: 'attachments',
            element: 'slot',
            slotName: 'uploadFile',
            previewFormatter: (value) => {
              return (
                value?.map((item) => {
                  return (
                    <a
                      href={this.baseImgUrl + item.fileUrl}
                      target="_blank"
                      class="mr10"
                    >
                      {item.fileName}
                    </a>
                  );
                }) || '无附件'
              );
            },
          },
        ],
      };
    },
  },
  created() {
    this.tableColumn = [
      {
        title: '检查时间',
        field: 'checkTime',
      },
      {
        title: '检查对象',
        field: 'checkObject',
      },
      {
        title: '是否有整改项',
        field: 'isRectify',
        formatter({ cellValue }) {
          return cellValue ? '是' : '否';
        },
      },
    ];
  },
  mounted() {
    this.loadData();
    getCheckObject().then((res) => {
      this.checkObjectList = res;
    });
  },
  methods: {
    async loadData() {
      this.loading = true;
      console.log(this.params);
      const params = {
        startTime: this.params.checkTime?.[0]?.format('YYYY-MM-DD'),
        endTime: this.params.checkTime?.[1]?.format('YYYY-MM-DD'),
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
        isRectify: this.getIsRectify(),
      };
      const [res, err] = await laboratorySafetyList(params);
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    async modalConfirmHandler(formValues) {
      const { checkObject, attachments, checkTime, isRectify } = formValues;
      const params = {
        checkObject,
        attachments,
        checkTime: checkTime.format('YYYY-MM-DD'),
        isRectify: isRectify === '1' ? true : false,
      };
      const [, err] = await laboratorySafetySave(params);
      if (err) return;
      this.$message.success('新增成功');
      this.loadData();
    },
    deleteRowHandler(row) {
      this.$confirm({
        title: '确认删除',
        content: '是否删除该行数据',
        onOk: async () => {
          const [, err] = await laboratorySafetyDelete({ id: row.id });
          if (err) return;
          this.$message.success('删除成功');
          this.loadData();
        },
      });
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, 'ADD');
    },
    getIsRectify() {
      if (
        this.params.isRectify === '0' ||
        this.params.isRectify === undefined
      ) {
        return undefined;
      } else {
        return this.params.isRectify === '1' ? true : false;
      }
    },
    setAnnexList(fileList, params) {
      params.attachments = fileList.map((item) => {
        return {
          fileName: item.name,
          fileUrl: item.response.fileName,
          fileSize: item.size,
          fileType: item.type,
        };
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
