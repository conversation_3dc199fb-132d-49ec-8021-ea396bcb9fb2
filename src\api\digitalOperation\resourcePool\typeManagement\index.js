import { request } from '@/utils/request/requestTkb';
/**
 * 资源类型列表
 */
export function pageList(data) {
  return request({
    url: '/gov/resoureceType/pageList',
    method: 'post',
    data,
  });
}

/**
 * 资源类型新增
 */
export function saveResourceType(data) {
  return request({
    url: '/gov/resoureceType/saveResourceType',
    method: 'post',
    params: data,
  });
}

/**
 * 资源类型编辑
 */
export function editResourceType(data) {
  return request({
    url: '/gov/resoureceType/editResourceType',
    method: 'post',
    data,
  });
}

/**
 * 资源类型删除
 */
export function deleteResourceType(data) {
  return request({
    url: '/gov/resoureceType/deleteResourceType',
    method: 'post',
    params: data,
  });
}

/**
 * 资源类型删除
 */
export function deleteBatchResourceType(data) {
  return request({
    url: '/gov/resoureceType/deleteBatchResourceType',
    method: 'post',
    data,
  });
}
