<template>
  <PortraitCard
    ref="PortraitCard"
    style="width: 724px"
    :span="15"
    title="能耗信息"
    detailUrl="/basicData/enterpriseManagement/dataSource/powerConsumption"
    v-bind="$attrs"
  >
    <a-row :gutter="12" class="energy-info-wrap">
      <a-col :span="8">
        <div class="detail-picture detail-picture-3">
          <div class="title">上月耗电量</div>
          <div class="info">
            <span class="num">{{ pictureInfo.lastMonthUsage || '-' }}</span
            ><span class="unit">KWh</span>
          </div>
        </div>
      </a-col>
      <a-col :span="16" class="detail">
        <ChartsCard chartTitle="近6个月用电量" :options="charts1Options" />
      </a-col>
    </a-row>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
import ChartsCard from '../ChartsCard.vue';
import { useLineCharts } from '../chartHooks';

export default {
  components: {
    PortraitCard,
    ChartsCard,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    charts1: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
  },
  data() {
    return {
      charts1Options: {},
    };
  },
  watch: {
    charts1: {
      deep: true,
      handler() {
        this.handleInitCharts1();
      },
    },
  },
  methods: {
    handleInitCharts1() {
      const unit = 'KW/h';
      const { xAxis = [], data = [] } = this.charts1;

      this.charts1Options = useLineCharts({
        xAxis,
        unit,
        series: [{ name: '近6个月用电量', data: data || [], color: '#ffb406' }],
      });
      // this.charts1Options = {
      //   grid: {
      //     left: 45,
      //     right: 10,
      //     top: 40,
      //     bottom: 20,
      //   },
      //   xAxis: {
      //     type: 'category',
      //     data: xAxis || [],
      //     axisLabel: {
      //       color: '#999999',
      //     },
      //     axisTick: {
      //       show: false,
      //     },
      //     splitLine: {
      //       show: false,
      //     },
      //     axisLine: {
      //       show: true,
      //       lineStyle: {
      //         color: 'rgba(0, 0, 0, 0.06)',
      //       },
      //     },
      //   },
      //   yAxis: {
      //     name: '单位：' + unit,
      //     type: 'value',
      //     splitLine: {
      //       show: true,
      //       lineStyle: {
      //         color: 'rgba(0, 0, 0, 0.06)',
      //         // 虚线
      //         type: 'dashed',
      //       },
      //     },
      //     nameTextStyle: {
      //       color: '#999999',
      //     },
      //     axisLabel: {
      //       color: '#999999',
      //     },
      //   },
      //   series: [
      //     {
      //       name: '用电量',
      //       type: 'bar',
      //       data,
      //       symbol: 'circle',
      //       symbolSize: 6,
      //       showSymbol: false,
      //       barWidth: '10px',
      //       itemStyle: {
      //         normal: {
      //           color: '#ffb406',
      //         },
      //       },
      //     },
      //   ],
      // };
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;
  .title {
    width: 70px;
    text-align: right;
    color: #999999;
  }
  .info {
    width: calc(100% - 70px);
    .fold-button {
      color: #86bded;
      float: right;
    }
    .num {
      font-family: D-DIN;
      font-size: 20px;
      font-weight: bold;
      line-height: 20px;
      letter-spacing: 0px;
      color: #333333;
      z-index: 0;
    }
    .unit {
      margin-left: 4px;
    }
  }
  &-inline {
    display: inline-block;
  }
  &-picture {
    &-3 {
      background-image: url('@/assets/images/portraits/1-003.png');
    }
  }
}
/deep/ .portrait-card-title {
  padding-right: 80px !important;
}
.energy-info-wrap {
  padding: 0 10px 30px 10px;
}
</style>
