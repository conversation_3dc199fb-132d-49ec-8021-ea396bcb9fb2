import moment from 'moment';
import * as api from '@/api/basicData/index';

var patentTypeList = [];
var pctTypeList = [];
getCodeByType('patent_type').then((res) => {
  patentTypeList = res;
});
getCodeByType('pct_type').then((res) => {
  pctTypeList = res;
});
// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    fixed: 'left',
    width: 60,
  },
  {
    type: 'seq',
    title: '序号',
    fixed: 'left',
    width: 60,
  },
  {
    field: 'mon',
    title: '日期',
    width: 110,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM') : '';
    },
  },
  {
    field: 'ipoAddress',
    title: '知识产权人地址',
    minWidth: 150,
  },
  {
    field: 'enterpriseName',
    title: '企业名称',
    width: 100,
  },
  {
    field: 'patentType',
    title: '专利类型',
    width: 130,
    formatter: ({ cellValue }) => {
      return getStatus(cellValue, patentTypeList);
    },
  },
  {
    field: 'affiliatingArea',
    title: '所属地区',
    width: 130,
  },
  {
    field: 'townshipStreet',
    title: '乡镇街道',
    width: 110,
  },
  {
    field: 'ipname',
    title: '知识产权名称',
    width: 110,
  },
  {
    field: 'iponame',
    title: '知识产权人名称',
    width: 140,
  },
  {
    field: 'applicationNum',
    title: '申请号',
    width: 110,
  },
  {
    field: 'iptype',
    title: '知识产权类型',
    width: 110,
  },
  {
    field: 'ipotype',
    title: '知识产权人类型',
    width: 140,
  },
  {
    field: 'bcTownshipStreet',
    title: '补充乡镇街道',
    width: 140,
  },
  {
    field: 'type',
    title: '类型',
    width: 140,
    formatter: ({ cellValue }) => {
      return getStatus(cellValue, pctTypeList);
    },
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 160,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'rateTime',
    title: '日期',
    element: 'slot',
    slotName: 'year',
    rules: [{ required: true, message: '请输入日期' }],
  },
  {
    field: 'name',
    title: '企业名称',
    element: 'a-input',
  },
  {
    field: 'applicationNum',
    title: '申请号',
    element: 'a-input',
  },
];

export const initFormValue = () => {
  return {
    mon: undefined,
    ipoAddress: '',
    enterpriseId: '',
    enterprise: {},
    enterpriseName: '',
    patentType: '',
    affiliatingArea: '',
    townshipStreet: '',
    bcTownshipStreet: '',
    ipname: '',
    iponame: '',
    applicationNum: '',
    iptype: '',
    ipotype: '',
    type: '',
  };
};

function getStatus(cellValue, list) {
  return list
    .filter((obj) => obj.value === cellValue)
    .map((obj) => obj.label)
    .join(',');
}

// 获取枚举列表
async function getCodeByType(codeType) {
  const [res, err] = await api.codeByType({
    codeType: codeType,
  });
  if (err) return;
  const list = res.data.map((item) => {
    return {
      codeType: item.codeType,
      codeTypeName: item.codeTypeName,
      label: item.name,
      value: item.value,
    };
  });
  return list;
}
