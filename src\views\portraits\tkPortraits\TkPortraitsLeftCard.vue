<template>
  <div>
    <template v-if="pageName === 'businessPortraits'">
      <div class="page-title signal-line">
        {{ pictureInfo.name }}
      </div>
      <div class="sub-title signal-line">
        法定代表人：{{ pictureInfo.legalPerson }}
      </div>
      <div class="sub-title signal-line">
        统一社会信用代码：{{ pictureInfo.unifiedCreditCode }}
      </div>
    </template>
    <template v-else-if="pageName === 'parkPortraits'">
      <div class="page-title signal-line">
        {{ pictureInfo.parkName }}
      </div>
      <div class="sub-title signal-line">
        电话：{{ pictureInfo.phone || '-' }}
      </div>
      <!-- <div class="sub-title signal-line">
        载体面积：{{ pictureInfo.floorSpace || '-' }} m²
      </div> -->
    </template>
    <template v-else>
      <div class="page-title signal-line">{{ pictureInfo.title || '-' }}</div>
      <div class="sub-title signal-line">
        行政区划：{{ pictureInfo.regionalismCode || '-' }}
      </div>
      <div class="sub-title signal-line">
        载体面积：{{ pictureInfo.carrierArea || '-' }} 万平方米
      </div>
    </template>
    <div class="page-description">
      <h3>{{ pageName === 'taikePortraits' ? '主导产业' : '标签' }}</h3>
      <div>
        <span
          v-for="(item, index) in pictureInfo.label"
          :class="['tag-item', 'tag-item-' + (index % 5)]"
          :key="index"
          :title="item"
          >{{ item }}</span
        >
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    pageName: {
      type: String,
    },
  },
};
</script>
<style lang="less" scoped>
.page-description {
  margin: 32px auto 12px;
  width: 326px;
  h3 {
    font-family: HarmonyOS Sans SC;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0px;
    padding: 0;
    padding-left: 36px;
    position: relative;
    color: #333333;
    &::before {
      content: '';
      display: inline-block;
      width: 28px;
      height: 16px;
      background-image: url('@/assets/images/portraits/1-4.png');
      background-size: 100% 100%;
      position: absolute;
      left: 0;
      bottom: 2px;
    }
  }
  p {
    text-indent: 2em;
    font-family: HarmonyOS Sans SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 150%;
    letter-spacing: 0px;

    /* 文字/主要 */
    color: rgba(51, 51, 51, 0.8);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 9; /* 设置最多显示10行 */
    line-clamp: 9; /* 标准语法，部分浏览器可能尚不支持 */
  }
  & > div {
    height: 260px;
    overflow: hidden auto;
  }
}

.tag-item {
  display: inline-block;
  margin: 0 0 8px 8px;
  border: none;
  padding: 8px 8px;
  // font-family: AlibabaPuHuiTi;
  font-size: 14px;
  font-weight: normal;
  line-height: 14px;
  border-radius: 4px;
  cursor: pointer;
  &-0 {
    color: #26d0ca;
    background-color: rgba(38, 208, 202, 0.08);
  }
  &-1 {
    color: #4a9cff;
    background-color: rgba(74, 156, 255, 0.08);
  }
  &-2 {
    color: #34ca57;
    background-color: rgba(90, 203, 116, 0.08);
  }
  &-3 {
    color: #ff6f3f;
    background-color: rgba(255, 111, 63, 0.08);
  }
  &-4 {
    color: #f3cd0d;
    background-color: rgba(181, 155, 30, 0.08);
  }
}

.page-title {
  margin: 244px auto 11px;
  width: 326px;
  height: 44px;
  line-height: 44px;
  font-family: HarmonyOS Sans SC;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0em;
  color: #333;
  padding: 0;
  padding-left: 56px;
  background: rgba(0, 0, 0, 0.02);
  background-image: url('@/assets/images/portraits/1-1.png');
  background-size: 78px 72px;
  background-position: -18px -12.5px;
  background-repeat: no-repeat;
}
.sub-title {
  margin: 0 auto 11px;
  width: 326px;
  height: 32px;
  line-height: 32px;
  background: rgba(0, 0, 0, 0.02);
  font-family: AlibabaPuHuiTi;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: 0px;

  padding-left: 12px;
}
</style>
