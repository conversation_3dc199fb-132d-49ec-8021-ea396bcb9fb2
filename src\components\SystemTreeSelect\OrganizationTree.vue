<template>
  <a-card
    title="组织列表"
    :bordered="false"
    style="height: 100%"
    :bodyStyle="{
      padding: '16px',
      overflow: 'auto',
    }"
  >
    <a
      v-if="organizationTree && organizationTree.length"
      slot="extra"
      href="javascript:;"
      @click="() => (showAll = !showAll)"
    >
      <a-icon v-if="showAll" type="shrink" />
      <a-icon v-else type="arrows-alt" />
      {{ showAll ? '收起全部' : '展开全部' }}
    </a>
    <a-input-search style="width: 100%" v-model="filterWord" size="small" />
    <a-spin :spinning="treeLoading" style="overflow: 'auto'">
      <a-tree
        v-if="showTree"
        ref="tree"
        show-icon
        :expandedKeys="expandedKeys"
        :selectedKeys="[organizeId]"
        :default-expand-all="showAll"
        :treeData="organizationTree"
        :replaceFields="replaceFields"
        :filterTreeNode="filterTreeNode"
        @select="handleTreeSelect"
        @expand="handleNodeExpand"
      >
      </a-tree>
      <div v-else class="tree-empty">
        <a-empty />
      </div>
    </a-spin>
  </a-card>
</template>

<script>
import { Empty } from 'ant-design-vue';
import { getTreeList } from '@/api/system/organization';
import { recursionDataSlide } from '@/utils';
import { handleTreeToArray } from '@/utils/common/tree';

export default {
  props: {
    organizeId: {
      type: String,
      default: '',
    },
    organizeName: {
      type: String,
      default: '',
    },
  },

  computed: {
    merchantId() {
      return this.$store.state?.base?.merchant?.merchantId;
    },
    //树结构的菜单--》平铺
    treeToArrayList() {
      return handleTreeToArray(this.organizationTree);
    },
  },
  watch: {
    // 根据名称筛选部门树
    filterWord(val) {
      if (val) {
        this.filterTreeNode(this.$refs.tree);
      }
    },
    // 展开所有
    showAll(val) {
      if (val) {
        this.expandedKeys = this.treeToArrayList.map((item) => item.id);
      } else {
        this.expandedKeys = [];
      }
    },
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      expandedKeys: [], //展开的key
      // 树相关参数
      treeLoading: false, //树展开
      filterWord: '', //搜索文案
      // 树相关数据
      showTree: false, //展示树
      showAll: false, //展开树
      organizationTree: [], //组织树
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE, //空图片
      replaceFields: {
        children: 'children',
        title: 'label',
        key: 'id',
      },
    };
  },
  mounted() {
    this.getTreeOrganization();
  },
  methods: {
    /**
     * 查询菜单下拉树结构
     */
    async getTreeOrganization() {
      this.treeLoading = true;
      const [result, error] = await getTreeList({
        merchantId: this.merchantId,
      });
      this.treeLoading = false;
      if (error) {
        // 获取树数据完成
        this.$emit('getTreeListError', error);
        return;
      }
      let treeData = result?.data || [];
      // 如果需要写默认值的话
      const data = recursionDataSlide(treeData);
      this.organizationTree = data;
      this.showTree = true;
      this.showAll = true;
      if (data.length) {
        this.$emit('update:organizeId', data[0].id);
        this.$emit('update:organizeName', data[0].label);
      }
      // 获取树数据完成
      this.$emit('getTreeDone', {
        array: this.treeToArrayList || [],
        tree: this.organizationTree || [],
      });
    },
    // 树组件 => 筛选部门
    handleTreeSelect(selectedKeys, info) {
      if (Array.isArray(selectedKeys) && selectedKeys.length) {
        const organizeName = info.node.dataRef.label;
        const selectedKey = selectedKeys[0];
        // console.log(selectedKey);
        // 更新选择数据
        this.$emit('update:organizeId', selectedKey);
        this.$emit('update:organizeName', organizeName);
        // 完成选择
        this.$emit('afterChooseMerchant');
      }
    },
    /**
     * 节点点击展开
     */
    async handleNodeExpand(data) {
      this.expandedKeys = data;
    },
    // 筛选节点
    filterTreeNode(node) {
      if (this.filterWord) {
        return node.title && node.title.indexOf(this.filterWord) !== -1;
      } else return false;
    },
    //刷新树数据
    refresh() {
      this.getTreeOrganization();
    },
  },
};
</script>
