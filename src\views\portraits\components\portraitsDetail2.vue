<template>
  <a-spin
    :spinning="loading"
    class="portrait"
    :style="
      '--parent-width:' + parentWidth + 'px;--parent-scale:' + parentScale
    "
  >
    <div class="portrait-cnt">
      <div class="portrait-cnt-header">
        <h2></h2>
        <div class="time-area">
          {{ nowDate }}<br />
          {{ nowTime }}
        </div>
      </div>
      <div class="portrait-cnt-back" @click="closeModal">返回</div>
      <div class="portrait-cnt-bottom">
        <div class="portrait-cnt-bottom-left">
          <template v-if="pageName === 'businessPortraits'">
            <div class="page-title signal-line">
              {{ pictureInfo.companyName }}
            </div>
            <div class="sub-title signal-line">
              法定代表人：{{ pictureInfo.legalPerson }}
            </div>
            <div class="sub-title signal-line">
              统一社会信用代码：{{ pictureInfo.companyCode }}
            </div>
          </template>
          <template v-else>
            <div class="page-title signal-line">{{ pictureInfo.parkName }}</div>
            <div class="sub-title signal-line">
              电话：{{ pictureInfo.phone }}
            </div>
          </template>
          <div class="page-description">
            <h3>标签</h3>
            <div>
              <a-tag
                v-for="(item, index) in pictureInfo.label"
                :class="['tag-item', 'tag-item-' + (index % 5)]"
                :key="item.index"
                >{{ item.labelName }}</a-tag
              >
            </div>
          </div>
        </div>
        <div class="portrait-cnt-bottom-right">
          <a-row :gutter="24">
            <div v-if="pageName !== 'businessPortraits'" class="park">
              <a-col :span="24">
                <a-card :title="'基本信息'" class="portrait-card">
                  <div class="portrait-card-cnt">
                    <a-row style="margin-bottom: 10px">
                      <a-col :span="6" class="detail">
                        <div class="title">园区名称：</div>
                        <div class="info">
                          {{ pictureInfo.parkName }}
                        </div>
                      </a-col>
                      <a-col :span="6" class="detail">
                        <div class="title">电话：</div>
                        <div class="info">
                          {{ pictureInfo.phone }}
                        </div>
                      </a-col>
                      <a-col :span="12" class="detail">
                        <div class="title">地址：</div>
                        <div class="info">
                          {{ pictureInfo.address }}
                        </div>
                      </a-col>
                    </a-row>
                    <a-row>
                      <a-col :span="24" class="detail">
                        <div class="title">简介：</div>
                        <div class="info">
                          <p :class="introductionClass">
                            {{ pictureInfo.introduce }}
                          </p>
                          <span
                            class="fold-button"
                            @click="open"
                            v-if="!openFlag && pictureInfo.introduce"
                            >展开</span
                          >
                          <span
                            class="fold-button"
                            @click="close"
                            v-if="openFlag && pictureInfo.introduce"
                            >收起</span
                          >
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="24">
                <a-card :title="'入驻企业'" class="portrait-card">
                  <div class="portrait-card-cnt">
                    <div class="park-name">
                      <!-- <a-popover
                        v-for="(item, index) in pictureInfo.enterCompany"
                        :key="index"
                      >
                        <template #content>
                          {{ item.enterpriseName }}
                        </template> -->
                      <a-button
                        type="link"
                        v-for="(item, index) in pictureInfo.enterCompany"
                        :key="index"
                        :title="item.enterpriseName"
                        @click="handleCompanyClick(item)"
                        >{{ item.enterpriseName }}</a-button
                      >
                      <!-- </a-popover> -->
                    </div>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card
                  :title="'经营信息'"
                  class="portrait-card portrait-card-half"
                >
                  <div class="portrait-card-cnt">
                    <a-row>
                      <a-col :span="12" class="detail">
                        <div
                          class="title"
                          style="width: unset; text-align: unset"
                        >
                          <p>亩均收入：</p>
                        </div>
                        <div class="info">
                          <p>
                            <span class="num">{{
                              pictureInfo.averageIncome
                            }}</span
                            ><span class="unit">万元/亩</span>
                          </p>
                        </div>
                      </a-col>
                      <a-col :span="12" class="detail">
                        <div
                          class="title"
                          style="width: unset; text-align: unset"
                        >
                          <p>税收：</p>
                        </div>
                        <div class="info">
                          <p>
                            <span class="num">{{ pictureInfo.taxation }}</span
                            ><span class="unit">万元</span>
                          </p>
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card
                  :title="'建筑信息'"
                  class="portrait-card portrait-card-half"
                >
                  <div class="portrait-card-cnt">
                    <a-row>
                      <!-- <a-col :span="12" class="detail">
                  <div class="title" style="width: unset; text-align: unset">
                    <p>载体面积：</p>
                  </div>
                  <div class="info">
                    <p>
                      <span class="num">{{ pictureInfo.carrierArea }}</span
                      ><span class="unit">万平方米</span>
                    </p>
                  </div>
                </a-col> -->
                      <a-col :span="12" class="detail">
                        <div
                          class="title"
                          style="width: unset; text-align: unset"
                        >
                          <p>占地面积：</p>
                        </div>
                        <div class="info">
                          <p>
                            <span class="num">{{ pictureInfo.coverArea }}</span
                            ><span class="unit">万平方米</span>
                          </p>
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>
            </div>
            <div v-else class="company">
              <a-col :span="24">
                <a-card :title="'基本信息'" class="portrait-card">
                  <div class="portrait-card-cnt">
                    <a-row>
                      <a-col :span="6" class="detail">
                        <div class="title">公司名称：</div>
                        <div class="info">
                          {{ pictureInfo.companyName || '-' }}
                        </div>
                      </a-col>
                      <a-col :span="6" class="detail">
                        <div class="title">法定代表人：</div>
                        <div class="info">
                          {{ pictureInfo.legalPerson || '-' }}
                        </div>
                      </a-col>
                      <a-col :span="6" class="detail">
                        <div class="title">统一社会信用代码：</div>
                        <div class="info">
                          {{ pictureInfo.companyCode || '-' }}
                        </div>
                      </a-col>
                      <a-col :span="6" class="detail">
                        <div class="title">电话：</div>
                        <div class="info">
                          {{ pictureInfo.phone || '-' }}
                        </div>
                      </a-col>
                      <a-col :span="24" class="detail">
                        <div class="title">
                          <p>地址：</p>
                        </div>
                        <div class="info">
                          <p>{{ pictureInfo.address }}</p>
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>

              <a-col :span="24">
                <a-card :title="'经营数据'" class="portrait-card">
                  <div class="portrait-card-cnt">
                    <a-row>
                      <a-col :span="6" class="detail">
                        <div class="title">本期应税收入(累计)：</div>
                        <div class="info">
                          <span class="num">{{
                            pictureInfo.currentIncome || '0'
                          }}</span
                          ><span class="unit">万元</span>
                        </div>
                      </a-col>
                      <a-col :span="6" class="detail">
                        <div class="title">同期应税收入(累计)：</div>
                        <div class="info">
                          <span class="num">{{
                            pictureInfo.synchronismIncome || '0'
                          }}</span
                          ><span class="unit">万元</span>
                        </div>
                      </a-col>
                      <a-col :span="6" class="detail">
                        <div class="title">净入库金额(累计)：</div>
                        <div class="info">
                          <span class="num">{{
                            pictureInfo.receiptAmount || '0'
                          }}</span
                          ><span class="unit">万元</span>
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="24">
                <a-card :title="'知识产权'" class="portrait-card">
                  <div class="portrait-card-cnt">
                    <a-row>
                      <a-col :span="6" class="detail">
                        <div class="title">专利授权：</div>
                        <div class="info">
                          <span class="num">{{
                            pictureInfo.patent || '0'
                          }}</span
                          ><span class="unit">个</span>
                        </div>
                      </a-col>
                      <a-col :span="6" class="detail">
                        <div class="title">实用新型：</div>
                        <div class="info">
                          <span class="num">{{
                            pictureInfo.utility || '0'
                          }}</span
                          ><span class="unit">个</span>
                        </div>
                      </a-col>
                      <a-col :span="6" class="detail">
                        <div class="title">商标：</div>
                        <div class="info">
                          <span class="num">{{
                            pictureInfo.trademark || '0'
                          }}</span
                          ><span class="unit">个</span>
                        </div>
                      </a-col>
                      <a-col :span="6" class="detail">
                        <div class="title">软著：</div>
                        <div class="info">
                          <span class="num">{{
                            pictureInfo.softwarePatent || '0'
                          }}</span
                          ><span class="unit">个</span>
                        </div>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="24">
                <a-card :title="'人才信息'" class="portrait-card">
                  <div class="portrait-card-cnt">
                    <a-row
                      v-if="pictureInfo.talents && pictureInfo.talents.length"
                    >
                      <a-col
                        :span="6"
                        class="detail"
                        v-for="(item, index) in pictureInfo.talents"
                        :key="index"
                      >
                        <div class="title">{{ item.itemName }}</div>
                        <div class="info">
                          <span class="num">{{ item.valueFactMon || '0' }}</span
                          ><span class="unit">个</span>
                        </div>
                      </a-col>
                    </a-row>
                    <a-row v-else>
                      <a-col :span="6" class="detail">
                        <div class="title" style="padding-right: 1em">暂无</div>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </a-col>
            </div>
          </a-row>
        </div>
      </div>
    </div>

    <a-modal
      :visible="company.visible"
      :title="null"
      :closable="false"
      width="100%"
      wrapClassName="full-modal"
      :footer="null"
      @cancel="company.visible = false"
    >
      <portraits
        v-if="company.visible"
        :pageName="'businessPortraits'"
        :pictureId="company.pictureId"
        @close="handleModalClose"
      ></portraits>
    </a-modal>
  </a-spin>
</template>

<script>
import moment from 'moment';
// import portraitsDetail from '@/views/portraits/components/portraitsDetail';
import * as api from '@/api/portraits';
import { throttle } from 'xe-utils';

export default {
  name: 'portraits',
  // components: { portraitsDetail },
  props: {
    pageName: {
      type: String,
      default: function () {
        return this.$route.query?.pageName || '';
      },
    },
    pictureId: {
      type: String,
      default: function () {
        return this.$route.query?.pictureId || '';
      },
    },
  },
  data() {
    return {
      pictureInfo: { companyTaxableIncome: [] },
      loading: true,
      parentWidth: 1920,
      parentScale: 1,
      nowDate: moment().format('YYYY-MM-DD'),
      nowTime: moment().format('dddd HH:mm:ss'),
      company: {
        visible: false,
        pictureId: '',
      },
      introductionClass: 'close-height',
      openFlag: false,
    };
  },
  computed: {},
  mounted() {
    this.getData();
    setTimeout(this.resize, 100);
    window.onresize = throttle(this.resize, 300);
  },
  methods: {
    handleModalClose() {
      this.company.visible = false;
    },
    closeModal() {
      this.$emit('close');
    },
    open() {
      this.openFlag = !this.openFlag;
      this.introductionClass = 'open-height';
    },
    // 收起
    close() {
      this.openFlag = !this.openFlag;
      this.introductionClass = 'close-height';
    },
    resize() {
      this.parentWidth =
        [...document.querySelectorAll('.portrait')].at(-1)?.parentNode
          ?.offsetWidth || 1920;

      this.parentScale = this.parentWidth / 1920;
      console.log(this.parentWidth);
    },
    moment,
    // 查询页面数据信息
    async getData() {
      const _this = this;
      let params = {
        parkId: this.pictureId,
      };
      let method = api.queryParkPicture;
      if (this.pageName === 'businessPortraits') {
        params = {
          companyId: this.pictureId,
        };
        method = api.queryCompanyPicture;
      }
      this.loading = true;
      const [res, err] = await method(params);
      this.loading = false;
      if (err) return;
      this.pictureInfo = res.data;

      // const colorList = ['#6A5ECC', '#F79D00', '#00B42A', '#1890FF', '#FF7B00'];
      // if (this.pictureInfo?.label) {
      //   this.pictureInfo.label.forEach((item, index) => {
      //     this.$set(item, 'color', colorList[index % 5]);
      //   });
      // }

      setInterval(function () {
        _this.nowDate = moment().format('YYYY-MM-DD');
        _this.nowTime = moment().format('dddd HH:mm:ss');
      }, 1000);
    },
    handleCompanyClick(item) {
      console.log(item);
      if (item?.enterpriseId) {
        this.company.pictureId = item?.enterpriseId;
        this.company.visible = true;
      } else {
        this.$message.warn('暂无对应企业画像');
      }
    },
  },
};
</script>

<style scoped lang="less">
.portrait {
  --parent-width: 1920px;
  --parent-scale: 1;
  width: 1920px;
  // aspect-ratio: 16/9;
  // min-height: 2080px;
  height: 1080px;
  background: url('@/assets/images/portraits/bg.png');
  background-repeat: no-repeat;
  background-size: 120%;
  background-position: 5% 5%;
  transform: translate(-50%, -50%) scale(var(--parent-scale))
    translate(50%, 50%);
  &-cnt {
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    & > div {
      margin-bottom: 30px;
    }
    &-header {
      width: 100%;
      height: 83px;
      background: url('@/assets/images/portraits/5.png');
      background-size: 100% 100%;
      position: relative;
      h2 {
        font-family: Alimama ShuHeiTi;
        font-size: 32px;
        font-weight: bold;
        line-height: 73px;
        text-align: center;
        letter-spacing: 0px;
        color: #ffffff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .time-area {
        position: absolute;
        right: 20px;
        top: 0;
        height: 43px;
        padding: 20px 0;
        width: 110px;

        font-family: AlibabaPuHuiTi;
        font-size: 13.47px;
        font-weight: 500;
        line-height: 18.64px;
        letter-spacing: 0em;

        /* 二级文字 */
        color: rgba(255, 255, 255, 0.8);
      }
    }
    &-back {
      width: 132px;
      height: 32px;
      line-height: 32px;
      background: url('@/assets/images/portraits/7.png');
      background-size: 100% 100%;
      color: #ffffff;
      font-family: Alimama ShuHeiTi;
      font-size: 16px;
      font-weight: bold;
      text-align: center;
      letter-spacing: 0em;
      cursor: pointer;
      margin: 0 40px 30px auto;
    }
    &-bottom {
      width: 1610px;
      height: 731px;
      margin: 0 auto;
      display: flex;
      &-left {
        width: 374px;
        height: 100%;
        margin-right: 60px;
        background-color: rgba(5, 35, 68, 0.3);
        background-image: url('@/assets/images/portraits/6.png');
        background-size: 100% 100%;
        font-family: HarmonyOS Sans SC;
        font-size: 14px;
        font-weight: normal;
        line-height: 150%;
        letter-spacing: 0px;
        color: rgba(205, 225, 255, 0.8);
      }
      &-right {
        width: 1186px;
        height: 100%;
      }
    }
  }
  .page-title {
    margin: 244px auto 11px;
    width: 326px;
    height: 44px;
    line-height: 44px;
    font-family: HarmonyOS Sans SC;
    font-weight: 500;
    letter-spacing: 0em;
    color: #ffffff;
    padding: 0;
    padding-left: 56px;
    background-image: url('@/assets/images/portraits/1.png');
    background-size: 100% 100%;
  }
  .sub-title {
    margin: 0 auto 11px;
    width: 326px;
    height: 32px;
    line-height: 32px;
    background: linear-gradient(
      90deg,
      rgba(81, 159, 255, 0.3) 0%,
      rgba(121, 186, 255, 0) 100%
    );

    padding-left: 12px;
  }
  .page-description {
    margin: 32px auto 12px;
    width: 326px;
    h3 {
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: 0px;
      padding: 0;
      padding-left: 36px;
      position: relative;
      /* 纯白 */
      color: #ffffff;
      &::before {
        content: '';
        display: inline-block;
        width: 28px;
        height: 16px;
        background-image: url('@/assets/images/portraits/4.png');
        background-size: 100% 100%;
        position: absolute;
        left: 0;
        bottom: 2px;
      }
    }
    p {
      text-indent: 2em;
      font-family: HarmonyOS Sans SC;
      font-size: 14px;
      font-weight: normal;
      line-height: 150%;
      letter-spacing: 0px;

      /* 文字/主要 */
      color: rgba(205, 225, 255, 0.8);
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 9; /* 设置最多显示10行 */
      line-clamp: 9; /* 标准语法，部分浏览器可能尚不支持 */
    }
    & > div {
      height: 290px;
      overflow: auto;
    }
  }

  .portrait-card {
    margin-top: 24px;
    ::v-deep .ant-card-head-wrapper {
      background: url('@/assets/images/portraits/3.png');
      background-size: 100% 100%;
      padding-left: 77px;
      font-family: Alimama ShuHeiTi;
      font-size: 18px;
      font-weight: bold;
      line-height: 18px;
      letter-spacing: 0px;
      height: 35px;
      // & .ant-card-head-title {
      //   padding: 12px 0 20px;
      // }
    }
    ::v-deep .ant-card-head {
      min-height: 35px;
    }
    &-half {
      ::v-deep .ant-card-head-wrapper {
        background: url('@/assets/images/portraits/8.png');
        background-size: 100% 100%;
      }
    }
    &-cnt {
      padding: 24px 0px 24px 24px;
    }
  }
  .ant-card,
  ::v-deep .ant-card-head {
    background: transparent;
    border: none;
    color: #ffffff;
    padding: 0;
  }
  ::v-deep .ant-card-body {
    padding: 0;
  }
  .signal-line {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  scrollbar-color: auto;
  // scrollbar-width: thin;
  ::-webkit-scrollbar {
    width: 6px; /* 滚动条宽度 */
    background: #223946;
    border-radius: 1px;
  }

  /* 滚动条背景 */
  // ::-webkit-scrollbar-track {
  //   background-color:
  // }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background-color: #86bded; /* 滚动条滑块颜色 */
    border-radius: 1px;
  }
}

.open-height,
.close-height {
  margin-bottom: 0.25em;
}
.close-height {
  height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.park-name {
  display: flex;
  flex-wrap: wrap;
  height: 300px;
  overflow: auto;

  button {
    display: inline-block;
    width: 25%;
    text-align: left;
    margin-bottom: 15px;
    ::v-deep span {
      display: inline-block;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
::v-deep .ant-spin-blur,
::v-deep .ant-spin-container {
  height: 100%;
}
::v-deep .ant-spin-spinning {
  min-height: 1080px;
}

.ant-btn-link {
  color: #ffffff;
}

.detail {
  display: flex;
  flex-direction: row;

  .title {
    width: 70px;
    text-align: right;
    color: rgba(205, 225, 255, 0.8);
  }

  .info {
    width: calc(100% - 70px);

    .fold-button {
      color: #86bded;
      float: right;
    }

    .num {
      font-family: HarmonyOS Sans SC;
      font-size: 20px;
      font-weight: bold;
      line-height: 20px;
      letter-spacing: 0px;

      /* 主要文字 */
      color: #ffffff;

      z-index: 0;
    }

    .unit {
      margin-left: 4px;
    }
  }
}
.company {
  .detail {
    .title {
      width: 154px;
    }

    .info {
      width: calc(100% - 154px);
    }
  }
}

.tag-item {
  margin-top: 5px;
  border: none;
  padding: 4px 8px;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0px;
  &-0 {
    color: #00fff6;
    background-color: rgba(0, 255, 246, 0.08);
  }
  &-1 {
    color: #4a9cff;
    background-color: rgba(74, 156, 255, 0.08);
  }
  &-2 {
    color: #6cff8e;
    background-color: rgba(108, 255, 142, 0.08);
  }
  &-3 {
    color: #ff6f3f;
    background-color: rgba(255, 111, 63, 0.08);
  }
  &-4 {
    color: #ffe45f;
    background-color: rgba(255, 228, 95, 0.08);
  }
}
::v-deep .ant-divider-inner-text {
  text-align: left;
  display: flex;
  flex-direction: row;
  align-items: center;

  .point {
    background: #ffffff;
    border-radius: 50%;
    height: 6px;
    width: 6px;
    display: block;
    margin-right: 10px;
  }
}
</style>
