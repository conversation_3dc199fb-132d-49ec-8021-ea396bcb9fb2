<template>
  <a-form
    class="form-container"
    :form="form"
    :label-col="{ span: 5 }"
    :wrapper-col="{ span: 12 }"
    @submit="handleSubmit"
  >
    <h2>{{ pageType === 'add' ? '填写整改情况' : '隐患情况' }}</h2>
    <a-form-item label="所属园区">
      <a-input disabled :value="detailInfo.parkName"></a-input>
    </a-form-item>
    <a-form-item label="单位名称">
      <a-input disabled :value="detailInfo.enterpriseName"></a-input>
    </a-form-item>
    <a-form-item label="安全隐患名称">
      <a-input disabled :value="detailInfo.dangerName"></a-input>
    </a-form-item>

    <a-form-item label="隐患类型" v-if="pageType === 'view'">
      <a-input disabled :value="detailInfo.dangerType"></a-input>
    </a-form-item>

    <a-form-item label="整改期限" v-if="pageType === 'view'">
      <a-input disabled :value="detailInfo.deadline"></a-input>
    </a-form-item>

    <a-form-item label="数据来源" v-if="pageType === 'view'">
      <a-input disabled :value="detailInfo.source"></a-input>
    </a-form-item>

    <a-form-item label="整改方案">
      <a-input
        type="textarea"
        :disabled="pageType === 'view'"
        @input="(e) => (rectifyProjectNumber = e.target.value.length)"
        v-decorator="[
          'rectifyProject',
          {
            initialValue: detailInfo.rectifyProject || undefined,
            rules: [{ required: true, message: '请填写整改方案' }],
          },
        ]"
      ></a-input>
      <div class="right-tip-number">{{ rectifyProjectNumber }}/200</div>
    </a-form-item>

    <h2 v-if="pageType === 'view'">整改情况</h2>
    <a-form-item label="整改时间">
      <a-date-picker
        :disabled="pageType === 'view'"
        v-decorator="[
          'rectifyTime',
          {
            initialValue: moment(detailInfo.rectifyTime) || undefined,
            rules: [{ required: true, message: '请选择整改时间' }],
          },
        ]"
      ></a-date-picker>
    </a-form-item>
    <a-form-item label="整改状态">
      <a-select
        :disabled="pageType === 'view'"
        :options="Config.RectifyStatus"
        v-decorator="[
          'rectifyStatus',
          {
            initialValue: detailInfo.rectifyStatus || undefined,
            rules: [{ required: true, message: '请选择整改状态' }],
          },
        ]"
      ></a-select>
    </a-form-item>
    <a-form-item label="整改进展情况">
      <a-input
        :disabled="pageType === 'view'"
        type="textarea"
        :maxLength="200"
        @input="(e) => (rectifyProgressNumber = e.target.value.length)"
        v-decorator="[
          'rectifyProgress',
          {
            initialValue: detailInfo.rectifyProgress || undefined,
            rules: [{ required: true, message: '请填写整改进展情况' }],
          },
        ]"
      ></a-input>
      <div class="right-tip-number">{{ rectifyProgressNumber }}/200</div>
    </a-form-item>
    <a-form-model-item
      prop="attachments"
      label="上传附件"
      :label-col="{ span: 3 }"
      :wrapper-col="{ span: 20 }"
    >
      <div class="upload-tip">
        请上传整改完成情况文档或照片，单个文件大小不要超过5M,最多上传5个文件（文件类型仅支持jpg；png；doc；docx；xls；xlsx；pdf）
      </div>
      <vxe-table
        class="flow-detail-table"
        headerAlign="center"
        align="center"
        showOverflow="tooltip"
        :data="detailInfo.attachments"
        :column-config="{ resizable: true }"
      >
        <vxe-column field="fileName" title="附件名称" width="40%"></vxe-column>
        <!-- <vxe-column field="fileSize" title="附件大小"> </vxe-column> -->
        <vxe-table-column title="操作">
          <template #default="{ row }">
            <a @click="downloadFile(row)" class="mr10">下载</a>
            <a @click="delFile(row)" v-if="pageType === 'add'">删除</a>
          </template>
        </vxe-table-column>
      </vxe-table>
      <uploadFiles
        v-show="pageType === 'add' && detailInfo.attachments.length < 5"
        v-model="detailInfo.attachments"
        :maxSize="5"
        :accept="'.pdf, .ppt, .pptx, .doc, .docx, .png, .jpg, .bmp, .mp4, .zip, .rar'"
        :showDefaultUploadList="false"
        @setAnnexList="setAnnexList"
        :delFileId="delFileId"
        :fileListTemp="detailInfo.attachments"
      ></uploadFiles>
    </a-form-model-item>
    <a-form-item :wrapper-col="{ span: 12, offset: 5 }" class="bottom-btn">
      <a-button
        :disabled="saveBtnDisabled"
        type="primary"
        @click="handleSubmit"
        class="mr10"
        v-if="pageType === 'add'"
      >
        保存
      </a-button>
      <a-button type="default" @click="() => $router.go(-1)"> 返回 </a-button>
    </a-form-item>
  </a-form>
</template>

<script>
import Config from './config';
import uploadFiles from '@/components/Uploads/uploadFiles.vue';
import {
  getDangerDetail,
  fillRectification,
} from '@/api/digitalOperation/securityManagement/danger';
import moment from 'moment';
export default {
  components: {
    uploadFiles,
  },
  data() {
    this.moment = moment;
    this.Config = Config;
    return {
      rectifyProjectNumber: 0,
      rectifyProgressNumber: 0,
      pageType: 'add',
      saveBtnDisabled: false,
      formLayout: 'horizontal',
      form: this.$form.createForm(this, { name: 'coordinated' }),
      detailInfo: {
        attachments: [],
      },
      delFileId: '',
    };
  },
  mounted() {
    this.pageType = this.$route.query.pageType || 'add';
    this.getDangerDetailInfo();
  },
  methods: {
    handleSubmit(e) {
      e.preventDefault();
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('Received values of form: ', values);
          this.fillRectification(values);
        }
      });
    },
    async fillRectification(values) {
      this.saveBtnDisabled = true;
      const dangerId = this.$route.query.dangerId;
      if (!dangerId) {
        this.$message.error('参数错误,dangerId不能为空');
        return;
      }
      const [res, err] = await fillRectification({
        ...values,
        dangerId,
        rectifyTime: values.rectifyTime.format('YYYY-MM-DD'),
        attachments: this.detailInfo.attachments,
      });
      this.saveBtnDisabled = false;
      if (err) {
        this.$message.error(err.msg || '保存失败');
        return;
      }
      this.$message.success('保存成功');
      this.$router.go(-1);
    },
    handleSelectChange(value) {
      console.log(value);
      this.form.setFieldsValue({
        note: `Hi, ${value === 'male' ? 'man' : 'lady'}!`,
      });
    },
    setAnnexList(fileList, type) {
      const baseURL = process.env.VUE_APP_BASE_API_IMG;
      const list = fileList.map((item) => {
        const tempUrl = baseURL + item.response?.fileName;
        return {
          fileName: item.name || item.fileName,
          fileUrl: item.fileUrl || tempUrl,
        };
      });
      this.detailInfo.attachments = list;
    },
    delFile(row) {
      this.detailInfo.attachments = this.detailInfo.attachments.filter(
        (item) => item.fileName !== row.fileName
      );
    },
    downloadFile(row) {
      const a = document.createElement('a'); // 创建一个HTML 元素
      a.setAttribute('target', '_blank');
      a.setAttribute('download', row.fileName); //download属性
      a.setAttribute('href', row.fileUrl); // href链接
      a.click(); // 自执行点击事件
    },
    async getDangerDetailInfo() {
      const [res, err] = await getDangerDetail({
        dangerId: this.$route.query.dangerId,
      });
      if (err) return;
      this.detailInfo = res.data;
      this.detailInfo.rectifyTime = res.data.rectifyTime
        ? moment(res.data.rectifyTime)
        : undefined;
      this.detailInfo.source = Config.SourceType.find(
        (item) => item.value === res.data.source
      ).label;
      this.detailInfo.dangerType = Config.DangerType.find(
        (item) => item.value === res.data.dangerType
      ).label;
      this.setAnnexList(res.data.attachments || []);
    },
  },
};
</script>

<style lang="less" scoped>
.mr10 {
  margin-right: 10px;
}
.form-container {
  background: #fff;
  padding: 24px;
}
.bottom-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.right-tip-number {
  position: absolute;
  right: 0;
  bottom: -30px;
  font-size: 12px;
  color: #999999;
  line-height: 22px;
  text-align: right;
}
.upload-tip {
  font-size: 12px;
  color: #999999;
}
</style>
