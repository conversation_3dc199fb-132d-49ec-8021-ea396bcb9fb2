<template>
  <!-- 添加或修改菜单对话框 -->
  <a-modal
    :title="title"
    :visible="visible"
    :loading="loading"
    :maskClosable="false"
    width="700px"
    :footer="false"
    @cancel="closeModal"
  >
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      v-bind="formLayout"
      label-width="80px"
    >
      <a-form-model-item label="字典编码" prop="dictCode">
        <a-input v-model="form.dictCode" :disabled="true" />
      </a-form-model-item>

      <a-form-model-item label="字典Value" prop="dictValue">
        <a-input v-model="form.dictValue" placeholder="请输入字典Value" />
      </a-form-model-item>
      <a-form-model-item label="字典Label" prop="dictLabel">
        <a-input v-model="form.dictLabel" placeholder="请输入字典Label" />
      </a-form-model-item>
      <a-form-model-item :wrapper-col="{ span: 12, offset: 6 }">
        <a-button
          type="primary"
          v-hasPermi="['system:dict:addData']"
          @click="submitForm"
        >
          添加
        </a-button>
      </a-form-model-item>
    </a-form-model>
    <div style="padding: 0 20px">
      <a-table
        :columns="tableColumns"
        :data-source="dataList"
        :loading="loading"
        :toolbarConfig="null"
        :pagination="false"
      >
        <template slot="operation" slot-scope="text, record">
          <a-button
            v-hasPermi="['system:dict:deleteData']"
            v-if="record.userId !== 1"
            icon="delete"
            type="link"
            style="padding: 0; margin-right: 8px"
            @click="handleDelete(record)"
          >
            删除
          </a-button>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>
<script>
import { addData, delData } from '@/api/system/dict/data';
import { Modal } from 'ant-design-vue';
import { mapState } from 'vuex';
import {
  tableColumns2dictCode,
  formRules2dictCode,
  initForm2dictCode,
} from './constant';

export default {
  props: {
    visible: Boolean,
    dictId: String,
    dictCode: String,
  },
  data() {
    return {
      loading: false,
      dataList: [],
      // 表单参数
      form: initForm2dictCode(),
      // 表单校验
      rules: formRules2dictCode,
      tableColumns: tableColumns2dictCode,
      formLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
      },
    };
  },
  computed: {
    ...mapState({
      merchantId: (state) =>
        state.base && state.base.user && state.base.user.merchantId,
    }),
    title() {
      return `${
        this.form.dictCode || (this.loading && this.dictCode) ? '编辑' : '新增'
      }字典值`;
    },
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.dictCode) {
          this.getDetail();
        }
      } else {
        this.dataList = [];
        this.$refs.form && this.$refs.form.resetFields();
      }
    },
  },
  methods: {
    /**
     * 获取菜单详情
     */
    async getDetail() {
      this.dataList = [];
      this.form = initForm2dictCode({ dictCode: this.dictCode });
      this.loading = true;
      const [result, error] = await this.getDicts(this.dictCode, {
        merchantId: this.merchantId,
      });
      this.loading = false;
      if (error) {
        return;
      }
      this.dataList = result.data || [];
    },
    /**
     * 提交按钮
     */
    submitForm() {
      if (this.loading) return;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const [, error] = await addData({
            ...this.form,
            typeId: this.dictId,
          });
          this.loading = false;
          if (error) return;
          this.$message.success(`${this.title}成功`);
          this.getDetail();
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.loading) return;
      Modal.confirm({
        title: '警告',
        content: '是否确认删除字典"' + row.dictLabel + '"的数据项?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.loading = true;
          const [, error] = await delData(row.dataId);
          this.loading = false;
          if (error) return;
          this.$message.success('删除成功');
          this.getDetail();
        },
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .ant-form-item {
  margin-bottom: 16px !important;
}
</style>
