<template>
  <a-modal
    title="导入组织架构"
    :visible="visible"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
    width="600px"
    :footer="false"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <h3 style="margin-bottom: 20px">导入组织架构:</h3>
      <a-steps direction="vertical" :current="url ? 1 : 0">
        <a-step>
          <template #title>
            下载组织架构模板，填写组织信息
            <a-button size="small" style="margin-left: 10px" @click="downLoad"
              >下载模版</a-button
            >
          </template>
        </a-step>
        <a-step>
          <template #title>
            上传填写好的组织架构信息表
            <a-button
              type="primary"
              style="margin-left: 10px"
              v-if="url"
              @click="submit"
              size="small"
              >上传</a-button
            ></template
          >
          <template #description>
            <Upload
              v-if="visible"
              list-type="text"
              ref="fileUpload"
              :file-list.sync="url"
              accept=".xlsx"
              @change="(e) => (url = e)"
            >
            </Upload>
          </template>
        </a-step>
      </a-steps>
    </a-spin>
  </a-modal>
</template>

<script>
// import { downLoadFile } from './requestFile';
import { downloadExcel } from '@/utils/common/fileDownload';
import { importExcel } from '@/api/system/organization';
import Upload from './UploadExcel';
// import { getBaseUrl } from '@/utils/common/util.js';
export default {
  components: { Upload },
  props: {
    visible: Boolean,
    organizeId: String,
  },
  data() {
    return {
      // 菜单树
      url: '',
      loading: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.url = '';
      }
    },
  },
  methods: {
    // async downLoad() {
    // downLoadFile({
    //   method: 'get',
    //   url: ` ${
    //     process.env.VUE_APP_USE_BUILD_TYPE
    //       ? getBaseUrl()
    //       : process.env.VUE_APP_BASE_API
    //   }/api/authority/admin/organize/getExcelTemplate`,
    //   fileType: 'xlsx',
    //   fileName: '组织架构模板',
    // });
    // },
    // 下载模版
    downLoad() {
      const downLoadName = `组织架构模板`;
      downloadExcel(
        '/api/authority/admin/organize/getExcelTemplate',
        {},
        downLoadName
      );
    },

    chooseFile() {
      //
    },
    async submit() {
      this.loading = true;
      const formData = new FormData();
      formData.append('file', this.url);
      const [, err] = await importExcel(formData);
      this.loading = false;
      if (!err) {
        this.$emit('update:visible', false);
        this.$emit('refresh');
      }
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
