import { request } from '@/utils/request/requestTkb';

// 用户管理-列表
export function parkUserList(data) {
  return request({
    url: '/gov/parkUser/list',
    method: 'post',
    data,
  });
}
// 用户管理-保存
export function saveOrEdit(data) {
  return request({
    url: '/gov/parkUser/saveOrEdit',
    method: 'post',
    data,
  });
}

// 用户管理-启用禁用
export function changeState(data) {
  return request({
    url: '/gov/parkUser/changeState',
    method: 'post',
    data,
  });
}
//二期根据园区进行企业查询

export function getUserAccountAll(data) {
  return request({
    url: '/userAccount/all',
    method: 'post',
    data,
  });
}
