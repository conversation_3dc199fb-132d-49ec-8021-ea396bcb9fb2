<template>
  <a-modal
    width="600px"
    :title="modelTitle === 'add' ? '新增' : '编辑'"
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm ref="ruleForm" :config="formConfig" :params="formValue">
        <!-- 园区 -->
        <template #enterSlot>
          <a-select
            :allowClear="true"
            :filterOption="filterOption"
            :showSearch="true"
            placeholder="请选择"
            v-model="formValue.parkId"
          >
            <a-select-option
              v-for="item in affiliatedPark"
              :key="item.id"
              :value="item.id"
            >
              {{ item.parkName }}
            </a-select-option>
          </a-select>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import { initFormValue } from '../constant';
import { saveEnterprise, editEnterprise } from '@/api/basicData';
export default {
  props: [
    'visible',
    'detail',
    'isLook',
    'modelTitle',
    'affiliatedPark',
    'companyList',
  ],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  computed: {
    formConfig() {
      const formConfig = [
        {
          field: 'enterpriseName',
          title: '企业名称',
          element: 'a-input',
          // props: {
          //   showSearch: true,
          //   filterOption: this.filterOption,
          //   options: this.companyList
          // },
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'enterpriseAddress',
          title: '企业地址',
          props: {
            placeholder: '请输入',
          },
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'enterpriseProperty',
          title: '企业性质',
          element: 'a-select',
          props: {
            options: [
              { value: '1', label: '科技企业' },
              { value: '2', label: '工业企业' },
              { value: '3', label: '后勤管理' },
              { value: '4', label: '商铺' },
              { value: '5', label: '其它' },
            ],
            showSearch: true,
            optionFilterProp: 'children',
          },
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'parkId',
          title: '所属园区',
          // props: {
          //   options: this.affiliatedPark,
          // },
          element: 'slot',
          slotName: 'enterSlot',
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'floor',
          title: '楼栋',
          element: 'a-input',
          rules: [{ required: true, message: '请输入' }],
        },
      ];
      return formConfig;
    },
  },
  methods: {
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        console.log(valid);
        if (valid) {
          if (this.modelTitle === 'add') {
            this.saveEnterprise();
          } else {
            this.editEnterprise();
          }
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 新增
    async saveEnterprise() {
      const [, err] = await saveEnterprise(this.formValue);
      if (err) return;
      this.$message.success('新增成功!');
      this.$emit('loadData');
    },
    // 编辑
    async editEnterprise() {
      const [, err] = await editEnterprise(this.formValue);
      if (err) return;
      this.$message.success('编辑成功!');
      this.$emit('loadData');
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
