import request from '@/utils/request';

// 新增
export function addPoition(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/position/addPosition',
    method: 'POST',
    data: query,
  });
}
// 修改
export function updatePosition(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/position/updatePosition',
    method: 'POST',
    data: query,
  });
}
// 批量删除
export function removeBatch(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/position/removeBatch',
    method: 'POST',
    data: query,
  });
}
// 新增岗位组用户
export function addPositionUsers(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/position/addPositionUsers',
    method: 'POST',
    data: query,
  });
}
// 删除岗位组用户
export function removePositionUser(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/position/removePositionUser',
    method: 'POST',
    data: query,
  });
}

// 获取岗位列表
export function getPoitionList(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/position/list',
    method: 'GET',
    params: query,
  });
}
// 根据岗位组编号获取
export function getPoitionDetail(positionId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      `/api/authority/admin/position/${positionId}`,
    method: 'GET',
  });
}
// 删除
export function deletePoition(positionId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      `/api/authority/admin/position/remove/${positionId}`,
    method: 'GET',
  });
}
// 获取岗位组用户列表
export function getPositionUserList(positionId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      `/api/authority/admin/position/positionUserList/${positionId}`,
    method: 'GET',
  });
}
// 获取机构组织下用户列表
export function getOrganizeUserList(positionId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      `/api/authority/admin/position/organizeUserList/${positionId}`,
    method: 'GET',
  });
}
// 获取岗位组角色ID列表
export function getPositionRoleList(positionId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      `/api/authority/admin/position/positionRoleList/${positionId}`,
    method: 'GET',
  });
}
// 修改岗位组角色绑定
export function updatePositionPermission(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      `/api/authority/admin/position/updatePositionPermission`,
    method: 'POST',
    data: query,
  });
}
// 修改岗位组角色绑定
export function copyPosition(positionId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      `/api/authority/admin/position/copyPosition/${positionId}`,
    method: 'GET',
  });
}
