<template>
  <BuseCrud
    ref="crud"
    title="推送管理"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalConfirm="modalConfirmHandler"
    @loadData="loadData"
    @rowView="rowView"
    @handleCreate="rowAdd"
  >
    <template slot="defaultTitle">
      <TabRadio
        v-model="currentSelectTab"
        :radioList="statusRadioList"
        @change="onStatusChange"
        class="mb10"
      >
      </TabRadio>
    </template>
    <template slot="pushResource" slot-scope="{ row }">
      <pushTransfer ref="pushResource" :params="pushParams" :rowInfo="row" />
    </template>

    <template #defaultHeader>
      <a-button
        type="primary"
        :loading="exportLoading"
        @click="handleExport"
        class="mr10"
        >导出</a-button
      >
    </template>
    <template slot="pageCenter">
      <a-row class="page-center-overview">
        <a-col :span="8">
          <dl>
            <dt class="title">接收总量</dt>
            <dd class="desc">{{ data.totalRecive ?? '/' }}</dd>
          </dl>
        </a-col>
        <a-col :span="8">
          <dl>
            <dt class="title">查看总量</dt>
            <dd class="desc">{{ data.totalRead ?? '/' }}</dd>
          </dl></a-col
        >
        <a-col :span="8">
          <dl>
            <dt class="title">查看率</dt>
            <dd class="desc">
              {{
                data.readRate || data.readRate === 0 ? data.readRate + '%' : '/'
              }}
            </dd>
          </dl>
        </a-col>
      </a-row>
    </template>
  </BuseCrud>
</template>

<script>
import pushTransfer from '@/components/pushTransfer/index.vue';
import TabRadio from '@/components/tabRadio/TabRadio.vue';
import { getPushTableColumn } from './resourceManagement';
import {
  pushList,
  cancelPush,
  rePushRes,
  exportResPushInfo,
  pushDetailStatistics,
} from '@/api/digitalOperation/resourcePool/resourceManagement/pushManagement.js';
import { push } from '@/api/digitalOperation/resourcePool/resourceManagement/index.js';
import { getOrganizeAll } from '@/views/digitalOperation/taskManagement/utils/index.js';
import { saveAs } from 'file-saver';
export default {
  name: 'resourceManagement',
  components: {
    TabRadio,
    pushTransfer,
  },

  data() {
    return {
      tableData: [],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      params: {
        cancelTime: undefined,
        pushTargetUnit: undefined,
        checkState: undefined,
      },
      resourceType: [],
      /**
       * 当前选中的tab，1是已推送列表，2是撤销推送列表
       */
      currentSelectTab: '0',
      organizeAllList: [], //推送人列表
      pushParams: {},
      data: {
        totalRecive: undefined,
        totalRead: undefined,
        readRate: undefined,
      }, //推送数据统计
      exportLoading: false,
    };
  },
  created() {
    // this.pushDetailStatistics();
    this.statusRadioList = [
      {
        label: '已推送列表',
        value: '0',
      },
      {
        label: '撤销推送列表',
        value: '1',
      },
    ];
    this.tableColumn = getPushTableColumn();
  },
  computed: {
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            title: '撤销推送时间',
            field: 'cancelTime',
            element: 'a-range-picker',
            show: this.currentSelectTab == '1',
          },
          {
            title: '推送人单位',
            field: 'pushTargetUnit',
          },
          {
            title: '查看状态',
            field: 'checkState',
            element: 'a-select',
            props: {
              options: [
                {
                  label: '未查看',
                  value: '0',
                },
                {
                  label: '已查看',
                  value: '1',
                },
              ],
            },
            show: this.currentSelectTab == '0',
          },
        ],
      };
    },
    modalConfig() {
      return {
        editBtn: false,
        delBtn: false,
        addBtn: true,
        menuWidth: 300,
        viewBtn: false,
        modalWith: 1000,
        addBtnText: '添加推送人',
        customOperationTypes: [
          {
            title: '添加推送人',
            typeName: 'pushResource',
            slotName: 'pushResource',
            event: () => {
              // this.pushResource(row);
            },
            condition: () => {
              return false;
            },
          },
          {
            title: '撤销推送资源',
            typeName: 'cancelPushResource',
            condition: (row) => {
              //判断是否可以撤销推送资源
              return this.currentSelectTab == '0' && row.checkState != '1';
            },
            event: (row) => {
              let that = this;
              this.$confirm({
                title: '撤销提醒',
                content: `是否确定撤销对‘${row.pushTargetUnit}-${row.pushTarget}’的推送`,
                onOk() {
                  that.cancelPush(row);
                },
                onCancel() {
                  console.log('Cancel');
                },
                class: 'test',
              });
            },
          },
          {
            title: '重新推送资源',
            typeName: 'rePushResource',
            condition: (row) => {
              const result =
                this.currentSelectTab == '1' && row.shelfStatus === '1';
              return result;
            },
            event: (row) => {
              let that = this;
              this.$confirm({
                title: '推送提醒',
                content: `是否重新推送‘${row.resName}’`,
                onOk() {
                  that.rePushRes(row);
                },
                onCancel() {
                  console.log('Cancel');
                },
                class: 'test',
              });
            },
          },
        ],
      };
    },
  },

  mounted() {
    getOrganizeAll('ZZ1724674633342541824').then((res) => {
      this.organizeAllList = res;
    });
    this.loadData();
    this.getResourceTypes();
  },

  methods: {
    async pushDetailStatistics() {
      const [res, err] = await pushDetailStatistics({
        id: this.$route.query.id,
      });
      if (err) return;
      this.data = res.data;
    },
    async handleExport() {
      this.exportLoading = true;
      const [res, err] = await exportResPushInfo({
        resId: this.$route.query.id,
      });
      this.exportLoading = false;
      if (err) return;
      const blob = new Blob([res], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, '推送详情.xlsx');
    },
    async loadData() {
      this.loading = true;
      this.pushDetailStatistics();
      let p = this.paramsHandle();
      const [res, err] = await pushList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data.records;
      this.tablePage.total = res.data.total;
    },
    paramsHandle() {
      let p = {
        resId: this.$route.query.id,
        ...this.params,
        startTime:
          this.currentSelectTab == '1'
            ? this.params.cancelTime?.[0]?.format('YYYY-MM-DD')
            : undefined,
        endTime:
          this.currentSelectTab == '1'
            ? this.params.cancelTime?.[1]?.format('YYYY-MM-DD')
            : undefined,
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
        cancelPush: this.currentSelectTab,
      };
      delete p.cancelTime;
      return p;
    },
    getResourceTypes() {
      this.resourceType = [
        {
          label: '基金信息',
          value: '1',
        },
        {
          label: '基金信息2',
          value: '2',
        },
      ];
    },
    rowView(row) {
      this.$router.push({
        path: '/taskTarget/taskManagement/instructionDetail',
        query: {
          id: row.id,
        },
      });
    },
    handleAdd() {},
    onStatusChange(value) {
      this.currentSelectTab = value;
      this.tableColumn = getPushTableColumn(value);

      this.loadData();
    },

    //撤销推送
    async cancelPush(row) {
      let p = {
        resId: this.$route.query.id,
        userIds: row.accountId,
      };
      console.log(p);
      const [, err] = await cancelPush(p);
      if (err) return;
      this.$message.success('撤销推送成功');
      this.loadData();
    },
    //重新推送资源
    async rePushRes(row) {
      let p = {
        resId: this.$route.query.id,
        accountId: row.accountId,
      };
      const [, err] = await rePushRes(p);
      if (err) return;
      this.$message.success('重新推送成功');
      this.loadData();
    },
    //添加推送人
    async addPush(p) {
      const [, err] = await push(p);
      if (err) return;
      this.$message.success('推送成功');
      this.loadData();
    },
    //推送
    pushConfirmHandler(value) {
      return new Promise((resolve) => {
        if (!this.$refs.pushResource?.paramsSelectedRowKeys.length) {
          this.$message.error('请选择推送人员');
          //返回false，阻止弹窗关闭
          resolve(false);
        } else {
          //推送接口
          let p = {
            resId: value.resId,
            userIds: this.$refs.pushResource.paramsSelectedRowKeys,
          };
          this.addPush(p);

          resolve();
        }
      });
    },
    //弹窗确认按钮事件
    async modalConfirmHandler(value) {
      console.log('value', value);
      const { crudOperationType } = value;
      if (crudOperationType === 'pushResource') {
        const res = await this.pushConfirmHandler(value);
        return res;
      }
    },
    rowAdd() {
      // return this.$refs.crud.switchModalView(true, 'pushResource', row);
      this.$refs.crud.switchModalView(true, 'pushResource', {
        resId: this.$route.query.id,
        userIds: undefined,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page-center-overview {
  background: #fff;
  height: 112px;
  margin: 16px 0;
  border-radius: 2px;
  dl {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: left;
    height: 112px;
    padding: 0 24px;
    text-align: left;
    .title {
      opacity: 0.65;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      line-height: 22px;
      margin-bottom: 6px;
    }
    .desc {
      opacity: 0.85;
      font-size: 30px;
      color: #000000;
      line-height: 38px;
      margin-bottom: 0;
    }
  }
}
</style>
