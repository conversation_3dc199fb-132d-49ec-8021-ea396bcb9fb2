<template>
  <a-modal
    title="岗位权限"
    :visible="visible"
    :loading="loading"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
    width="700px"
    @ok="submitForm"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <a-alert
        style="margin-bottom: 12px"
        message="权限修改后，该岗位下的所有用户权限都会变更"
        banner
      />
      <a-descriptions title="岗位信息">
        <a-descriptions-item label="租户名称">
          {{ baseInfo.merchantName }}
        </a-descriptions-item>
        <a-descriptions-item label="组织名称">
          {{ baseInfo.organizeName }}
        </a-descriptions-item>
        <a-descriptions-item label="岗位名称">
          {{ baseInfo.positionName }}
        </a-descriptions-item>
        <a-descriptions-item label="岗位权限" :span="3">
          <a-select
            v-model="roleIds"
            mode="multiple"
            :loading="roleLoading"
            style="width: 650px; margin-top: 10px"
            :filter-option="filterOption"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            placeholder="请选择岗位权限"
          >
            <a-select-option
              v-for="item in roleOptionsList"
              :key="item.roleId"
              :value="item.roleId"
              >{{ item.roleName }}</a-select-option
            >
          </a-select>
          <span class="tips">定义岗位的页面和操作权限</span>
        </a-descriptions-item>
        <a-descriptions-item label="扩展权限" :span="3">
          <a-select
            v-model="type"
            style="width: 200px; margin-right: 20px; margin-top: 10px"
            :filter-option="filterOption"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            placeholder="请选择扩展权限"
          >
            <a-select-option
              v-for="item in typeOptionsList"
              :key="item.dictId"
              :value="item.dictId"
              >{{ item.dictName }}</a-select-option
            >
          </a-select>
          <a-select
            v-model="value"
            mode="multiple"
            style="width: 430px; margin-top: 10px"
            :filter-option="filterOption"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            placeholder="请选择扩展权限"
          >
            <a-select-option
              v-for="item in valueOptions"
              :key="item.dataId"
              :value="item.dataId"
              >{{ item.dictLabel }}</a-select-option
            >
          </a-select>
        </a-descriptions-item>
      </a-descriptions>
    </a-spin>
  </a-modal>
</template>

<script>
import {
  getPoitionDetail,
  getPositionRoleList,
  updatePositionPermission,
} from '@/api/system/position';
import { listType } from '@/api/system/dict/type';
import { getDicts } from '@/api/system/dict/data';
import { listRole } from '@/api/system/role';

export default {
  components: {},
  props: {
    visible: Boolean,
    positionId: String, //岗位ID
    positionName: String, //岗位ID
    organizeId: String, //组织架构ID
  },
  data() {
    return {
      // 菜单树
      roleOptionsList: [],
      valueOptions: [],
      typeOptionsList: [],
      baseInfo: {},

      type: undefined, //权限类型
      roleIds: [], //角色列表
      value: [], //权限值
      loading: false,
      roleLoading: false, //角色列表加载
    };
  },
  watch: {
    async visible(val) {
      if (val) {
        await this.initSelect();
        if (this.positionId) {
          this.getDetail();
        }
      }
    },
    type(val) {
      if (val) {
        this.getValueList();
      } else {
        this.valueOptions = [];
        this.value = [];
      }
    },
  },
  methods: {
    async initSelect() {
      // 晴空数据
      this.type = undefined;
      this.roleIds = [];
      this.value = [];
      // 获取权限类型列表
      await this.getTypeList();
      // 获取角色列表
      this.getRoleList();
    },
    /**
     * 获取用户列表
     */
    async getRoleList() {
      this.roleLoading = true;
      const [result] = await listRole({ limit: 99999, page: 1 });
      this.roleOptionsList = result.data || [];
      this.roleLoading = false;
    },

    /**
     * 获取扩展权限类型
     */
    async getTypeList() {
      this.loading = true;
      const [result] = await listType({
        limit: 9999,
        page: 1,
        dictType: 'DATASOURCE',
      });
      this.loading = false;
      this.typeOptionsList = result?.data || [];
    },

    /**
     * 获取扩展权限类型--值
     */
    async getValueList() {
      this.loading = true;
      this.valueOptions = [];
      this.value = [];
      const dictCode = this.typeOptionsList.find(
        (item) => item.dictId === this.type
      )?.dictCode;
      const [result] = await getDicts(dictCode);

      this.loading = false;
      this.valueOptions = result?.data || [];
    },
    // 获取岗位详情
    async getDetail() {
      this.baseLoading = true;
      const [result] = await getPoitionDetail(this.positionId);
      const [info, err] = await getPositionRoleList(this.positionId);
      this.baseLoading = false;
      this.baseInfo = result?.data;
      if (!err) {
        const { roleIds, extendPermissions } = info.data;
        this.roleIds = roleIds || [];
        const chooseType = Object.keys(extendPermissions);
        if (chooseType.length) {
          this.type = chooseType[0];
          this.$nextTick(() => {
            this.value = extendPermissions[this.type];
          });
        }
      }
    },
    /**
     * 搜索
     */
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    /**
     * 提交按钮
     */
    async submitForm() {
      if (this.loading) return;
      this.loading = true;
      let extendPermissions = {};
      this.type && (extendPermissions[this.type] = this.value);

      const [, error] = await updatePositionPermission({
        roleIds: this.roleIds,
        positionId: this.positionId,
        extendPermissions,
      });
      this.loading = false;
      if (error) return;
      this.$message.success(`编辑成功`);
      this.closeModal();
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
<style lang="less" scoped>
.tips {
  font-size: 12px;
  padding-left: 5px;
  padding-top: 10px;
  color: #bfbfbf;
}
</style>
