<template>
  <BuseCrud
    ref="crud"
    title="目标列表"
    :loading="loading"
    :filterOptions="filterOptions"
    :tableColumn="tableColumn"
    :tableData="tableDataFilter"
    :modalConfig="modalConfig"
    @modalCancel="modalCancelHandler"
    @modalSubmit="modalSubmit"
    @modalConfirm="modalConfirmHandler"
    @loadData="loadData"
    @handleCreate="rowAdd"
  >
    <template slot="defaultHeader">
      <a-button type="default" class="mr10" @click="exportData">导出</a-button>
    </template>
    <template slot="yearForModal" slot-scope="{ params }">
      <BuseRangePicker
        type="year"
        v-model="params.year"
        :needShowSecondPicker="() => false"
        format="YYYY"
      />
    </template>
    <template slot="totalPlan" slot-scope="{ params }">
      <a-input v-model="params.totalPlan" placeholder="请输入目标">
        <a-select slot="addonAfter" default-value="1" style="width: 80px">
          <a-select-option value="1"> 个 </a-select-option>
          <a-select-option value="2"> % </a-select-option>
          <a-select-option value="3"> 亿元 </a-select-option>
        </a-select>
      </a-input>
    </template>
    <template slot="modalDefault" slot-scope="{ row, operationType }">
      <TargetAssignment
        ref="TargetAssignment"
        v-bind="{ row, operationType }"
      ></TargetAssignment>
    </template>
    <!-- <template slot="targetAssignment" slot-scope="{ row, operationType }">
      <TargetAssignment v-bind="{ row, operationType }"></TargetAssignment>
    </template> -->
  </BuseCrud>
</template>

<script>
import {
  getTargetCompletionTableColumns,
  getCompletionStatusList,
} from './targetManagement';
import TargetAssignment from './components/targetAssignment.vue';
import { cenaAdd } from '@/api/digitalOperation/targetPerformance/targetManagement/index.js';
import {
  cenaList,
  update,
  downloadInformation,
} from '@/api/digitalOperation/targetPerformance/targetManagement/completionStatus.js';
import { resolveBlob } from '@/utils/common/fileDownload';
import { checkNumber } from '@/utils/index';
export default {
  name: 'TargetManagement',
  components: {
    TargetAssignment,
  },
  data() {
    return {
      checkNumber,
      menuShow: true,
      tableData: [],
      params: { progress: 'all' },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableDataFilter: [],
    };
  },
  created() {
    this.tableColumn = getTargetCompletionTableColumns.call(this);
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            title: '完成进度',
            field: 'progress',
            element: 'a-select',
            props: {
              options: getCompletionStatusList(),
            },
            on: {
              change: this.progressChange,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        menuWidth: 300,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        addBtn: true,
        addBtnText: '添加分配对象',
        formConfig: [
          {
            title: '目标年份',
            field: 'year',
            element: 'slot',
            slotName: 'yearForModal',
            rules: [{ required: true, message: '请选择目标年份' }],
          },
          {
            title: '一级指标',
            field: 'primaryIndex',
            element: 'a-select',
            props: {
              options: [],
            },
            rules: [{ required: true, message: '请选择一级指标' }],
          },
          {
            title: '二级指标',
            field: 'secondaryIndex',
            element: 'a-select',
            rules: [{ required: true, message: '请选择二级指标' }],
            on: {
              change() {
                console.log('change');
              },
            },
          },
          {
            title: '总计划完成目标',
            field: 'totalPlan',
            element: 'slot',
            slotName: 'totalPlan',
            rules: [
              { required: true, message: '请输入总计划完成目标' },
              { validator: checkNumber, trigger: '请输入正整数' },
            ],
          },
          {
            title: '指标内涵',
            field: 'indexInterpretation',
            element: 'a-textarea',
            props: {
              rows: 5,
              placeholder: '请对目标添加说明描述，不超过200个字',
            },
          },
        ],
        customOperationTypes: [
          {
            title: '查看完成详情',
            typeName: 'completionDetail',
            event: (row) => {
              localStorage.setItem('completionDetail', JSON.stringify(row));
              this.$router.push({
                path: '/taskTarget/targetPerformance/completionDetail',
                query: {
                  id: row.cenaInfoId,
                },
              });
            },
          },
          {
            title: (row, h) => {
              console.log(h);
              return (
                <a-button
                  type="link"
                  props={{ disabled: row.isWarning == '1' }}
                >
                  进度预警提醒
                </a-button>
              );
            },
            typeName: 'ProgressWarningReminder',
            event: (row) => {
              this.$confirm({
                title: '预警提醒',
                content: `是否确定对'${row.parkName}'发送预警提醒？`,
                onOk: () => {
                  this.waringHandle(row);
                },
                onCancel() {},
              });
            },
          },
          {
            title: '修改分配目标',
            typeName: 'editTarget',
            slotName: 'targetAssignment',
            showForm: false,
            showDefault: true,
            event: (row) => {
              return this.$refs.crud.switchModalView(true, 'editTarget', {
                ...row,
                baseName: 'zyj',
              });
            },
          },
          {
            title: '添加分配对象',
            typeName: 'addTarget',
            showForm: false,
            showDefault: true,
            event: () => {},
            condition: () => {
              return false;
            },
          },
        ],
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      const [res, err] = await cenaList({
        managementId: this.$route.query.id,
      });
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      // this.tablePage.total = res.total;
      this.tableDataFilter = this.tableData;
    },
    progressChange(val) {
      let arr = [];
      this.tableData.forEach((item) => {
        if (val === 'all') {
          arr.push(item);
        } else if (val === '20' && !item.finishingRate) {
          arr.push(item);
        } else {
          if (
            (Number(item.finishingRate) < Number(val) &&
              Number(item.finishingRate) > Number(val - 20)) ||
            Number(item.finishingRate) == Number(val - 20)
          ) {
            arr.push(item);
          }
        }
      });
      this.tableDataFilter = arr;
    },
    //目标管理分配
    async cenaAdd(params) {
      const [, err] = await cenaAdd(params);
      if (err) return this.$message.error('分配失败');
      this.$message.success('分配成功');
      this.loadData();
    },
    //修改分配目标及进度预警提醒
    async update(params, type) {
      const [, err] = await update(params);
      if (err)
        return this.$message.error(
          `${type === 'waring' ? '预警提醒' : '修改分配目标'}失败`
        );
      this.$message.success(
        `${type === 'waring' ? '预警提醒' : '修改分配目标'}成功`
      );
      this.loadData();
    },
    //进度预警提醒
    waringHandle(row) {
      console.log(row);
      this.update({ cenaInfoId: row.cenaInfoId, isWarning: '1' }, 'waring');
    },
    modalCancelHandler() {},
    async modalConfirmHandler(value) {
      const { crudOperationType } = value;
      const formData = await this.$refs.TargetAssignment.getAllFormData();
      console.log(formData, 'formData');
      if (!formData) return false;
      let timeParams = formData.timeNodeDataForCustom.map((item) => {
        return item ? item.format('YYYY-MM-DD') : '';
      });

      let p = {
        managementId: this.$route.query.id,
        dateSelection: formData.timeNodeData.dateSelection,
        customTime: timeParams ? timeParams.join(',') : '',
        status: '1', //草稿
        bpmCenaList: formData.areaDataParams,
      };

      if (crudOperationType == 'addTarget') {
        this.cenaAdd(p);
      } else {
        let p = {
          cenaInfoId: value.cenaInfoId,
          assignedAmount: formData.areaDataParams[0].assignedAmount,
          remark: formData.areaDataParams[0].remark,
        };
        this.update(p);
      }
    },
    modalSubmit() {},
    rowAdd() {
      this.$refs.crud.switchModalView(true, 'addTarget', {
        targetUnit: this.tableData[0].targetUnit,
      });
    },
    async exportData() {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
      // let p = this.filterParamsHandle();
      const [res] = await downloadInformation({
        managementId: this.$route.query.id,
      });
      resolveBlob(res, mimeMap.xlsx, '目标列表', '.xls');
    },
  },
};
</script>

<style lang="less" scoped></style>
