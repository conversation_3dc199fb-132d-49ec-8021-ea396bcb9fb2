<template>
  <div class="fn-header-nav-wrapper">
    <div class="fn-header-logo">
      <!-- <slot v-if="$slots.main - logo" name="main-logo"></slot>
      <img v-else :src="logo" /> -->
      <img src="@/assets/images/logo.png" alt="" />
    </div>
    <!-- 中间顶部一级导航栏 -->
    <div class="fn-header-middle-nav">
      <a-menu
        v-if="navMenuListShow && navMenuListShow.length > 0"
        mode="horizontal"
        :selectedKeys="middleOptions.currentNavMenu || currentNavMenu"
      >
        <template v-for="item in navMenuListShow">
          <template v-if="!item.subMenu || item.subMenu.length === 0">
            <a-menu-item :key="item.key">
              <a
                :href="item.path"
                :target="item.openType === 'NEW_TAB' ? '_blank' : '_self'"
              >
                {{ item.title }}
              </a>
            </a-menu-item>
          </template>
          <template v-else>
            <a-sub-menu :key="item.key">
              <template #title>
                <div class="nav-menu-arrow-box">
                  {{ item.title }}
                  <a-icon type="caret-down" class="arrow-icon" />
                </div>
              </template>
              <a-menu-item v-for="subItem in item.subMenu" :key="subItem.key">
                <a
                  :href="subItem.path"
                  :target="item.openType === 'NEW_TAB' ? '_blank' : '_self'"
                >
                  {{ subItem.title }}
                </a>
              </a-menu-item>
            </a-sub-menu>
          </template>
        </template>
      </a-menu>
      <a-divider
        v-if="hasMenuDivider"
        type="vertical"
        style="background-color: #8a94a4; height: 20px; margin: 0 10px"
      />
      <a-menu
        v-if="extraMenuOptions && extraMenuOptions.length > 0"
        mode="horizontal"
      >
        <a-menu-item v-for="(item, index) in extraMenuOptions" :key="index">
          <a
            v-if="item.type === 'link'"
            :href="item.linkUrl || item.url"
            :target="linkOpenType"
          >
            {{ item.title }}
          </a>
          <a-popover v-if="item.type === 'image'" placement="bottom">
            <template #content>
              <img width="158" :src="item.url" />
            </template>
            <a-icon v-if="item.icon" :type="item.icon" />
            <span>{{ item.title }}</span>
          </a-popover>
        </a-menu-item>
      </a-menu>
      <slot name="menu-extra"></slot>
    </div>
    <!-- 右侧登录状态展示+扩展插槽 -->
    <slot name="right-extra"></slot>
    <HeaderAvatar class="fn-header-avatar" v-bind="userInfo" @logout="logout" />
  </div>
</template>

<script>
import HeaderAvatar from './HeaderAvatar.vue';

export default {
  name: 'NavHeader',
  components: { HeaderAvatar },
  props: {
    // 左侧logo，建议尺寸：408*64
    logo: {
      type: String,
      default:
        // 'https://adserving-oss.bangdao-tech.com/ad-serving/assets/d5c575af32cf40cc8016ab3ac285f7a91647590465579.png',
        require('@/assets/images/logo.png'),
    },
    // 左侧logo点击的跳转地址
    logoHref: {
      type: String,
      default: '',
    },
    // 外链跳转模式 '_blank'(新开窗口) ， '_self'（当前窗口打开）
    linkOpenType: {
      type: String,
      default: '_blank',
    },
    // 新版配置项
    navMenuListShow: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 当前选中的导航菜单
    currentNavMenu: {
      type: Array,
      default: () => [],
    },
    // 中间导航菜单相关配置项（优先外部配置，词配置逐步放弃）
    middleOptions: {
      type: Object,
      default: () => ({}),
    },
    // 右侧用户信息展示内容
    userInfo: {
      type: Object,
      default: () => {
        return {
          username: '',
          merchantName: '',
        };
      },
    },
  },
  data() {
    return {
      // 点击logo的跳转地址，一般跳转到门户
      logoHrefInJson: '',
      // 导航列表右侧扩展配置
      extraMenuOptions: [],
    };
  },
  computed: {
    // 点击logo的跳转地址  javascript:void(0);
    logoHrefPath() {
      return this.logoHref || this.logoHrefInJson || '';
    },
    // 是否展示导航和扩展菜单之间的间隔符
    hasMenuDivider() {
      if (
        this.navMenuListShow.length > 0 &&
        (this.extraMenuOptions.length > 0 || this.$slots['menu-extra'])
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
  methods: {
    // 登出出发地 回调
    logout: function () {
      this.$emit('logout');
    },
    // 消息中心hover触发的数据
    handleMessageHoverChange: function (e) {
      this.$emit('handleMessageHoverChange', e);
    },
  },
};
</script>

<style lang="less" scoped>
.fn-header-nav-wrapper {
  z-index: 1;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  color: #000;
  font-size: 12px;
  height: 60px;
  line-height: 60px;
  background-image: linear-gradient(
    135deg,
    #ffffff 0%,
    #f9fcff 50%,
    #3bfb2d08 100%,
    #3bfb2d08 100%
  );
  // position: absolute;
  width: 100%;
  box-shadow: 0 10px 10px -10px rgba(174, 183, 207, 0.25);
  .fn-header-logo {
    margin-left: 24px;
    display: flex;
    height: 32px;
    // min-width: 38px;
    max-width: 504px;
    overflow: hidden;
    // vertical-align: middle;
    img {
      height: 100%;
      width: auto;
    }
  }
  .fn-header-middle-nav {
    flex: 1;
    margin-left: 80px;
    display: flex;
    align-items: center;
    /deep/ .ant-menu-horizontal {
      line-height: 60px;
      color: #333;
      border-bottom: none;
      &.ant-menu {
        background: none;
      }
      > .ant-menu-submenu-selected,
      > .ant-menu-item,
      > .ant-menu-item:hover,
      > .ant-menu-submenu,
      > .ant-menu-submenu:hover {
        border-bottom: 0;
      }
    }
    .nav-menu-arrow-box {
      display: flex;
      align-items: center;
    }
    .arrow-icon {
      font-size: 10px;
      margin-left: 10px;
    }
  }
  .fn-header-avatar {
    margin-left: 18px;
    // background: url('@/assets/images/top-right-bg.png');
    width: 390px;
    padding-right: 12px;
    background-repeat: no-repeat;
    justify-content: flex-end;
  }
}
</style>
<style lang="less">
.ant-popover-inner-content-no-padding {
  .ant-popover-inner-content {
    padding: 0;
  }
}
</style>
