<template>
  <!-- 添加或修改菜单对话框 -->
  <a-modal
    :title="title"
    :visible="visible"
    :loading="loading"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
    width="700px"
    @ok="submitForm"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="form" :rules="rules" v-bind="formLayout">
        <a-form-model-item label="上级菜单" prop="parentId">
          <treeselect
            v-model="form.parentId"
            :options="menuTree"
            placeholder="请选择上级菜单"
            :default-expand-level="10"
          />
        </a-form-model-item>
        <a-form-model-item label="菜单类型" prop="menuType">
          <a-radio-group
            v-model="form.menuType"
            :options="menuTypeOptions"
            @change="changeMenuType"
          >
          </a-radio-group>
        </a-form-model-item>
        <template v-if="form.menuType !== menuTypeEnum.BUTTON">
          <a-form-model-item label="菜单图标" prop="icon">
            <a-input v-model="form.icon" placeholder="请输入菜单图标" />
          </a-form-model-item>
          <a-form-model-item
            label="菜单标题"
            prop="menuTitle"
            extra="菜单标题：左侧菜单中展示的菜单名称"
          >
            <a-input v-model="form.menuTitle" :placeholder="`请输入菜单标题`" />
          </a-form-model-item>
          <a-form-model-item
            label="菜单name"
            prop="menuName"
            extra="菜单name：菜单页面的唯一标识"
          >
            <a-input v-model="form.menuName" :placeholder="`请输入菜单名称`" />
          </a-form-model-item>
          <a-form-model-item label="路由path" prop="path">
            <a-input v-model="form.path" placeholder="请输入路由地址" />
          </a-form-model-item>
          <a-form-model-item
            label="路由component"
            prop="component"
            :extra="`${
              form.menuType === 'M'
                ? '默认布局：Layout；顶部导航栏放一级菜单：LayoutInline；自定义请填写/layout/xxx格式'
                : '请填写@/views/后的内容'
            }`"
          >
            <a-input v-model="form.component" placeholder="请输入组件路径" />
          </a-form-model-item>
          <a-form-model-item
            v-if="form.menuType === menuTypeEnum.DIR"
            label="路由redirect"
            prop="redirect"
          >
            <a-input v-model="form.redirect" placeholder="请输入路由跳转" />
          </a-form-model-item>
        </template>
        <template v-else-if="form.menuType === menuTypeEnum.BUTTON">
          <a-form-model-item label="按钮标题" prop="menuTitle">
            <a-input v-model="form.menuTitle" :placeholder="`请输入按钮标题`" />
          </a-form-model-item>
          <!-- <a-form-model-item label="按钮标识" prop="menuName">
            <a-input v-model="form.menuName" :placeholder="`请输入按钮标识`" />
          </a-form-model-item> -->
          <a-form-model-item label="权限标识" prop="perms">
            <a-input v-model="form.perms" placeholder="请输入权限标识" />
          </a-form-model-item>
        </template>
        <a-form-model-item label="显示排序" prop="orderNum">
          <a-input-number
            v-model="form.orderNum"
            style="width: 100%"
            placeholder="请输入显示排序"
            :min="0"
          />
        </a-form-model-item>
        <a-form-model-item
          label="扩展参数"
          prop="extra"
          extra='例如: {"keepAlive":false}'
        >
          <a-textarea
            v-model="form.extra"
            style="width: 100%"
            placeholder="请输入扩展参数"
          />
        </a-form-model-item>
        <template v-if="form.menuType !== menuTypeEnum.BUTTON">
          <a-form-model-item label="是否外链" prop="isFrame">
            <a-radio-group v-model="form.isFrame">
              <a-radio value="0">是</a-radio>
              <a-radio value="1">否</a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item label="菜单状态">
            <a-radio-group v-model="form.visible" :options="visibleOptions">
            </a-radio-group>
          </a-form-model-item>
        </template>
        <a-form-model-item label="后端API地址" prop="serverUrl">
          <a-input
            type="textarea"
            v-model="form.serverUrl"
            placeholder="请输入后端API地址"
          />
        </a-form-model-item>
        <a-form-model-item label="后端API调用方式" prop="serverMethod">
          <a-input
            v-model="form.serverMethod"
            placeholder="请输入后端API调用方式"
          />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { addMenu, updateMenu, getMenu } from '@/api/system/menu';
import { treeselect as menuTreeselect } from '@/api/system/menu';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import {
  initMenuFormData,
  formLayout,
  rules,
  replaceFields,
  menuTypeOptions,
  menuTypeEnum,
  getMenuTypeLabel,
} from './constant.js';
import { visibleOptions } from '../constant/system';

export default {
  components: {
    Treeselect,
  },
  props: {
    visible: Boolean,
    menuId: String,
    appId: String,
    parentId: String,
  },
  data() {
    return {
      // 数据字典
      visibleOptions,
      menuTypeOptions,
      menuTypeEnum,
      replaceFields,
      // 菜单树
      menuTree: [],
      loading: false,
      formLayout,
      rules,
      form: {},
    };
  },
  computed: {
    title() {
      return `${
        this.form.appId || (this.loading && this.appId) ? '编辑' : '新增'
      }${getMenuTypeLabel(this.form.menuType)}`;
    },
    menuTypeLabel() {
      return getMenuTypeLabel(this.form.menuType);
    },
  },

  watch: {
    visible(val) {
      if (val) {
        this.getMenuTree();
        if (this.menuId) {
          this.getMenuDetail();
        } else {
          this.form = initMenuFormData({
            parentId:
              this.parentId && this.parentId !== '0'
                ? this.parentId
                : undefined,
          });
        }
      } else {
        this.$refs.form && this.$refs.form.resetFields();
      }
    },
  },
  methods: {
    /** 查询菜单树结构 */
    async getMenuTree() {
      const [result, error] = await menuTreeselect({
        appId: this.appId,
      });
      if (!error) {
        this.menuTree = result.data;
      }
    },
    /**
     * 获取菜单详情
     */
    async getMenuDetail() {
      this.loading = true;
      const [result, error] = await getMenu({ menuId: this.menuId });
      this.loading = false;
      if (error) {
        this.form = initMenuFormData({
          parentId:
            this.parentId && this.parentId !== '0' ? this.parentId : undefined,
        });
        return;
      }
      const { parentId } = result.data || {};
      this.form = {
        ...(result.data || {}),
        parentId: parentId && parentId !== '0' ? parentId : undefined,
      };
    },
    /**
     * 修改菜单类型
     */
    changeMenuType(e) {
      this.form = initMenuFormData({
        ...this.form,
        menuType: e.target.value,
        menuId: this.menuId,
      });
    },
    /**
     * 提交按钮
     */
    submitForm() {
      if (this.loading) return;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const queryParams = {
            ...this.form,
            parentId:
              this.form.parentId && this.form.parentId !== '0'
                ? this.form.parentId
                : '0',
            // r如果是按钮菜单，动态给menuName用title填入
            menuName:
              this.form.menuType === menuTypeEnum.BUTTON
                ? this.form.menuTitle
                : this.form.menuName,
            appId: this.appId,
          };
          const [, error] = this.form.menuId
            ? await updateMenu(queryParams)
            : await addMenu(queryParams);
          this.loading = false;
          if (error) return;
          this.$message.success(`${this.title}成功`);
          this.$emit('ok');
          this.closeModal();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
