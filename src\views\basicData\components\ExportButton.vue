<template>
  <div>
    <a-button
      type="button"
      style="margin-right: 8px"
      :checkItems="checkItems"
      @click="handelAllExport"
      v-if="!notExportAll"
    >
      全部导出
    </a-button>
    <a-button
      type="button"
      style="margin-right: 8px"
      :checkItems="checkItems"
      :disabled="!checkItems.length && !notExportAll"
      :loading="exportLoading"
      @click="handelExport"
    >
      导出
    </a-button>
  </div>
</template>

<script>
import moment from 'moment';
import * as api from '@/api/basicData';
import { resolveBlob } from '@/utils/common/fileDownload';

export default {
  props: ['pageName', 'checkItems', 'notExportAll', 'params'],
  components: {},
  data() {
    return {
      mimeMap: {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
      resolveBlob,
      exportLoading: false,
    };
  },
  methods: {
    // 导出
    async handelExport() {
      const params = {
        list: this.checkItems,
        ids: this.checkItems,
        ...(this.params ? this.params : {}),
        year: this.params?.year?.startValue
          ? moment(this.params.year.startValue).format('YYYY')
          : '',
      };
      this.exportLoading = true;
      const [res] = await api.downloadInformation(params, this.pageName);
      this.exportLoading = false;
      this.resolveBlob(res, this.mimeMap, '导出', '.xlsx');
    },
    // 导出全部
    async handelAllExport() {
      const [res] = await api.downloadAllInformation(
        {
          ...(this.params ? this.params : {}),
        },
        this.pageName
      );
      this.resolveBlob(res, this.mimeMap, '导出', '.xlsx');
    },
  },
};
</script>
