<template>
  <BuseCrud
    ref="crud"
    :loading="loading"
    :filterOptions="filterOptions"
    :tableColumn="tableColumn"
    :tablePage="tablePage"
    :tableProps="{
      headerAlign: 'left',
      border: 'none',
      columnConfig: { resizable: true },
      showOverflow: 'tooltip',
      align: 'left',
    }"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
  >
  </BuseCrud>
</template>

<script>
import { getOperationLog } from '@/api/basicData/index.js';
import { filterOption } from '@/utils';
export default {
  props: {
    rowData: {
      type: Object,
    },
    dataDict: {
      type: Object,
    },
  },
  components: {},
  data() {
    return {
      filterParams: {
        operator: '',
        fieldCode: undefined,
      },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'operator',
            title: '操作人',
            props: {
              placeholder: '请输入操作人',
            },
          },
          {
            field: 'fieldCode',
            element: 'a-select',
            title: '字段名称',
            props: {
              placeholder: '请选择字段名称',
              options: this.dataDict.qcc_field_mapping,
              showSearch: true,
              filterOption: filterOption,
            },
          },
        ],
        params: this.filterParams,
      };
    },
    tableColumn() {
      return [
        {
          field: 'operator',
          title: '更新人',
          minWidth: 120,
        },
        {
          field: 'createTime',
          title: '更新时间',
          minWidth: 120,
        },
        {
          field: 'fieldName',
          title: '字段名称',
          minWidth: 120,
        },
        {
          field: 'newFieldName',
          title: '修改后',
          minWidth: 120,
        },
        {
          field: 'oldFieldName',
          title: '修改前',
          minWidth: 120,
        },
      ];
    },
    modalConfig() {
      return {
        menu: false,
        addBtn: false,
      };
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      const [res] = await getOperationLog({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...this.filterParams,
        businessId: this.rowData.unifiedCreditCode,
      });
      this.loading = false;
      if (res && res.data) {
        this.tableData = res.data;
        this.tablePage.total = res.total;
      }
    },
  },
};
</script>

<style scoped lang="less"></style>
