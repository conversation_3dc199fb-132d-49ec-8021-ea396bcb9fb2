<template>
  <a-modal
    width="600px"
    :title="modelTitle === 'add' ? '新增' : '编辑'"
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm ref="ruleForm" :config="formConfig" :params="formValue">
        <template #inital>
          <a-input-number
            :min="0"
            :max="*********"
            :step="0.11"
            style="width: 100%"
            placeholder="请输入首发融资数字"
            v-model="formValue.initialFinancing"
            class="model-unit"
          />
          <span class="unit">亿元</span>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import { initFormValue } from '../constant';
import { saveCompany, editCompany } from '@/api/basicData';
import { institutionsMixin } from '../../mixins/institutionsMixin';
import FuzzySelect from '@/components/FuzzySelect/index.vue';
import moment from 'moment';
export default {
  props: ['visible', 'detail', 'isLook', 'modelTitle', 'dictData'],
  mixins: [institutionsMixin],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          this.formValue.enterpriseName = this.detail.enterpriseName;
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
            ...{ enterpriseName: this.formValue.enterpriseName },
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
      formConfig: [
        {
          field: 'module',
          title: '版块',
          element: 'a-select',
          rules: [{ required: true, message: '请输入版块' }],
          props: {
            options: this.dictData?.company_module || [],
            // options: [
            //   { value: '1', label: '上交所主板' },
            //   { value: '2', label: '创业板' },
            //   { value: '3', label: '科创板' },
            //   { value: '4', label: '其它' },
            // ],
            showSearch: true,
            optionFilterProp: 'children',
          },
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          element: (h, item, params) => {
            return (
              <FuzzySelect
                value={params?.enterpriseName}
                onChangeSelect={(val) => {
                  if (val) {
                    this.formValue = {
                      ...this.formValue,
                      enterpriseName: val.name,
                      usci: val.unifiedCreditCode,
                    };
                  } else {
                    this.formValue = {
                      ...this.formValue,
                      enterpriseName: '',
                      usci: '',
                    };
                  }
                }}
                disabled={this.modelTitle == 'see'}
              />
            );
          },
          // element: 'slot',
          // slotName: 'nameSlot',
          rules: [{ required: true, message: '请选择企业名称' }],
        },
        {
          field: 'usci',
          title: '统一社会信用代码',
          element: 'a-input',
          props: {
            disabled: true,
          },
          // rules: [
          // { required: true, validator: this.checkCode, trigger: 'change' },
          // ],
        },
        {
          field: 'csrcIndustry',
          title: '所属证监会行业',
          element: 'a-select',
          props: {
            options: this.dictData?.csrc_industry || [],
            showSearch: true,
            optionFilterProp: 'children',
          },
          rules: [{ required: true, message: '请选择所属证监会行业' }],
        },
        {
          field: 'stockAbbreviation',
          title: '股票简称',
          element: 'a-input',
          props: {
            maxLength: 30,
          },
          rules: [{ required: true, message: '请输入股票简称' }],
        },
        {
          field: 'stockCode',
          title: '股票代码',
          element: 'a-input',
          props: {
            maxLength: 30,
          },
          rules: [{ required: true, message: '请输入股票代码' }],
        },
        {
          field: 'establishedTime',
          title: '成立时间',
          element: 'a-date-picker',
          rules: [{ required: true, message: '请选择成立时间' }],
        },
        {
          field: 'marketTime',
          title: '上市时间',
          element: 'a-date-picker',
          rules: [{ required: true, message: '请选择上市时间' }],
        },
        {
          field: 'initialFinancing',
          title: '首发融资',
          element: 'slot',
          slotName: 'inital',
          rules: [
            { required: true, message: '请输入首发融资' },
            {
              pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
              trigger: 'blur',
              message: '请输入大于或等于零的数字，最多保留两位小数。',
            },
          ],
        },
      ],
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      this.formConfig[0].props.options = this.stateSelect;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        console.log(valid);
        if (valid) {
          this.formValue.establishedTime = this.formValue?.establishedTime
            ? moment(this.formValue?.establishedTime).format(
                'YYYY-MM-DD hh:mm:ss'
              )
            : '';
          this.formValue.marketTime = this.formValue.marketTime
            ? moment(this.formValue.marketTime).format('YYYY-MM-DD hh:mm:ss')
            : '';
          if (this.modelTitle === 'add') {
            this.saveCompany();
          } else {
            this.editCompany();
          }
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 新增
    async saveCompany() {
      const [, err] = await saveCompany(this.formValue);
      if (err) return;
      this.$message.success('新增成功!');
      this.handleCancel();
      this.$emit('loadData');
    },
    // 编辑
    async editCompany() {
      const [, err] = await editCompany(this.formValue);
      if (err) return;
      this.$message.success('编辑成功!');
      this.handleCancel();
      this.$emit('loadData');
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
