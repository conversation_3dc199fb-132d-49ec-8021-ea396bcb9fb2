export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'groupName',
      title: '报警群名称',
      props: {
        placeholder: '请输入报警群名称',
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { groupName: '' },
};
export const tableColumns = [
  {
    title: '报警群ID',
    field: 'dingNotifyId',
    key: 'dingNotifyId',
  },
  {
    title: '报警群名称',
    field: 'groupName',
    key: 'groupName',
  },
  {
    title: '操作',
    field: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 200,
    slots: { default: 'operation' },
  },
];

export const initForm = () => {
  return {
    groupName: '',
    secret: '',
    accessToken: '',
    phones: '',
  };
};

export const formRules = {
  groupName: [
    {
      required: true,
      message: '报警群名不能为空',
      trigger: 'blur',
      whitespace: true,
    },
  ],
  secret: [
    {
      required: true,
      message: '钉钉验证信息不能为空',
      trigger: 'blur',
      whitespace: true,
    },
  ],
  accessToken: [
    {
      required: true,
      message: '登录TOKEN不能为空',
      trigger: 'blur',
      whitespace: true,
    },
  ],
  phones: [
    { required: true, message: '手机号不能为空', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value) {
          try {
            let reg = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
            if (reg.test(value)) {
              callback();
            } else {
              callback(new Error('数据格式错误'));
            }
          } catch (err) {
            callback(new Error('数据格式错误'));
          }
        } else {
          callback(new Error('手机号不能为空'));
        }
      },
      trigger: 'blur',
    },
  ],
};
