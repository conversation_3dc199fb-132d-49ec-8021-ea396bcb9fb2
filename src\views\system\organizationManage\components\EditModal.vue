<template>
  <a-modal
    :title="title"
    :visible="visible"
    :loading="loading"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
    width="900px"
    @ok="submitForm"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="form" :rules="rules" v-bind="formLayout">
        <a-form-model-item label="租户名称">
          {{ merchantName }}
        </a-form-model-item>
        <a-form-model-item label="上级组织" prop="parentId">
          <treeselect
            style="line-height: 36px"
            v-model="form.parentId"
            :options="organizationTree"
            placeholder="请选择上级组织"
          />
        </a-form-model-item>
        <a-form-model-item label="组织名称" prop="organizeName">
          <a-input
            v-model="form.organizeName"
            placeholder="请输入组织名称"
            :maxLength="30"
            :default-expand-level="10"
          >
          </a-input>
        </a-form-model-item>
        <a-form-model-item label="联系人" prop="leader">
          <a-input
            :maxLength="50"
            v-model="form.leader"
            placeholder="请输入联系人"
          >
          </a-input>
        </a-form-model-item>
        <a-form-model-item label="联系电话" prop="phone">
          <a-input v-model="form.phone" placeholder="请输入联系电话"> </a-input>
        </a-form-model-item>
        <a-form-model-item label="邮箱" prop="email">
          <a-input
            v-model="form.email"
            :max-length="50"
            placeholder="请输入邮箱"
          >
          </a-input>
        </a-form-model-item>
        <!-- <a-form-model-item label="组织状态" prop="status">
          <a-radio-group v-model="form.status">
            <a-radio value="0">启用</a-radio>
            <a-radio value="1">停用</a-radio>
          </a-radio-group>
        </a-form-model-item> -->
        <a-form-model-item label="组织权限">
          <a-transfer
            v-if="visible"
            class="tree-transfer"
            :data-source="dataSource"
            :target-keys="targetKeys"
            :render="(item) => item.title"
            :show-select-all="false"
            @change="onChange"
          >
            <template
              slot="children"
              slot-scope="{
                props: { direction, selectedKeys },
                on: { itemSelect },
              }"
            >
              <a-tree
                v-if="direction === 'left'"
                blockNode
                checkable
                :replaceFields="replaceFields"
                checkStrictly
                defaultExpandAll
                :checkedKeys="[...selectedKeys, ...targetKeys]"
                :treeData="organizationTree"
                @check="
                  (_, props) => {
                    onChecked(
                      _,
                      props,
                      [...selectedKeys, ...targetKeys],
                      itemSelect
                    );
                  }
                "
                @select="
                  (_, props) => {
                    onChecked(
                      _,
                      props,
                      [...selectedKeys, ...targetKeys],
                      itemSelect
                    );
                  }
                "
              />
            </template>
          </a-transfer>
        </a-form-model-item>
        <a-form-model-item label="扩展权限">
          <a-select
            v-model="type"
            style="width: 200px; margin-right: 10px"
            :filter-option="filterOption"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            placeholder="请选择扩展权限"
            @change="value = []"
          >
            <a-select-option
              v-for="item in typeOptionsList"
              :key="item.dictId"
              :value="item.dictId"
              >{{ item.dictName }}</a-select-option
            >
          </a-select>
          <a-select
            v-model="value"
            mode="multiple"
            style="width: 355px"
            :filter-option="filterOption"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            placeholder="请选择扩展权限"
          >
            <a-select-option
              v-for="item in valueOptions"
              :key="item.dataId"
              :value="item.dataId"
              >{{ item.dictLabel }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import {
  addOrganize,
  updateOrganize,
  getOrganizeDetail,
} from '@/api/system/organization';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { initFormData, formLayout, rules } from '../constant.js';
import { aseDecrypt, rsaCode } from '@/utils/common/auth';
import { replaceFields } from '../constant';
import { getDicts } from '@/api/system/dict/data';
import { listType } from '@/api/system/dict/type';

function isChecked(selectedKeys, eventKey) {
  return selectedKeys.indexOf(eventKey) !== -1;
}

function handleTreeData(data, targetKeys = []) {
  data.forEach((item) => {
    item['disabled'] = targetKeys.includes(item.key);
    if (item.children) {
      handleTreeData(item.children, targetKeys);
    }
  });
  return data;
}

export default {
  components: {
    Treeselect,
  },
  props: {
    visible: Boolean,
    organizeId: String,
    parentId: String,
    organizationTree: Array,
  },
  data() {
    return {
      // 菜单树
      loading: false,
      formLayout,
      rules,
      replaceFields,
      form: initFormData(),
      targetKeys: [],
      dataSource: [],

      type: '',
      value: [],
      typeOptionsList: [],
      valueOptions: [],
    };
  },
  computed: {
    title() {
      return `${this.organizeId ? '编辑' : '新增'}组织架构`;
    },
    merchantName() {
      return this.$store.state?.base?.merchant?.merchantName;
    },
    treeData() {
      return handleTreeData(this.organizationTree, this.targetKeys);
    },
  },
  watch: {
    async visible(val) {
      if (val) {
        await this.initSelect();
        if (this.organizeId) {
          this.getDetail();
        } else {
          this.form = initFormData({
            parentId: this.parentId || undefined,
          });
        }
      } else {
        this.$refs.form && this.$refs.form.resetFields();
      }
    },
    type(val) {
      if (val) {
        this.getValueList();
      }
    },
  },
  methods: {
    async initSelect() {
      // 晴空数据
      this.dataSource = [];
      this.targetKeys = [];
      this.select;
      this.type = undefined;
      this.value = [];
      // 获取权限类型列表
      await this.getTypeList();
      this.flatten(this.organizationTree);
    },
    flatten(list = [], parentName) {
      list.forEach((item) => {
        this.dataSource.push({
          key: item.id,
          title: `${parentName ? parentName + '-' : ''}${item.label}`,
        });
        this.flatten(item.children, item.label);
      });
    },

    /**
     * 获取扩展权限类型--值
     */
    async getValueList() {
      this.loading = true;
      this.valueOptions = [];
      this.value = [];
      const dictCode = this.typeOptionsList.find(
        (item) => item.dictId === this.type
      )?.dictCode;
      const [result] = await getDicts(dictCode);

      this.loading = false;
      this.valueOptions = result?.data || [];
    },
    /**
     * 获取扩展权限类型
     */
    async getTypeList() {
      this.loading = true;
      const [result] = await listType({
        limit: 9999,
        page: 1,
        dictType: 'DATASOURCE',
      });
      this.loading = false;
      this.typeOptionsList = result?.data || [];
    },
    /**
     * 获取菜单详情
     */
    async getDetail() {
      this.loading = true;
      const [result, error] = await getOrganizeDetail({
        organizeId: this.organizeId,
      });
      this.loading = false;
      if (error || !result.data) {
        this.form = initFormData({
          parentId: this.parentId || undefined,
        });
        return;
      }
      const { email, phone, extendPermissions, permissionOrganizeIds, ...req } =
        result?.data;
      this.targetKeys = permissionOrganizeIds || [];
      const chooseType = Object.keys(extendPermissions);

      if (chooseType.length) {
        this.type = chooseType[0];
        this.$nextTick(() => {
          this.value = extendPermissions[chooseType[0]];
        });
      }

      const organizationData = {
        ...req,
        email: email ? aseDecrypt(email) : undefined,
        phone: phone ? aseDecrypt(phone) : undefined,
      };
      this.form = Object.assign(this.form, organizationData);
    },

    onChange(targetKeys) {
      // console.log('Target Keys:', targetKeys);
      this.targetKeys = targetKeys;
    },

    onChecked(_, e, checkedKeys, itemSelect) {
      const { eventKey } = e.node;
      itemSelect(eventKey, !isChecked(checkedKeys, eventKey));
    },
    /**
     * 提交按钮
     */
    submitForm() {
      if (this.loading) return;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          // this.loading = true;
          let extendPermissions = {};
          this.type && (extendPermissions[this.type] = this.value);
          const queryParams = {
            ...this.form,
            extendPermissions,
            permissionOrganizeIds: this.targetKeys,
            email: rsaCode(this.form.email),
            phone: this.form.phone ? rsaCode(this.form.phone) : '',
          };
          const [, error] = this.form.organizeId
            ? await updateOrganize(queryParams)
            : await addOrganize(queryParams);
          this.loading = false;
          if (error) return;
          this.$message.success(`${this.title}成功`);
          this.form.organizeId
            ? this.$emit('queryList')
            : this.$emit('refreshList');
          this.closeModal();
        }
      });
    },
    /**
     * 搜索
     */
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
