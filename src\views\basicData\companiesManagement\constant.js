import moment from 'moment';
// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    width: 60,
    fixed: 'left',
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    field: 'module',
    title: '板块',
    minWidth: 90,
    formatter: ({ cellValue }) => {
      return getStatus(cellValue);
    },
  },
  {
    field: 'usci',
    title: '统一社会信用代码',
    width: 180,
  },
  {
    field: 'enterpriseName',
    title: '企业名称',
    width: 200,
  },
  {
    field: 'csrcIndustry',
    title: '所属证监会行业',
    width: 200,
    formatter: ({ cellValue }) => {
      return getCsrcIndustry(cellValue);
    },
  },
  {
    field: 'stockAbbreviation',
    title: '股票简称',
    width: 110,
  },
  {
    field: 'stockCode',
    title: '股票代码',
    width: 110,
  },
  {
    field: 'establishedTime',
    title: '成立时间',
    width: 110,
    formatter: ({ cellValue }) => {
      return moment(cellValue).format('YYYY-MM-DD');
    },
  },
  {
    field: 'marketTime',
    title: '上市时间',
    width: 110,
    formatter: ({ cellValue }) => {
      return moment(cellValue).format('YYYY-MM-DD');
    },
  },
  {
    field: 'initialFinancing',
    title: '首发融资(亿元)',
    width: 130,
  },
  {
    field: 'updateBy',
    title: '更新人',
    minWidth: 120,
  },
  {
    field: 'updateTime',
    title: '更新时间',
    minWidth: 180,
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 100,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'enterpriseName',
    title: '企业名称',
    // element: 'slot',
    // slotName: 'enterSlot',
    element: 'a-input',
  },
  {
    field: 'usci',
    title: '统一社会信用代码',
    itemProps: {
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
    },
    // element: 'slot',
    // slotName: 'enterSlot',
    // element: 'a-input',
  },
];

export const initFormValue = () => {
  return {
    enterprise: {},
    module: undefined,
    enterpriseId: '',
    usci: '',
    enterpriseName: '',
    csrcIndustry: undefined,
    stockAbbreviation: '',
    stockCode: '',
    establishedTime: '',
    marketTime: '',
    initialFinancing: '',
  };
};

export const getStatus = (code) => {
  switch (code) {
    case '1':
      return '上交所主板';
    case '2':
      return '创业板';
    case '3':
      return '科创板';
    case '4':
      return '其它';
  }
};

export const getCsrcIndustry = (code) => {
  switch (code) {
    case '1':
      return '制造业';
    case '2':
      return '信息传输、软件和信息技术服务业';
    case '3':
      return '生物医药';
    case '4':
      return '电子设备';
    case '5':
      return '其它';
  }
};
