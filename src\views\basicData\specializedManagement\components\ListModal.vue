<template>
  <a-modal
    width="800px"
    :title="
      modelTitle === 'add' ? '新增' : modelTitle === 'see' ? '详情' : '编辑'
    "
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="ruleForm"
        :config="formConfig"
        :params="formValue"
        :preview="preview"
      >
        <!-- 企业 -->
        <template #year>
          <BuseRangePicker
            type="year"
            :needShowSecondPicker="() => false"
            v-model="formValue.year"
            placeholder="请选择年份"
            format="YYYY"
            :disableDateFunc="disableDateFunc"
          />
        </template>
        <template #enterpriseName="{ params }">
          <FuzzySelect
            v-model="params.enterpriseName"
            @changeSelect="handlerChange"
            :disabled="modelTitle == 'see'"
          ></FuzzySelect>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment';
import { initFormValue } from '../constant';
import { saveProEmphasis, editProEmphasis } from '@/api/basicData';
import { institutionsMixin } from '../../mixins/institutionsMixin';
import FuzzySelect from '@/components/FuzzySelect/index.vue';
import { initParams } from '@/utils';

export default {
  props: ['visible', 'detail', 'preview', 'isLook', 'modelTitle', 'dictData'],
  mixins: [institutionsMixin],
  components: { FuzzySelect },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) {
            this.formValue = initParams(this.formConfig);
            console.log(this.formValue, 'form');
            return;
          }
          this.formValue.enterpriseName = this.detail.enterpriseName;
          this.formValue.enterpriseId = this.detail.enterpriseId;
          this.formValue = {
            ...initParams(this.formConfig),
            ...this.detail,
            year: {
              endOpen: false,
              endValue: null,
              startOpen: true,
              startValue: moment(this.detail.year),
            },
          };
        } else {
          this.formValue = initParams(this.formConfig);
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: {},
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  computed: {
    formConfig() {
      return [
        {
          field: 'enterpriseName',
          element: 'slot',
          slotName: 'enterpriseName',
          title: '企业名称',
          rules: [{ required: true, message: '请选择企业名称' }],
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          props: {
            placeholder: '请输入统一社会信用代码',
            disabled: true,
          },
        },
        {
          field: 'registeredAddress',
          title: '企业地址',
          props: {
            placeholder: '请输入企业地址',
            disabled: true,
            maxLength: 50,
          },
        },
        {
          field: 'parkName',
          title: '所属园区',
          props: {
            placeholder: '请输入所属园区',
            disabled: true,
          },
        },
        {
          field: 'establishedTime',
          title: '成立时间',
          props: {
            placeholder: '请输入成立时间',
            disabled: true,
          },
        },
        {
          field: 'year',
          title: '认定年份',
          element: 'slot',
          slotName: 'year',
          rules: [
            { required: true, message: '请选择认定年份' },
            {
              validator: (rule, value, callback) => {
                if (value && value.startValue) {
                  callback();
                } else {
                  callback('请选择认定年份');
                }
              },
              trigger: 'blur',
            },
          ],
        },
        {
          field: 'determineStatus',
          title: '认定状态',
          element: 'a-select',
          props: {
            placeholder: '请选择认定状态',
            options: this.dictData.determine_status,
            filterOption: (inputValue, option) => {
              return (
                option.children
                  .toLowerCase()
                  .indexOf(inputValue.toLowerCase()) >= 0
              );
            },
          },
          rules: [{ required: true, message: '请选择认定状态' }],
        },
        {
          field: 'remark',
          title: '备注',
          props: {
            placeholder: '请输入备注',
            type: 'textarea',
            maxLength: 30,
          },
        },
        {
          field: 'type',
          title: '认定级别',
          element: 'a-select',
          props: {
            options: this.dictData?.recognition_level || [],
            showSearch: true,
            optionFilterProp: 'children',
          },
          on: {
            change: (val) => {
              this.formValue = {
                ...this.formValue,
                fieldIndustry: '',
                appearMarket: undefined,
                contacts: '',
                contactInformation: '',
                productName: '',
              };
              this.$refs.ruleForm && this.$refs.ruleForm.resetFields();
            },
          },
          rules: [{ required: true, message: '请选择认定级别' }],
        },
        ...(this.formValue.type == '1'
          ? [
              {
                field: 'fieldIndustry',
                title: '领域',
                props: {
                  placeholder: '请输入领域',
                  type: 'textarea',
                  maxLength: 30,
                },
                rules: [{ required: true, message: '请输入领域' }],
              },
              {
                field: 'appearMarket',
                title: '是否上市',
                element: 'a-select',
                props: {
                  options: this.dictData?.appear_market || [],
                },
                rules: [{ required: true, message: '请输入' }],
              },
              {
                field: 'contacts',
                title: '联系人',
                element: 'a-input',
                props: {
                  maxLength: 10,
                  placeholder: '请输入联系方式',
                },
                rules: [{ required: true, message: '请输入联系人' }],
              },
              {
                field: 'contactInformation',
                title: '联系方式',
                element: 'a-input',
                props: {
                  maxLength: 20,
                  placeholder: '请输入联系方式',
                },
                rules: [
                  { required: true, message: '请输入联系方式' },
                  // {
                  //   pattern: /^1[3456789]\d{9}$/,
                  //   message: '请输入正确联系方式',
                  //   trigger: 'blur',
                  // },
                ],
              },
            ]
          : this.formValue.type == '3'
          ? [
              {
                field: 'productName',
                title: '产品名称',
                element: 'a-input',
                props: {
                  maxLength: 30,
                },
                rules: [{ required: true, message: '请输入产品名称' }],
              },
            ]
          : []),
        {
          field: 'supportAmount',
          title: '支持金额 (万元)',
          props: {
            placeholder: '请输入支持金额',
            min: 0,
            max: 99999999999,
            step: 0.11,
          },
          defaultValue: 0,
          element: 'a-input-number',
          rules: [
            { required: true, message: '请输入支持金额' },
            {
              pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
              trigger: 'blur',
              message: '请输入大于或等于零的数字，最多保留两位小数。',
            },
          ],
        },
      ];
    },
  },
  mounted() {},
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      this.formConfig[0].props.options = this.stateSelect;
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.formValue,
            year: this.formValue.year?.startValue
              ? moment(this.formValue.year?.startValue).format('YYYY')
              : '',
          };
          if (this.modelTitle === 'add') {
            this.saveProEmphasis({
              ...params,
              appearMarket: params.appearMarket || '',
            });
          } else {
            this.editProEmphasis({
              ...params,
              appearMarket: params.appearMarket || '',
            });
          }
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    // 新增
    async saveProEmphasis(params) {
      const [res, err] = await saveProEmphasis(params);
      if (err) return;
      this.$message.success('新增成功!');
      this.handleCancel();
      this.$emit('loadData');
    },
    // 编辑
    async editProEmphasis(params) {
      const [, err] = await editProEmphasis(params);
      if (err) return;
      this.$message.success('编辑成功!');
      this.handleCancel();
      this.$emit('loadData');
    },
    handlerChange(val) {
      if (val) {
        this.formValue = {
          ...this.formValue,
          enterpriseName: val.name,
          unifiedCreditCode: val.unifiedCreditCode,
          registeredAddress: val.address,
          parkName: val.parkName,
          parkId: val.parkId,
          establishedTime: val.registerDate,
        };
      } else {
        this.formValue = {
          ...this.formValue,
          enterpriseName: '',
          unifiedCreditCode: '',
          registeredAddress: '',
          parkName: '',
          parkId: '',
          establishedTime: '',
        };
      }
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
