<template>
  <div>
    <a-tabs v-model="activeKey" @change="tabChange">
      <a-tab-pane v-for="item in tabList" :key="item.id" :tab="item.name">
        <div v-if="item.visible">
          <import-page :pageName="item.pageName"></import-page>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import importPage from '@/views/home/<USER>';

export default {
  name: 'index',
  components: { importPage },
  data() {
    return {
      activeKey: '1',
      tabList: [
        {
          id: '1',
          name: '基础信息',
          pageName: 'basicInformation',
          visible: false,
        },
        {
          id: '2',
          name: '资质资格',
          pageName: 'qualification',
          visible: false,
        },
        {
          id: '3',
          name: '软件著作权',
          pageName: 'copyrights',
          visible: false,
        },
        {
          id: '4',
          name: '商标信息',
          pageName: 'trademarkInformation',
          visible: false,
        },
        {
          id: '5',
          name: '专利信息',
          pageName: 'patentInformation',
          visible: false,
        },
        {
          id: '6',
          name: '作品著作权',
          pageName: 'worksCopyright',
          visible: false,
        },
      ],
    };
  },
  mounted() {
    this.tabChange();
  },
  methods: {
    // tab切换
    tabChange() {
      this.tabList.forEach((item) => {
        if (item.id === this.activeKey) {
          item.visible = true;
        } else {
          item.visible = false;
        }
      });
    },
  },
};
</script>

<style scoped></style>
