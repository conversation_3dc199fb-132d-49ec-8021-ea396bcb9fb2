module.exports = {
  vueTemplate: `<template>
  <PageWrapper
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      createText="新建"
      @loadData="loadData"
      @handleCreate="handleCreate"
  >
    <!-- table插槽 -->
    <template #operate="{ row }">
      <span class="operate-button" @click="onClickLook(row)">投放资源</span>
      <span class="operate-button" @click="onClickEdit(row)">编辑展位</span>
    </template>
  </PageWrapper>
</template>
<script>
import {defaultFilterConfig, defaultTableColumn} from './config';

export default {
  data() {
    return {
      loading: false,
      visible: false,
      tablePage: {total: 0, currentPage: 1, pageSize: 10},
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
        params: {
          name: '',
          state: '',
          identity: '',
          birthday: '',
          birthdayRange: ['', ''],
        }, // 筛选器结果数据
      },
      tableColumn: defaultTableColumn(),
      tableData: [],
      modalData: null,
      isLook: false,
    };
  },
  methods: {
    loadData() {
    },
    handleCancel() {
    },
    handleCreate() {
			// 新建
    },
    onClickLook() {
    },
    onClickEdit() {
    },
  },
};
</script>
<style lang="less"></style>
`,
  configTemplate: `
export const defaultTableColumn = () => [
  {
    field: 'id',
    title: '展位标识',
  },
  {
    field: 'img',
    title: '展位图片',
  },
  {
    field: 'name',
    title: '展位名称',
  },
  {
    field: 'app',
    title: '投放端',
  },
  {
    field: 'page',
    title: '所属页面',
  },
  {
    field: 'type',
    title: '展位类型',
  },
  {
    field: 'count',
    title: '投放数量',
  },
  {
    field: 'change',
    title: '最后修改',
  },
  {
    field: 'statue',
    title: '状态',
  },
  {
    field: 'operation',
    slots: { default: 'operate' },
    title: '操作',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    key: 'name',
    type: 'select',
    label: '现状',
    defaultValue: '',
    options: [
      { value: '', label: '全部' },
      { value: 'enable', label: '坐牢' },
      { value: 'disable', label: '打篮球' },
    ],
  },
];
export const editModalConfig = () => [
  {
    label: '展位名称',
    type: 'input',
    key: 'name',
    placeholder: '请输入展位名称',
    rules: [{ required: true, message: '请输入展位名称' }],
  },
  {
    label: '展位标识',
    type: 'text',
    key: 'age',
    placeholder: '请输入展位标识',
    rules: [{ required: true, message: '请输入展位标识' }],
    width: 120,
    unit: '岁',
  },
  {
    label: '所属页面',
    type: 'select',
    key: 'state',
    placeholder: '请选择所属页面',
    rules: [{ required: true, message: '请选择所属页面' }],
    options: [
      {
        label: '坐牢',
        value: '坐牢',
      },
      {
        label: '打篮球',
        value: '打篮球',
      },
    ],
  },
  {
    label: '投放资源描述',
    type: 'slot',
    key: 'desc',
    slotName: 'desc',
    rules: [{ required: true, message: '请输入描述内容' }],
  },
];
`,
};
