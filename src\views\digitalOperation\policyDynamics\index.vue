<template>
  <page-layout>
    <div class="policy-dynamics">
      <div class="policy-dynamics-seacher">
        <div class="policy-dynamics-seacher-left">
          <img src="@/assets/icons/policy.png" alt="" />
          <span class="policy-dynamics-seacher-left-logo">政策动态</span>
        </div>

        <div class="policy-dynamics-seacher-right">
          <img src="@/assets/icons/search.png" alt="" />
          <a-input v-model.trim="searchValue" placeholder="请输入关键词" />
          <a-button type="primary" @click="searchHander">搜索</a-button>
        </div>
      </div>
      <div class="policy-dynamics-content">
        <div class="policy-dynamics-content-left">
          <div class="policy-dynamics-content-left-top">
            <img src="@/assets/icons/knowledeg.png" alt="" /><span class="text"
              >信息分类</span
            >
          </div>
          <div
            class="policy-dynamics-content-left-detail"
            :style="
              isMore
                ? 'max-height:58vh;overflow: scroll;'
                : ' overflow: hidden;'
            "
          >
            <div
              v-for="(item, index) in knowledgeListTemp"
              :key="index"
              class="detail-item"
            >
              <div class="item-header">
                <h6>{{ item.label }}</h6>
                <a
                  :style="{ marginLeft: '8px', fontSize: '12px' }"
                  @click="toggle(item)"
                >
                  {{ item.expand ? '收起' : '展开' }}
                  <a-icon :type="item.expand ? 'up' : 'down'" />
                </a>
              </div>
              <div
                class="item-tags"
                :style="item.expand ? '' : 'max-height: 76px;min-height:38px'"
              >
                <a-tag
                  v-for="(tag, tagIndex) in item.children"
                  :key="tagIndex"
                  color="#f7f8fa"
                  @click="tagClick(tag)"
                  :class="[tag.checked && 'tag-checked']"
                >
                  {{ tag.label }}
                </a-tag>
              </div>
            </div>
          </div>
          <div :class="['more', isMore && 'is-more']">
            <a @click="moreHander()">
              更多
              <a-icon type="double-left" />
            </a>
          </div>
        </div>
        <div class="policy-dynamics-content-right">
          <div>
            <div class="policy-dynamics-content-right-top">
              <img src="@/assets/icons/suggest.png" alt="" />
              <span>推荐</span>
              <div class="policy-dynamics-content-right-top-tabs">
                <a-tabs v-model="activeKey" @change="tabChange">
                  <a-tab-pane
                    v-for="item in tabList"
                    :key="item.id"
                    :tab="item.name"
                  >
                  </a-tab-pane>
                </a-tabs>
              </div>
            </div>
            <div
              v-if="list && list.length > 0"
              class="policy-dynamics-content-right-detail"
            >
              <div
                v-for="(item, index) in list"
                :key="index"
                class="item"
                @click="itemClick(item)"
              >
                <h4>{{ item.title }}</h4>
                <p>{{ item.displayContent }}</p>
                <div class="item-bottom">
                  <div>
                    <img :src="item.imgUrl" alt="" />
                    <span class="item-name">{{ item.author }}</span>
                  </div>
                  <div class="item-nums">
                    <div>
                      <a-icon type="eye" /><span>{{
                        item.totalRead || 0
                      }}</span>
                    </div>
                    <div>
                      <a-icon type="download" /><span>{{
                        item.totalRecive || 0
                      }}</span>
                    </div>
                    <div>
                      <a-icon type="link" /><span>{{
                        item.attachmentsCount
                      }}</span>
                    </div>

                    <div>发布时间：{{ item.releaseTime }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="policy-dynamics-content-right-detail empty">
              <div>
                <img src="@/assets/images/empty.png" alt="" />
                <div>暂无相关内容</div>
              </div>
            </div>
          </div>
          <div v-if="list && list.length > 0" class="pagination">
            <a-pagination
              size="small"
              :total="total"
              :default-current="1"
              @change="onPageChange"
              @showSizeChange="showSizeChange"
              show-size-changer
              show-quick-jumper
              :show-total="(total) => `共 ${total} 条`"
            />
          </div>
        </div>
      </div>
    </div>
  </page-layout>
</template>

<script>
import imgUrl from '@/assets/icons/ava.png';
import { articleList } from '@/api/digitalOperation/policyDynamics/index.js';
import { categoryList } from '@/api/digitalOperation/managementCenter/classManagement/index.js';
export default {
  name: 'PolicyDynamics',

  data() {
    return {
      isMore: false, //是否展示更多
      imgUrl,
      searchValue: '',
      activeKey: '0',
      tabList: [
        {
          id: '0',
          name: '图文',
        },
        {
          id: '1',
          name: '图片',
        },
        {
          id: '2',
          name: '文档',
        },
      ],

      list: [],
      knowledgeList: [],
      knowledgeListTemp: [],
      params: {
        title: '', //标题
        articleType: '0', //文章类型
        firstClassId: '', //一级类别
        secondClassId: '', //二级类别
        secondClassName: '',
        pageNum: 1,
        limit: 10,
        state: '1', //当前状态（0保存，1上架，，2下架）
      },
      total: 0,
    };
  },

  mounted() {
    this.categoryList();
    this.articleList();
  },

  methods: {
    //类别查询
    async categoryList() {
      const [res, err] = await categoryList();
      if (err) return;
      let data = res?.data;
      data = JSON.parse(
        JSON.stringify(data)
          .replace(/tkbCategoryRes/g, 'children')
          .replace(/categoryName/g, 'label')
          .replace(/id/g, 'value')
      );
      let arr = this.addExpand(data);
      this.knowledgeList = arr;
      this.knowledgeListTemp = this.knowledgeList.slice(0, 3);
    },
    async articleList() {
      const [res, err] = await articleList(this.params);
      if (err) return;
      this.list = res.data;
      this.total = res.total;
    },
    //左边数据增加是否展开expand字段
    addExpand(data) {
      data.forEach((element) => {
        element.expand = false;
        element.children.forEach((item) => {
          item.checked = false;
        });
      });
      return data;
    },
    tagClick(tag) {
      let flag = tag.checked;
      this.knowledgeListTemp.forEach((element) => {
        element.children.forEach((item) => {
          item.checked = false;
        });
      });
      if (!flag) {
        tag.checked = true;
        this.params.secondClassId = tag.value;
        this.params.secondClassName = tag.label;
      } else {
        this.params.secondClassId = '';
        this.params.secondClassName = '';
      }

      this.articleList();
    },
    onPageChange(pageNumber) {
      this.params.pageNum = pageNumber;
      this.articleList();
    },
    showSizeChange(pageNumber, pageSize) {
      this.params.limit = pageSize;
      this.articleList();
    },
    searchHander() {
      this.params.title = this.searchValue;
      this.articleList();
    },
    itemClick(item) {
      this.$router.push({
        path: '/policyResource/policyDynamics/articleDetail',
        query: {
          id: item.articleId,
        },
      });
    },
    toggle(item) {
      item.expand = !item.expand;
    },
    moreHander() {
      this.isMore = !this.isMore;
      this.isMore
        ? (this.knowledgeListTemp = this.knowledgeList)
        : (this.knowledgeListTemp = this.knowledgeList.slice(0, 3));
    },
    tabChange(tab) {
      this.params.articleType = tab;
      this.articleList();
    },
  },
};
</script>

<style lang="less" scoped>
.policy-dynamics {
  background-color: #fff;
  min-height: 700px;
  padding: 24px;
  .policy-dynamics-seacher {
    // width: 780px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32px;
    .policy-dynamics-seacher-left {
      width: 180px;
      display: flex;
      align-items: center;
      justify-content: center;

      .policy-dynamics-seacher-left-logo {
        margin-left: 8px;
        width: 112px;
        height: 28px;
        font-family: Alimama ShuHeiTi;
        font-size: 28px;
        font-weight: bold;
        line-height: 28px;
        background: linear-gradient(270deg, #1890ff 0%, #43bdff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    .policy-dynamics-seacher-right {
      width: 600px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 48px;
      border-radius: 12px;
      padding: 8px;
      background: #f7f8fa;
      .ant-input:focus {
        box-shadow: none;
      }
      img {
        margin: 6px 8px;
        width: 20px;
        height: 20px;
      }
      .ant-input {
        background-color: #ffffff00;
        background-image: none;
        border: none;
      }
      button {
        width: 80px;
        height: 32px;
        border-radius: 6px;
        padding: 0px 24px;
        color: #fff;
        border: 0;
        background: linear-gradient(270deg, #1890ff 0%, #43bdff 100%);
      }
    }
  }
  .policy-dynamics-content {
    width: 100%;
    // height: 69.4vh;
    display: flex;
    .policy-dynamics-content-left {
      background: url('@/assets/images/knowledeg-bg.png') no-repeat;
      background-size: 100% 282px;
      background-color: #f7f8fa;
      width: 505px;
      // min-height: 65vh;
      margin-right: 12px;
      padding: 12px;

      .policy-dynamics-content-left-top {
        display: flex;
        align-items: center;
        height: 29px;
        img {
          margin-right: 6.5px;
          width: 17.43px;
          height: 20.31px;
        }
        .text {
          font-family: Alimama ShuHeiTi;
          font-size: 20px;
          font-weight: bold;
          line-height: 29px;
          letter-spacing: 0px;
          color: #333333;
        }
      }

      .policy-dynamics-content-left-detail {
        position: relative;
        margin-top: 12px;
        width: 100%;
        border-radius: 12px 12px 0px 0px;
        padding: 12px;
        background: #fff;

        a {
          color: #1890ff;
        }
        .detail-item:not(:first-child) {
          margin-top: 20px;
        }
        .item-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          h6 {
            font-family: HarmonyOS Sans SC;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            color: #333333;
            margin-bottom: 0;
          }
        }
        .item-tags {
          margin-bottom: 8px;
          overflow: hidden;

          .tag-checked {
            background-color: #edf7ff !important;
            color: #1890ff;
          }
          span {
            margin: 8px 0 0 4px;
            color: #767676;
            padding: 2px 6px;
            font-size: 14px;
          }
        }
      }
      .more {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60px;
        padding: 32px 0 12px;
        bottom: 12px;
        background-color: #fff;
        a {
          color: #1890ff;
        }
        i {
          transform: rotate(-90deg);
        }
      }
      .is-more {
        i {
          transform: rotate(90deg);
        }
      }
    }
    .policy-dynamics-content-right {
      position: relative;
      width: 993px;
      background: url('@/assets/images/suggest-bg.jpg') no-repeat;
      background-size: 100% 452px;
      // min-height: 65vh;
      background-color: #f7f8fa;
      padding: 12px 12px 60px 12px;

      .policy-dynamics-content-right-top {
        display: flex;
        align-items: center;
        height: 29px;
        img {
          margin-right: 6.5px;
          width: 17.71px;
          height: 20.31px;
        }
        span {
          font-family: Alimama ShuHeiTi;
          font-size: 20px;
          font-weight: bold;
          line-height: 29px;
          letter-spacing: 0px;
          color: #333333;
        }
        .policy-dynamics-content-right-top-tabs {
          /deep/.ant-tabs-bar {
            background: #ffffff00;
            margin-bottom: 0;
            border: 0;
          }
          /deep/.ant-tabs-nav-wrap {
            // width: 116px;
            .ant-tabs-nav .ant-tabs-tab {
              padding: 4px;
              margin: 0 8px;
              color: #767676;
            }
            .ant-tabs-nav .ant-tabs-tab-active {
              color: #333;
            }
          }
          /deep/ .ant-tabs-ink-bar {
            background-color: #1890ff;
            width: 16px !important;
            height: 3px;
            border-radius: 2px;
            margin-left: 8px;
          }
        }
      }
      .policy-dynamics-content-right-detail {
        margin-top: 12px;
        width: 100%;
        height: 60vh;
        overflow: scroll;
        .item {
          width: 100%;
          padding: 12px;
          margin-bottom: 12px;
          height: 128px;
          border-radius: 8px;
          background: #ffffff;
          h4 {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            height: 24px;
            font-family: HarmonyOS Sans SC;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            color: #333333;
          }
          p {
            height: 44px;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            font-family: HarmonyOS Sans SC;
            font-size: 14px;
            font-weight: normal;
            line-height: 22px;
            color: #666666;
          }
          .item-bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-family: AlibabaPuHuiTi;
            font-size: 12px;
            font-weight: normal;
            line-height: 12px;
            color: #999999;

            img {
              width: 20px;
              height: 20px;
              border-radius: 10px;
              margin-right: 4px;
            }
            .item-nums {
              display: flex;
              div {
                margin-left: 16px;
                /deep/i {
                  margin-right: 4px;
                }
              }
            }
          }
        }
      }
      .pagination {
        position: absolute;
        height: 40px;
        padding-top: 24px;
        bottom: 24px;
        right: 24px;
      }
    }
    .empty {
      width: 993px;
      // height: 65vh;
      display: flex;
      align-items: center;
      margin-top: 220px;
      justify-content: center;
      div {
        // height: 22px;
        font-family: HarmonyOS Sans SC;
        font-size: 15px;
        font-weight: normal;
        line-height: 22px;
        text-align: center;
        color: #6f7580;
      }
      // img {
      //   margin-top: 50px;
      // }
    }
  }
}
</style>
