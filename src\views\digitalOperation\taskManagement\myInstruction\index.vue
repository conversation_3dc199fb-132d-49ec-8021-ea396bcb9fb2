<template>
  <BuseCrud
    ref="crud"
    title="我的批示"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @rowView="rowView"
  >
    <template slot="defaultHeader">
      <a-button @click="exportHandle">导出</a-button>
    </template>
  </BuseCrud>
</template>

<script>
import { getDicts } from '@/api/system/dict/data';
import {
  taskList,
  downloadInformation,
} from '@/api/digitalOperation/taskManagement/taskList.js';
import { getTableColumn } from './myinstruction';
import { resolveBlob } from '@/utils/common/fileDownload';
export default {
  name: 'ManagementTkIndex',

  data() {
    return {
      taskStatus: [],
      instructionStatus: [],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      params: {
        taskName: undefined,
        content: undefined,
        creatorName: undefined,
        taskStatus: undefined,
        transactorTime: undefined,
        tag: '2', //列表标签 1:任务列表     2:我的批示   3:我的待办
      },
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        formLayoutConfig: {
          defaultColSpan: 12,
        },
      };
    },
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            title: '任务名称',
            field: 'taskName',
          },
          {
            title: '任务内容',
            field: 'content',
          },
          {
            title: '创建人',
            field: 'creatorName',
          },
          {
            title: '任务状态',
            field: 'taskStatus',
            element: 'a-select',
            props: {
              options: this.taskStatus,
            },
          },
          {
            title: '办理时限',
            field: 'transactorTime',
            element: 'a-range-picker',
          },
        ],
      };
    },
  },

  created() {
    this.getInstructionStatus();
    this.getTaskStatus();
    this.tableColumn = getTableColumn.call(this);
  },
  mounted() {
    this.loadData();
  },

  methods: {
    modalConfirmHandler() {},
    async loadData() {
      this.loading = true;
      let p = this.paramsHandle();
      //获取我的批示列表
      const [res, err] = await taskList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    paramsHandle() {
      let startTime = this.params.transactorTime?.[0]?.format('YYYY-MM-DD');
      let endTime = this.params.transactorTime?.[1]?.format('YYYY-MM-DD');
      let p = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
        startTime,
        endTime,
      };
      delete p.transactorTime;
      return p;
    },
    // 导出
    async exportHandle() {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
      let p = this.paramsHandle();
      const [res] = await downloadInformation(p);
      resolveBlob(res, mimeMap.xlsx, '我的批示', '.xls');
    },
    async getTaskStatus() {
      const [res, err] = await getDicts(['task_state']);
      if (err) return;
      this.taskStatus = res.data.map((item) => {
        return {
          value: item.dictValue,
          label: item.dictLabel,
        };
      });
    },
    async getInstructionStatus() {
      const [res, err] = await getDicts(['instruction_status']);
      if (err) return;
      this.instructionStatus = res.data.map((item) => {
        return {
          value: item.dictValue,
          label: item.dictLabel,
        };
      });
    },
    rowView(row) {
      this.$router.push({
        path: '/taskTarget/taskManagement/instructionDetail',
        query: {
          id: row.id,
          detailPageType: 'myInstruction', //我的批示
        },
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
