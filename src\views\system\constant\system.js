// 状态
export const statusEnum = {
  ENABLED: '0',
  DISABLED: '1',
  LOCKED: '2',
};

// 状态字符串类型
export const statusEnum2String = {
  ENABLED: 'NORMAL',
  DISABLED: 'ABNORMAL',
};
export const statusOptions = [
  {
    value: statusEnum.ENABLED,
    label: '启用',
    color: 'success',
  },
  {
    value: statusEnum.DISABLED,
    label: '停用',
    color: 'default',
  },
];
export const statusOptionsWithAll = [
  {
    value: '',
    label: '全部',
  },
  {
    value: statusEnum2String.ENABLED,
    label: '启用',
  },
  {
    value: statusEnum2String.DISABLED,
    label: '停用',
  },
];
export const statusOptions3 = [
  ...statusOptions,
  {
    value: statusEnum.LOCKED,
    label: '锁定',
  },
];

export const getStatusLabel = (value) => {
  return statusOptions3.find((item) => item.value === value)?.label;
};
export const getStatusBadgeColor = (value) => {
  return statusOptions3.find((item) => item.value === value)?.color;
};
export const getStatusOptionsWithAll = (value) => {
  return statusOptionsWithAll.find((item) => item.value === value)?.label;
};

export const statusOptions2String = [
  {
    value: statusEnum2String.ENABLED,
    label: '生效',
  },
  {
    value: statusEnum2String.DISABLED,
    label: '失效',
  },
];
export const statusOptionsSecond = [
  {
    value: 'NORMAL',
    label: '启用',
  },
  {
    value: 'ABNORMAL',
    label: '停用',
  },
];

export const getStatusLabel2String = (value) => {
  return statusOptions.find((item) => item.value === value)?.label;
};
// 第二种转化状态的模式
export const getStatusLabelSecond = (value) => {
  return statusOptionsSecond.find((item) => item.value === value)?.label;
};

// 是否可见
export const visibleEnum = {
  INVISIBLE: '0',
  VISIBLE: '1',
};

export const visibleOptions = [
  {
    value: visibleEnum.VISIBLE,
    label: '显示',
    badgeStatus: 'processing',
  },
  {
    value: visibleEnum.INVISIBLE,
    label: '隐藏',
    badgeStatus: 'default',
  },
];
export const getVisibleLabel = (value) => {
  return visibleOptions.find((item) => item.value === value)?.label;
};
export const getVisibleBadgeInfo = (value) => {
  const item = visibleOptions.find((item) => item.value === value);
  const { label = '--', badgeStatus } = item || {};
  return {
    text: label,
    status: badgeStatus,
  };
};

// 密码格式验证正则
export const PASSWORD_REGEX =
  /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,}/;
// /^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$/;
// /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,}/
export const PASSWORD_REGEX_DESC =
  '必须是包含大小写字母、数字和特殊符号的8位以上组合';
