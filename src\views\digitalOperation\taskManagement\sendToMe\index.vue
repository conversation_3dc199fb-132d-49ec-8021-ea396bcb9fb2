<template>
  <BuseCrud
    ref="crud"
    title="抄送我的"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @rowView="rowView"
  >
    <template slot="defaultHeader">
      <a-button type="primary" class="mr-10" @click="handleHasRead"
        >标记已读</a-button
      >
    </template>
  </BuseCrud>
</template>

<script>
import { getTaskStatus, getReadState } from '../utils/index';
import { getTableColumn } from './sendToMe.js';
import {
  sendToMeList,
  isRead,
} from '@/api/digitalOperation/taskManagement/sendToMe.js';

export default {
  name: 'SendToMe',

  data() {
    return {
      tableData: [],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      taskStatus: [],
      readStatus: [],
      params: {
        taskName: undefined,
        content: undefined,
        transactorName: undefined,
        taskStatus: undefined,
        transactorTime: undefined,
        ifRead: undefined,
      },
    };
  },
  created() {
    this.tableColumn = getTableColumn.call(this);
  },
  computed: {
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            title: '任务名称',
            field: 'taskName',
          },
          {
            title: '任务内容',
            field: 'content',
          },
          {
            title: '办理人',
            field: 'transactorName',
          },
          {
            title: '任务状态',
            field: 'taskStatus',
            element: 'a-select',
            props: {
              options: this.taskStatus,
            },
          },
          {
            title: '办理时限',
            field: 'transactorTime',
            element: 'a-range-picker',
          },
          {
            title: '阅读状态',
            field: 'ifRead',
            element: 'a-select',
            props: {
              options: this.readStatus,
            },
          },
        ],
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },

  mounted() {
    this.loadData();
    getTaskStatus().then((res) => {
      this.taskStatus = res;
    });
    getReadState().then((res) => {
      this.readStatus = res;
    });
  },

  methods: {
    async loadData() {
      this.loading = true;
      let p = this.paramsHandle();
      const [res, err] = await sendToMeList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    paramsHandle() {
      let startTime = this.params.transactorTime?.[0]?.format('YYYY-MM-DD');
      let endTime = this.params.transactorTime?.[1]?.format('YYYY-MM-DD');
      let p = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
        startTime,
        endTime,
      };
      delete p.transactorTime;
      return p;
    },
    async isRead(arr) {
      const [res, err] = await isRead(arr);
      if (err) return;
      console.log(res);
      this.$message.success('标记已读成功');
      this.loadData();
    },
    handleHasRead() {
      const records = this.$refs.crud.getVxeTableRef().getCheckboxRecords();
      console.log(
        '-----已读',

        records
      );

      // if(records.length == 1 && )
      this.readConfirm(records);
    },
    readConfirm(records) {
      let arr = [];
      records.forEach((item) => {
        if (item.ifRead == '0') {
          arr.push(item.id);
        }
      });
      if (!arr.length) return this.$message.error('请至少选择一条未读的任务');
      let that = this;
      this.$confirm({
        content: `确认要‘标记已读’所选任务吗？`,
        onOk() {
          that.isRead(arr);
        },
        cancelText: '取消',
      });
    },
    rowView(row) {
      this.$router.push({
        path: '/taskTarget/taskManagement/instructionDetail',
        query: {
          id: row.taskId, //抄送我的传taskId
          detailPageType: 'sendToMe', //抄送
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.mr-10 {
  margin-right: 10px;
}
</style>
