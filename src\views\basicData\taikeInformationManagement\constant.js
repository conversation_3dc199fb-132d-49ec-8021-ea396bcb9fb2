// import moment from 'moment';
// 表格列配置
export const defaultTableColumn = () => [
  {
    field: 'indicator',
    title: '基础信息',
    minWidth: 180,
    formatter: ({ cellValue }) => {
      return this.type?.dict?.tkc_basic?.find((q) => q.value == cellValue)
        ?.label;
    },
  },
  {
    field: 'value',
    title: '值',
    minWidth: 180,
  },
  {
    field: 'unit',
    title: '单位',
    width: 180,
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 180,
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'workingType',
    title: '统计类型',
    element: 'slot',
    slotName: 'workingType',
  },
  {
    field: 'rateTime',
    title: '月份',
    element: 'slot',
    slotName: 'month',
  },
];

export const initFormValue = () => {
  return {
    workingType: '2',
    rateTime: undefined,
    indicator: [],
    value: '',
    unit: '',
    idmType: '',
  };
};

export const initBasicFormValue = () => {
  return {
    indicator: '',
    value: '',
    unit: '',
  };
};

export const formBasicConfig = [
  {
    field: 'indicator',
    title: '基础信息',
    element: 'a-input',
    props: {
      disabled: true,
    },
    rules: [
      { required: true, message: '请输入' },
      { max: 10, message: '基础信息不能超过10位' },
    ],
  },
  {
    field: 'value',
    title: '值',
    element: 'a-input',
    rules: [{ required: true, validator: checkNumber, trigger: 'change' }],
  },
  {
    field: 'unit',
    title: '单位',
    element: 'a-input',
    rules: [{ required: true, message: '请输入' }],
  },
];

export const formConfig = [
  {
    field: 'rateTime',
    title: '时间',
    element: 'slot',
    slotName: 'workTime',
  },
  {
    field: 'quota',
    title: '指标',
    element: 'a-select',
    rules: [{ required: true, message: '请输入' }],
    props: {
      options: [
        { value: '1', label: '应税收入' },
        { value: '2', label: '工业产值' },
        { value: '3', label: '规上服务业营收' },
        { value: '4', label: '盈利性服务业' },
        { value: '5', label: '合作基金' },
        { value: '6', label: '招商企业' },
        { value: '7', label: '招入人才' },
      ],
      showSearch: true,
      optionFilterProp: 'children',
    },
  },
  {
    field: 'value',
    title: '值',
    element: 'a-input',
    rules: [{ required: true, message: '请输入值' }],
  },
  {
    field: 'unit',
    title: '单位',
    element: 'a-input',
  },
];
// 整数规则校验
async function checkNumber(rule, value) {
  if (!value) {
    return Promise.reject(`请输入内容`);
  }
  if (isNaN(Number(value))) {
    return Promise.reject('请输入数字');
  } else {
    if (value.indexOf('.') !== -1) {
      return Promise.reject('请输入整数');
    } else {
      return Promise.resolve();
    }
  }
}
