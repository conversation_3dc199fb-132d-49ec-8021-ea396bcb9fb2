<template>
  <div>
    <BuseCrud
      ref="crud"
      title="整改信息维护列表"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowEdit="rowEdit"
      @rowView="rowView"
      @handleCreate="rowAdd"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
    >
      <template #enterpriseId="{ params }">
        <FuzzySelect
          v-model="params.enterpriseId"
          @changeSelect="changeSelect"
        ></FuzzySelect>
      </template>
      <template slot="defaultHeader">
        <a-button
          type="default"
          @click="onClickImport()"
          style="margin-right: 10px"
          >批量导入</a-button
        >
        <a-button
          :disabled="exportDisabled"
          type="default"
          @click="exportData()"
          style="margin-right: 10px"
          >导出</a-button
        >
        <a-button type="primary" @click="rowAdd()" style="margin-right: 10px"
          >新增</a-button
        >
        <a-button type="default" @click="delAll" style="margin-right: 10px"
          >删除</a-button
        >
        <!-- :disabled="checkItems.length == 0" -->
      </template>
    </BuseCrud>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, getCurrentInstance } from 'vue';
import { message, Modal } from 'ant-design-vue';
import Config from './config';
import { getDicts } from '@/api/system/dict/data';
import { parkList } from '@/api/basicData';
import { useRouter, useRoute } from '@/router';
import { resolveBlob } from '@/utils/common/fileDownload';

import {
  getEnterpriseByName,
  addSafeDanger,
  deleteSafeDanger,
  getSafeDangerList,
  downloadInformation,
  updateSafeDanger,
} from '@/api/digitalOperation/securityManagement/danger';
import FuzzySelect from '@/components/FuzzySelect/index.vue';
import { debounce } from '@/utils/common/util';
import moment from 'moment';
const loading = ref(false);
const tableData = ref([]);
const tablePage = reactive({ total: 0, currentPage: 1, pageSize: 10 });
const parkListInfo = ref([]);
const exportDisabled = ref(false);
const crud = ref(null);
const route = useRoute();
const { ctx } = getCurrentInstance(); // 获取当前实例的上下文
const params = reactive({
  dangerName: undefined,
  dangerType: undefined,
  parkId: undefined,
  enterpriseName: '',
  rectifyData: undefined,
  rectifyStatus: undefined,
  source: undefined,
  deadline: undefined,
  unifiedCreditCode: route?.query?.pictureId,
});
const checkItems = computed(() => {
  return crud.value
    ? crud.value.getCheckboxRecords().map((q) => q.dangerId)
    : [];
});
const dictsObj = ref({
  correction_data_source: [],
  danger_type: [],
  rectify_status: [],
});

async function getDictsCallback() {
  let dicts = ['correction_data_source', 'danger_type', 'rectify_status'];
  // 使用 Promise.all 来确保所有请求都完成
  const promises = dicts.map(async (item) => {
    const [res] = await getDicts(item);
    dictsObj.value[item] = (res?.data || []).map((q) => {
      return {
        ...q,
        label: q.dictLabel,
        value: q.dictValue,
      };
    });
  });

  // 等待所有的请求完成
  await Promise.all(promises);
}
getDictsCallback();
const pageName = ref('tkbSafeDanger');
const currentParkIdOfSearch = ref(undefined);
const enterpriseListOfSearch = ref([]);
const filterOptions = computed(() => {
  return {
    //默认显示筛选项的个数
    showCount: 8,
    //筛选控件配置
    config: [
      {
        field: 'dangerName',
        title: '隐患名称',
      },
      {
        field: 'dangerType',
        title: '隐患类型',
        element: 'a-select',
        props: {
          //这里是通过接口异步获取，也可以直接在这写死
          options: Config.DangerType,
        },
      },
      {
        field: 'parkId',
        title: '所属园区',
        element: 'a-select',
        props: {
          //这里是通过接口异步获取，也可以直接在这写死
          options: parkListInfo.value,
          labelInValue: true,
        },
        on: {
          change: (val) => {
            currentParkIdOfSearch.value = val;
          },
        },
      },
      {
        field: 'enterpriseName',
        title: '单位名称',
        props: {
          placeholder: '请输入单位名称',
        },
      },
      {
        field: 'deadline',
        title: '整改期限',
        element: 'a-range-picker',
      },
      {
        field: 'rectifyStatus',
        title: '整改状态',
        element: 'a-select',
        props: {
          options: Config.RectifyStatus,
        },
      },
      {
        field: 'source',
        title: '数据来源',
        element: 'a-select',
        props: {
          options: Config.SourceType,
        },
      },
      {
        field: 'unifiedCreditCode',
        title: '统一社会信用代码',
      },
    ],
    params: params,
  };
});

const fetching = ref(false);
const currentParkId = ref('');
const enterpriseList = ref([]);
const modalConfig = computed(() => {
  return {
    delBtn: false,
    addBtn: false,
    menu: true,
    editBtnText: '填写整改情况',
    editTitle: '填写整改情况',
    viewBtnText: '查看详情',
    menuWidth: 300,
    menuFixed: 'right',
    formConfig: [
      {
        field: 'dangerName',
        title: '安全隐患名称',
        rules: [{ required: true, message: '请填写安全隐患名称' }],
      },
      {
        field: 'dangerType',
        title: '隐患类型',
        element: 'a-select',
        props: {
          placeholder: '请选择隐患类型',
          options: dictsObj.value?.danger_type || [],
        },
        rules: [{ required: true, message: '请选择隐患类型' }],
      },
      {
        field: 'source',
        title: '数据来源',
        element: 'a-select',
        props: {
          placeholder: '请选择数据来源',
          options: dictsObj.value.correction_data_source,
        },
        rules: [{ required: true, message: '请选择数据来源' }],
      },
      {
        field: 'parkName',
        title: '所属园区',
        props: {
          disabled: true,
        },
      },
      {
        field: 'enterpriseId',
        title: '单位名称',
        element: 'slot',
        slotName: 'enterpriseId',
        rules: [{ required: true, message: '请选择单位名称' }],
      },
      {
        field: 'unifiedCreditCode',
        title: '统一社会信用代码',
        props: {
          placeholder: '请输入统一社会信用代码',
          disabled: true,
        },
      },
      {
        field: 'deadline',
        title: '整改期限',
        element: 'a-date-picker',
        rules: [{ required: true, message: '请选择整改期限' }],
      },
    ],
    customOperationTypes: [
      {
        title: '编辑',
        typeName: 'EDIT',
        event: (row) => {
          crud.value.switchModalView(true, 'EDIT', row);
        },
      },
    ],
  };
});
const tableColumn = [
  {
    type: 'checkbox',
    width: 80,
  },
  {
    title: '序号',
    type: 'seq',
    width: 80,
  },
  {
    title: '整改状态',
    field: 'rectifyStatus',
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return (
        dictsObj.value?.rectify_status.find((item) => item.value === cellValue)
          ?.label || '其他'
      );
    },
  },
  {
    title: '所属园区',
    field: 'parkName',
    minWidth: 120,
  },
  {
    field: 'unifiedCreditCode',
    title: '统一社会信用代码',
    minWidth: 140,
  },
  {
    title: '单位名称',
    field: 'enterpriseName',
    minWidth: 120,
  },
  {
    title: '安全隐患名称',
    minWidth: 120,
    field: 'dangerName',
  },
  {
    title: '隐患类型',
    field: 'dangerType',
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return (
        dictsObj.value?.danger_type.find((item) => item.value === cellValue)
          ?.label || '其他'
      );
    },
  },
  {
    title: '整改期限',
    field: 'deadline',
    minWidth: 120,
  },
  {
    title: '数据来源',
    field: 'source',
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return (
        dictsObj.value?.correction_data_source.find(
          (item) => item.value === cellValue
        )?.label || '其他'
      );
    },
  },
  {
    title: '创建时间',
    field: 'createTime',
    minWidth: 120,
  },
];
const changeSelect = (val) => {
  console.log(val, 'val');
  crud.value.setFormFields({
    parkName: val?.parkName,
    parkId: val?.parkId,
    enterpriseId: val?.unifiedCreditCode,
    enterpriseName: val?.name,
    unifiedCreditCode: val?.unifiedCreditCode,
  });
};
async function modalConfirmHandler(val) {
  console.log(val, 'val');
  const { crudOperationType, ...arg } = val;
  if (crudOperationType == 'EDIT') {
    return new Promise((resolve) => {
      updateSafeDanger({
        ...val,
        parkId: val.parkId,
        enterpriseId: val.enterpriseId,
        parkName: val.parkName,
        enterpriseName: val.enterpriseName,
        deadline: val?.deadline
          ? moment(val?.deadline).format('YYYY-MM-DD')
          : '',
      })
        .then(([, err]) => {
          if (err) {
            message.error('编辑失败');
            resolve(false);
          } else {
            resolve(true);
            message.success('编辑成功');
            loadData();
          }
        })
        .catch((err) => {
          resolve(false);
          err.message && message.error(err.message);
        })
        .finally(() => {
          resolve(false);
        });
    });
  }
  return new Promise((resolve) => {
    addSafeDanger({
      ...val,
      parkId: val.parkId,
      enterpriseId: val.enterpriseId,
      parkName: val.parkName,
      enterpriseName: val.enterpriseName,
      deadline: val.deadline.format('YYYY-MM-DD'),
    })
      .then(([, err]) => {
        if (err) {
          message.error('新增失败');
          resolve(false);
        } else {
          resolve(true);
          message.success('新增成功');
          loadData();
        }
      })
      .catch((err) => {
        resolve(false);
        err.message && message.error(err.message);
      })
      .finally(() => {
        resolve(false);
      });
  });
}

function resolveParams() {
  return {
    ...params,
    enterpriseId: params.enterpriseId?.key || undefined,
    parkId: params.parkId?.key || undefined,
    startTime: params.deadline?.[0].format('YYYY-MM-DD') || '',
    endTime: params.deadline?.[1].format('YYYY-MM-DD') || '',
  };
}
async function loadData() {
  loading.value = true;
  const [res, err] = await getSafeDangerList({
    ...resolveParams(),
    limit: tablePage.pageSize,
    pageNum: tablePage.currentPage,
  });
  loading.value = false;
  if (err) return;
  tableData.value = res.data.records;
  tablePage.total = res.data.total;
}

function rowAdd() {
  crud.value.switchModalView(true, 'ADD');
}

function rowEdit(row) {
  const router = useRouter();
  router.push({
    name: 'EditRectificationInfo',
    query: {
      dangerId: row.dangerId,
    },
  });
}

function rowView(row) {
  const router = useRouter();
  router.push({
    name: 'EditRectificationInfo',
    query: {
      dangerId: row.dangerId,
      pageType: 'view',
    },
  });
}

async function getParkList() {
  const [res, err] = await parkList();
  if (err) {
    return;
  }
  parkListInfo.value = res.data.map((item) => {
    return {
      label: item.parkName,
      value: item.id,
    };
  });
}
async function delAll() {
  Modal.confirm({
    title: '确认删除',
    content: () => '确认删除当前选中数据？',
    cancelText: '取消',
    okText: '确定',
    async onOk() {
      const [res] = await deleteSafeDanger({
        ids: crud.value
          ? crud.value?.getCheckboxRecords().map((q) => q.dangerId)
          : [],
      });
      if (res && res.code == '10000') {
        message.success('删除成功');
        loadData();
      }
    },
  });
}
async function exportData() {
  const mimeMap = {
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  };
  exportDisabled.value = true;
  const [res] = await downloadInformation({
    ...resolveParams(),
    ids: crud.value
      ? crud.value?.getCheckboxRecords().map((q) => q.dangerId)
      : [],
  });
  exportDisabled.value = false;
  resolveBlob(res, mimeMap, '整改信息维护列表', '.xlsx');
}
// 导入
function onClickImport() {
  const router = useRouter();
  router.push({
    path: '/basicData/importPage',
    query: {
      pageName: pageName.value,
    },
  });
}

onMounted(() => {
  getParkList();
  loadData();
});
</script>

<style lang="less" scoped></style>
