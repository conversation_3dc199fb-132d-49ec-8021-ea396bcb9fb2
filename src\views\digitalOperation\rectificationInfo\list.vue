<template>
  <BuseCrud
    ref="crud"
    :title="timeTips"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @handleCreate="() => {}"
  >
    <template slot="defaultHeader">
      <a-button type="primary" @click="exportData()">导出</a-button>
    </template>
  </BuseCrud>
</template>

<script>
import { resolveBlob } from '@/utils/common/fileDownload';

import Config from './config.js';
import {
  getRectificationStatistical,
  downloadRectificationStatistical,
} from '@/api/digitalOperation/securityManagement/danger';
import moment from 'moment';
export default {
  name: 'HiddenDanger',
  data() {
    return {
      menuShow: true,
      tableData: [],
      tableColumn: Config.TableListColumns,
      params: { time: undefined },
      loading: false,
      startTime: '',
      endTime: '',
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'time',
            title: '排查信息提交时间',
            element: 'a-range-picker',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        menu: false,
      };
    },
    timeTips() {
      if (this.startTime && this.endTime) {
        return `非生产型研发单位安全隐患整改情况统计表(${this.startTime}——${this.endTime})`;
      } else {
        return '非生产型研发单位安全隐患整改情况统计表';
      }
    },
  },
  mounted() {},
  methods: {
    async loadData() {
      if (!this.params.time) {
        this.$message.warn('请选择排查信息提交时间');
        return;
      }
      this.startTime = this.params.time[0].format('YYYY-MM-DD');
      this.endTime = this.params.time[1].format('YYYY-MM-DD');
      console.log(this.startTime);
      this.loading = true;
      const [result, err] = await getRectificationStatistical({
        startTime: this.startTime,
        endTime: this.endTime,
      });
      if (err) return;
      this.loading = false;
      this.tableData = result.data;
      this.tablePage.total = result.total;
    },
    async exportData() {
      if (!this.startTime || !this.endTime) {
        this.$message.error('请选择时间范围');
        return;
      }
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
      const [res] = await downloadRectificationStatistical({
        startTime: this.startTime,
        endTime: this.endTime,
      });
      resolveBlob(res, mimeMap, this.timeTips, '.xlsx');
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.vxe-grid--pager-wrapper {
  display: none;
}
</style>
