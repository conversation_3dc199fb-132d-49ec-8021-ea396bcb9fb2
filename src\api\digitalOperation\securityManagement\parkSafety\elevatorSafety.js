import { request } from '@/utils/request/requestTkb';

//电梯安全分页接口
export function elevatorSafetyPageList(data) {
  return request({
    url: '/elevatorSafety/list',
    method: 'post',
    data,
  });
}
//电梯安全查看历史记录接口
export function elevatorSafetyHistory(data) {
  return request({
    url: `/elevatorSafety/history`,
    method: 'post',
    data,
  });
}

//电梯安全列表
export function listStatistics() {
  return request({
    url: `/elevatorSafety/listStatistics`,
    method: 'GET',
  });
}
