import request from '@/utils/request';

// 查询字典数据列表
export function listData(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/data/list',
    method: 'get',
    params: query,
  });
}

// 查询字典数据详细
export function getData(dictCode) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/dict/data/' +
      dictCode,
    method: 'get',
  });
}

// 根据字典类型查询字典数据信息
export function getDict(dictCode, query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/dict/data/dictCode/' +
      dictCode,
    method: 'get',
    params: query,
  });
}
// 根据字典类型查询字典数据信息
export function getDicts(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/data/batch',
    method: 'POST',
    data,
  });
}
// 根据字典类型查询字典数据信息
export function getDictsNew(data) {
  return request({
    url: '/common/dict/list' + dictCode,
    method: 'POST',
    data,
  });
}
