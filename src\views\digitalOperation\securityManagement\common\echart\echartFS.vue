<template>
  <div>
    <div class="btn-box">
      <a-radio-group class="btn" v-model="value" button-style="solid">
        <a-radio-button value="a">图表</a-radio-button>
        <a-radio-button value="b" @click="goListPage">列表</a-radio-button>
      </a-radio-group>
    </div>
    <div class="chart-box">
      <div class="left">
        <RankingEchart />
      </div>
      <div class="right">
        <ActionsEchart />
        <HistoryEchart />
      </div>
    </div>
  </div>
</template>

<script>
import RankingEchart from './FS/ranking.vue';
import ActionsEchart from './FS/actions.vue';
import HistoryEchart from './FS/history.vue';
export default {
  name: 'ManagementTkEchartFS',
  components: {
    RankingEchart,
    ActionsEchart,
    HistoryEchart,
  },
  data() {
    return { value: 'a' };
  },
  created() {},

  mounted() {},

  methods: {
    goListPage() {
      this.$emit('goListPage');
    },
  },
};
</script>

<style lang="less" scoped>
.chart-box {
  margin: 24px 0;
  display: flex;
  flex-direction: row;
  .left {
    flex: 4;
    background-color: #fff;
    margin-right: 24px;
  }
  .right {
    flex: 6;
    background-color: #fff;
  }
}
.btn-box {
  display: flex;
  justify-content: flex-end;
  background-color: #fff;
  .btn {
    margin: 12px 24px;
  }
}
</style>
