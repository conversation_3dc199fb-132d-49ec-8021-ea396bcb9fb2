<template>
  <div class="overview">
    <div class="logo">
      <img src="../img/logo.png" />
    </div>
    <div class="info">
      <div v-if="pageName === 'businessPortraits'">
        <p>
          <span class="icon"><img src="../img/bussiness.png" /></span>
          <span>企业名称：</span>
          <span>{{ pictureInfo.companyName }}</span>
        </p>
        <p>
          <span><img src="../img/user.png" /></span>
          <span>法定代表人：</span>
          <span>{{ pictureInfo.legalPerson }}</span>
        </p>
        <p>
          <span><img src="../img/code.png" /></span>
          <span>统一社会信用代码：</span>
          <span>{{ pictureInfo.companyCode }}</span>
        </p>
      </div>
      <div v-else>
        <p>
          <span><img src="../img/bussiness.png" /></span>
          <span>园区名称：</span>
          <span>{{ pictureInfo.parkName }}</span>
        </p>
        <p>
          <span><img src="../img/phone.png" /></span>
          <span>电话：</span>
          <span>{{ pictureInfo.phone }}</span>
        </p>
      </div>
    </div>
    <div class="tag-list">
      <h3><span class="point"></span><span>标签</span></h3>
      <div>
        <a-tag
          class="tag-item"
          v-for="item in pictureInfo.label"
          :key="item.id"
          :color="item.color"
          >{{ item.labelName }}</a-tag
        >
        <a-popover
          placement="rightTop"
          trigger="click"
          :overlayStyle="{ width: '300px' }"
          v-if="pictureInfo.label && pictureInfo.label.length > 15"
        >
          <template #content>
            <a-tag
              style="margin-top: 5px; border: none"
              v-for="item in pictureInfo.label"
              :key="item.id"
              :color="item.color"
              >{{ item.labelName }}
            </a-tag>
          </template>
          <a-tag class="tag-item">更多 &gt;</a-tag>
        </a-popover>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'overview',
  props: {
    pageName: {
      type: String,
      default: function () {
        return '';
      },
    },
    pictureInfo: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  watch: {
    pictureInfo: {
      handler(val) {
        this.getColor(val);
      },
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    // 获取标签颜色
    getColor(data) {
      const colorList = ['#6A5ECC', '#F79D00', '#00B42A', '#1890FF', '#FF7B00'];
      if (data?.label) {
        data.label.forEach((item, index) => {
          this.$set(item, 'color', colorList[index % 5]);
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.overview {
  min-height: 746px;
  background: white;
  background-image: url('../img/background.png');
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position: bottom;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  padding: 24px;

  .logo {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
      width: 140px;
      height: 140px;
    }
  }

  .info {
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    font-size: 12px;

    img {
      width: 15px;
      height: 15px;
      margin-right: 10px;
    }
  }

  .tag-list {
    margin-top: 40px;
    h3 {
      text-align: left;
      display: flex;
      flex-direction: row;
      align-items: center;
      .point {
        background: #3d3d3d;
        border-radius: 50%;
        height: 6px;
        width: 6px;
        display: block;
        margin-right: 10px;
      }
    }
    .tag-item {
      margin-top: 5px;
      border: none;
    }
  }
}
</style>
