<template>
  <div class="pdf-box">
    <div class="pdf">
      <a-spin :spinning="isShowPdf">
        <div class="spin-content"></div>
      </a-spin>

      <pdf
        class="pdf-detail"
        :style="{ width: scale + '%' }"
        :rotate="pageRotate"
        v-for="i in numPages"
        :key="i"
        :src="laoclUrl"
        :page="i"
        @progress="pdfload"
      ></pdf>
      <div class="pdf-tools">
        <div class="left">
          <a-tooltip placement="top">
            <template slot="title">
              <span>放大</span>
            </template>
            <a-icon
              style="color: #ffffff; cursor: pointer"
              @click="clickBig"
              type="plus-circle"
            />
          </a-tooltip>
          <a-tooltip placement="top">
            <template slot="title">
              <span>缩小</span>
            </template>
            <a-icon
              style="color: #ffffff; cursor: pointer"
              @click="clickSmall"
              type="minus-circle"
            />
          </a-tooltip>
        </div>
        <hr class="line" />
        <div class="right">
          <a-tooltip placement="top">
            <template slot="title">
              <span>左旋转</span>
            </template>
            <a-icon
              style="color: #ffffff; cursor: pointer"
              @click="clickLeft"
              type="undo"
            />
          </a-tooltip>
          <a-tooltip placement="top">
            <template slot="title">
              <span>右旋转</span>
            </template>
            <a-icon
              style="color: #ffffff; cursor: pointer"
              @click="clickRight"
              type="redo"
            />
          </a-tooltip>
          <a-tooltip placement="top">
            <template slot="title">
              <span>适合宽度</span>
            </template>
            <a-icon
              style="color: #ffffff; cursor: pointer"
              @click="clickWidth"
              type="column-width"
            />
          </a-tooltip>
          <a-tooltip placement="top">
            <template slot="title">
              <span>适合页面</span>
            </template>
            <a-icon
              style="color: #ffffff; cursor: pointer"
              @click="clickPage"
              type="column-height"
            />
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import pdf from 'vue-pdf';
export default {
  name: 'index',
  props: {
    // 当前pdf的路径
    url: {
      type: String,
      default: '',
    },
    rotate: {
      default: 0,
    },
  },
  components: {
    pdf,
  },
  data() {
    return {
      pageRotate: this.rotate, // 旋转角度
      scale: 80, // 放大系数
      // 当前页数
      numPages: 1,
      // 预览路径
      laoclUrl: '',
      isShowPdf: true,
    };
  },
  methods: {
    pdfload(e) {
      console.error(e);
      if (e === 1) {
        this.isShowPdf = false;
      }
    },
    getNumPages() {
      let loadingTask = pdf.createLoadingTask(this.laoclUrl);
      loadingTask.promise
        .then((pdf) => {
          // this.laoclUrl = loadingTask;
          this.numPages = pdf.numPages;
        })
        .catch((err) => {
          this.isShowPdf = false;
          this.$message.error('pdf加载失败');
        });
    },
    // 放大
    clickBig() {
      if (this.scale == 95) {
        return;
      }
      this.scale += 5;
    },
    //缩小
    clickSmall() {
      if (this.scale == 30) {
        return;
      }
      this.scale += -5;
    },
    // 逆时针旋转角度
    clickLeft() {
      this.pageRotate -= 90;
    },
    // 顺时针选中角度
    clickRight() {
      this.pageRotate += 90;
    },
    // 适合宽度
    clickWidth() {
      this.scale = 95;
    },
    // 适合页面
    clickPage() {
      this.scale = 40;
    },
  },
  watch: {
    url: {
      deep: true,
      handler(val) {
        if (val) {
          this.laoclUrl = this.url;

          this.isShowPdf = true;
          this.getNumPages();
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.pdf-box {
  width: 100%;
  height: 80vh;
  position: relative;
  background: rgb(243, 243, 243);

  .pdf {
    width: 100%;
    height: 100%;
    overflow: hidden;
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-left: 0px;
    padding-right: 0px;
    padding-top: 30px;
    padding-bottom: 90px;

    .pdf-detail {
      margin-bottom: 10px;
    }

    .pdf-tools {
      position: absolute;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      height: 42px;
      -webkit-box-align: center;
      align-items: center;
      padding: 0px 6px;
      background: rgb(66, 66, 66);
      border-radius: 4px;
      animation-duration: 0.3s;
      animation-fill-mode: forwards;
      animation-name: ljHGhL;

      .line {
        display: block;
        width: 1px;
        height: 100%;
        margin: 0px;
        align-self: stretch;
        border: 0px;
        background: rgba(255, 255, 255, 0.8);
      }

      .left {
        padding: 6px 0 6px 12px;
        display: flex;
        font-size: 32px;

        i {
          margin-right: 10px;
        }
      }

      .right {
        padding: 6px 0 6px 12px;
        display: flex;
        font-size: 32px;

        i {
          margin-right: 10px;
        }
      }
    }
  }
}
</style>