import * as api from '@/api/portraits/index';

export const portraitsMixin = {
  data() {
    return {};
  },

  methods: {
    // 对数据映射处理
    translateValue(cellValue, list) {
      return list
        .filter((obj) => obj.value === cellValue)
        .map((obj) => obj.label)
        .join(',');
    },
    // 获取枚举类型
    async getCodeByType(codeType) {
      const [res, err] = await api.codeByType({
        codeType: codeType,
      });
      if (err) return;
      const list = res.data.map((item) => {
        return {
          codeType: item.codeType,
          codeTypeName: item.codeTypeName,
          label: item.name,
          value: item.value,
        };
      });
      return list;
    },
  },
};
