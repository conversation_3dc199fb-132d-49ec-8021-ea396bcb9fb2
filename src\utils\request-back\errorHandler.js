import { notification } from 'ant-design-vue';
// import store from '@/store';
// import router from '@/router';
// import { throttle } from 'lodash';
// import { redirectLogin } from '@/router/guards';

/**
 * 请求错误处理
 * handleHttpError -- http 异常处理
 * 1. 如果存在网关，那么该方法是处理网关 http 请求返回非 200 or 304 的异常
 * 2. 如果不存在网关，那么该方法是处理 http 请求返回非 200 or 304 的异常
 *
 * handleCommonError -- 通用业务异常处理
 */

/**
 * 网关错误处理（http请求问题）
 * @param {*} error
 */
export function handleCommonHttpError(error) {
  console.error('请求出现异常！<handleCommonHttpError>: ', error);
}

// const throttledConfirmModal = throttle(
//   () => {
//     Modal.confirm({
//       title: '系统提示',
//       content: '登录状态已过期或者不合法，您可以继续留在该页面，或者重新登录',
//       okText: '重新登录',
//       cancelText: '取消',
//       onOk() {
//         store.dispatch('base/FedLogOut').finally(() => {
//           router.push(redirectLogin());
//         });
//       },
//     });
//   },
//   3000,
//   { trailing: false }
// );

/**
 * 通用业务异常错误处理
 * @param response
 */
export function handleCommonServiceError(response) {
  const { subCode, code: resCode } = response.data;
  const { config } = response;
  if (
    subCode === 'auth-check-error' &&
    config.url !== '/api/authority/admin/logout'
  ) {
    // 登录过期或者不合法
    // throttledConfirmModal();
    return true;
  } else if (!resCode && response.data) {
    // 非业务接口，并有数据返回的
    return false;
  } else if (resCode !== '10000' && resCode !== 200) {
    notification.error({
      message: response.data.subMsg || response.data.msg,
    });
    return true;
  } else {
    return false;
  }
}
