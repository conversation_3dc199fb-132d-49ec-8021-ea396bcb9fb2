<template>
  <div>
    <a-form :form="form" layout="horizontal">
      <a-form-item label="阶段性反馈日期选择">
        <a-radio-group
          @change="radioChange"
          :disabled="operationType === 'editTarget'"
          v-decorator="[
            'dateSelection',
            {
              rules: [{ required: true, message: '请选择阶段性反馈日期选择' }],
              initialValue: selectType,
            },
          ]"
        >
          <a-radio value="0"> 每月反馈一次 </a-radio>
          <a-radio value="1"> 自定义时间节点 </a-radio>
        </a-radio-group>
      </a-form-item>
      <template v-if="currentRadio === '1'">
        <a-form-item
          v-for="(k, i) in form.getFieldValue('keys')"
          :key="k"
          :labelCol="{ span: 0 }"
          :wrapperCol="{ span: 24 }"
          :required="false"
        >
          <a-date-picker
            :disabled="operationType === 'editTarget'"
            v-decorator="[
              `times[${k}]`,
              {
                rules: [
                  {
                    required: true,
                    message: '请选择时间节点',
                  },
                ],
                initialValue:
                  (customTimeNodes && customTimeNodes[i]) || undefined,
              },
            ]"
            placeholder="请选择时间节点"
            style="width: 50%; margin-right: 8px"
          />
          <a-icon
            v-if="form.getFieldValue('keys').length > 1"
            class="dynamic-delete-button"
            type="minus-circle-o"
            :disabled="form.getFieldValue('keys').length === 1"
            @click="() => remove(k)"
          />
        </a-form-item>

        <a-form-item>
          <a-button type="dashed" style="width: 60%" @click="add">
            <a-icon type="plus" /> 新增时间节点
          </a-button>
        </a-form-item>
      </template>
    </a-form>
  </div>
</template>

<script>
let id = 0;
export default {
  props: {
    selectType: {
      type: String,
      default: '0',
    },
    customTimeNodes: {
      type: Array,
      default: null,
    },
    operationType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      currentRadio: this.selectType,
      formItemLayout: {
        labelCol: {
          span: 0,
        },
        wrapperCol: {
          span: 20,
        },
      },
    };
  },
  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'dynamic_form_item' });
  },
  created() {
    const keys = [];
    const len = this.customTimeNodes?.length;
    if (this.currentRadio === '1' && len > 0) {
      for (let i = 0; i < len; i++) {
        keys.push(id++);
      }
    }
    this.form.getFieldDecorator('keys', { initialValue: keys, preserve: true });
  },
  mounted() {},
  methods: {
    radioChange(e) {
      this.currentRadio = e.target.value;
    },
    remove(k) {
      const { form } = this;
      // can use data-binding to get
      const keys = form.getFieldValue('keys');
      // We need at least one passenger
      if (keys.length === 1) {
        return;
      }

      // can use data-binding to set
      form.setFieldsValue({
        keys: keys.filter((key) => key !== k),
      });
    },

    add() {
      const { form } = this;
      // can use data-binding to get
      const keys = form.getFieldValue('keys');
      const nextKeys = keys.concat(id++);
      // can use data-binding to set
      // important! notify form to detect changes
      form.setFieldsValue({
        keys: nextKeys,
      });
    },
  },
};
</script>
<style>
.dynamic-delete-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  font-size: 24px;
  color: #999;
  transition: all 0.3s;
}
.dynamic-delete-button:hover {
  color: #777;
}
.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}
</style>
