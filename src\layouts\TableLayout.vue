<template>
  <div class="table-layout">
    <h2 v-if="props.canBack">
      <a href="javascript:;" @click="router.go(-1)" class="form-back">
        <!-- <img :src="require('@/assets/icons/Vector.png')" alt="back" /> -->
        {{ props.title || '-' }}
      </a>
    </h2>
    <!-- <h2 v-else>{{ props.title }}</h2> -->
    <slot>
      <BuseCrud
        ref="crud"
        :title="' '"
        :tabRadioList="tabRadioList"
        :tableData="table.data"
        :tablePage="table.page"
        :tableColumn="props.tableColumn"
        :tableProps="table.props"
        :filterOptions="{
          config: filter.config,
          ...filter,
        }"
        :loading="loading"
        :modalConfig="props.buseConfig"
        v-bind="$attrs"
        @tabRadioChange="tabRadioChangeHandler"
        @loadData="loadData"
        v-on="$listeners"
      >
        <template #defaultHeader>
          <BtnGroup
            :row="{}"
            :btns="props.tableButtons"
            :layout="{
              type: 'flex',
              align: 'middle',
              justify: 'end',
            }"
            :buttonSpan="0"
          />
        </template>
        <template v-for="(_, name) in $scopedSlots" v-slot:[name]="data">
          <slot :name="name" v-bind="data" />
        </template>
      </BuseCrud>
    </slot>
  </div>
</template>
<script setup>
import {
  reactive,
  defineProps,
  defineEmits,
  defineExpose,
  onMounted,
  computed,
  ref,
  h,
  nextTick,
  watch,
} from 'vue';
import { initParams } from '@/utils';
import BtnGroup from '@/components/BtnGroup';
import { isEqual } from 'xe-utils';

const loading = ref(true);

const props = defineProps({
  canBack: {
    type: Boolean,
    default: false,
  },
  title: String,
  tableApiList: {
    type: Array,
    default() {
      return [];
    },
  },
  buseConfig: {
    type: Object,
    default() {
      return {
        addBtn: false,
        menu: false,
      };
    },
  },
  tableColumn: {
    type: Array,
    default() {
      return [];
    },
  },
  filterConfig: {
    type: Array,
    default() {
      return [];
    },
  },
  dataHandler: {
    type: Function,
    default(x) {
      return x;
    },
  },
  tableButtons: {
    type: Function,
    default() {
      return () => [];
    },
  },
  params: {
    type: Object,
    default() {
      return {};
    },
  },
});

const tabRadioList = computed(() => {
  if (props.tableApiList?.length) {
    return props.tableApiList.map((x, i) => ({
      ...(typeof x === 'function' ? { api: x } : x),
      id: i + 1,
      value: i + 1,
    }));
  }
  return [];
});

const listType = ref(0);

/**
 * filter related configuration
 */
const filter = reactive({
  params: {},
  config: props.filterConfig,
});

watch(
  () => filter.params,
  (val, old) => {
    // console.log(
    //   'filter.old:',
    //   structuredClone(old),
    //   'filter.new:',
    //   structuredClone(val),
    //   'props.params:',
    //   structuredClone(props.params)
    // );
    if (!val) return;
    emit('update:params', val);
  },
  {
    immediate: true,
    deep: true,
  }
);

watch(
  () => props.params,
  (val, old) => {
    // console.log(
    //   'prop.old',
    //   structuredClone(old),
    //   'prop.new',
    //   structuredClone(val),
    //   'filter.params:',
    //   structuredClone(filter.params)
    // );

    if (!isEqual(val, filter.params)) {
      filter.params = val;
    }
  },
  {
    deep: true,
  }
);
filter.params = initParams(filter.config, props.params);
/**
 * table related variables
 */
const table = reactive({
  props: {
    custom: true,
    columnConfig: { resizable: true },
    showOverflow: false,
  },
  page: { total: 0, currentPage: 1, pageSize: 10 },
  column: props.tableColumn,
  data: [],
});

function tabRadioChangeHandler(value) {
  table.page.currentPage = 1;
  listType.value = value - 1;
  // setTimeout(() => {
  //   loadData();
  // }, 500);
}

const loadData = async (otherParams = {}) => {
  loading.value = true;
  const { ...resArg } = filter.params;
  const currentListObj = tabRadioList.value?.[listType.value] || {};
  const apiMethod =
    (typeof currentListObj === 'function'
      ? currentListObj
      : currentListObj?.api) || (() => [null, null]);
  table.data = [];
  let params = {
    ...resArg,
    pageNum: table.page.currentPage,
    limit: table.page.pageSize,
    ...(currentListObj?.params || {}),
    ...otherParams,
  };
  // console.log(params, '--', otherParams, '--', currentListObj?.params);
  const [res, err] = await apiMethod(params);
  loading.value = false;
  if (err) return;
  table.page.total = res.total;
  table.data = props.dataHandler(res.data || []);
  emit('getTableData', table.data);
};

onMounted(async () => {
  // await useDict(props.dict);
  tabRadioChangeHandler(1);
});

const emit = defineEmits(['update:params', 'getTableData']);

defineExpose({
  loadData,
  getTable() {
    return table;
  },
});
</script>
<style lang="less" scoped>
.table-layout {
  padding: 20px;

  // background-color: #fff;
  .form-back {
    img {
      position: relative;
      top: -2px;
      width: 12px;
      height: 12px;
      margin-right: 10px;
    }
    color: #000;
  }
}
::v-deep .public-list-page {
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e7edf2;
  border-radius: 12px;
  box-shadow: 0 4px 4px 0 #9eaec11a;
}
.table-layout {
  padding: 0;
}
::v-deep .bd3001-page-wrapper-container {
  margin: 0;
}
</style>
