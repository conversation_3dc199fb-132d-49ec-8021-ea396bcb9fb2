import { login, logout, getInfo, getRouters } from '@/api/system/base';
import { filterAsyncRouter } from '@/utils/common/routerUtil';
import { constantRoutes } from '@/router/config';
import {
  getToken,
  setToken,
  removeToken,
  removeTokenWithDomain,
} from '@/utils/common/auth';
import {
  setUser,
  removeUser,
  getUser,
  getUserProperty,
} from '@/utils/common/user-persistent';

export default {
  namespaced: true,
  state: {
    token: getToken(),
    name: getUserProperty('userName'),
    username: getUserProperty('userName'),
    nickName: getUserProperty('nickName'),
    avatar: getUserProperty('avatar'),
    email: getUserProperty('email'),
    merchant: getUserProperty('merchant'),
    user: getUser(),
    sysApps: [],
    permissions: [],
    routes: [],
    addRoutes: [],
  },
  getters: {
    permissions: (state) => {
      return state.permissions;
    },
  },
  mutations: {
    // 设置用户基础信息
    SET_BASE_INFO: (state, data) => {
      const { merchant = {}, user = {}, sysApps = [], permissions = [] } = data;

      state.name = user.userName || '';
      state.username = user.userName || '';
      state.avatar = require('@/assets/images/profile.jpg');
      // state.avatar = user.avatar
      //   ? process.env.VUE_APP_BASE_API + user.avatar
      //   : require('@/assets/images/profile.jpg');

      if (permissions.length > 0) {
        state.permissions = permissions;
      }
      state.user = user;
      setUser({ ...user, merchant: merchant });
      state.sysApps = sysApps;
      state.merchant = merchant;
    },
    // 设置路由
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes;
      state.routes = constantRoutes.concat(routes);
    },
    SET_TOKEN: (state, token) => {
      state.token = token;
      setToken(token);
    },
    RESET_STATE: (state) => {
      removeToken();
      removeTokenWithDomain();
      state.token = '';
      state.name = '';
      state.avatar = '';
      state.user = {};
      removeUser();
      state.sysApps = [];
      state.permissions = [];
      state.routes = [];
      state.addRoutes = [];
    },
  },

  actions: {
    // 获取用户信息
    async GetInfo({ commit }) {
      const token = getToken();
      const [res, err] = await getInfo(token);
      if (err) throw new Error(err);
      const { data } = res;
      commit('SET_BASE_INFO', data);
      return data;
    },

    // 生成路由
    async GenerateRoutes({ commit }, { appId = '' }) {
      // 向后端请求路由数据
      const [systemRes, err] = await getRouters({ appId });
      if (err) throw new Error(err);
      // 系统管理菜单
      let systemRoutes = filterAsyncRouter(systemRes.data);
      // 菜单综合
      const dynamicRoutes = systemRoutes;
      commit('SET_ROUTES', dynamicRoutes);
      return dynamicRoutes;
    },

    // 登录
    async Login({ commit }, { username, password, code, requestNo }) {
      commit('RESET_STATE');
      const [res, err] = await login(username, password, code, requestNo);
      if (err) return [undefined, err];
      const token = res?.data?.token;
      commit('SET_TOKEN', token);
      return ['success', undefined];
    },

    // 退出系统
    async LogOut({ commit }) {
      const token = getToken();
      await logout(token);
      commit('RESET_STATE');
    },

    // 前端 登出
    FedLogOut({ commit }) {
      commit('RESET_STATE');
    },
  },
};
