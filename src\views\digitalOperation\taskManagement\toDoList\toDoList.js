import { renderTaskStates } from '../utils/index';

function getTableColumn() {
  return [
    {
      title: '任务名称',
      field: 'taskName',
    },
    {
      title: '任务内容',
      field: 'content',
    },
    {
      title: '办理人',
      field: 'transactorName',
    },
    {
      title: '创建人',
      field: 'creatorName',
    },
    {
      title: '任务状态',
      field: 'taskStatus',
      slots: {
        default(row) {
          const result = renderTaskStates.call(this, row);
          if (typeof result === 'string') return result;
          return (
            <span style={{ color: result.colorValue }}>{result.text}</span>
          );
        },
      },
    },
    {
      title: '办理时限',
      field: 'transactorTime',
    },
    {
      title: '创建时间',
      field: 'createTime',
    },
  ];
}

export { getTableColumn };
