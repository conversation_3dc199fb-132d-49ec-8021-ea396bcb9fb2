// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    width: 60,
    fixed: 'left',
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    field: 'enterpriseName',
    title: '企业名称',
    minWidth: 160,
  },
  {
    field: 'address',
    title: '企业地址',
    width: 250,
  },
  {
    field: 'enterpriseNum',
    title: '企业人数',
    width: 250,
  },
  {
    field: 'contactPerson',
    title: '联系人',
    width: 250,
  },
  {
    field: 'phone',
    title: '电话',
    width: 180,
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 100,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'enterpriseName',
    title: '企业名称',
    element: 'a-input',
    // element: 'slot',
    // slotName: 'enterSlot',
  },
  {
    field: 'phone',
    title: '电话',
    element: 'a-input',
    props: {
      placeholder: '请输入电话',
    },
  },
];

export const handleFilter = () => {
  console.log(121);
};

export const initFormValue = () => {
  return {
    enterprise: {},
    enterpriseId: '',
    enterpriseName: '',
    address: '',
    contactPerson: '',
    phone: '',
    enterpriseNum: '',
  };
};
