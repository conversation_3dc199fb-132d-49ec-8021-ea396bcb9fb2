import { getDicts } from '@/api/system/dict/data';
import {
  organizeAll,
  enterpriseBasicAll,
} from '@/api/digitalOperation/taskManagement/taskList.js';
function renderTaskStates({ row }) {
  const taskStatus = {
    1: '进行中',
    2: '已完成（归档）',
    3: '已终止',
  };
  //1:发起任务  2:提交结果  3:确认办结  4:归档  5:已逾期  6:已到期
  const operateStatus = {
    1: {
      title: '发起任务',
      color: '#666666',
    },
    2: {
      title: '提交结果',
      color: '#666666',
    },
    3: {
      title: '确认办结',
      color: '#666666',
    },
    4: {
      title: '归档',
      color: '#666666',
    },
    5: {
      title: '已逾期',
      color: 'rgb(217, 0, 27)',
    },
    6: {
      title: '已到期',
      color: 'rgb(245, 154, 35)',
    },
  };
  if (row.taskStatus == 1) {
    const colorValue = `${operateStatus[Number(row.operateStatus)]?.['color']}`;
    return {
      colorValue,
      text: `${taskStatus[Number(row.taskStatus)]}(${
        operateStatus[Number(row.operateStatus)]['title']
      })`,
    };
  }
  return taskStatus[Number(row.taskStatus)] || '其他';
}

const getTaskType = async function () {
  const [res, err] = await getDicts(['task_type']);
  if (err) return;
  return res.data.map((item) => {
    return {
      value: item.dictValue,
      label: item.dictLabel,
    };
  });
};

const getTaskStatus = async function () {
  const [res, err] = await getDicts(['task_state']);
  if (err) return;
  return res.data.map((item) => {
    return {
      value: item.dictValue,
      label: item.dictLabel,
    };
  });
};

/**
 * 获取阅读状态字典表
 */
const getReadState = async function () {
  const [res, err] = await getDicts(['ifRead']);
  if (err) return;
  return res.data.map((item) => {
    return {
      value: item.dictValue,
      label: item.dictLabel,
    };
  });
};

/**
 * 获取账户状态字典表
 */
const getAccountState = async function () {
  const [res, err] = await getDicts(['account_state']);
  if (err) return;
  return res.data.map((item) => {
    return {
      value: item.dictValue,
      label: item.dictLabel,
    };
  });
};

/**
 * 获取账户角色
 */
const getAccountType = async function () {
  const [res, err] = await getDicts(['account_type']);
  if (err) return;
  return res.data.map((item) => {
    return {
      value: item.dictValue,
      label: item.dictLabel,
    };
  });
};
/**
 * 获取检查对象
 */
const getCheckObject = async function () {
  const [res, err] = await getDicts(['check_object']);
  if (err) return;
  return res.data.map((item) => {
    return {
      value: item.dictValue,
      label: item.dictLabel,
    };
  });
};
//查b端园区
const getOrganize = async function (organizeName) {
  const [res, err] = await organizeAll({
    // id: '*********************',
    organizeName,
  }); //查园区id
  if (err) return;
  return res.data.map((item) => {
    return {
      value: item.organizeId,
      label: item.organizeName,
    };
  });
};
//根据园区id,查企业单位
const enterpriseBasicAllList = async function (parkId, enterpriseName) {
  const [res, err] = await enterpriseBasicAll({ parkId, enterpriseName }); //查园区id
  if (err) return;
  return res.data.map((item) => {
    return {
      unifiedCreditCode: item.unifiedCreditCode,
      value: item.enterpriseId,
      label: item.enterpriseName,
    };
  });
};

//ZZ1725053315114491904 -查A端局下用户  *********************-查b端园区
const getOrganizeAll = async function (id = 'ZZ1725053315114491904', userName) {
  const [res, err] = await organizeAll({
    // id,
    userName,
  }); //太科办-局-用户,一次性返回
  if (err) return;
  let data = res?.data;
  data = JSON.parse(
    JSON.stringify(data)
      .replace(/userInfoList/g, 'children')
      .replace(/organizeName/g, 'label')
      .replace(/organizeId/g, 'value')
      .replace(/userId/g, 'value')
      .replace(/nickName/g, 'label')
  );
  return data.map((item) => {
    item.disableCheckbox = true;
    if (!item.children.length) {
      item.disabled = true;
    }
    return item;
  });
};

export {
  renderTaskStates,
  getTaskType,
  getTaskStatus,
  getReadState,
  getAccountState,
  getAccountType,
  getCheckObject,
  getOrganize,
  getOrganizeAll,
  enterpriseBasicAllList,
};
