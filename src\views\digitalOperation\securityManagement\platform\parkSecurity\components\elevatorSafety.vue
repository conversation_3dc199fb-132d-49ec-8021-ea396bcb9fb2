<template>
  <BuseCrud
    ref="crud"
    title="电梯安全"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalConfirm="modalConfirmHandler"
    @handleCreate="rowAdd"
    @loadData="loadData"
    :tableProps="{
      headerAlign: 'left',
      border: 'none',
      columnConfig: { resizable: true },
      showOverflow: 'tooltip',
      align: 'left',
    }"
  >
    <template slot="checkHistory" slot-scope="{ row }">
      <HistoryDetail historyType="ELE" :row="row" />
    </template>
    <template slot="pageCenter">
      <a-row class="page-center-overview">
        <a-col :span="8">
          <div class="item-content-box">
            <div class="circle"><a-icon class="icon" type="warning" /></div>
            <dl>
              <dt class="title">已过期信息</dt>
              <dd class="desc">
                <span class="number">{{ statisticsData.expiredNum }}</span
                >条
              </dd>
            </dl>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="item-content-box">
            <div class="circle white">TOP1</div>
            <dl>
              <dt class="title">{{ statisticsData.top1ParkName }}</dt>
              <dd class="desc">
                <span class="number">{{ statisticsData.top1Num }}</span
                >条
              </dd>
            </dl>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="item-content-box">
            <div class="circle white">TOP2</div>
            <dl>
              <dt class="title">{{ statisticsData.top2ParkName }}</dt>
              <dd class="desc">
                <span class="number">{{ statisticsData.top2Num }}</span
                >条
              </dd>
            </dl>
          </div>
        </a-col>
      </a-row>
    </template>
  </BuseCrud>
</template>

<script>
import HistoryDetail from './history.vue';
import { getTableColumn } from './elevatorSafety.js';
import {
  elevatorSafetyPageList,
  listStatistics,
} from '@/api/digitalOperation/securityManagement/parkSafety/elevatorSafety.js';
import { getOrganize } from '@/views/digitalOperation/taskManagement/utils/index.js';
export default {
  name: 'ElevatorSafety',
  components: {
    HistoryDetail,
  },
  data() {
    return {
      menuShow: true,
      tableData: [],
      tableColumn: [],
      params: { status: undefined, parkId: undefined },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      showCompanyList: false,
      parkList: [],
      statisticsData: [], //过期信息
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            title: '信息状态',
            element: 'a-select',
            field: 'status',
            props: {
              options: [
                { label: '过期', value: '0' },
                { label: '正常', value: '1' },
              ],
            },
          },
          {
            title: '所属园区',
            element: 'a-select',
            field: 'parkId',
            props: {
              options: this.parkList,
              showSearch: true,
              filterOption: false,
              notFoundContent: null,
              labelInValue: true,
            },
            on: {
              search: this.parkSearch,
            },
            previewFormatter: (value) => {
              return value.label;
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        editBtn: false,
        viewBtn: false,
        delBtn: false,
        menuWidth: 150,
        addBtnText: '新增被检查单位',
        addTitle: '新增被检查单位',
        menuFixed: 'right',
        formConfig: [
          {
            title: '所属园区',
            element: 'a-select',
            field: 'parkId',
            props: {
              options: this.parkList,
              showSearch: true,
              filterOption: false,
              notFoundContent: null,
              labelInValue: true,
            },
            on: {
              search: this.parkSearch,
            },
            previewFormatter: (value) => {
              return value.label;
            },
          },

          {
            title: '被检查单位',
            element: 'a-select',
            field: 'companyId',
            rules: [
              {
                required: true,
                message: '请选择被检查单位',
              },
            ],
            show: this.showCompanyList,
            props: {
              options: this.companyListForAdd,
            },
          },
        ],
        customOperationTypes: [
          {
            title: '查看历史记录',
            typeName: 'checkHistory',
            slotName: 'checkHistory',
            showForm: false,
            modalProps: {
              footer: null,
            },
            event: (row) => {
              return this.$refs.crud.switchModalView(true, 'checkHistory', row);
            },
            condition: (row) => {
              //TODO:如果最近检查时间没有填写，则不显示查看详情按钮
              return row.verifyDate ? true : false;
            },
          },
        ],
      };
    },
  },
  created() {
    this.tableColumn = getTableColumn();
    this.listStatistics();
  },
  mounted() {
    this.loadData();
    //获取园区列表数据
    this.getParkList();
  },
  methods: {
    async loadData() {
      console.log(this.params, 'this.params');
      this.loading = true;
      const [res, err] = await elevatorSafetyPageList({
        parkId: this.params.parkId?.key || '',
        parkName: this.params.parkId?.label || '',
        status: this.params.status,
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      });
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    getParkList(name) {
      getOrganize(name).then((res) => {
        this.parkList = res;
      });
    },
    async listStatistics(params) {
      const [res, err] = await listStatistics(params);
      if (err) return;
      console.log('res,', res);
      this.statisticsData = res.data;
    },
    parkSearch(value) {
      this.getParkList(value);
    },
    modalConfirmHandler() {},
    deleteRowHandler() {},
    rowAdd() {
      this.showCompanyList = false;
      return this.$refs.crud.switchModalView(true);
    },
  },
};
</script>

<style lang="less" scoped>
.item-content-box {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .circle {
    width: 50px;
    line-height: 50px;
    border-radius: 50%;
    background: #c75c5c;
    text-align: center;
    .icon {
      font-size: 24px;
    }
    &.white {
      color: white;
    }
  }
  dl {
    margin-bottom: 0;
    padding-left: 8px;
    dd {
      margin-bottom: 0;
      span.number {
        font-size: 26px;
      }
    }
  }
}
.page-center-overview {
  background: #fff;
  height: 112px;
  margin: 16px 0;
  padding-left: 24px;
  border-radius: 2px;
  dl {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: left;
    height: 112px;
    padding: 0 8px;
    text-align: left;
    .title {
      opacity: 0.65;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      line-height: 22px;
      margin-bottom: 6px;
    }
    .desc {
      opacity: 0.85;
      color: #000000;
      line-height: 38px;
      margin-bottom: 0;
    }
  }
}
</style>
