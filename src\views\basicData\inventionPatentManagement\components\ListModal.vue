<template>
  <a-modal
    width="600px"
    :title="
      modelTitle === 'add' ? '新增' : modelTitle === 'see' ? '详情' : '编辑'
    "
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="ruleForm"
        :config="formConfig"
        :params="formValue"
        :preview="preview"
      >
        <!-- 企业 -->
        <template #nameSlot>
          <a-select
            :allowClear="true"
            :filterOption="false"
            :showSearch="true"
            placeholder="请选择"
            v-model="formValue.enterprise"
            :notFoundContent="null"
            @change="handleSearch"
            @search="handleSearch"
            :labelInValue="true"
          >
            <a-select-option
              v-for="item in companyList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </template>
        <!-- 年份插槽 -->
        <template #year>
          <BuseRangePicker
            type="year"
            :needShowSecondPicker="() => false"
            v-model="formValue.rateTime"
            format="YYYY"
            :disableDateFunc="disableDateFunc"
          />
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment';
import { initFormValue } from '../constant';
import { savePatentEmphasis, editPatentEmphasis } from '@/api/basicData';
import { institutionsMixin } from '../../mixins/institutionsMixin';

export default {
  props: ['visible', 'detail', 'preview', 'isLook', 'modelTitle'],
  mixins: [institutionsMixin],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.companyList = [];
          if (!this.detail) return;
          let rateTime = undefined;
          if (this.modelTitle !== 'see') {
            rateTime = {
              endOpen: false,
              endValue: null,
              startOpen: true,
              startValue: moment(this.detail.rateTime),
            };
            this.formValue.enterprise = {
              label: this.detail.enterpriseName,
              key: this.detail.enterpriseId,
            };
          } else {
            rateTime = moment(this.detail.rateTime).format('YYYY-MM');
            this.formValue.enterprise = this.detail.enterpriseName;
          }
          this.formValue.enterpriseName = this.detail.enterpriseName;
          this.formValue.enterpriseId = this.detail.enterpriseId;
          this.companyList = [
            {
              label: this.formValue.enterpriseName,
              value: this.formValue.enterpriseId,
            },
          ];
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
            ...{ enterprise: this.formValue.enterprise },
            rateTime: rateTime,
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  computed: {
    formConfig() {
      const formConfig = [
        {
          field: 'rateTime',
          title: '日期',
          element: 'slot',
          slotName: 'year',
          rules: [
            { required: true, message: '请输入' },
            {
              validator: (rule, value, callback) => {
                console.log(value, 'value');
                if (value && value.startValue) {
                  callback();
                } else {
                  callback('请选择年份');
                }
              },
              trigger: 'blur',
            },
          ],
        },
        {
          field: 'mainClassificationNum',
          title: '主分类号',
          element: 'a-input',
        },
        {
          field: 'enterprise',
          title: '企业名称',
          element: 'slot',
          slotName: 'nameSlot',
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'address',
          title: '地址',
          element: 'a-input',
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'organization',
          title: '专利机构',
          element: 'a-input',
        },
        {
          field: 'authorizationDate',
          title: '授权日期',
          element: 'a-date-picker',
        },
        {
          field: 'inventName',
          title: '发明名称',
          element: 'a-input',
        },
        {
          field: 'applicationNum',
          title: '申请号',
          element: 'a-input',
        },
        {
          field: 'applicationDate',
          title: '申请日期',
          element: 'a-date-picker',
        },
        {
          field: 'patentmanPost',
          title: '专利人邮编',
          element: 'a-input',
        },
        {
          field: 'patentType',
          title: '专利类型',
          element: 'a-select',
          props: {
            options: [
              { value: '1', label: '发明' },
              { value: '2', label: '其它' },
            ],
          },
        },
        {
          field: 'applicantType',
          title: '申请人类型',
          element: 'a-select',
          props: {
            options: [
              { value: '1', label: '企业' },
              { value: '2', label: '大专院校' },
              { value: '3', label: '科研机构' },
              { value: '4', label: '个人' },
              { value: '5', label: '其它' },
            ],
            showSearch: true,
            optionFilterProp: 'children',
          },
        },
      ];
      return formConfig;
    },
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      console.log(2222);
      this.formConfig[0].props.options = this.stateSelect;
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.formValue.enterpriseId = this.formValue.enterprise.key;
          this.formValue.enterpriseName = this.formValue.enterprise.label;
          this.formValue.rateTime = moment(
            this.formValue.rateTime?.startValue
          ).format('YYYY');
          this.formValue.applicationDate = this.formValue.applicationDate
            ? moment(this.formValue.applicationDate).format('YYYY-MM-DD')
            : '';
          this.formValue.authorizationDate = this.formValue.authorizationDate
            ? moment(this.formValue.authorizationDate).format('YYYY-MM-DD')
            : '';
          if (this.modelTitle === 'add') {
            this.savePatentEmphasis();
          } else {
            this.editPatentEmphasis();
          }
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 新增
    async savePatentEmphasis() {
      const [, err] = await savePatentEmphasis(this.formValue);
      if (err) return;
      this.$message.success('新增成功!');
      this.$emit('loadData');
    },
    // 编辑
    async editPatentEmphasis() {
      const [, err] = await editPatentEmphasis(this.formValue);
      if (err) return;
      this.$message.success('编辑成功!');
      this.$emit('loadData');
    },
    // 调用父组件方法
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
