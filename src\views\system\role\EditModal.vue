<template>
  <!-- 添加或修改角色配置对话框 -->
  <a-modal
    :title="title"
    :visible="visible"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
    width="800px"
    @ok="submitForm"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <a-form-model
        ref="form"
        :model="form"
        :rules="formRules"
        v-bind="formLayout"
        label-width="80px"
      >
        <a-form-model-item label="角色名称" prop="roleName">
          <a-input v-model="form.roleName" placeholder="请输入角色名称" />
        </a-form-model-item>
        <a-form-model-item label="状态" prop="status">
          <a-radio-group v-model="form.status" :options="statusOptions">
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="应用权限">
          <a-row>
            <a-col :span="12">
              <a-card
                title="应用列表"
                size="small"
                style="width: 300px"
                :bodyStyle="{ paddingLeft: 0 }"
              >
                <a-spin :spinning="appLoading">
                  <a-tree
                    v-if="appList && appList.length > 0"
                    ref="menu"
                    checkable
                    v-model="form.appIds"
                    :treeData="appList"
                    :selectedKeys="[selectedKeys]"
                    :replaceFields="appReplaceFields"
                    @check="handleAppSelect"
                    @select="handleAppSelect"
                  >
                    <template slot="title" slot-scope="record">
                      <a-tooltip placement="bottom">
                        <template slot="title">
                          <span style="wdith: 100%">{{ record.label }}</span>
                        </template>
                        <div class="app-title">{{ record.label }}</div>
                      </a-tooltip>
                    </template>
                  </a-tree>
                  <a-empty
                    v-else
                    image="https://gw.alipayobjects.com/mdn/miniapp_social/afts/img/A*pevERLJC9v0AAAAAAAAAAABjAQAAAQ/original"
                    :image-style="{
                      height: '60px',
                    }"
                  >
                    <span slot="description" style="color: #bbb">暂无数据</span>
                  </a-empty>
                </a-spin>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card
                :title="`菜单列表${selectedName ? ' - ' + selectedName : ''}`"
                size="small"
                style="width: 300px"
              >
                <a-spin :spinning="menuLoading">
                  <a-tree
                    v-if="appMenuList && appMenuList.length > 0"
                    ref="menu"
                    checkable
                    :show-icon="true"
                    v-model="menuIds"
                    :treeData="appMenuList"
                    :replaceFields="menuReplaceFields"
                    @check="checkMenu"
                  >
                    <template slot="custom" slot-scope="row">
                      <a-icon
                        :type="
                          row.dataRef.type === 'M'
                            ? 'folder-open'
                            : row.dataRef.type === 'C'
                            ? 'folder'
                            : 'star'
                        "
                      />
                    </template>
                  </a-tree>
                  <a-empty
                    v-else
                    image="https://gw.alipayobjects.com/mdn/miniapp_social/afts/img/A*pevERLJC9v0AAAAAAAAAAABjAQAAAQ/original"
                    :image-style="{
                      height: '60px',
                    }"
                  >
                    <span
                      slot="description"
                      style="color: #bbb"
                      v-if="selectedKeys.length === 0"
                      >暂无菜单数据</span
                    >
                    <span slot="description" style="color: #bbb" v-else
                      >请先从左边选择一个应用</span
                    >
                  </a-empty>
                </a-spin>
              </a-card>
            </a-col>
          </a-row>
        </a-form-model-item>
        <a-form-model-item label="备注">
          <a-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>
<script>
import {
  addRole,
  updateRole,
  getRole,
  roleAppTree,
  roleMenuTree,
} from '@/api/system/role';
import {
  formRules,
  appReplaceFields,
  menuReplaceFields,
  initForm,
  statusOptions,
} from './constant';

export default {
  props: {
    visible: Boolean,
    roleId: String,
  },
  data() {
    return {
      loading: false,
      appLoading: false,
      menuLoading: false,
      // 表单参数
      form: initForm(),
      // 表单校验
      formRules,
      // 应用树的对应关系
      appReplaceFields,
      // 应用菜单树的对应关系
      menuReplaceFields,
      menuIds: [], //当前选中的列表
      menuIdsAll: {},
      // 应用列表
      appList: [],
      //当前应用id
      selectedKeys: '',
      selectedName: '',
      // 应用菜单列表
      appMenuList: [],
      halfCheckedKeys: [],
      // 菜单列表

      // 状态数据字典
      statusOptions,
      formLayout: {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
      },
      //半选的数据
      halfChecked: [],
    };
  },
  computed: {
    title() {
      return `${
        this.form.roleId || (this.loading && this.roleId) ? '编辑' : '新增'
      }角色`;
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
        // 获取应用树列表
        this.getAppTreeselect();
        // 获取详情
        if (this.roleId) {
          this.getRoleDetail(this.roleId);
        }
      } else {
        this.$refs.form && this.$refs.form.resetFields();
      }
    },
  },
  methods: {
    /**
     * 表单初始化
     */
    init() {
      this.form = initForm();
      this.menuIds = [];
      this.menuIdsAll = {};
      this.appMenuList = [];
      this.selectedKeys = '';
      this.selectedName = '';
    },
    /** 查询应用列表 */
    async getAppTreeselect() {
      this.appLoading = true;
      const [result, error] = await roleAppTree({
        roleId: this.roleId,
      });
      if (!error) {
        this.appList = result?.data?.roles || [];
      }
      this.appLoading = false;
    },
    /**
     * 获取角色详情
     */
    async getRoleDetail(roleId) {
      this.loading = true;
      // 1、获取角色详情🎭
      const [result, error] = await getRole({ roleId });
      if (!error) {
        this.form = {
          ...this.form,
          ...result.data,
        };
      }
      // 2、获取应用树🌲
      const [role, rerror] = await roleAppTree({ roleId: roleId });
      if (!rerror) {
        const list = Array.from(
          new Set(
            JSON.parse(
              JSON.stringify((role.data && role.data.checkedKeys) || [])
            )
          )
        );
        this.form.appIds = list;
        //3、如果存在关联的应用树，遍历获取关联的菜单树🌲
        if (list && list.length > 0) {
          await this.getAllMenu(list);
        }
      }
      this.loading = false;
    },

    /**
     * 获取全部应用的菜单树的数据
     */
    async getAllMenu(list) {
      try {
        let urlList = list.map((item) =>
          roleMenuTree({
            roleId: this.form.roleId,
            parentRoleId: item,
          })
        );
        Promise.all(urlList)
          .then((res) => {
            res.forEach((item, index) => {
              const [result, error] = item;
              const { data } = result;
              if (!error) {
                // 按照 appID:{checkedKeys:[],menus:[]}格式存储
                // checkedKeys:已经选中的数据,
                // menus 当前应用的菜单
                this.menuIdsAll[list[index]] = data.checkedKeys;
              }
            });
          })
          // eslint-disable-next-line no-unused-vars
          .catch((err) => {
            this.menuIdsAll = {};
          });
      } catch (err) {
        console.error(err);
        this.menuIdsAll = {};
      }
    },
    /**
     * 获取应用的菜单列表.同时保存查询的ID
     */
    handleAppSelect(selectedKeys, info) {
      this.selectedKeys = selectedKeys[selectedKeys.length - 1] || '';
      this.selectedName = info.selectedNodes[0]?.data?.props?.label;
      // 勾选应用
      if (info.event === 'check') {
        //如果勾选应用删除了就删除对应的应用菜单数据
        const ChooseKey = Object.keys(this.menuIdsAll) || [];
        ChooseKey.filter((item) => selectedKeys.indexOf(item) < 0).forEach(
          (item) => {
            delete this.menuIdsAll[item];
          }
        );
      }
      this.appMenuList = [];
      this.selectedKeys && this.getMenuTreeselect(this.selectedKeys);
    },
    /**
     * 菜单列表----勾选菜单
     */
    checkMenu(selectedKeys, info) {
      // 返向勾选应用
      this.form.appIds.findIndex((item) => item === this.selectedKeys) === -1 &&
        this.form.appIds.push(this.selectedKeys);
      // 保存已经被勾选的菜单以及半勾选的菜档
      this.menuIdsAll[this.selectedKeys] = {
        selectedKeys: selectedKeys,
        halfCheckedKeys: info.halfCheckedKeys || [],
      };
    },

    /** 查询菜单树数据 */
    async getMenuTreeselect(appId) {
      this.appMenuList = [];
      this.menuLoading = true;
      const [result, error] = await roleMenuTree({
        roleId: this.roleId,
        parentRoleId: appId,
      });
      if (!error) {
        //设置展示的菜单数据
        this.appMenuList = result.data?.menus?.length
          ? this.settingIcon(result.data?.menus)
          : [];

        //绑定展示的勾选数据---》半选和全选问题
        this.menuIds = this.checkId(this.menuIdsAll[appId], result.data.menus);
      }
      this.menuLoading = false;
    },
    settingIcon(data) {
      return data.map((item) => {
        return {
          ...item,
          children: item.children?.length
            ? this.settingIcon(item.children)
            : [],
          scopedSlots: { icon: 'custom' },
        };
      });
    },

    //检查被选中的id
    checkId(list, appMenuList) {
      if (list?.selectedKeys) {
        return {
          checked: list?.selectedKeys || [],
          halfChecked: list?.halfCheckedKeys || [],
        };
      }
      //所有已经选择的id列表
      let checked = [];
      if (list) {
        checked = [...list];
      }
      //遍历整棵树：比对所有存在子节点的数据是否和当前的选中的数据匹配；
      this.getMenuBtnList(appMenuList, this.halfChecked, checked);

      return {
        checked: checked.filter((item) => this.halfChecked.indexOf(item) < 0),
        halfChecked: this.halfChecked,
      };
    },

    /**
     * 遍历整棵树：比对所有存在子节点的数据是否和当前的选中的数据匹配；
     */
    getMenuBtnList(menuTreeList, halfChecked, checkoutList) {
      //循环遍历每一个有孩子级别的数据
      for (let item of menuTreeList) {
        //检查当前数据是否匹配中check的数据
        if (item.children.length) {
          if (checkoutList.indexOf(item.id) > -1) {
            let checkChildNum =
              item.children.filter(
                (child) => checkoutList.indexOf(child.id) > -1
              ).length !== item.children;

            if (checkChildNum) {
              halfChecked.push(item.id);
            }
          }
          this.getMenuBtnList(item.children, halfChecked, checkoutList);
        }
      }
    },
    /**
     * 提交按钮
     */
    submitForm() {
      if (this.loading) return;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          let menuIds = {};
          this.form.appIds.forEach((item) => {
            // 获取勾选的应用的菜单勾选数据总览
            let initSelectedKeys = this.menuIdsAll?.[item] || [];
            //获取勾选的应用的菜单全勾选数据
            let selectedKeys = this.menuIdsAll?.[item]?.selectedKeys || [];
            //获取勾选的应用的菜单半勾选数据
            let halfCheckedKeys =
              this.menuIdsAll?.[item]?.halfCheckedKeys || [];
            const hasChange =
              this.menuIdsAll?.[item]?.selectedKeys ||
              this.menuIdsAll?.[item]?.halfCheckedKeys;
            // 如果存在全勾选和半勾选的数据--》说明是编辑过的
            if (hasChange) {
              menuIds[item] = selectedKeys
                .concat(halfCheckedKeys)
                .filter((item) => item);
            } else {
              // 没有编辑过数据
              menuIds[item] = initSelectedKeys?.filter((item) => item) || [];
            }
          });
          let queryParams = {
            ...this.form,
            roleMenus: menuIds,
          };
          delete queryParams.appIds;
          const [, error] = this.form.roleId
            ? await updateRole(queryParams)
            : await addRole(queryParams);
          this.loading = false;
          if (error) return;
          this.$message.success(`${this.title}成功`);
          this.$emit('ok');
          this.closeModal();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
<style lang="less" scoped>
.app-title {
  width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
