<template>
  <page-layout>
    <PageWrapper
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      :tableOn="{
        'checkbox-change': selectChangeEvent,
        'checkbox-all': selectChangeEvent,
      }"
    >
      <!-- 创建按钮区域插槽 -->
      <template #defaultHeader>
        <a-button
          type="primary"
          style="margin-right: 8px"
          @click="handleCreate"
        >
          新增
        </a-button>
        <a-button style="margin-right: 8px" @click="onClickImport">
          导入
        </a-button>
        <ExportButton
          :notExportAll="true"
          :checkItems="checkItems"
          :pageName="pageName"
          :params="filterOptions.params"
        />
        <a-button
          type="danger"
          style="margin-right: 8px"
          :disabled="!checkItems.length"
          @click="handelDelete"
        >
          删除
        </a-button>
      </template>
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
        <span class="operate-button" @click="onClickManage(row)">管理</span>
        <span class="operate-button" @click="labelEdit(row)">标签</span>
      </template>
      <!-- 编辑弹窗 -->
      <ListModal
        :visible="visible"
        :detail="modalData"
        :modelTitle="modelTitle"
        @handleCancel="handleCancel"
        @loadData="loadData"
      />
      <!-- 管理弹窗 -->
      <ManageModal
        :visible="manageVisible"
        :modelTitle="modelTitle"
        :detail="modalManageData"
        :parkId="parkId"
        :parkName="parkName"
        @loadData="loadData"
        @handleCancel="handleCancel"
      />
      <!-- 标签编辑弹窗 -->
      <LabelModal
        :visible="labelVisible"
        :detail="modalData"
        :modelTitle="modelTitle"
        :label="label"
        @loadData="loadData"
        @handleCancel="handleCancel"
      />
    </PageWrapper>
  </page-layout>
</template>

<script>
// import moment from 'moment';
import { institutionsMixin } from '../mixins/institutionsMixin';
import { defaultTableColumn, defaultFilterConfig } from './constant';
import ListModal from './components/ListModal.vue';
import ManageModal from './components/ManageModal.vue';
import LabelModal from './components/LabelModal.vue';
import ExportButton from '@/views/basicData/components/ExportButton.vue';
import {
  parkPages,
  deletePark,
  labelList,
  parkManageDetail,
} from '@/api/basicData';
export default {
  components: { ListModal, ManageModal, ExportButton, LabelModal },
  dicts: ['my_notify_rule', 'park_type'],
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'parkManage',
      loading: false,
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
        params: {
          parkName: '',
          type: undefined,
          isCompleted: '',
        }, // 筛选器结果数据
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      visible: false,
      manageVisible: false,
      labelVisible: false,
      modalData: null,
      modalManageData: null,
      checkItems: [],
      modelTitle: 'add',
      parkId: '',
      parkName: '',
      label: [],
    };
  },
  computed: {
    tableColumn() {
      return [
        {
          type: 'checkbox',
          fixed: 'left',
          width: 60,
        },
        {
          type: 'seq',
          title: '序号',
          fixed: 'left',
          width: 60,
        },
        {
          field: 'type',
          title: '园区类别',
          minWidth: 180,
          formatter: ({ cellValue }) => {
            return this.dict?.type?.park_type?.find((q) => q.value == cellValue)
              ?.label;
          },
        },
        {
          field: 'parkName',
          title: '园区名称',
          minWidth: 180,
        },
        {
          field: 'address',
          title: '主要地址',
          width: 180,
        },
        {
          field: 'parkNameDetail',
          title: '详细园区名称',
          width: 180,
        },
        {
          field: 'addressDetail',
          title: '详细地址',
          width: 180,
        },
        {
          field: 'totalArea',
          title: '总规划面积(m²)',
          width: 180,
        },
        {
          field: 'completedArea',
          title: '已建成面积(m²)',
          width: 180,
        },
        {
          field: 'isCompleted',
          title: '建成状况',
          width: 180,
          formatter: ({ cellValue }) => {
            return cellValue === '1'
              ? '已建成'
              : cellValue === '0'
              ? '未建成'
              : '';
          },
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 180,
          fixed: 'right',
        },
      ];
    },
  },
  created() {
    this.loadData();
    this.labelList();
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      this.filterOptions.config[1].props.options =
        this.dict?.type?.park_type || [];
    },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [res, err] = await parkPages({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...params,
      });
      this.loading = false;

      if (err) return;
      // 设置数据
      this.tablePage.total = res.data.total;
      this.tableData = res.data.records;
    },
    // 创建按钮点击事件
    handleCreate() {
      this.modelTitle = 'add';
      this.visible = true;
    },
    // 编辑按钮点击事件
    onClickEdit(row) {
      this.modelTitle = 'edit';
      this.visible = true;
      this.modalData = row;
    },
    // 关闭弹窗
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
      this.modalData = null;
      this.manageVisible = false;
      this.labelVisible = false;
    },
    // 管理按钮点击事件
    async onClickManage(row) {
      this.parkId = row.id;
      this.parkName = row.parkName;
      await this.parkManageDetail();
      this.manageVisible = true;
    },
    // 标签编辑
    labelEdit(row) {
      this.labelVisible = true;
      this.modalData = row;
    },
    // 查看详情
    onClickDetail() {
      // this.$router.push('./second/detail');
    },
    // 删除
    handelDelete() {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [, err] = await deletePark({
            list: that.checkItems,
          });
          if (!err) {
            that.$message.success('删除成功！');
            that.checkItems = [];
            // 刷新数据
            that.loadData();
            return;
          }
        },
      });
    },
    // 园区类别
    stateChange(value) {
      console.log('选中值', value);
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: this.pageName,
        },
      });
    },
    // 标签列表
    async labelList() {
      const [res, err] = await labelList({
        type: '1',
      });
      if (err) return;
      this.label = res.data;
    },
    // 管理弹框数据
    async parkManageDetail() {
      const [res, err] = await parkManageDetail({ parkId: this.parkId });
      if (err) {
        this.modalManageData = [];
      } else {
        this.modalManageData = res.data;
      }
    },
  },
};
</script>
<style scoped>
/deep/.page-wrapper-container {
  margin: 0 !important;
}
</style>
