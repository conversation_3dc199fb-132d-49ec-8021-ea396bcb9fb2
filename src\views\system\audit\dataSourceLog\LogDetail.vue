<template>
  <a-drawer
    width="600px"
    title=" 操作日志详情"
    :visible="visible"
    cancelText="取消"
    :footer="null"
    @close="handleCancel"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <a-descriptions title="基础信息">
        <a-descriptions-item label="日志内容" :span="3"> </a-descriptions-item>
        <a-descriptions-item label="操作模块" :span="3"> </a-descriptions-item>
        <a-descriptions-item label="操作信息" :span="3"> </a-descriptions-item>
        <a-descriptions-item label="操作内容" :span="3"> </a-descriptions-item>
        <a-descriptions-item label="请求地址" :span="3"> </a-descriptions-item>
        <a-descriptions-item label="执行报文" :span="3"> </a-descriptions-item>
        <a-descriptions-item label="执行状态" :span="3"> </a-descriptions-item>
        <a-descriptions-item label="操作说明" :span="3"> </a-descriptions-item>
      </a-descriptions>
    </a-spin>
  </a-drawer>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      form: {},
    };
  },
  methods: {
    // 关闭弹窗
    handleCancel() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped></style>
