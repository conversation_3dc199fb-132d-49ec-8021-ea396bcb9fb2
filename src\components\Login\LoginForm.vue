<template>
  <div class="login-box-wrapper">
    <div class="login-box-header">
      <div class="title">太湖湾科创城</div>
      <div class="desc">数字孪生智慧园区管理平台</div>
    </div>

    <a-form-model class="bd-form" ref="loginForm" :model="form" :rules="rules">
      <a-form-model-item prop="username">
        <a-input size="small" v-model="form.username" placeholder="请输入账户">
          <a-icon slot="prefix" type="user" />
        </a-input>
      </a-form-model-item>
      <a-form-model-item prop="password">
        <a-input-password
          size="small"
          v-model="form.password"
          placeholder="请输入密码"
          type="password"
        >
          <a-icon slot="prefix" type="lock" />
        </a-input-password>
      </a-form-model-item>
      <a-form-model-item prop="code">
        <div class="form-item-code">
          <a-input
            v-model="form.code"
            placeholder="请输入验证码"
            :maxLength="4"
            size="small"
            @pressEnter="onClickLogin"
          />
          <div class="bd-code-box" @click="getCode">
            <a-spin :spinning="codeLoading">
              <img class="bd-code-img" :src="codeImg" />
            </a-spin>
          </div>
        </div>
      </a-form-model-item>
      <a-button
        :loading="loading"
        class="login-btn"
        size="small"
        type="primary"
        htmlType="submit"
        @click="onClickLogin"
      >
        立即登录
      </a-button>
    </a-form-model>
  </div>
</template>
<script>
import { rsaCode } from '@/utils/common/auth';
import { getBaseUrl } from '@/utils/common/routerUtil.js';
import moment from 'moment';
const getDefaultLoginFormData = () => {
  return {
    username: '',
    password: '',
    code: '',
  };
};

export default {
  data() {
    return {
      formItemLayout: {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
      },
      loading: false,
      requestNo: '',
      codeImg: '',
      codeLoading: false,
      form: getDefaultLoginFormData(),
      rules: {
        username: [
          { required: true, message: '请输入账户名', trigger: 'change' },
          { max: 30, message: '账户名不得超过30个字符' },
        ],
        code: [{ required: true, message: '请输入验证码', trigger: 'change' }],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'change',
            whitespace: true,
          },
        ],
      },
    };
  },
  computed: {
    systemName() {
      return process.env.VUE_APP_NAME || '登录';
    },
    systemDesc() {
      return process.env.VUE_APP_DESC;
    },
  },
  created() {
    this.getCode();
  },
  methods: {
    // 登录
    async onClickLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const { username, password, code } = this.form;
          const passwordRsa = rsaCode(password); //加密密码
          const [, err] = await this.$store.dispatch('base/Login', {
            username,
            password: passwordRsa,
            code,
            requestNo: this.requestNo,
          });
          this.loading = false;
          if (err) {
            const { subCode } = err;
            if (subCode && subCode === 'password-need-reset') {
              this.goResetPassword();
              return;
              // } else if (subCode && subCode === 'biz-process-failed') {
              //   this.getCode();
            } else {
              this.form.code = '';
              this.getCode();
            }
            return;
          }
          // 登录成功
          this.$emit('success');
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 表单重置
    resetFields() {
      this.form = getDefaultLoginFormData();
      this.$refs.loginForm.resetFields();
    },

    // 获取验证码
    async getCode() {
      if (this.codeLoading) return;
      this.codeLoading = true;
      this.requestNo = moment().valueOf();
      this.codeImg = `${
        process.env.VUE_APP_USE_BUILD_TYPE ? getBaseUrl() : ''
      } ${process.env.VUE_APP_BASE_API}/api/authority/admin/captcha?requestNo=${
        this.requestNo
      }`;
      // this.codeImg = `${process.env.VUE_APP_USE_BUILD_TYPE}/api/authority/admin/captcha?requestNo=${this.requestNo}`;
      // 监听图片加载完成，移除loading
      const img = new Image();
      img.onload = () => {
        this.codeLoading = false;
      };
      img.onerror = () => {
        this.$message.error('获取验证码失败！');
        this.codeLoading = false;
      };
      img.src = this.codeImg;
    },
    // 跳转修改密码页面
    goResetPassword() {
      this.$router.push({ path: '/resetPassword' });
    },
  },
};
</script>
<style lang="less" scoped>
.title {
  // height: 44px;
  // font-weight: 600;
  position: relative;
  margin-bottom: 10px;
  width: 100%;
  font-family: Alimama ShuHeiTi;
  // font-size: 36px;
  font-size: 18px;
  font-weight: bold;
  line-height: normal;
  text-align: center;
  letter-spacing: 0px;
  font-variation-settings: 'opsz' auto;
  color: rgba(0, 0, 0, 0.85);
}

.login-box-wrapper {
  width: 593px;
  height: 437px;
  background: url('@/assets/images/login_form_bg.png') no-repeat;
  background-size: 100% 100%;
  .login-box-header {
    margin: 36px auto 12px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 490px;
    color: #0b6ba8;
    .title {
      color: #0b6ba8;
    }
    .platform-logo {
      margin-bottom: 5px;
      height: 42px;
      color: #0b6ba8;
      width: auto;
    }
    .login-title {
      margin: 0;
      font-size: 18px;
      text-align: center;
      color: #0b6ba8;
    }
    .desc {
      text-align: center;
      font-family: Alimama ShuHeiTi;
      font-size: 18px;
      color: #0b6ba8;
    }
  }
  .bd-form {
    width: 430px;
    margin: 0 auto;
    /deep/ .ant-form-item {
      margin-bottom: 14px;
      .ant-input {
        background: #fff;
        border-color: #d9d9d9;
        color: #0b6ba8;
        height: 44px;
        &::-webkit-input-placeholder {
          color: #087ec7;
        }

        &::-ms-input-placeholder {
          color: #0b6ba8;
        }

        &::placeholder {
          color: #0b6ba8;
        }
        &:focus {
          border-color: #0b6ba8;
        }
      }
      .anticon {
        color: #0b6ba8;
      }
    }
    .form-item-code {
      display: flex;
      .bd-code-box {
        width: 155px;
        height: 54px;
        margin-left: 16px;
        .bd-code-img {
          width: 155px;
          height: 50px;
        }
      }
    }
    .login-btn {
      border-radius: 2px;
      width: 100%;
      height: 44px;
      border: none;
      background: #0b6ba8;
      border-radius: 10px;
      &:hover {
        border: 1px solid #0b6ba8;
      }
    }
  }
}
</style>
