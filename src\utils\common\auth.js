import Cookies from 'js-cookie';

import CryptoJS from 'crypto-js'; //秘钥
const TokenKey = 'Admin-Token';
const Domain = '.bangdao-tech.com';

// import store from "@/store";
import { JSEncrypt } from 'jsencrypt';
const MerchantKey = 'Admin-Merchant-Id';

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(TokenKey, token);
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}

export function getMerchant() {
  return Cookies.get(MerchantKey);
}

export function setMerchant(token) {
  return Cookies.set(MerchantKey, token);
}

export function removeMerchant() {
  return Cookies.remove(MerchantKey);
}
export function removeTokenWithDomain() {
  return Cookies.remove(TokenKey, { domain: Domain });
}
export function checkPermission(permissionFlag) {
  return !permissionFlag && true;
  // const all_permission = "*:*:*";
  // const permissions = store.state.base.permissions;
  // return permissions.some((permission) => {
  //   return all_permission === permission || permissionFlag.includes(permission);
  // });
}
// 加密数据 rsa
export function rsaCode(password = '') {
  //new 一个对象
  let encrypt = new JSEncrypt();
  encrypt.setPublicKey(
    'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAre6yYn1vqWbId7E3VURXpw5VxTzQJptkXPm+ArdQE6NBhKSAnHj5bB6d3Pos4YVTgAhl7k3n81KpWXGW6M4hWB9iv9MfVowfJ/zWOdmx9KS8+39Z6+8jmFyobzV0Uq8gOs8p9waqHkaK7IvCLvW9FC5umrg0PMjW4feXiGhBM4YSwRftPFe78eaav5lO/ih+9sKKZxQH1pVH3XMfmI1yuxBKMe40j+60VktX1uoIJ3TXRMxm7xz/Larfxe1PO8tEtUfPaiL8181GoAkiaQWpjoPraKBjKpExLh+vTjY4PW4zUpR1kzBGgn7dbJoDm+4lPCgFz/i+e27JccsPANWY0QIDAQAB'
  );
  return encrypt.encrypt(password);
}

const CRYPTOJSKEY = 'liian_2022030700';
//解密
/*
  * {param} plaintText 解密密文
  
  * return  str 解密结果
  */
export function aseDecrypt(encryptedBase64Str) {
  var options = {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  };
  var key = CryptoJS.enc.Utf8.parse(CRYPTOJSKEY);
  // 解密
  var decryptedData = CryptoJS.AES.decrypt(encryptedBase64Str, key, options);
  // 解密后，需要按照Utf8的方式将明文转位字符串
  var decryptedStr = decryptedData.toString(CryptoJS.enc.Utf8);
  return decryptedStr;
}
