import { request } from '@/utils/request/requestTkb';
/**
 * 任务列表数据   任务列表、批示列表、待办列表通用接口
 */
export function taskList(data) {
  return request({
    url: '/task/page/list',
    method: 'post',
    data,
  });
}
/**
 * 任务详情
 */

export function taskInfo(data) {
  return request({
    url: `/task/info/${data.id}`,
    method: 'get',
    // params: data,
  });
}

/**
 * 发布任务/请示活动
 */
export function taskAdd(data) {
  return request({
    url: '/task/add',
    method: 'post',
    data,
  });
}

/**
 * 表格数据导出
 */
export function downloadInformation(data) {
  return request({
    url: '/task/downloadInformation',
    method: 'post',
    data,
    responseType: 'blob',
  });
}

/**
 * 任务信息---yapi重新推送
 */

/**
 * 办理情况
 */

/**
 * 操作记录
 */

/**
 * 提交结果
 */

/**
 * 确认办结
 */

/**
 * 归档
 */

/**
 * 终止
 */

//一次性返回园区和局及局下的用户
export function organizeAll(data) {
  return request({
    // url: `/organize/all`,
    url: `/organize/parkName`,
    method: 'post',
    data,
  });
}
//根据园区id,查企业id
export function enterpriseBasicAll(data) {
  return request({
    url: `/organize/enterpriseBasic/all`,
    method: 'post',
    data,
  });
}

//办理情况撤回
export function revocation(transactorId) {
  return request({
    url: `/task/transactor/revocation/${transactorId}`,
    method: 'GET',
  });
}
