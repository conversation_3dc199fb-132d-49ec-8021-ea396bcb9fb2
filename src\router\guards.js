/** @format */
import { getToken } from '@/utils/common/auth';
import { loginWhiteList } from '@/router/config';
import NProgress from 'nprogress';
import router from './index';
import { businessRoutes } from './config';
import { getFirstSiteMenuPath } from './utils';

// 安装进度条
NProgress.configure({ showSpinner: false });

/**
 * 进度条开始
 * @param to
 * @param form
 * @param next
 */
const progressStart = (to, from, next) => {
  // start progress bar
  if (!NProgress.isStarted()) {
    NProgress.start();
  }
  next();
};

/**
 * 进度条结束
 * @param to
 * @param form
 * @param options
 */
const progressDone = () => {
  NProgress.done();
  // 页面切换，默认滚动回顶部
  document.body.scrollTo = 0;
  if (document.getElementById('layoutContent')) {
    document.getElementById('layoutContent').scrollTop = 0;
  }
};

/**
 * 登录守卫 && 权限守卫
 * @param to
 * @param form
 * @param next
 * @param options
 */
const authorityGuard = async (to, from, next, { store }) => {
  // 检测目标路由是否在白名单中
  if (loginWhiteList.includes(to)) {
    return next();
  }
  // 没有token则去登录
  const token = getToken();

  // 如果不是白名单：首页-404-401重置密码页面
  if (!token) {
    console.log('如果不是白名单：首页-404-401重置密码页面');
    return next(redirectLogin(true));
  }
  // 有用户信息，跳转到目标路由
  if (store.state.base.sysApps.length > 0) {
    // console.log('有用户信息，跳转到目标路由');
    // 判断用户是否有权限
    const appId = hasAppPermissions(store.state.base.sysApps);
    console.log('appId', appId);
    //当前应用不存在就去401
    if (!appId) return next(redirect401());
    return next();
  }

  // 无用户信息，进行查询校验
  try {
    // 获取用户信息
    console.log('无用户信息，进行查询校验');
    const { sysApps = [] } = await store.dispatch('base/GetInfo');
    const appId = hasAppPermissions(sysApps);
    // 如果没有返回appid
    // 本地开发时候跳去登录页面
    if (!appId) return next(redirectLogin(false));
    // 获取接口返回的动态路由
    const dynamicRoutes = await store.dispatch('base/GenerateRoutes', {
      appId,
    });
    // 根据roles权限生成可访问的路由表
    store.commit('setting/setMenuData', dynamicRoutes);
    dynamicRoutes.concat(businessRoutes).forEach((route) => {
      console.log('==========*****========');
      router.addRoute(route);
    });
    if (to.path === '/') {
      // 登录后跳转/（无指定路由）则跳转到可用路由的第一个地址
      console.log(getFirstSiteMenuPath(dynamicRoutes));
      next({ path: getFirstSiteMenuPath(dynamicRoutes) });
    } else {
      next({ ...to, replace: true });
    }
  } catch (error) {
    // 获取用户信息失败
    console.log('guards error: 获取用户信息失败', error);
    store.dispatch('base/FedLogOut').finally(() => {
      next(redirectLogin(true));
    });
  }
};

// 是否有当前应用权限
function hasAppPermissions(sysApps) {
  if (!process.env.VUE_APP_CODE) return '';
  // 根据应用标识找到对应appId
  const app = sysApps.find((item) => item.appCode === process.env.VUE_APP_CODE);
  return app ? app.appId : '';
}

// 创建动态路由
export async function initRoute(store) {
  // 获取用户信息
  try {
    const { sysApps = [] } = await store.dispatch('base/GetInfo');
    const appId = hasAppPermissions(sysApps);
    if (!appId) {
      businessRoutes.forEach((route) => {
        router.addRoute(route);
      });
      return true;
    }
    // 获取接口返回的动态路由
    const dynamicRoutes = await store.dispatch('base/GenerateRoutes', {
      appId,
    });
    // 根据roles权限生成可访问的路由表
    store.commit('setting/setMenuData', dynamicRoutes);
    // 在路由最后增加兜底路由 * 匹配规则
    businessRoutes.concat(dynamicRoutes).forEach((route) => {
      router.addRoute(route);
    });
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

// 跳转401无权限链接
function redirect401() {
  return `/401?redirect=${encodeURIComponent(location.href)}`;
}
/**
 * 跳转登录页链接
 * @param {Boolean} hasRedirect
 * @returns
 */
export function redirectLogin(hasRedirect) {
  return `/login${
    hasRedirect ? `?redirect=${encodeURIComponent(location.hash)}` : ''
  }`;
}

export default {
  beforeEach: [progressStart, authorityGuard],
  afterEach: [progressDone],
};
