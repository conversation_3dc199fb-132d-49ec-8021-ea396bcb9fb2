<template>
  <div>
    <BuseCrud
      ref="crud"
      title="文章列表"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableOn="{
        'checkbox-change': handleTableSelect,
        'checkbox-all': handleTableSelect,
      }"
      :tableProps="{
        'checkbox-config': {
          checkMethod,
        },
      }"
      :modalConfig="modalConfig"
      @modalCancel="modalCancelHandler"
      @modalSubmit="modalSubmit"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="rowDel"
      @rowEdit="rowEdit"
    >
      <template slot="defaultTitle">
        <TabRadio
          v-model="params.state"
          :radioList="statusRadioList"
          @change="onStatusChange"
          class="mb10"
        >
        </TabRadio>
      </template>
      <template #defaultHeader>
        <BtnGroup :btns="createBtn" :row="{ selectRows }" />
      </template>
      <template slot="push" slot-scope="{ row }">
        <pushTransfer ref="push" :params="pushParams" :rowInfo="row" />
      </template>
    </BuseCrud>
    <BuseModal
      ref="modal"
      :modalConfig="modal.modalConfig"
      :formConfig="formConfig"
      :submit="handleModalSubmit"
      v-model="modal.formParams"
      :type="modalType"
    >
      <template #batchPush="{ row }">
        <pushTransfer
          ref="batchPush"
          :params="batchPushParams"
          :rowInfo="row"
        />
      </template>
    </BuseModal>
  </div>
</template>

<script>
import pushTransfer from './components/pushTransfer.vue';
import TabRadio from '@/components/tabRadio/TabRadio.vue';
import BuseModal from '@/components/BuseModal/index.vue';
import BtnGroup from '@/components/BtnGroup/index.vue';
import { articleList } from '@/api/digitalOperation/policyDynamics/index.js';
import {
  articleOnShelf,
  articleOffShelf,
  articleDelete,
  publicPush,
  push,
  pushPublicBatch,
  batchPush,
  pushArticleByLabels,
} from '@/api/digitalOperation/managementCenter/articleManagement/index.js';
import { categoryList } from '@/api/digitalOperation/managementCenter/classManagement/index.js';
import { getDicts } from '@/api/system/dict/data';
import { getOrganizeAll } from '@/views/digitalOperation/taskManagement/utils/index.js';

import { searchLabelNames } from '@/api/tagLibrary';
import { confirmFunc, getDictByApi } from '@/utils';
const draftTableColumns = [
  {
    title: '所属分类',
    field: 'firstClassName',
    scopedSlots: { customRender: 'firstClassName' },
    formatter: ({ row }) => {
      return `${row.firstClassName}/${row.secondClassName}`;
    },
  },
  {
    title: '文章标题',
    field: 'title',
  },
  {
    title: '创建时间',
    field: 'saveTime',
  },
];
const publishTableColumns = [
  {
    type: 'checkbox',
    width: '50px',
    fixed: 'left',
  },
  {
    title: '发布时间',
    field: 'releaseTime',
    width: '13%',
  },
  {
    title: '标题',
    field: 'title',
    width: '14%',
  },
  {
    title: '所属分类',
    field: 'firstClassName',
    width: '10%',
    scopedSlots: { customRender: 'firstClassName' },
    formatter: ({ row }) => {
      return `${row.firstClassName}/${row.secondClassName}`;
    },
  },
  {
    title: '所属类型',
    field: 'articleType',
    formatter: ({ cellValue }) => {
      return cellValue === '0' ? '图文' : cellValue === '1' ? '图片' : '文档';
    },
  },
  {
    title: '推送方式',
    field: 'pushType',
    formatter: ({ cellValue }) => {
      return cellValue || cellValue == 0
        ? {
            2: '公开推送',
            1: '按标签推送',
            0: '定向推送',
            3: '按标签推送、定向推送',
          }[+cellValue]
        : '/';
    },
  },
  {
    title: '作者',
    field: 'author',
  },
  {
    title: '接收量',
    field: 'totalRecive',
    formatter: ({ row }) => {
      return row.isPublicly ? '-' : row.totalRecive;
    },
  },
  {
    title: '查看量',
    field: 'totalRead',
    formatter: ({ row }) => {
      return row.isPublicly ? '-' : row.totalRead;
    },
  },
  {
    title: '查看率',
    field: 'readRate',
    formatter: ({ row }) => {
      return row.isPublicly ? '-' : `${row.cellValue || 0}%`;
    },
  },
];
export default {
  name: 'ManagementTkIndex',
  components: {
    TabRadio,
    pushTransfer,
    BtnGroup,
    BuseModal,
  },
  data() {
    return {
      pushParams: {},
      batchPushParams: {},
      pushTargetKeys: [],
      statusRadioList: [
        //当前状态（0草稿，1上架，，2下架）
        {
          label: '已发布',
          value: '1',
        },
        {
          label: '已下架',
          value: '2',
        },
        {
          label: '草稿',
          value: '0',
        },
      ],
      tableData: [],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: publishTableColumns,
      /**
       * 筛选参数
       */
      organizeAllList: [],
      params: {
        category: [],
        title: '', //标题
        articleType: undefined, //文章类型
        firstClassId: '',
        secondClassId: '',
        pageNum: 1,
        limit: 10,
        state: '1', //当前状态（0保存，1上架，，2下架）
        author: [],
        article: undefined,
        rateTime: [],
      },
      /**文章类型*/
      articleTypes: [],
      /**作者 */
      authors: [],
      /**文章分类 */
      categories: [],
      author: '',
      modal: {
        modalConfig: [
          {
            type: 'push',
            props: {
              title: '推送',
              width: '400px',
              okText: '确定',
            },
            formProps: {
              //  layout: 'vertical',
              defaultColSpan: 24,
            },
          },
          {
            type: 'batchPush',
            props: {
              title: '定向推送',
              width: '800px',
              okText: '确定',
            },
            formProps: {
              defaultColSpan: 24,
            },
          },
        ],
        formParams: {},
      },
      pushType: [
        { label: '公开推送', value: '2' },
        { label: '按标签推送', value: '1' },
        { label: '定向推送', value: '0' },
        // { label: '按标签推送、定向推送', value: '3' },
      ],
      selectRows: [],
      enterpriseLabels: [],
      parkLabels: [],
      modalType: 'push',
    };
  },
  computed: {
    formConfig() {
      return [
        {
          field: 'pushType',
          title: '推送方式',
          element: 'a-radio-group',
          props: {
            options: this.pushType,
          },
          itemProps: {
            labelCol: {
              span: 24,
            },
            wrapperCol: {
              span: 24,
            },
          },
          // on: {
          //   change: this.handlePushTypeChange,
          // },
          rules: [
            { required: true, message: '请选择推送方式', trigger: 'change' },
          ],
        },
        {
          field: 'pushType_1',
          title: '按标签推送到园区',
          element: 'a-checkbox-group',
          props: {
            options: [
              {
                label: '',
                value: '1',
              },
            ],
          },
          itemProps: {
            labelCol: {
              span: 10,
            },
            wrapperCol: {
              span: 14,
            },
            labelAlign: 'left',
          },
          show: this.modal.formParams?.pushType === '1',
        },
        {
          field: 'parkLabels',
          title: '',
          element: 'a-select',
          props: {
            options: this.parkLabels,
            mode: 'multiple',
            placeholder: '请选择园区标签',
            maxTagCount: 1,
            showSearch: true,
            filterOption(inputValue, option) {
              return option?.componentOptions?.children[0]?.text?.includes(
                inputValue
              );
            },
            getPopupContainer: (triggerNode) =>
              triggerNode.parentNode.parentNode.parentNode.parentNode.parentNode
                .parentNode.parentNode.parentNode,
          },
          itemProps: {
            wrapperCol: {
              span: 24,
            },
          },
          rules: [
            {
              required: !!(
                this.modal.formParams?.pushType === '1' &&
                this.modal.formParams?.pushType_1?.includes('1')
              ),
              type: 'array',
              message: '请选择园区标签',
              trigger: 'change',
            },
          ],
          show: !!(
            this.modal.formParams?.pushType === '1' &&
            this.modal.formParams?.pushType_1?.includes('1')
          ),
        },

        {
          field: 'pushType_2',
          title: '按标签推送到企业',
          element: 'a-checkbox-group',
          props: {
            options: [
              {
                label: '',
                value: '1',
              },
            ],
          },
          itemProps: {
            labelCol: {
              span: 10,
            },
            wrapperCol: {
              span: 14,
            },
            labelAlign: 'left',
          },
          show: this.modal.formParams?.pushType === '1',
        },
        {
          field: 'enterpriseLabels',
          title: '',
          element: 'a-select',
          props: {
            options: this.enterpriseLabels,
            mode: 'multiple',
            placeholder: '请选择企业标签',
            maxTagCount: 1,
            showSearch: true,
            getPopupContainer: (triggerNode) =>
              triggerNode.parentNode.parentNode.parentNode.parentNode.parentNode
                .parentNode.parentNode.parentNode,
            filterOption(inputValue, option) {
              return option?.componentOptions?.children[0]?.text?.includes(
                inputValue
              );
            },
          },
          itemProps: {
            wrapperCol: {
              span: 24,
            },
          },
          show: !!(
            this.modal.formParams?.pushType === '1' &&
            this.modal.formParams?.pushType_2?.includes('1')
          ),
          rules: [
            {
              required: !!(
                this.modal.formParams?.pushType === '1' &&
                this.modal.formParams?.pushType_2?.includes('1')
              ),
              type: 'array',
              message: '请选择企业标签',
              trigger: 'change',
            },
          ],
        },
      ];
    },
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            field: 'category',
            title: '类别',
            element: 'a-cascader',
            props: {
              options: this.categories,
            },
            on: {
              change: this.categoriesChange,
            },
          },
          {
            field: 'rateTime',
            title: this.params.state == '0' ? '创建时间' : '发布时间',
            element: 'a-range-picker',
          },
          {
            field: 'author',
            title: '作者',
            element: 'a-cascader',
            props: {
              options: this.organizeAllList,
            },
            on: {
              change: this.authorChange,
            },
          },
          {
            field: 'title',
            title: '文章',
          },
          {
            field: 'articleType',
            title: '所属类型',
            element: 'a-select',
            props: {
              options: this.articleTypes,
            },
          },
        ],
      };
    },
    modalConfig() {
      return {
        viewBtn: false,
        addBtn: false,
        delBtn: true,
        modalWith: 1000,
        menuWidth: 300,
        crudPermission: [
          {
            type: 'DELETE',
            condition: () => {
              return true;
            },
          },
        ],
        customOperationTypes: [
          {
            title: '预览',
            typeName: 'preview',
            event: (row) => {
              this.jump(row);
            },
            condition: () => {
              return true;
            },
          },
          {
            title: '上架',
            typeName: 'up',
            event: (row) => {
              let that = this;
              this.$confirm({
                content: `确认要上架文章标题为‘${row.title}’的文章吗？`,
                onOk() {
                  return new Promise((resolve) => {
                    that.articleOnShelf(row.articleId);
                    resolve();
                  });
                },
                cancelText: '取消',
              });
            },
            condition: (row) => {
              return row.state !== '1';
            },
          },
          {
            title: '下架',
            typeName: 'down',
            event: (row) => {
              let that = this;
              this.$confirm({
                content: `确认要下架文章标题为‘${row.title}’的文章吗？`,
                onOk() {
                  return new Promise((resolve) => {
                    that.articleOffShelf(row.articleId);
                    resolve();
                  });
                },
                cancelText: '取消',
              });
            },
            condition: (row) => {
              return row.state === '1';
            },
          },
          {
            title: '推送详情',
            typeName: 'pushDetail',
            event: (row) => {
              return this.$router.push({
                path: '/policyResource/policyDynamics/viewDetail',
                query: {
                  id: row.articleId,
                },
              });
            },
            condition: (row) => {
              // console.log('12312321', row, row.state === '1' && row.pushType);
              // window.c = this.modalConfig.customOperationTypes;
              return !!(
                row.state === '1' &&
                row.pushType &&
                row.pushType !== '2'
              );
            },
          },
          // {
          //   title: '定向推送',
          //   typeName: 'push',
          //   slotName: 'push',
          //   event: (row) => {
          //     return this.$refs.crud.switchModalView(true, 'push', row);
          //   },
          //   condition: (row) => {
          //     return row.state === '1' && !row.isPublicly;
          //   },
          // },
          // {
          //   title: '公开发布',
          //   typeName: 'openPublish',
          //   slotName: 'openPublish',
          //   event: (row) => {
          //     let that = this;
          //     this.$confirm({
          //       content: `确认要公开发布文章标题为‘${row.title}’的文章吗？`,
          //       onOk() {
          //         return new Promise((resolve) => {
          //           that.openPublishHandle(row.articleId);
          //           resolve();
          //         });
          //       },
          //       cancelText: '取消',
          //     });
          //   },
          //   condition: (row) => {
          //     return row.state === '1' && !row.isPublicly;
          //   },
          // },
        ],
      };
    },
  },
  mounted() {
    this.getBaseInfo();
    this.loadData();
    this.getLabelId({ labelClass: 1 });
    this.getLabelId({ labelClass: 2 });
  },

  methods: {
    checkMethod({ row }) {
      return row.pushType !== '2';
    },
    // handlePushTypeChange(val) {
    //   // if(val === '0'){
    //   //   this.modalType = 'batchPush';
    //   //   this.$refs.modal.open();
    //   // }
    // },
    /**
     * @description:
     * @param {*} labelClass 1 园区 2 企业
     * @return {*}
     */
    async getLabelId({ labelClass = 1, labelName }) {
      const res = await getDictByApi({
        api: searchLabelNames,
        target: labelClass === 1 ? this.parkLabels : this.enterpriseLabels,
        params: {
          labelClass,
          labelName,
        },
        label: 'labelName',
        value: 'id',
      });
    },
    handleTableSelect({ records }) {
      this.selectRows = records;
    },
    async handleModalSubmit(val) {
      console.log('val', val);
      if (
        val?.pushType === '1' &&
        !val?.pushType_1?.length &&
        !val?.pushType_2?.length
      ) {
        this.$message.error('请选择推送标签');
        return false;
      }
      let params = {};
      let api = async function () {};
      let flag;
      switch (val.pushType) {
        case '2':
          params = this.selectRows.map((x) => x.articleId);
          api = pushPublicBatch;
          flag = await confirmFunc('确定要公开推送该信息吗？');
          if (!flag) return false;
          break;
        case '1':
          params = {
            articleList: this.selectRows.map((x) => x.articleId),
            enterpriseLabels: val?.enterpriseLabels || [],
            parkLabels: val?.parkLabels || [],
          };
          api = pushArticleByLabels;
          break;
        case '0':
          this.modalType = 'batchPush';
          this.$refs.modal.open();
          return false;
        default:
          api = batchPush;
          if (!this.$refs.batchPush?.paramsSelectedRowKeys.length) {
            this.$message.error('请选择推送人员');
            //返回false，阻止弹窗关闭
            return false;
          } else {
            const { dataSource, paramsSelectedRowKeys } = this.$refs.batchPush;
            let parkUserIds = []; //B端用户id
            let govUserIds = []; //A端用户id
            dataSource.forEach((item1) => {
              paramsSelectedRowKeys.forEach((item2) => {
                if (item1.key === item2) {
                  if (item1.type == '1') {
                    govUserIds.push(item2);
                  } else {
                    parkUserIds.push(item2);
                  }
                }
              });
            });
            //推送接口
            params = {
              articleList: this.selectRows.map((x) => x.articleId),
              parkUserIds,
              govUserIds,
              enterpriseUserIds: [],
            };
          }
      }
      console.log('params', params, 'api', api);
      const [, err] = await api(params);
      if (err) return;
      this.$message.success('推送成功');
      this.loadData();
    },
    createBtn() {
      return [
        {
          label: '推送',
          event: () => {
            // if (
            //   this.selectRows?.length === 1 &&
            //   this.selectRows[0].pushType !== null
            // ) {
            //   let row = this.selectRows[0];
            //   let arr = [+row.pushType];
            //   if (+row.pushType === 3) arr = [1, 2];
            //   this.pushType = this.pushType.map((x) => ({
            //     ...x,
            //     disabled: arr.includes(+x.value),
            //   }));
            // }
            this.modalType = 'push';
            this.$refs.modal.open();
          },
          props: {
            type: 'primary',
            disabled: !this.selectRows?.length,
          },
        },
      ];
    },
    //列表查询
    async loadData() {
      this.loading = true;
      let p = this.paramsHandle(this.params);
      const [res, err] = await articleList(p);
      if (err) return;
      this.loading = false;
      // 设置数据
      this.tablePage.total = res.total;
      this.tableData = res.data;
    },
    paramsHandle(params) {
      let p = {
        firstClassId: params.category?.[0] || '',
        secondClassId: params.category?.[1] || '',
        author: params.author?.length ? this.author : '',
        title: params.title,
        articleType: params.articleType,
        state: params.state,
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      };
      let startTime = params.rateTime?.[0]?.format('YYYY-MM-DD');
      let endTime = params.rateTime?.[1]?.format('YYYY-MM-DD');
      if (params.state === '0') {
        p.startCreateTime = startTime;
        p.endCreateTime = endTime;
      } else {
        p.startReleaseTime = startTime;
        p.endReleaseTime = endTime;
      }
      return p;
    },
    /**
     * 获取基础信息
     */
    getBaseInfo() {
      this.getArticleTypes();
      this.getCategories();
      getOrganizeAll().then((res) => {
        this.organizeAllList = res;
      });
    },
    //类别查询
    async getCategories() {
      const [res, err] = await categoryList();
      if (err) return;
      let data = res?.data;
      data = JSON.parse(
        JSON.stringify(data)
          .replace(/tkbCategoryRes/g, 'children')
          .replace(/categoryName/g, 'label')
          .replace(/id/g, 'value')
      );
      this.categories = data;
    },

    /**
     * 获取文章类型
     */
    async getArticleTypes() {
      const [res, err] = await getDicts(['article_types']);
      if (err) return;
      this.articleTypes = res.data.map((item) => {
        return {
          value: item.dictValue,
          label: item.dictLabel,
        };
      });
    },

    //上架
    async articleOnShelf(id) {
      const [, err] = await articleOnShelf({ id });
      if (err) return;
      this.$message.success('上架成功');
      this.loadData();
    },
    //下架
    async articleOffShelf(id) {
      const [, err] = await articleOffShelf({ id });
      if (err) return;
      this.$message.success('下架成功');
      this.loadData();
    },
    //删除
    async articleDelete(id) {
      const [, err] = await articleDelete({ id });
      if (err) return;
      this.$message.success('删除成功');
      this.loadData();
    },
    //推送
    async push(params) {
      const [, err] = await push(params);
      if (err) return this.$message.error('定向推送失败');
      this.$message.success('定向推送成功');
      this.loadData();
    },
    //公开发布
    async openPublishHandle(id) {
      const [, err] = await publicPush({ id });
      if (err) return this.$message.error('公开发布失败');
      this.$message.success('公开发布成功');
      this.loadData();
    },
    jump(item) {
      this.$router.push({
        path: '/policyResource/policyDynamics/articleDetail',
        query: {
          id: item.articleId,
        },
      });
    },
    rowDel(row) {
      let that = this;
      this.$confirm({
        content: `确认要删除文章标题为‘${row.title}’的文章吗？`,
        onOk() {
          return new Promise((resolve) => {
            that.articleDelete(row.articleId);
            resolve();
          });
        },
        cancelText: '取消',
      });
    },
    rowEdit(row) {
      console.log('row', row);
      this.$router.push({
        path: '/policyResource/policyDynamics/publishKnowledge',
        query: {
          id: row.articleId,
        },
      });
    },
    authorChange(val, val1, val2) {
      this.author = val2?.[1]?.label || '';
    },
    modalSubmit() {},
    //推送
    pushConfirmHandler(value) {
      return new Promise((resolve) => {
        if (!this.$refs.push?.paramsSelectedRowKeys.length) {
          this.$message.error('请选择推送人员');
          //返回false，阻止弹窗关闭
          resolve(false);
        } else {
          const { dataSource, paramsSelectedRowKeys } = this.$refs.push;
          let userIds = []; //B端用户id
          let govUserIds = []; //A端用户id
          dataSource.forEach((item1) => {
            paramsSelectedRowKeys.forEach((item2) => {
              if (item1.key === item2) {
                if (item1.type == '1') {
                  govUserIds.push(item2);
                } else {
                  userIds.push(item2);
                }
              }
            });
          });
          //推送接口
          let p = {
            articleId: value.articleId,
            userIds,
            govUserIds,
          };
          this.push(p);
          resolve();
        }
      });
    },
    //弹窗确认按钮事件
    async modalConfirmHandler(value) {
      const { crudOperationType } = value;
      if (crudOperationType === 'push') {
        const res = await this.pushConfirmHandler(value);
        return res;
      }
    },
    modalCancelHandler() {},
    handleTransferChange() {},
    handleTransferSearch() {},
    onStatusChange(val) {
      this.params.state = val;
      /**草稿状态下，切换表格列 */
      this.tableColumn = val === '0' ? draftTableColumns : publishTableColumns;
      this.loadData();
    },
  },
};
</script>

<style lang="less" scoped>
.mb10 {
  margin-bottom: 10px;
}
.push-box-container {
  /deep/.ant-card-head {
    display: none;
  }
}
</style>
