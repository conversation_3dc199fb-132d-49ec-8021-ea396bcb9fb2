<template>
  <PortraitCard :span="24" title="经营数据" :canExpand="true" v-bind="$attrs">
    <template slot="leftTitle">
      <span
        >经营数据
        <a-month-picker
          v-model="month"
          size="default"
          style="width: 140px; margin-left: 12px"
          placeholder="选择月份"
          valueFormat="YYYY-MM"
          :allowClear="false"
          :disabledDate="disabledDate"
          @change="handleChangeMonth"
        />
      </span>
    </template>
    <a-row>
      <a-col :span="6" class="detail">
        <span class="title"> 税收(本年累计)： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.tax">暂无数据</span>
          <template v-else
            ><span class="num">{{ pictureInfo.tax || '-' }}</span
            ><span class="unit">万元</span></template
          >
        </span>
      </a-col>
      <a-col :span="6" class="detail">
        <span class="title"> 营收：</span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.incomeCurrPer">暂无数据</span>
          <template v-else>
            <span class="num">{{ pictureInfo.incomeCurrPer || '-' }}</span
            ><span class="unit">万元</span>
          </template>
        </span>
      </a-col>
      <a-col :span="6" class="detail">
        <span class="title"> 亩均税收： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.muAvgTax">暂无数据</span>
          <template v-else>
            <span class="num">{{ pictureInfo.muAvgTax || '-' }}</span
            ><span class="unit">万元/亩</span>
          </template>
        </span>
      </a-col>
      <a-col :span="8" class="detail">
        <span class="title"> 同期营收(上年累计)： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.incomeCorrPer">暂无数据</span>
          <template v-else
            ><span class="num">{{ pictureInfo.incomeCorrPer || '-' }}</span
            ><span class="unit">万元</span></template
          >
        </span>
      </a-col>
    </a-row>
    <a-row :gutter="12" style="margin-top: 12px">
      <a-col :span="9">
        <ChartsCard
          chartTitle="近6个月税收(累计)"
          :options="charts1Options"
          height="174px"
        />
        <ChartsCard
          chartTitle="近6个月营收(累计)"
          :options="charts2Options"
          style="margin-top: 16px"
          height="174px"
        />
      </a-col>
      <a-col :span="7">
        <ScrollTable
          tableTitle="税收排名TOP10"
          :columns="table1.columns"
          :dataCount="table1.total"
          :tableData="table1.data"
          height="422px"
        />
      </a-col>
      <a-col :span="8">
        <ScrollTable
          tableTitle="亩均税收排名TOP10"
          :columns="table2.columns"
          :dataCount="table2.total"
          :tableData="table2.data"
          height="422px"
        />
      </a-col>
    </a-row>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
import ChartsCard from '../ChartsCard.vue';
import ScrollTable from '../ScrollTable.vue';
import { useLineCharts } from '../chartHooks';
import moment from 'moment';
export default {
  components: {
    PortraitCard,
    ChartsCard,
    ScrollTable,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    charts1: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [[], []],
      }),
    },
    charts2: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts3: {
      type: Object,
      default: () => ({
        data: [],
      }),
    },
    charts4: {
      type: Object,
      default: () => ({
        data: [],
      }),
    },
    rateTime: {
      type: String,
      default: moment().format('YYYY-MM'),
    },
  },
  data() {
    return {
      charts1Options: {},
      charts2Options: {},
      month: moment().format('YYYY-MM'),
    };
  },
  computed: {
    table1() {
      return {
        columns: [
          {
            title: '排名',
            field: 'taxRank',
            width: 90,
            type: 'html',
            formatter: ({ cellValue }) =>
              `<div class='top-title top-title-${+cellValue}' ">TOP${+cellValue}</div>`,
          },
          {
            title: '园区名称',
            field: 'parkName',
            minWidth: 80,
          },
          {
            title: '税收(万元)',
            field: 'tax',
            width: 100,
            type: 'html',
            headerAlign: 'right',
            align: 'right',
            formatter: ({ cellValue }) =>
              `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${
                cellValue || '-'
              }</span`,
          },
        ],
        data: this.charts4.data || [],
        total: 0,
      };
    },
    table2() {
      return {
        columns: [
          {
            title: '排名',
            field: 'muAvgTaxRank',
            width: 90,
            type: 'html',
            formatter: ({ cellValue }) =>
              `<div class='top-title top-title-${cellValue}' ">TOP${cellValue}</div>`,
          },
          {
            title: '园区名称',
            field: 'parkName',
            minWidth: 80,
          },
          {
            title: '亩均税收(万元/亩)',
            field: 'muAvgTax',
            width: 140,
            type: 'html',
            headerAlign: 'right',
            align: 'right',
            formatter: ({ cellValue }) =>
              `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${
                cellValue || '-'
              }</span`,
          },
        ],
        data: this.charts3.data || [],
        total: 0,
      };
    },
  },
  watch: {
    charts1: {
      deep: true,
      handler() {
        this.handleInitCharts1();
      },
    },
    charts2: {
      deep: true,
      handler() {
        this.handleInitCharts2();
      },
    },
  },
  methods: {
    handleChangeMonth() {
      this.$emit('resetBusinessData', this.month, 'Taike');
    },
    disabledDate(current) {
      return current && current > moment().endOf('month');
    },
    loadData() {
      console.log('从新加载数据');
    },
    handleInitCharts1() {
      const unit = '万元';
      if (!this.charts1?.data) return;
      const { xAxis = [], data = [] } = this.charts1;

      this.charts1Options = useLineCharts({
        xAxis,
        unit,
        series: [
          { name: '近6个月税收(累计)', data: data || [], color: '#5184f8' },
        ],
        grid: {
          left: 60,
          right: 10,
          top: 40,
          bottom: 20,
        },

        formatDate: true, // 启用日期格式化
        dateFormat: 'YY-MM', // 设置日期格式为 "25-11"
      });
    },
    handleInitCharts2() {
      const unit = '万元';
      if (!this.charts2?.data) return;
      const { xAxis = [], data = [] } = this.charts2;

      this.charts2Options = useLineCharts({
        xAxis,
        unit,
        series: [
          { name: '本年累计', data: data[0] || [], color: '#5184f8' },
          { name: '上年同期累计', data: data[1] || [], color: '#56d8a0' },
        ],
        grid: {
          left: 70,
          right: 10,
          top: 40,
          bottom: 20,
        },

        formatDate: true, // 启用日期格式化
        dateFormat: 'YY-MM', // 设置日期格式为 "25-11"
      });
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .title {
    text-align: right;
    color: #999999;
    // width: 148px;
    line-height: 26px;
    max-width: 148px;
  }

  .info {
    text-align: left;
    .num {
      font-family: D-DIN;
      font-size: 24px;
      font-weight: bold;
      line-height: 24px;
      color: #333333;
      z-index: 0;
    }

    .unit {
      margin-left: 4px;
      font-family: PingFang SC;
      font-size: 12px;
      color: #333333;
    }
  }
}
.ant-col-6 {
  width: 22%;
}
</style>
