import request from '@/utils/request';
/**
 * 1 bucket文件下载
 *
 */

// 1、文件下载-查询列表
export function documentUploadList(query) {
  return request({
    url: `/api/authority/admin/upload/record/download/list`,
    method: 'GET',
    params: query,
  });
}
// 2、文件下载-删除
export function documentUploadRemove(recordId) {
  return request({
    url: `/api/authority/admin/upload/record/download/remove/${recordId}`,
    method: 'GET',
  });
}
// 3、批量删除
export function documentUploadRemoveBatch(data) {
  return request({
    url: `/api/authority/admin/upload/record/download/removeBatch`,
    method: 'POST',
    data,
  });
}
