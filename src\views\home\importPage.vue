<template>
  <div class="upload-wrap">
    <div class="back-btn">
      <a-button icon="rollback" @click="() => $router.go(-1)"> 返回</a-button>
    </div>
    <div class="upload">
      <a-upload-dragger
        :multiple="true"
        :fileList="fileList"
        :showUploadList="false"
        :before-upload="beforeUpload"
        @change="handlechange"
        :customRequest="customRequestPic"
        :loading="uploadLoading"
      >
        <a-spin :spinning="uploadLoading">
          <div class="ant-upload-text">点击上传/ 拖拽到此区域</div>
        </a-spin>
      </a-upload-dragger>
      <div class="upload-tips">
        <div class="tips-left">请上传Excel文件，大小在5M以内</div>
        <a-button
          type="link"
          :loading="exportLoading"
          class="downLoad"
          @click="downloadTemplate"
          >点击下载导入模板.xlsx</a-button
        >
      </div>
    </div>
    <PageWrapper
      :loading="loading"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      @handleCreate="handleCreate"
    >
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
      </template>
      <!-- 编辑弹窗 -->
      <ListModal
        :visible="visible"
        :detail="modalData"
        @handleCancel="handleCancel"
      />
    </PageWrapper>
    <a-modal
      title="选择时间段"
      :visible="modelVisible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancelModal"
      :afterClose="afterClose"
    >
      <BuseRangePicker type="month" v-model="monthDate" format="YYYY-MM" />
    </a-modal>
  </div>
</template>

<script>
import { importTableColumn, defaultFilterConfig } from './constant';
import * as api from '@/api/basicData';
import { request } from '@/utils/request/requestTkb';
import { resolveBlob } from '@/utils/common/fileDownload';
import ListModal from './components/ListModal.vue';
import { getBaseUrl } from '@/utils/common/util.js';
import { getToken } from '@/utils/common/auth';
import { notification } from 'ant-design-vue';
import moment from 'moment';

export default {
  components: { ListModal },
  dicts: ['my_notify_rule'],
  props: {
    pageName: {
      type: String,
      default: function () {
        return '';
      },
    },
  },
  data() {
    return {
      loading: false,
      uploadUrl: '',
      fromPage: '',
      headers: {
        Authorization: '',
        responseType: 'blob',
      },
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: importTableColumn(),
      tableData: [],
      visible: false,
      modalData: null,
      fileList: [],
      disabled: false,
      multiple: true,
      accept: '.xls,.xlsx',
      importApi: '',
      maxNum: 1,
      maxSize: 5,
      actionBaseUrl: process.env.VUE_APP_USE_BUILD_TYPE
        ? getBaseUrl()
        : process.env.VUE_APP_BASE_TKB,
      action: `/base/taxableMarket/importData`,
      exportPageName: '',
      // headers: {
      //   Authorization: getToken(),
      // },
      modelVisible: false,
      confirmLoading: false,
      monthDate: { startValue: '', endValue: '' },
      exportLoading: false,
      uploadLoading: false,
    };
  },
  mounted() {},
  created() {
    if (!this.pageName) {
      this.fromPage = this.$route.query.pageName;
      this.exportPageName =
        this.$route.query.exportPageName || this.$route.query.pageName;
    } else {
      this.fromPage = this.pageName;
      // this.exportPageName = this.exportPageName;
    }

    this.headers.Authorization = getToken();
    this.loadData();
  },
  methods: {
    // 字典加载完成
    onDictReady() {
      this.filterOptions.config[1].props.options =
        this.dict.type.my_notify_rule;
    },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const [res, err] = await api.getRecordList(
        {
          limit: this.tablePage.pageSize,
          pageNum: this.tablePage.currentPage,
        },
        this.fromPage
      );
      this.loading = false;

      if (err) return;
      // 设置数据
      this.tablePage.total = res.total;
      this.tableData = res.data;
    },
    // 创建按钮点击事件
    handleCreate() {
      this.visible = true;
    },
    // 编辑按钮点击事件
    onClickEdit(row) {
      this.visible = true;
      this.modalData = row;
    },
    // 关闭弹窗
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
      this.modalData = null;
    },
    // 查看详情
    onClickDetail() {
      // this.$router.push('./second/detail');
    },
    // 上传限制
    beforeUpload(file) {
      const flieArr = file.name.split('.');
      const fileaccept = this.accept.split(',');
      const suffix = flieArr[flieArr.length - 1];
      // 获取类型结果
      const result = fileaccept.some(function (item) {
        return item.slice(1) === suffix;
      });
      this.uploadUrl = api.uploadFile(this.fromPage);
      return new Promise((resolve, reject) => {
        if (this.fileList.length >= Number(this.maxNum)) {
          this.$message.warning(`最大上传数量为${this.maxNum}`);
          reject(new Error(`最大上传数量为${this.maxNum}`));
        } else if (!result) {
          this.$message.warning('上传格式不正确');
          reject(new Error('上传格式不正确'));
        } else if (file.size > this.maxSize * 1024 * 1024) {
          // 判断文件大小是否超标
          const errorMsg = `${file.name}超过${this.maxSize}M大小的限制!`;
          this.$message.warning(errorMsg);
          reject(new Error(errorMsg));
        } else {
          resolve();
        }
      });
    },
    customRequestPic(data) {
      const formData = new FormData();
      formData.append('file', data.file);
      this.uploadfilePic(formData, data);
    },
    Utf8ArrayToStr(array) {
      let out, i, c;
      let char2, char3;

      out = '';
      const len = array.length;
      i = 0;
      while (i < len) {
        c = array[i++];
        switch (c >> 4) {
          case 0:
          case 1:
          case 2:
          case 3:
          case 4:
          case 5:
          case 6:
          case 7:
            // 0xxxxxxx
            out += String.fromCharCode(c);
            break;
          case 12:
          case 13:
            // 110x xxxx 10xx xxxx
            char2 = array[i++];
            out += String.fromCharCode(((c & 0x1f) << 6) | (char2 & 0x3f));
            break;
          case 14:
            // 1110 xxxx 10xx xxxx 10xx xxxx
            char2 = array[i++];
            char3 = array[i++];
            out += String.fromCharCode(
              ((c & 0x0f) << 12) | ((char2 & 0x3f) << 6) | ((char3 & 0x3f) << 0)
            );
            break;
        }
      }
      return out;
    },
    // 文件上传
    async uploadfilePic(formData, data) {
      console.log(data, 'data');
      this.uploadLoading = true;
      const [result, err] = await request(
        {
          url: this.uploadUrl.replace('/api', ''),
          method: 'post',
          headers: {
            'Content-Type': 'multipart/form-data',
            // 'Content-Type': 'application/json; application/octet-stream',
          },
          data: { file: data.file },
          responseType: 'arraybuffer',
        },
        {},
        (res) => [res]
      );
      this.uploadLoading = false;
      if (err) {
        data.onError();
        return;
      }
      const res = result.data;
      try {
        const decoder = new TextDecoder('utf-8');
        const decodedString = decoder.decode(res);
        const response = JSON.parse(decodedString);
        if (response?.code) {
          if (response.code === '10000') {
            data.onSuccess(data.file, data.file);
            this.$message.success('上传成功');
            this.loadData();
          } else {
            if (response?.msg) {
              this.$message.info(response?.msg);
            }
            data.onError();
          }
        } else {
          this.downLoad(data, res);
        }
      } catch {
        this.downLoad(data, res);
      }
    },
    downLoad(data, res) {
      if (!res) return;
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
      };
      resolveBlob(
        new Blob([
          res,
          {
            type: 'application/vnd.ms-excel;charset=utf-8',
          },
        ]),
        mimeMap.xlsx,
        data.file.name,
        '.xlsx'
      );
      data.onError();
    },
    handlechange(info) {
      // const { file, fileList } = info;
      // this.fileList = fileList;
    },
    // 删除
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      this.fileList.splice(index, 1);
    },
    // 下载模板
    downloadTemplate() {
      if (this.$route.query.ifTime) {
        this.modelVisible = true;
      } else {
        this.downLoadTem();
      }
    },
    async downLoadTem() {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
      };
      this.exportLoading = true;
      const [res] = await api.downloadTemplate(
        {
          ...(this.monthDate.startValue && this.monthDate.endValue
            ? {
                startYm: this.monthDate.startValue
                  ? moment(this.monthDate.startValue).format('yyyy-MM')
                  : '',
                endYm: this.monthDate.endValue
                  ? moment(this.monthDate.endValue).format('yyyy-MM')
                  : '',
              }
            : {}),
        },
        this.exportPageName
      );
      this.exportLoading = false;
      resolveBlob(res, mimeMap.xlsx, '导入模板', '.xlsx');
    },
    handleOk() {
      if (this.monthDate.startValue && this.monthDate.endValue) {
        this.downLoadTem();
        this.modelVisible = false;
      } else {
        this.$message.info('请选择时间段');
      }
    },
    handleCancelModal() {
      this.modelVisible = false;
    },
    afterClose() {
      this.monthDate = { startValue: '', endValue: '' };
      this.modelVisible = false;
    },
  },
};
</script>
<style scoped>
::v-deep .page-wrapper-container {
  margin: 0 !important;
}
::v-deep .ant-upload.ant-upload-drag {
  width: 400px;
  height: 200px;
  margin-bottom: 20px;
}
::v-deep .ant-upload-list {
  margin-bottom: 20px;
  width: 400px;
}
.upload-wrap {
  padding: 50px 30px 20px 30px;
}
.downLoad {
  color: #1677ff;
  cursor: pointer;
}
.upload-tips {
  width: 400px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.back-btn {
  position: absolute;
  right: 50px;
  top: 40px;
  z-index: 1;
}
</style>
