const getTableColumn = function () {
  return [
    {
      title: '序号',
      type: 'seq',
      width: 80,
    },
    {
      title: '电梯所属园区',
      field: 'parkName',
      minWidth: 120,
    },
    {
      title: '设备代码',
      field: 'deviceCode',
      minWidth: 120,
    },
    {
      title: '统一社会信用代码',
      field: 'unifiedCreditCode',
      minWidth: 140,
    },
    {
      title: '使用单位名称',
      field: 'userComName',
      minWidth: 120,
    },
    {
      title: '使用单位地址',
      field: 'userComAddress',
      minWidth: 120,
    },
    {
      title: '设备使用地点',
      field: 'devAddress',
      minWidth: 120,
    },
    {
      title: '维保单位名称',
      field: 'maintainOrgName',
      minWidth: 120,
    },
    {
      title: '检验结论',
      field: 'verifyResult',
      minWidth: 120,
    },
    {
      title: '检验时间',
      field: 'verifyDate',
      minWidth: 120,
    },
    {
      title: '下次检验日期',
      field: 'verifyDateNext',
      minWidth: 120,
    },
    {
      title: '信息录入时间',
      field: 'inputTime',
      minWidth: 120,
    },
    {
      title: '信息状态',
      field: 'status',
      minWidth: 120,
      slots: {
        default({ row }) {
          return row.status === '1' ? (
            '正常'
          ) : (
            <span style={{ color: 'red' }}>过期</span>
          );
        },
      },
    },
  ];
};

export { getTableColumn };
