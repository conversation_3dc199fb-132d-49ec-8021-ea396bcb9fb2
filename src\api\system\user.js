import request from '@/utils/request';

// 查询用户列表
export function listUser(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/user/list',
    method: 'get',
    params: query,
  });
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/user/' + userId,
    method: 'get',
  });
}

// 新增用户
export function addUser(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/user/add',
    method: 'post',
    data: data,
  });
}

// 修改用户
export function updateUser(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/user/update',
    method: 'post',
    data: data,
  });
}

// 删除用户
export function delUser(userId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/user/remove/' +
      userId,
    method: 'get',
  });
}
// 删除用户-批量
export function removeBatch(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/user/removeBatch',
    method: 'post',
    data: data,
  });
}

// 用户密码重置
export function resetUserPwd(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/user/resetPwd',
    method: 'POST',
    data: data,
  });
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status,
  };
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/user/changeStatus',
    method: 'POST',
    data: data,
  });
}
// 锁定用户
export function lockUser(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/user/lock',
    method: 'POST',
    data: data,
  });
}
// 解锁用户
export function unlockUser(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/user/unlock',
    method: 'POST',
    data: data,
  });
}
// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/user/profile/avatar',
    method: 'post',
    data: data,
  });
}
