import moment from 'moment';
// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    width: 60,
    fixed: 'left',
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    field: 'year',
    title: '年份',
    width: 210,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY') : '';
    },
  },
  {
    field: 'unifiedCreditCode',
    title: '统一社会信用代码',
    minWidth: 150,
  },
  {
    field: 'code',
    title: '称号',
    minWidth: 150,
  },
  {
    field: 'updateBy',
    title: '更新人',
    minWidth: 150,
  },
  {
    field: 'updateTime',
    title: '更新时间',
    minWidth: 180,
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 160,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'rateTime',
    title: '年份',
    element: 'slot',
    slotName: 'year',
    rules: [{ required: true, message: '请输入年份' }],
  },
  {
    field: 'name',
    title: '企业名称',
    element: 'a-input',
    // slotName: 'enterSlot',
  },
  {
    field: 'code',
    title: '企业类别',
    element: 'slot',
    slotName: 'codeSlot',
    // element: 'a-select',
    // props: {
    //   placeholder: '请输入名称',
    //   options: [],
    // },
  },
];

export const initFormValue = () => {
  return {
    enterpriseId: '',
    enterprise: {},
    enterpriseName: '',
    rateTime: undefined,
    code: undefined,
  };
};

export const getStatus = (code) => {
  switch (code) {
    case '1':
      return '雏鹰';
    case '2':
      return '瞪羚';
    case '3':
      return '准独角兽';
    case '4':
      return '其它';
  }
};
