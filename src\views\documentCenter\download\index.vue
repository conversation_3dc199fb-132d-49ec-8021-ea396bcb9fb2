<template>
  <page-layout>
    <div class="container">
      <PageWrapper
        style="margin: 0"
        ref="vxeRef"
        title="文件列表"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        @loadData="documentUploadList"
        @handleReset="handleReset"
      >
        <!-- 创建按钮区域插槽 -->
        <template #defaultHeader>
          <a-button
            type="primary"
            style="margin-right: 8px"
            @click="batchDeletion"
          >
            批量删除
          </a-button>
        </template>
        <!-- table插槽 -->
        <template #operate="{ row }">
          <span
            class="operate-button"
            v-clipboard:copy="row.url"
            v-clipboard:success="onCopy"
            v-clipboard:error="onError"
            >链接</span
          >
          <span class="operate-button" @click="onClickDownload(row)">下载</span>
          <span class="operate-button" @click="onClickDelete(row)">删除</span>
        </template>
        <!-- 编辑弹窗 -->
        <ModalData :visible="visible" @handleCancel="handleCancel" />
      </PageWrapper>
    </div>
  </page-layout>
</template>
<script>
import ModalData from './components/Modal.vue';
import { Modal } from 'ant-design-vue';
import { filterOptions, tableColumn } from './constant';
import {
  documentUploadList,
  documentUploadRemove,
  documentUploadRemoveBatch,
} from '@/api/system/documentCenter/fileDownload';
import { changeByte } from '@/utils/index';
import { saveAs } from 'file-saver';
export default {
  components: { ModalData },
  data() {
    return {
      disabled: true,
      loading: false,
      filterOptions,
      // 表头
      tableColumn: tableColumn(),
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      visible: false,
      center: 'center',
    };
  },
  created() {
    this.documentUploadList();
  },
  methods: {
    // 重置
    handleReset() {
      for (let key in this.filterOptions.params) {
        this.filterOptions.params[key] = undefined;
      }
      this.filterOptions.params.fileType = '';
      this.tablePage.currentPage = 1;
      this.documentUploadList();
    },
    //下载
    onClickDownload(row) {
      saveAs(row.url, row.fileName);
    },
    // 单行删除
    onClickDelete(row) {
      Modal.confirm({
        title: '警告',
        content: '删除后云存储中文件将会被同步删除，请谨慎操作！',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.documentUploadRemove(row.recordId);
        },
      });
    },
    handleCancel(update) {
      if (update) {
        this.documentUploadList();
      }
      this.visible = false;
    },
    // 复制成功
    onCopy() {
      this.$message.success('复制成功');
    },
    onError() {
      this.$message.error('复制失败');
    },
    // 批量删除
    batchDeletion() {
      const recordIdList = [];
      const deleteList = this.$refs.vxeRef.getCheckboxRecords();
      if (deleteList.length === 0)
        return this.$message.warn('请选择需要删除的记录');
      deleteList.forEach((element) => {
        recordIdList.push(element.recordId);
      });
      Modal.confirm({
        title: '警告',
        content: '删除后云存储中文件将会被同步删除，请谨慎操作！',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.documentUploadRemoveBatch(recordIdList);
        },
      });
    },
    /**
     * 接口处理数据Start
     */
    // 文件下载-查询列表
    async documentUploadList() {
      this.loading = true;
      const {
        fileName = '',
        fileType = '',
        uploadTime = ['', ''],
      } = this.filterOptions.params;
      const paramsObject = {
        fileName,
        fileType,
        uploadTimeBegin: uploadTime[0],
        uploadTimeEnd: uploadTime[1],
      };
      const getData = {
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
        ...paramsObject,
      };
      const [result, error] = await documentUploadList(getData);
      this.loading = false;
      if (error) return;
      if (result) {
        const { data, count } = result;
        this.tableData = data;
        this.tableData.forEach((element) => {
          element.fileSize = changeByte(Number(element.fileSize));
        });
        this.tablePage.total = count;
      }
    },
    // 文件下载-删除
    async documentUploadRemove(recordId) {
      const [result, error] = await documentUploadRemove(recordId);
      if (error) return;
      if (result) {
        this.$message.success('删除成功');
        this.documentUploadList();
      }
    },
    // 文件下载-批量删除
    async documentUploadRemoveBatch(data) {
      const [result, error] = await documentUploadRemoveBatch(data);
      if (error) return;
      if (result) {
        this.$message.success('删除成功');
        this.documentUploadList();
      }
    },
    /**
     * 接口处理数据End
     */
  },
};
</script>

<style lang="less" scoped>
.container {
  background-color: #f4f4f4;
}
// table操作按钮
.operate-button {
  display: inline-block;
  margin-right: 16px;
  padding: 8px 0;
  color: #1677ff;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  cursor: pointer;
}
/deep/.ant-select-dropdown-menu-item {
  text-align: left;
}
</style>
