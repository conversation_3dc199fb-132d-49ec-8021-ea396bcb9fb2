<template>
  <div class="tab-radio">
    <a-radio-group :value="value" @change="onStatusChange">
      <a-radio-button
        v-for="item in radioList"
        :value="item.value"
        :key="item.id"
      >
        {{ item.label }}
      </a-radio-button>
    </a-radio-group>
  </div>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'update',
  },
  props: {
    value: {
      type: [String, Number],
    },
    radioList: {
      type: Array,
    },
  },
  data() {
    return {};
  },
  methods: {
    onStatusChange(e) {
      const val = e.target.value;
      if (this.value !== val) {
        this.$emit('update', val);
        this.$emit('change', val);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.tab-radio {
  background-color: #f2f3f5;
  padding: 3px;
  .ant-radio-button-wrapper {
    border: none;
    height: 26px;
    line-height: 26px;
    background-color: transparent;
    border-radius: 0 !important;
  }
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    background-color: #fff;
  }
  .ant-radio-button-wrapper:not(:first-child)::before {
    background: #e5e6e8;
    top: 6px;
    bottom: 6px;
    height: auto;
  }
}
</style>
