<template>
  <page-layout>
    <table-component
      ref="tableComponent"
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :parentData="params"
      :pageName="pageName"
      :notExportAll="true"
    ></table-component>
  </page-layout>
</template>

<script>
import { institutionsMixin } from '../mixins/institutionsMixin';
import tableComponent from '@/views/basicData/components/tableComponent';
import { filterOption } from '@/utils';
import FuzzySelect from '@/components/FuzzySelect';
import moment from 'moment';
import options from '@/utils/dict/DictOptions';

export default {
  name: 'riskInvesmentManage',
  components: { tableComponent },
  dicts: ['enterprise_industry_type'],
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'riskInvesmentManage',
      industryList: [],
      params: {
        year: { startValue: '' },
        enterpriseName: '',
        unifiedCreditCode: this.$route.query.pictureId,
        parkId: undefined,
      },
    };
  },
  computed: {
    tableColumn() {
      return [
        {
          field: '',
          title: '',
          type: 'checkbox',
          fixed: 'left',
          width: 70,
        },
        {
          field: '',
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: 70,
        },
        {
          field: 'year',
          title: '年份',
          width: 200,
          formatter: ({ cellValue }) => {
            return cellValue ? moment(cellValue).format('YYYY') : '';
          },
        },
        {
          field: 'parkName',
          title: '所属园区',
          width: 200,
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          width: 200,
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          width: 200,
        },
        {
          field: 'industryType',
          title: '产业类型',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(
              cellValue,
              this.dict?.type?.enterprise_industry_type || []
            );
          },
        },
        {
          field: 'investor',
          title: '投资方',
          width: 200,
        },
        {
          field: 'money',
          title: '金额（万元）',
          width: 200,
        },
        {
          field: 'remark',
          title: '备注',
          width: 200,
        },
        {
          field: 'updateBy',
          title: '更新人',
          width: 200,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          width: 200,
        },
      ];
    },
    filterOptions() {
      return {
        //筛选控件配置
        config: [
          {
            field: 'year',
            title: '年份',
            element: 'slot',
            slotName: 'year',
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            itemProps: {
              labelCol: { span: 10 },
              wrapperCol: { span: 14 },
            },
          },
          {
            field: 'parkId',
            title: '所属园区',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.parkList,
              showSearch: true,
              filterOption: filterOption,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        submitBtn: true,
        okBtn: false,
        addBtn: false,
        viewBtn: true,
        editBtn: true,
        delBtn: false,
        menu: true,
        menuWidth: 200,
        menuFixed: 'right',
        formConfig: [
          {
            field: 'year',
            title: '年份',
            element: (h, item, params) => {
              return (
                <BuseRangePicker
                  type="year"
                  needShowSecondPicker={() => false}
                  value={params.year}
                  placeholder="请选择年份"
                  onChange={(val) => {
                    params.year = val;
                  }}
                  format="YYYY"
                  disableDateFunc={(val) => val.isAfter(moment())}
                />
              );
            },
            rules: [
              {
                required: true,
                message: '请选择年份',
                validator: (rule, val, callback) => {
                  if (!val) callback('请选择年份');
                  if (!val.startValue) callback('请选择年份');
                  callback();
                },
              },
            ],
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
            rules: [{ required: true, message: '请输入企业名称' }],
            element: (h, item, params) => {
              return (
                <FuzzySelect
                  value={params?.enterpriseName}
                  onChangeSelect={(val) => {
                    if (val) {
                      this.$refs.tableComponent.setForm({
                        enterpriseName: val.name,
                        unifiedCreditCode: val.unifiedCreditCode,
                        parkName: val.parkName,
                        parkId: val.parkId,
                      });
                    } else {
                      this.$refs.tableComponent.setForm({
                        enterpriseName: '',
                        unifiedCreditCode: '',
                        parkName: '',
                        parkId: '',
                      });
                    }
                  }}
                  disabled={this.modelTitle == 'see'}
                />
              );
            },
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              disabled: true,
              placeholder: '请输入统一社会信用代码',
            },
          },
          {
            field: 'parkName',
            title: '所属园区',
            props: {
              disabled: true,
              placeholder: '请输入所属园区',
            },
          },
          {
            field: 'industryType',
            title: '产业类型',
            element: 'a-select',
            props: {
              options: this.dict?.type?.enterprise_industry_type || [],
              showSearch: true,
              filterOption: filterOption,
            },
            previewFormatter: (value) => {
              return this.translateValue(
                value,
                this.dict?.type?.enterprise_industry_type
              );
            },
            rules: [{ required: true, message: '请选择产业类型' }],
          },
          {
            field: 'investor',
            title: '投资方',
            props: {
              placeholder: '请输入投资方',
              maxLength: 30,
            },
            rules: [{ required: true, message: '请输入投资方' }],
          },
          {
            field: 'money',
            title: '金额 (万元)',
            element: 'a-input-number',
            props: {
              step: 0.11,
              min: 0,
              max: 99999999999,
            },
            rules: [
              { required: true, message: '请输入金额', trigger: 'change' },
              {
                pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
                trigger: 'blur',
                message: '请输入大于或等于零的数字，最多保留两位小数。',
              },
            ],
          },
          {
            field: 'remark',
            title: '备注',
            element: 'a-textarea',
            props: {
              placeholder: '请输入备注',
              maxLength: 30,
            },
          },
        ],
      };
    },
  },
  created() {},
  methods: {},
};
</script>

<style lang="scss" scoped></style>
