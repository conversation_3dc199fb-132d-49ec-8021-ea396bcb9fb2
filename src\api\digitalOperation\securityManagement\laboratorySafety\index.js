import { request } from '@/utils/request/requestTkb';

/**
 *实验室安全表格数据
 *@param data
 *
 */
export function laboratorySafetyList(data = {}) {
  return request({
    url: '/laboratorySafety/list',
    method: 'post',
    data,
  });
}

/**
 *新增检查
 * @param {*} data
 * @returns
 */
export function laboratorySafetySave(data = {}) {
  return request({
    url: '/laboratorySafety/save',
    method: 'post',
    data,
  });
}

/**
 * 删除
 * id
 * */
export function laboratorySafetyDelete(data = {}) {
  return request({
    url: '/laboratorySafety/delete',
    method: 'post',
    data,
  });
}
