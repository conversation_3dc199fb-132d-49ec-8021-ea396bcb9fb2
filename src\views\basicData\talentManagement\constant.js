import moment from 'moment';
// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    width: 60,
    fixed: 'left',
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
    fixed: 'left',
  },
  {
    field: 'year',
    title: '年份',
    width: 120,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY') : '';
    },
  },
  {
    field: 'unifiedCreditCode',
    title: '统一社会信用代码',
    width: 160,
  },
  {
    field: 'enterpriseName',
    title: '企业名称',
    width: 160,
  },
  {
    field: 'talentName',
    title: '姓名',
    width: 160,
  },
  {
    field: 'levelTypeName',
    title: '入选级别',
    width: 100,
  },
  {
    field: 'projectManTypeName',
    title: '人才/项目类别',
    minWidth: 160,
  },
  {
    field: 'projectName',
    title: '项目名称',
    width: 160,
  },
  {
    field: 'money',
    title: '项目资金(万元)',
    width: 160,
  },
  {
    field: 'teamNumbers',
    title: '团队人员',
    width: 160,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 150,
  },
  {
    field: 'updateBy',
    title: '更新人',
    minWidth: 150,
  },
  {
    field: 'updateTime',
    title: '更新时间',
    minWidth: 150,
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 160,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'talentName',
    title: '姓名',
    element: 'a-input',
  },
  {
    field: 'enterpriseName',
    title: '企业名称',
    element: 'a-input',
  },
  {
    field: 'unifiedCreditCode',
    title: '统一社会信用代码',
    props: {
      placeholder: '请输入统一社会信用代码',
    },
    itemProps: {
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
    },
  },
  {
    field: 'projectManType',
    title: '人才/项目类别',
    element: 'a-select',
    props: {
      placeholder: '请选择人才/项目类别',
      options: [],
    },
  },
  {
    field: 'year',
    title: '年份',
    element: 'slot',
    slotName: 'year',
  },
];

export const initFormValue = () => {
  return {
    year: { startValue: '' },
    enterpriseId: '',
    enterpriseName: '',
    talentName: '',
    unifiedCreditCode: '',
    level: undefined,
    projectManType: undefined,
    projectName: '',
    money: '',
    teamNumbers: '',
    remark: '',
  };
};
