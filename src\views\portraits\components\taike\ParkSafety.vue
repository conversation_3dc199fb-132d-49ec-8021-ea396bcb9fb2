<template>
  <PortraitCard :span="24" v-bind="$attrs">
    <div class="card-wrap">
      <div class="card-item">
        <div class="title">企业安全生产综合符合率(近期)</div>
        <div class="num">
          <span class="num-value">{{
            formatNumber(pictureInfo.safetyProductionComprehensive)
          }}</span>
          %
        </div>
      </div>
      <div class="card-item">
        <div class="title">消防安全检查合格率(平均)</div>
        <div class="num">
          <span class="num-value">{{
            formatNumber(pictureInfo.fireSafetyComprehensive)
          }}</span>
          %
        </div>
      </div>
    </div>
  </PortraitCard>
</template>

<script>
import { formatNumber } from '@/utils';
import PortraitCard from '../PortraitCard.vue';
export default {
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
  },
  components: { PortraitCard },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    console.log(this.pictureInfo, 'pictureInfo');
  },
  methods: {
    formatNumber,
  },
};
</script>

<style scoped lang="less">
.card-wrap {
  padding: 10px 0;
  height: 125px;
  display: flex;
  gap: 20px;
  .card-item {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    padding: 0 20px;
    &:nth-of-type(1) {
      background-image: url('@/assets/images/portraits/1-2020.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    &:nth-of-type(2) {
      background-image: url('@/assets/images/portraits/1-2021.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: 5px 0;
    }
    .title {
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0px;
      font-variation-settings: 'opsz' auto;
      color: #333333;
      margin-bottom: 5px;
    }
    .num {
      font-size: 14px;
      font-weight: 500;
      color: rgba(4, 15, 36, 0.65);
      display: flex;
      align-items: center;
      .num-value {
        font-family: D-DIN;
        font-size: 40px;
        font-weight: bold;
        color: rgba(4, 15, 36, 0.85);
      }
    }
  }
}
</style>
