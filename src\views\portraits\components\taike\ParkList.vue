<template>
  <div class="enterpriseL-list">
    <h3>园区列表</h3>
    <a-input-search
      placeholder="快速搜索园区"
      style="width: 100%"
      @search="onSearch"
      @change="onSearch"
    />
    <div class="tag-box">
      <a-empty v-if="!showList.length"></a-empty>
      <span
        v-for="(item, index) in showList"
        :title="item.parkName"
        :class="['tag-item', 'signal-line', 'tag-item-' + (index % 5)]"
        :key="index"
        @click="openParkDetail(item)"
        >{{ item.parkName }}</span
      >
    </div>
  </div>
</template>
<script>
export default {
  props: {
    pictureId: {
      type: String,
      default: '',
    },
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    parkList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showList: [],
      visible: false,
      parkId: undefined,
    };
  },
  watch: {
    parkList(val) {
      this.showList = [...val];
    },
  },
  methods: {
    onSearch(val) {
      const value = val?.target?.value ?? val;
      this.showList = value
        ? this.parkList.filter((x) => x?.parkName?.includes(value))
        : this.parkList;
    },
    openParkDetail(item) {
      this.$emit('openModal', 'parkPortraits', item?.id || '');
    },
  },
};
</script>
<style lang="less" scoped>
.enterpriseL-list {
  padding: 16px 24px 0;
  border-radius: 8px;
  background: linear-gradient(155deg, #00ff84 -74%, #fcfcfc 11%);
  h3 {
    font-family: PingFang SC;
    font-size: 20px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0em;
    font-variation-settings: 'opsz' auto;
    /* 文字/333333 */
    color: #333333;
  }
  .tag-box {
    background-color: #fff;
    margin-top: 16px;
    border-radius: 8px;
    padding: 16px;
    height: 328px;
    overflow: hidden auto;

    scrollbar-color: auto;
    ::-webkit-scrollbar {
      width: 8px; /* 滚动条宽度 */
      background: #eeeeee;
    }

    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
      background-color: #b8b8b8; /* 滚动条滑块颜色 */
      border-radius: 4px;
    }
  }
}
.tag-item {
  display: inline-block;
  margin-bottom: 6px;
  margin-right: 12px;
  border-radius: 6px;
  border: none;
  padding: 8px 16px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: normal;
  font-size: 18px;
  line-height: 25px;
  letter-spacing: 0px;
  background: #f5f5f5;
  max-width: (100%);
  cursor: pointer;
  user-select: none;
}
</style>
