<template>
  <page-layout>
    <BuseCrud
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      :tableOn="{
        'checkbox-change': selectChangeEvent,
        'checkbox-all': selectChangeEvent,
      }"
      :modalConfig="modalConfig"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
    >
      <template #defaultTitle>
        <span></span>
      </template>
      <!-- 创建按钮区域插槽 -->
      <template #defaultHeader>
        <a-button
          type="primary"
          style="margin-right: 8px"
          @click="handleCreate"
        >
          新增
        </a-button>
        <a-button style="margin-right: 8px" @click="onClickImport">
          导入
        </a-button>
        <ExportButton
          :notExportAll="true"
          :checkItems="checkItems"
          :pageName="pageName"
          :params="filterOptions.params"
        />
        <a-button
          type="danger"
          style="margin-right: 8px"
          :disabled="!checkItems.length"
          @click="handelDelete"
        >
          删除
        </a-button>
      </template>
      <!-- 年插槽 -->
      <template #year>
        <BuseRangePicker
          type="year"
          :needShowSecondPicker="() => false"
          v-model="filterParams.year"
          format="YYYY"
          placeholder="请选择年份"
          :disableDateFunc="disableDateFunc"
        />
      </template>
      <!-- 企业 -->
      <template #enterSlot>
        <a-select
          :allowClear="true"
          :showSearch="true"
          placeholder="请选择"
          @change="onEnterChange"
        >
          <a-select-option
            v-for="item in stateSelect"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
      </template>
      <!-- 编辑弹窗 -->

      <ListModal
        :visible="visible"
        :detail="modalData"
        :modelTitle="modelTitle"
        :preview="preview"
        @loadData="loadData"
        @handleCancel="handleCancel"
        :dictData="dict.type"
      />
    </BuseCrud>
  </page-layout>
</template>

<script>
import moment from 'moment';
import { institutionsMixin } from '../mixins/institutionsMixin';
import { defaultFilterConfig } from './constant';
import {
  pageProEmphasis,
  deleteProEmphasis,
  getParkList,
} from '@/api/basicData';
import ListModal from './components/ListModal.vue';
import ExportButton from '@/views/basicData/components/ExportButton.vue';
import DictData from '@/components/DictData';
import { filterOption } from '@/utils/index';
export default {
  components: { ListModal, ExportButton },
  dicts: [
    'my_notify_rule',
    'recognition_level',
    'determine_status',
    'appear_market',
  ],
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'professionManage',
      loading: false,
      filterParams: {
        type: undefined,
        enterpriseName: '',
        unifiedCreditCode: '',
        parkId: undefined,
        year: { startValue: '' },
        determineStatus: undefined,
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'checkbox',
          fixed: 'left',
          width: 60,
        },
        {
          type: 'seq',
          title: '序号',
          fixed: 'left',
          width: 60,
        },

        {
          field: 'enterpriseName',
          title: '企业名称',
          width: 160,
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          width: 160,
        },
        {
          field: 'registeredAddress',
          title: '企业地址',
          width: 130,
        },
        {
          field: 'parkName',
          title: '所属园区',
          width: 130,
        },
        {
          field: 'type',
          title: '认定级别',
          minWidth: 150,
          formatter: ({ cellValue }) => {
            return this.dict?.type?.recognition_level?.find(
              (q) => q.value == cellValue
            )?.label;
          },
        },
        {
          field: 'year',
          title: '认定年份',
          width: 110,
        },
        {
          field: 'determineStatus',
          title: '认定状态',
          minWidth: 150,
          formatter: ({ cellValue }) => {
            return this.dict?.type.determine_status?.find(
              (q) => q.value == cellValue
            )?.label;
          },
        },
        {
          field: 'remark',
          title: '备注',
          width: 130,
        },
        {
          field: 'supportAmount',
          title: '支持金额 (万元)',
          width: 130,
        },
        {
          field: 'establishedTime',
          title: '成立时间',
          width: 130,
        },
        {
          field: 'fieldIndustry',
          title: '领域',
          width: 130,
        },
        {
          field: 'productName',
          title: '产品名称',
          width: 130,
        },
        {
          field: 'appearMarket',
          title: '是否上市',
          width: 110,
          formatter: ({ cellValue }) => {
            return cellValue == 1
              ? '是'
              : cellValue == 2
              ? '否'
              : cellValue == 3
              ? '其它'
              : '';
          },
        },
        {
          field: 'contacts',
          title: '联系人',
          width: 140,
        },
        {
          field: 'contactInformation',
          title: '联系方式',
          width: 140,
        },
        {
          field: 'updateBy',
          title: '更新人',
          width: 140,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          minWidth: 180,
          formatter: ({ cellValue }) => {
            return cellValue
              ? moment(cellValue).format('yyyy-MM-DD HH:mm:ss')
              : '';
          },
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 100,
          fixed: 'right',
        },
      ],
      tableData: [],
      visible: false,
      modalData: null,
      checkItems: [],
      modelTitle: 'add',
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
      detail: {},
      preview: false,
      parkList: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'type',
            title: '认定级别',
            element: 'a-select',
            props: {
              placeholder: '请选择认定级别',
              options: this.dict.type.recognition_level,
              filterOption: (inputValue, option) => {
                return (
                  option.children
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                );
              },
            },
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
            element: 'a-input',
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            element: 'a-input',
            itemProps: {
              labelCol: { span: 12 },
              wrapperCol: { span: 12 },
            },
          },
          {
            field: 'parkId',
            element: 'a-select',
            title: '所属园区',
            props: {
              placeholder: '请选择所属园区',
              options: this.parkList,
              showSearch: true,
              filterOption: filterOption,
            },
          },
          {
            field: 'year',
            title: '认定年份',
            element: 'slot',
            slotName: 'year',
          },
          {
            field: 'determineStatus',
            title: '认定状态',
            element: 'a-select',
            props: {
              placeholder: '请选择认定状态',
              options: this.dict.type.determine_status,
              showSearch: true,
              filterOption: filterOption,
            },
          },
        ],
        params: this.filterParams,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        menu: false,
      };
    },
  },
  created() {
    this.getParkList();
    this.loadData();
  },
  methods: {
    // 下拉联动
    onEnterChange() {
      console.log(222);
    },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const [res, err] = await pageProEmphasis({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...this.filterParams,
        year: this.filterParams.year?.startValue
          ? moment(this.filterParams.year?.startValue)?.format('YYYY')
          : '',
      });
      this.loading = false;

      if (err) return;
      // 设置数据
      this.tablePage.total = res.total;
      this.tableData = res.data;
      this.checkItems = [];
    },
    // 创建按钮点击事件
    handleCreate() {
      this.modelTitle = 'add';
      this.visible = true;
    },
    // 编辑按钮点击事件
    onClickEdit(row) {
      this.modelTitle = 'edit';
      this.visible = true;
      this.modalData = row;
    },
    onClickDetail(row) {
      this.modelTitle = 'see';
      this.modalData = row;
      this.preview = true;
      this.visible = true;
    },
    // 关闭弹窗
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
      this.modalData = null;
      this.manageVisible = false;
      this.preview = false;
    },
    // 管理按钮点击事件
    onClickManage() {
      this.manageVisible = true;
    },
    // 删除
    handelDelete() {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [, err] = await deleteProEmphasis({
            list: that.checkItems,
          });
          if (!err) {
            that.$message.success('删除成功!');
            that.checkItems = [];
            // 刷新数据
            that.loadData();
            return;
          }
        },
      });
    },
    // 园区类别
    stateChange(value) {
      console.log('选中值', value);
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: this.pageName,
        },
      });
    },
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    // 获取园区列表
    async getParkList() {
      const [res] = await getParkList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.parkList = res.data;
      }
    },
  },
};
</script>
<style scoped>
/deep/.page-wrapper-container {
  margin: 0 !important;
}
</style>
