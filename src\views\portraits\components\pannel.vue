<template>
  <div class="pannel">
    <div class="header">
      <div class="icon"></div>
      <h3 class="title"><slot name="titleName"></slot></h3>
      <div class="logo"><slot name="titleLog"></slot></div>
    </div>
    <div class="content">
      <slot name="cardContent"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'pannel',
  props: {
    titleName: {
      type: String,
      default: function () {
        return '--';
      },
    },
  },
};
</script>

<style scoped lang="scss">
.pannel {
  background: white;
  display: flex;
  flex-direction: column;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 12px;
  .header {
    display: flex;
    flex-direction: row;
    .icon {
      margin-top: 2px;
      width: 4px;
      height: 20px;
      border-radius: 2px;
      background: #1890ff;
      margin-right: 8px;
    }

    .title {
      width: 50%;
      font-size: 20px;
      font-weight: 500;
      line-height: 24px;
      margin-bottom: unset;
    }
    .logo {
      width: 50%;
      display: flex;
      justify-content: flex-end;
    }
  }
  .content {
    margin-top: 24px;
  }
}
</style>
