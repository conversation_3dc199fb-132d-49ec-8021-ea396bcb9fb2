<template>
  <a-modal
    width="600px"
    :title="modelTitle === 'add' ? '新增' : '编辑'"
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm ref="ruleForm" :config="formConfig" :params="formValue">
        <template #statisticType>
          <a-select @change="typeChange" v-model="formValue.workingType">
            <a-select-option
              v-for="item in typeSelect"
              :key="item.value"
              :value="item.value"
              >{{ item.label }}</a-select-option
            >
          </a-select>
        </template>
        <template #workTime>
          <div v-if="modelTitle == 'add'">
            <div class="time-box" v-if="timeType == 1">
              <BuseRangePicker
                type="year"
                :needShowSecondPicker="() => false"
                format="YYYY"
                v-model="formValue.rateTime"
                :disableDateFunc="disableDateFunc"
              />
            </div>
            <div class="time-box" v-else>
              <BuseRangePicker
                type="month"
                :needShowSecondPicker="() => false"
                format="YYYY-MM"
                v-model="formValue.rateTime"
                :disableDateFunc="disableDateFunc"
              />
            </div>
          </div>
          <div v-else>
            <div
              class="time-box"
              v-if="(detail && detail.workingType === '1') || timeType == 1"
            >
              <BuseRangePicker
                type="year"
                :needShowSecondPicker="() => false"
                format="YYYY"
                v-model="formValue.rateTime"
                :disableDateFunc="disableDateFunc"
              />
            </div>
            <div class="time-box" v-else>
              <BuseRangePicker
                type="month"
                :needShowSecondPicker="() => false"
                format="YYYY-MM"
                v-model="formValue.rateTime"
                :disableDateFunc="disableDateFunc"
              />
            </div>
          </div>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment';
import { initFormValue } from '../constant';
import { saveInformation, editInformation } from '@/api/basicData';
export default {
  props: ['visible', 'detail', 'isLook', 'modelTitle', 'indicator'],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          const rateTime =
            this.detail?.workingType === '1'
              ? moment(this.detail.rateTime, 'YYYY')
              : moment(this.detail.rateTime, 'YYYY-MM');
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
            rateTime: {
              endOpen: false,
              endValue: null,
              startOpen: true,
              startValue: rateTime,
            },
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
      typeSelect: [
        {
          value: '2',
          label: '月',
        },
      ],
      timeType: '',
      isopen: false,
      yearValue: '',
    };
  },
  computed: {
    formConfig() {
      const formConfig = [
        {
          field: 'workingType',
          title: '统计类型',
          element: 'slot',
          rules: [{ required: true, message: '请输入' }],
          slotName: 'statisticType',
          formatter: ({ row }) => {
            console.log('时间', row);
          },
        },
        {
          field: 'rateTime',
          title: '时间',
          element: 'slot',
          slotName: 'workTime',
          rules: [{ required: true, message: '请输入' }],
          defaultValue: undefined,
        },
        {
          field: 'indicator',
          title: '指标',
          element: 'a-select',
          rules: [{ required: true, message: '请输入' }],
          props: {
            options: this.indicator,
            showSearch: true,
            optionFilterProp: 'children',
          },
          on: {
            change: this.handleIndicatorChange,
          },
        },
        {
          field: 'value',
          title: '值',
          element: 'a-input',
        },
        {
          field: 'unit',
          title: '单位',
          element: 'a-input',
          props: {
            disabled: true,
          },
        },
      ];
      return formConfig;
    },
  },
  methods: {
    handleIndicatorChange(val) {
      console.log(arguments);
      this.formValue.unit =
        this.indicator?.find((x) => x.value === val)?.source?.unit || '个';
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let rateTime = '';
          this.formValue = {
            ...this.formValue,
            idmType: '2',
          };
          rateTime =
            this.timeType == '1'
              ? moment(this.formValue.rateTime?.startValue).format('YYYY')
              : moment(this.formValue.rateTime?.startValue).format('YYYY-MM');
          this.formValue.rateTime = rateTime;
          if (this.modelTitle === 'add') {
            this.saveInformation();
          } else {
            this.editInformation();
          }
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 类型切换
    typeChange(value) {
      this.timeType = value;
      if (this.modelTitle == 'edit') {
        this.detail.workingType = ''; // eslint-disable-line
      }
    },
    // 新增
    async saveInformation() {
      const [, err] = await saveInformation(this.formValue);
      if (err) return;
      this.$emit('targetLoadData');
    },
    handleStartChange(value) {
      console.log('选中值', value);
    },
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    // 新增
    async editInformation() {
      const [res, err] = await editInformation(this.formValue);
      if (err) return;
      console.log('编辑成功', res);
      this.$emit('targetLoadData');
    },
    timeChange(value) {
      console.log('时间', value);
    },
  },
};
</script>
