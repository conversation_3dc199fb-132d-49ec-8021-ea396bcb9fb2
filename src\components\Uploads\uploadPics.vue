<template>
  <div>
    <a-form :form="form">
      <div class="clearfix">
        <a-upload
          :disabled="disabled"
          :accept="accept"
          list-type="picture-card"
          :file-list="fileList"
          :customRequest="customRequestPic"
          :before-upload="beforeUploadPic"
          @change="handleChangePic"
          @preview="handlePreview"
          :default-file-list="fileList"
        >
          <div v-if="fileList.length < maxCount">
            <a-icon type="plus" />
            <div class="ant-upload-text"></div>
          </div>
        </a-upload>
        <a-modal
          :visible="previewVisible"
          :footer="null"
          @cancel="handleCancel"
        >
          <img alt="example" style="width: 100%" :src="previewImage" />
        </a-modal>
      </div>
    </a-form>
  </div>
</template>

<script>
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}
import { fileUpload } from '@/api/upload/index.js';
export default {
  props: {
    //弹框类型
    disabled: {
      type: Boolean,
      default: false,
    },
    //上传文件类型
    accept: {
      type: String,
      default: '',
    },
    //
    activeIndex: {
      type: Number,
      default: 1,
    },
    maxSize: {
      type: Number,
      default() {
        return 100000;
      },
    },
    maxCount: {
      type: Number,
      default() {
        return 8;
      },
    },
    fileListTemp: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      previewVisible: false,
      previewImage: '',
      form: this.$form.createForm(this),
      urls: '', //上传成功后url集合
      photos: [],
      photoMap: null,
      fileList: [],
      isLt1M: true,
    };
  },

  mounted() {
    //图片回显
    // this.imgHander();
  },
  watch: {
    fileListTemp: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.fileList = val;
          });
        }
      },
      immediate: true, // 立即执行
    },
  },
  methods: {
    //图片回显和数据处理

    handleCancel() {
      this.previewVisible = false;
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },
    beforeUploadPic(file) {
      this.isLt1M = file.size / 1024 / 1024 < this.maxSize; //默认50kb
      if (!this.isLt1M) {
        console.log(file, 'file------');
        this.uploadFlag = false;
        this.$message.error(`文件大小不能超过 ${this.maxSize}M `);
      }
    },
    handleChangePic(info) {
      console.log(info, '0000000');
      this.fileList = info.fileList;
      if (info.file.status === 'removed') {
        this.$emit('setNewsImgs', info.fileList);
        return;
      }
      if (info.file.status === 'uploading') {
        this.loading = true;
        return;
      }
      if (info.file.status === 'done') {
        const fileReader = new FileReader();
        fileReader.readAsDataURL(info.file.originFileObj);
        this.getBase64(info.file.originFileObj, (imageUrl) => {
          this.imageUrl = imageUrl;
          this.loading = false;
        });
      }
    },
    getBase64(img, callback) {
      const reader = new FileReader();
      reader.addEventListener('load', () => callback(reader.result));
      reader.readAsDataURL(img);
    },
    customRequestPic(data) {
      // 上传提交
      if (!this.isLt1M) {
        return false;
      }
      const formData = new FormData();
      formData.append('file', data.file);
      this.uploadfilePic(formData, data);
    },
    // 文件上传
    async uploadfilePic(formData, data) {
      const [res] = await fileUpload({ file: data.file });

      console.log(res, 'obj----');
      if (res.code === '10000') {
        const responseInfo = {
          fileName: res.data.fileName,
        };
        data.onSuccess(responseInfo, data.file);
        this.$emit('setNewsImgs', this.fileList);

        this.$message.success('文件保存成功');
      } else {
        this.$emit('setNewsImgs', null);
        this.$message.error('文件保存失败');
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-upload-list-picture-card-container {
  width: 120px;
  height: 120px;
}
/deep/.ant-upload-list-picture-card .ant-upload-list-item {
  width: 120px;
  height: 120px;
}
/deep/.ant-upload.ant-upload-select-picture-card {
  width: 120px;
  height: 120px;
}
/deep/.ant-upload-list-picture-card .ant-upload-list-item-thumbnail,
/deep/.ant-upload-list-picture-card .ant-upload-list-item-thumbnail img {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
