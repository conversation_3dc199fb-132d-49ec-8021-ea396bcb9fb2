<template>
  <div class="full-screen"><screen :url="url"></screen></div>
</template>

<script>
import screen from '@/components/bigScreen';
import getBigScreenUrl from '@/global/bigScreen/bigScreenUrl';

export default {
  name: 'greenDevelopmentHeatMap',
  components: { screen },
  data() {
    return {
      url: getBigScreenUrl.call(this),
    };
  },
  mounted() {
    document.title = '绿色发展一张图';
  },
};
</script>

<style scoped></style>
