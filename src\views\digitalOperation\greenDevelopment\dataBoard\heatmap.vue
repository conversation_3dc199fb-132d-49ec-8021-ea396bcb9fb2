<template>
  <div>
    <svg
      class="heatmap-svg"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      fill="none"
      version="1.1"
      viewBox="0 0 912 230"
    >
      <defs>
        <clipPath id="master_svg0_1037_02124">
          <rect x="0" y="0" width="912" height="230" rx="0" />
        </clipPath>
      </defs>
      <g clip-path="url(#master_svg0_1037_02124)">
        <g>
          <g>
            <path
              d="M792.84537265625,137.75010234375L771.96537265625,134.87010234375L770.61537265625,135.55010234374998L765.04537265625,133.37010234375L763.66537265625,131.74010234374998L763.08537265625,129.84010234375L743.64538265625,104.86010234375L741.43536265625,102.99010234375001Q738.71539265625,101.11010234375,738.11535665625,100.44010234375C737.51531965625,99.77010234375,737.64538575625,98.49010234375001,738.08532665625,97.62010234375Q738.52532965625,96.74010234375001,745.68530265625,81.60010234375Q748.35527265625,73.10010234375,749.13527265625,72.05010234375Q749.91537265625,71.00010234375,761.35527265625,65.00010234375Q762.88527265625,63.79010234375,763.85527265625,62.730102343750005C764.82527265625,61.67010234375,768.18527265625,56.67010234375,770.54527265625,52.69008234375C772.90527265625,48.71008234375,775.85527265625,43.690086343749996,776.20527265625,43.65008544875Q776.55527265625,43.61008454375,791.15527265625,52.280082343749996L803.69527265625,59.56010234375L795.13527265625,74.57010234375L811.49527265625,84.18010234375001Q812.53517265625,84.64010234375,812.20527265625,85.93010234375001Q811.88527265625,87.21010234375001,806.09527265625,110.42010234375L807.37527265625,111.41010234375L806.75527265625,114.61010234375L803.2952726562501,114.48010234375L793.34527265625,137.22010234375L792.86527265625,137.75010234375L792.84537265625,137.75010234375Z"
              fill="#5996EF"
              fill-opacity="1"
              data-id="6"
            />
          </g>
          <g>
            <path
              d="M172.91552734375,115.110087890625L206.50552734375,121.070087890625L206.03552734375,103.180087890625L174.89552734375,100.090087890625L172.91552734375,115.110087890625Z"
              fill="#5996EF"
              data-id="18"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M163.66567989375,49.02000434875488L163.57568359375,51.38000434875488Q163.60568239375,51.67000434875489,163.89568359375,51.67000434875489Q164.18568459375,51.67000434875489,176.50568359375,51.360004348754885Q176.88568359375,51.44000434875488,176.86568359375,51.06000434875488Q176.84568359375,50.680004348754885,176.83568359375,48.27000434875488L176.04568359375,47.19000434875488L165.35568359375,47.39000534875488Q165.17568359375,47.39000534875488,164.99568359375,47.50000534875488Q164.81568359375,47.61000634875488,164.63568359375,47.80000534875488L163.84568059375,48.63000434875488C163.74568159375,48.74000434875488,163.68567659375,48.87000434875488,163.67568209375,49.02000434875488L163.66567989375,49.02000434875488Z"
              fill="#5996EF"
              data-id="10"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M295.265517921875,126.97011328125001L294.935515921875,126.71011328125Q294.815521221875,126.52011328124999,294.795516971875,126.35011328125Q294.775512721875,126.18011328125,294.825515721875,125.98011328125L295.195510921875,125.01011328125L295.555511921875,123.93011328125L296.075514921875,122.10011328125L296.465514921875,120.69011328125L296.88551492187503,119.22011328125001L297.245514921875,117.97011328125001L297.555514921875,116.87011328125L297.785504921875,116.02011328124999L298.505504921875,113.48011328125L298.835514921875,112.13011328125L299.305514921875,109.89011328125L299.88551492187503,107.29011328125L300.025514921875,103.84011328125L300.165514921875,100.38011328125L300.205504921875,96.97011328125001L300.235504921875,93.69007328125L299.985504921875,91.02007328125L299.575504921875,87.17007328125L299.335494921875,84.76007828125Q299.265484921875,84.52007628125,299.575504921875,84.14007928125Q299.88551492187503,83.76008228125,300.505494921875,83.96007918125L305.865494921875,84.75008028125L309.325494921875,85.27008328125L310.405494921875,85.46008328125L316.705494921875,87.08007328125L321.395494921875,88.82008328124999L326.405494921875,90.61008328125Q326.765494921875,90.83008328125,326.935494921875,91.17007328125Q327.105494921875,91.51007328125,326.965494921875,92.15008328125L325.105494921875,98.85011328125L323.535494921875,104.45011328125L322.785494921875,107.06011328125L320.775494921875,112.49011328125L318.765494921875,117.52011328124999L318.285494921875,118.65011328125L316.185494921875,123.16011328125L314.635494921875,126.19011328125L313.825494921875,127.85011328125Q313.595494921875,128.38011328125,313.245494921875,128.48011328125Q312.895494921875,128.58011328125,312.305494921875,128.49011328125L308.405494921875,127.89011328125L304.725504921875,127.55011328124999L300.665514921875,127.25011328125001L295.265517921875,126.96011328125L295.265517921875,126.97011328125001Z"
              fill="#5996EF"
              data-id="1"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M180.2156640625,74.28000222444534Q179.4456630625,74.38000222444535,179.3856580625,73.52000222444534Q179.3256530625,72.66000222444535,179.3656620625,68.94000222444535L179.2756650625,60.190002224445344L179.0956650625,52.510002224445344L178.7256620625,39.610002224445346L178.6856610625,33.98000222444534L178.5556640625,28.890002224445343Q178.6956630625,28.100002224445344,179.4456630625,28.010002224445344Q180.1956640625,27.920002224445344,187.1256640625,28.100002224445344L192.1156640625,28.170002224445344L197.4856640625,28.230002224445343L201.3956640625,28.250002224445343Q201.8556640625,28.400002224445345,202.1256640625,28.72000222444534Q202.3956640625,29.04000222444534,202.3756640625,29.670002224445344L202.2956640625,54.010002224445344Q202.3456640625,55.080002224445344,202.8756640625,55.48000222444534Q203.4056640625,55.88000222444534,205.0856640625,55.98000222444534Q206.0656640625,56.400002224445345,206.1256640625,57.190002224445344Q206.1856640625,57.98000222444534,206.1456640625,71.85000222444535Q206.0756640625,72.48000222444534,205.5256640625,72.61000222444534Q204.97566406250002,72.74000222444533,199.7156640625,72.46000222444533L194.5056640625,72.35000222444535L192.7256640625,72.21000222444533L190.6756640625,72.36000222444534L187.0656640625,73.05000222444534L180.1956640625,74.25000222444535L180.2156640625,74.28000222444534Z"
              fill="#5996EF"
              data-id="2"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M252.16532903125,140.33999237060547Q251.71533203125,140.21999237060547,251.71533203125,139.82999237060548L252.36532603125,137.06999237060546L253.32533203125,132.94998937060546Q253.44533203125,132.63999137060546,253.68533203125,132.45999137060548Q253.92534203125,132.27999117060546,254.40533203125,132.24999237060547L264.02533203125,132.46999337060547L272.76533203125,132.73999037060548L283.06533203125,133.41999237060546L285.16533203125,133.63999237060546Q285.58533203125,133.70999237060548,285.47533203125,134.05999237060547Q285.36533203125,134.40999237060547,284.45533203125,137.04999237060548L283.82533203125,138.84999237060546L282.23533203125,142.29999237060548L281.77533203125,143.33999237060547Q281.67533203125,143.56999237060546,281.44533203125,143.58999237060547Q281.21533203125,143.60999237060548,279.48533203125,143.08999237060547L277.16533203125,142.59999237060546L272.63533203125,141.54999237060548L271.70533203125,141.39998237060547L268.14533203125,140.91998237060548L265.40533203125,140.55998237060547L262.33533203125,140.47998237060546L257.88531203125,140.28998237060546L256.10531203125,140.25998237060546L253.77531203125,140.34997237060546L252.17530803125,140.34997237060546L252.16532903125,140.33999237060547Z"
              fill="#5996EF"
              data-id="4"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M93.45556640625,64.13L99.09556640625,66.27000000000001L101.66556640625001,68.13L103.39556640625,69.08L107.17556640625,71.06L111.84556640625,73.09L118.11556640625,74.44L118.96556640625,74.08L125.01556640625,73.50999999999999L129.70556640625,74.27000000000001L134.17556640625,73.81L138.15556640625,73.32L143.66556640625,74.25999999999999L143.95556640625,59.07L144.37556640625002,42.88L144.67556640625,33.519999999999996L144.39556640625,27.84L121.04556640625,27.99L121.05556640625,28.3L117.71556640625,28.15L112.68556640625,27.43L98.16556640625,27L96.54556640625,48.67L93.96556640625,48.53L93.45556640625,64.13Z"
              fill="#5996EF"
              data-id="5"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M398.885498046875,187.549951171875L410.615498046875,196.259951171875L418.105498046875,187.209951171875L415.435498046875,184.969951171875L418.415498046875,181.519961171875L410.355498046875,174.679947171875L408.965498046875,174.449951171875L398.885498046875,187.549951171875Z"
              fill="#5996EF"
              data-id="7"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M398.28564453125,164.16000366210938L397.03564453125,166.75000366210938L396.62564083125,168.57000366210937L396.53564453125,175.23000366210937L396.88565053125,178.17000366210937L397.54562453125,180.31000366210938L401.20562453125,182.95000366210937L408.33564453125,173.85000366210937L398.28564453125,164.16000366210938Z"
              fill="#5996EF"
              data-id="8"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M359.89563453125,149.45999908447266L354.03564453125,155.78998908447267L356.80563453125,158.77999908447265L358.69564453125,161.57999908447266L359.06564453125,161.84999908447264L359.96563453125,161.93999908447265L361.13565453125,161.97999908447267L362.74563453125,160.31999908447267L365.27564453125,156.88999908447266L365.09564453125,156.74999908447265L364.58564453125,156.88000908447265L360.79562453125,153.53999908447267L361.16564453125,153.10999908447266L359.63565453125,151.73999908447266L360.58563453125,150.69999908447267L361.70562453125,151.66999908447266L361.99563453125,151.29999908447266L359.89563453125,149.45999908447266Z"
              fill="#5996EF"
              data-id="9"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M162.595390625,47.620005275154114L152.575385625,47.23000527515411L152.405387625,47.21000527515412L152.275390625,47.09000527515411L152.655387625,33.48000527515411L152.765388625,30.020005275154112Q152.785385625,29.510000275154113,153.115386625,29.150000275154113Q153.445390625,28.790000675154115,154.005390625,28.860000575154114L162.645390625,29.040000275154114L163.065390625,38.150005275154115L162.675390625,47.34000527515411L162.645390625,47.510005275154114L162.595390625,47.63000527515412L162.595390625,47.620005275154114Z"
              fill="#5996EF"
              data-id="11"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M398.24560546875,162.56998046875L408.61560546875,172.54998046875L414.88560546875,164.48998046875L402.37560546875,152.72998046875L398.24560546875,162.56998046875Z"
              fill="#5996EF"
              data-id="12"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M294.80560546875,42.61000003814697L304.76560546875,42.86000003814697L303.76560546875,28.960000038146973L293.24560546875,29.560000038146974L294.80560546875,42.61000003814697Z"
              fill="#5996EF"
              data-id="15"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M385.83544921875,173.109990234375L393.46544921875,178.239990234375L394.19546921875,177.879990234375L393.66546921875,167.079990234375L390.69546921875,165.489990234375L385.83544921875,173.109990234375Z"
              fill="#5996EF"
              data-id="16"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M323.7255859375,119.090078125L329.5255859375,121.930078125L333.1555959375,127.400078125L343.9755859375,134.680078125L348.8555859375,129.350078125L354.0755859375,121.960078125L358.9455859375,113.940078125L331.7955959375,100.580078125L323.7255859375,119.090078125Z"
              fill="#5996EF"
              data-id="19"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M165.2855148125,38.779931640625L165.3655168125,43.969931640625L165.8155178125,44.719931640625L166.2455178125,44.929931640625L166.6755178125,45.089931640625Q175.9055078125,44.809931640625,175.9455078125,44.769931640625Q175.9855078125,44.729931640625,176.7355078125,44.359931640625L176.7555078125,39.009931640625L176.4455078125,36.079931640625L176.0055078125,35.679931640625L166.8855078125,35.949931640625L165.5355068125,35.949931640625L165.2355038125,35.949931640625L164.9155048125,36.069931640625L164.6455078125,36.369932640625L164.6755066125,38.269931640625L165.2855068125,38.799931640625L165.2855148125,38.779931640625Z"
              fill="#5996EF"
              data-id="0"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M368.6755659375,134.5499609375L363.4755859375,141.6099609375L363.8155819375,143.12996093750002L372.5055859375,151.0799609375L375.3455859375,154.2099609375L383.5255859375,161.2399609375L384.6055859375,161.3899609375L390.0255859375,154.4599609375L392.3355859375,151.6099609375L395.6755859375,147.0899609375L395.7155859375,141.4499609375L395.9755859375,141.0499609375L397.0255859375,135.7999609375L396.3355859375,134.9399609375L399.3255859375,130.8399609375L396.88558593749997,127.8699609375L394.6055859375,126.1799609375L395.2755859375,125.1499609375L384.4755859375,117.7099609375L376.9855859375,127.4499609375L374.8655859375,125.7899609375L370.2555859375,130.8299609375L370.0455659375,131.9999609375L368.6755659375,134.5499609375Z"
              fill="#5996EF"
              data-id="3"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M346.41552734375,147.140048828125Q346.49551394375,147.370058828125,350.47552734375,152.060048828125L358.51552734375,144.410048828125L354.03552734375,139.550048828125L346.41552734375,147.140048828125Z"
              fill="#5996EF"
              data-id="17"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M84.0755,99.530116640625L86.7955,91.200106640625C87.0455,90.440112640625,87.7855,89.960109740625,88.5855,90.050109840625L128.9355,94.690106640625C129.5555,94.760106640625,130.09550000000002,95.180106640625,130.3155,95.760106640625L134.8755,107.81010664062501C135.3355,109.03010664062501,134.2955,110.28010664062501,133.0155,110.06010664062501L85.3855,101.700106640625C84.3755,101.520106640625,83.7655,100.500106640625,84.0855,99.530116640625L84.0755,99.530116640625Z"
              fill="#5996EF"
              data-id="13"
              fill-opacity="1"
            />
          </g>
          <g>
            <path
              d="M202.77556640625,155.689921875L183.45556640625,149.799921875L187.90556640625,137.919921875L206.50556640625,142.579921875L202.77556640625,155.689921875Z"
              fill="#5996EF"
              data-id="14"
              fill-opacity="1"
            />
          </g>
        </g>
      </g>
      <foreignObject
        class="tooltip"
        x="0"
        y="0"
        width="104"
        height="25"
        style="display: none"
      >
        <div xmlns="http://www.w3.org/1999/xhtml" class="tooltip-content">
          <span> {{ detailNumber }}吨 </span>
        </div>
      </foreignObject>
    </svg>
  </div>
</template>

<script>
import * as d3 from 'd3';
import { offsets, DrawSeq, getStartPoint, DrawAddress } from './heatmap.js';
import _ from 'lodash/core';
export default {
  name: 'ManagementTkHeatmap',
  props: {
    //园区个数和id是固定的(参考UI图)，后端接口的数据要和UI图保持一致
    emissionData: {
      type: Array,
      default: () => [
        {
          name: '无锡（国家）软件园',
          id: 1,
          emission: 0,
        },
        {
          name: '中国物联网国际创新园',
          id: 2,
          emission: 0,
        },
      ],
    },
  },
  watch: {
    emissionData() {
      this.reset().init();
    },
  },
  data() {
    return {
      detailNumber: 0,
      preValue: null,
      time: null,
      extractedColors: [],
      idMap: new Map(),
    };
  },
  created() {
    this.getColors();
  },

  mounted() {
    this.svg = d3.select('.heatmap-svg');
    DrawAddress(this.svg);
    this.setParkSeq();
    this.popup = d3.select('.heatmap-svg').select('.tooltip');
    //把数据弹框放置到元素最前面，防止层级问题，被序号的UI挡住；
    const popupNode = this.popup.node();
    popupNode.parentNode.insertBefore(
      popupNode,
      popupNode.parentNode.lastChild
    );
    //添加监听事件
    this.addEventListener();
    this.init();
  },
  beforeDestroy() {
    clearImmediate(this.time);
  },
  methods: {
    reset() {
      this.svg.selectAll(`path[data-id]`).style('fill', '#5996EF');
      this.idMap.clear();
      return this;
    },
    init() {
      this.getEmissionRank();
      this.emissionData.forEach((item) => {
        //如果有排放数据就改变颜色
        if (Number(item.emission) > 0) {
          this.svg
            .selectAll(`path[data-id='${item.id}']`)
            .style('fill', this.extractedColors[item.rank - 1]);
        }
      });
    },
    highlight(id) {
      if (this.preValue) {
        this.svg
          .selectAll(`path[data-id='${this.preValue}']`)
          .style('filter', 'none');
      }
      if (!id) return;
      this.svg
        .selectAll(`path[data-id='${id}']`)
        .style('filter', 'drop-shadow(0px 0px 10px rgba(46, 248, 255, 0.5))');
      this.preValue = id;
    },
    showPopView(id) {
      const popOffset = offsets[id] || { x: -35, y: -30 };
      const pathElement = this.svg.selectAll(`path[data-id='${id}']`).node();
      const bbox = pathElement.getBBox();
      var x = bbox.x + popOffset.x;
      var y = bbox.y + popOffset.y;
      this.popup.attr('x', x).attr('y', y).style('display', 'block');
      const centerPoint = getStartPoint(pathElement);
      const endPoint = this.getEndPoint(centerPoint);
      this.addLine(
        { x: centerPoint[0], y: centerPoint[1] },
        { x: endPoint[0], y: endPoint[1] }
      );
    },
    addLine(startPoint, endPoint) {
      if (this.time) {
        this.circle.remove();
        this.line.remove();
        clearTimeout(this.time);
      }
      this.circle = this.svg
        .append('circle')
        .attr('cx', startPoint.x)
        .attr('cy', startPoint.y)
        .attr('r', 2)
        .attr('fill', '#56638D');
      this.line = this.svg
        .append('line')
        .attr('x1', startPoint.x)
        .attr('y1', startPoint.y)
        .attr('x2', endPoint.x)
        .attr('y2', endPoint.y)
        .attr('stroke', '#56638D')
        .attr('stroke-width', 1);
      this.time = setTimeout(() => {
        this.circle.remove();
        this.line.remove();
        this.popup.style('display', 'none');
      }, 3000);
    },
    getEndPoint(givenPoint) {
      const foreignObject = this.svg.select('.tooltip');

      // 获取 foreignObject 的位置和尺寸
      var foreignX = parseFloat(foreignObject.attr('x'));
      var foreignY = parseFloat(foreignObject.attr('y'));
      var foreignWidth = parseFloat(foreignObject.attr('width'));
      var foreignHeight = parseFloat(foreignObject.attr('height'));

      // 计算 foreignObject 的四个边的中心点坐标
      var topCenter = [foreignX + foreignWidth / 2, foreignY];
      var rightCenter = [foreignX + foreignWidth, foreignY + foreignHeight / 2];
      var bottomCenter = [
        foreignX + foreignWidth / 2,
        foreignY + foreignHeight,
      ];
      var leftCenter = [foreignX, foreignY + foreignHeight / 2];

      // 计算给定点到四个边中心点的距离
      var distances = [
        Math.hypot(givenPoint[0] - topCenter[0], givenPoint[1] - topCenter[1]),
        Math.hypot(
          givenPoint[0] - rightCenter[0],
          givenPoint[1] - rightCenter[1]
        ),
        Math.hypot(
          givenPoint[0] - bottomCenter[0],
          givenPoint[1] - bottomCenter[1]
        ),
        Math.hypot(
          givenPoint[0] - leftCenter[0],
          givenPoint[1] - leftCenter[1]
        ),
      ];

      // 找到最小距离的中心点坐标
      var nearestCenterIndex = distances.indexOf(Math.min(...distances));
      var nearestCenter;

      switch (nearestCenterIndex) {
        case 0:
          nearestCenter = topCenter;
          break;
        case 1:
          nearestCenter = rightCenter;
          break;
        case 2:
          nearestCenter = bottomCenter;
          break;
        case 3:
          nearestCenter = leftCenter;
          break;
      }

      return nearestCenter;
    },
    //获取排名需要的所有颜色，共19份数据
    getColors() {
      const linearGradient = d3
        .scaleLinear()
        .domain([0, 0.23, 0.49, 0.76, 1])
        .range(['#FF3D3D', '#FF9861', '#FFC561', '#FFFC61', '#6BD85D']);

      // 提取19个颜色值
      const numberOfColors = 19;
      const extractedColors = Array.from(
        { length: numberOfColors },
        (_, index) => {
          const t = index / (numberOfColors - 1);
          return linearGradient(t);
        }
      );
      this.extractedColors = extractedColors;
    },
    //获取园区排放排名，根据排名来给园区不同的背景颜色
    getEmissionRank() {
      let EmissionData = _.clone(this.emissionData);
      EmissionData.sort((a, b) => b.emission - a.emission);
      let currentRank = 1;
      let previousEmission = null;

      EmissionData.forEach((company, index) => {
        if (
          previousEmission !== null &&
          company.emission === previousEmission
        ) {
          company.rank = EmissionData[index - 1].rank;
        } else {
          company.rank = currentRank;
        }

        currentRank++;
        previousEmission = company.emission;
      });

      EmissionData.forEach((company) => {
        console.log(
          `${company.name} - emission: ${company.emission} - Rank: ${company.rank}`
        );
        this.idMap.set(company.id, company);
      });
    },
    addEventListener() {
      const allPaths = d3.selectAll('path[data-id]');
      const _this = this;
      // 添加鼠标悬停事件
      allPaths.on('mouseover', function () {
        // 在鼠标悬停时执行的操作
        const id = d3.select(this).attr('data-id'); // 例如，改变填充颜色为红色
        _this.selectedHandler(id);
      });
      allPaths.on('mouseout', function () {
        // 在鼠标移出时执行的操作
        _this.highlight(null);
      });
    },
    selectedHandler(id) {
      this.detailNumber = this.idMap.get(id)?.emission || 0;
      this.highlight(id);
      this.showPopView(id);
    },
    setParkSeq() {
      const paths = this.svg.selectAll('path[data-id]').nodes();
      const drawInstance = new DrawSeq(paths, this.svg);
      drawInstance.draw();
    },
  },
};
</script>

<style lang="less" scoped>
svg {
  transform: scale(1);
}
.tooltip-content {
  width: 104px;
  height: 25px;
  border-radius: 4px;
  opacity: 1;
  background: rgba(0, 0, 0, 0.7);
  text-align: center;
  span {
    font-size: 12px;
    font-weight: normal;
    color: #fff;
  }
}
</style>
