function getTableColumn() {
  const _this = this;
  return [
    {
      title: '任务名称',
      field: 'taskName',
    },
    {
      title: '任务内容',
      field: 'content',
    },
    {
      title: '批示状态',
      field: 'instructionStatus',
      formatter({ cellValue }) {
        const currentBiz = _this.instructionStatus.filter((item) => {
          return item.value == cellValue;
        });
        return currentBiz[0] ? currentBiz[0].label : cellValue;
      },
    },
    {
      title: '任务状态',
      field: 'taskStatus',
      formatter({ cellValue }) {
        const currentBiz = _this.taskStatus.filter((item) => {
          return item.value == cellValue;
        });
        return currentBiz[0] ? currentBiz[0].label : cellValue;
      },
    },
    {
      title: '创建人',
      field: 'creatorName',
    },
    {
      title: '办理时限',
      field: 'transactorTime',
    },
    {
      title: '创建时间',
      field: 'createTime',
    },
  ];
}

export { getTableColumn };
