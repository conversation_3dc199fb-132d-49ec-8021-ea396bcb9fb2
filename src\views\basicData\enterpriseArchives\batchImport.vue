<template>
  <div class="wrap">
    <a-spin :spinning="uploadLoading">
      <div class="card">
        <div class="back-btn">
          <a-button icon="rollback" @click="() => $router.go(-1)">
            返回</a-button
          >
        </div>
        <div class="tab-wrap">
          <a-tabs default-active-key="2" v-model="active" @change="changeTab">
            <a-tab-pane key="2" tab="批量更新"> </a-tab-pane>
            <a-tab-pane key="1" tab="企业新增"></a-tab-pane>
          </a-tabs>
        </div>
        <div class="upload-wrap">
          <a-upload-dragger
            :multiple="true"
            :fileList="fileList"
            :accept="accept"
            class="upload-box"
            :before-upload="beforeUpload"
            @change="handlechange"
            :customRequest="customRequestPic"
          >
            <div>
              <div class="ant-upload-text">
                将文件拖到此处或 <span style="color: #65a2fe">点击上传</span>
              </div>
              <div class="tip-text">
                支持上传单个Excel格式文件，文件不能超过5M <br />
                严禁上传包含色情、暴力、反动等相关违法信息的文件。
              </div>
            </div>
          </a-upload-dragger>
          <div class="upload-tips">
            <a-button type="link" @click.stop="downloadTemplate()"
              >点击下载导入模板.xlsx</a-button
            >
          </div>
        </div>

        <div class="btn-wrap">
          <a-button
            type="primary"
            class="mr10"
            @click="uploadItem"
            :exportLoading="exportLoading"
            >导入</a-button
          >
          <a-button
            type="primary"
            v-show="active == 2"
            @click="downloadTemplate(true, '企业清单')"
            >导出企业清单</a-button
          >
        </div>
        <BuseCrud
          ref="crud"
          :loading="loading"
          :tableColumn="tableColumn"
          :tablePage="tablePage"
          title="操作记录"
          :tableProps="{
            headerAlign: 'left',
            border: 'none',
            columnConfig: { resizable: true },
            showOverflow: 'tooltip',
            align: 'left',
          }"
          :tableData="tableData"
          :modalConfig="modalConfig"
          @loadData="loadData"
        >
          <template slot="filePath" slot-scope="{ row }">
            <a-button type="link" @click="downLoadItem(row)" v-if="row.filePath"
              >下载</a-button
            >
            <span v-else>暂无文件</span>
          </template>
        </BuseCrud>
      </div>
    </a-spin>
    <configParameter ref="configParameter" @okSuccess="okSuccess" />
  </div>
</template>

<script>
import { request } from '@/utils/request/requestTkb';
import {
  getEnterpriseLog,
  retryEnterprise,
  getParkList,
  getLabelList,
  saveOrUpdateEnterprise,
  pageReserveList,
} from '@/api/basicData/index.js';
import { resolveBlob } from '@/utils/common/fileDownload';
import * as api from '@/api/basicData';
import configParameter from './components/configParameter';
import { saveAs } from 'file-saver';
export default {
  props: {},
  dicts: [
    'enterprise_regulate_status',
    'enterprise_nature_status',
    'enterprise_register_status',
    'qcc_field_mapping',
  ],
  components: { configParameter },
  data() {
    return {
      active: '2',
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      fileList: [],
      statusOptions: [
        {
          label: '处理中',
          value: '1',
        },
        {
          label: '成功',
          value: '2',
        },
        {
          label: '失败',
          value: '3',
        },
        {
          label: '归档',
          value: '4',
        },
      ],
      accept: '.xlsx',
      parkList: [],
      labelList: [],
      maxNum: 1,
      upLoadInfo: {},
      exportLoading: false,
      enterpriseList: [],
      maxSize: 5,
      uploadLoading: false,
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        customOperationTypes: [
          {
            title: '重试',
            typeName: 'reply',
            showForm: false,
            event: (row) => {
              const that = this;
              this.$confirm({
                title: '确认重试',
                content: () => '是否确定重试',
                cancelText: '取消',
                okText: '确定',
                async onOk() {
                  const [res] = await pageReserveList({
                    id: row.id,
                  });
                  if (res && res.code == '10000') {
                    that.$message.success('重试成功');
                  }
                  that.loadData();
                },
              });
            },
            condition: (row) => {
              return row.status == '3';
            },
          },
        ],
      };
    },
    tableColumn() {
      return [
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'operationType',
          title: '操作类型',
        },
        {
          field: 'createTime',
          title: '提交时间',
        },
        {
          field: 'operator',
          title: '提交人',
        },
        {
          field: 'filePath',
          title: '提交文件',
          slots: { default: 'filePath' },
        },
        {
          field: 'status',
          title: '结果',
          formatter: ({ cellValue }) => {
            return this.statusOptions.find((q) => q.value == cellValue)?.label;
          },
        },
      ];
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.getParkList();
    this.getLabelList();
    this.loadData();
  },
  methods: {
    // 上传限制
    beforeUpload(file) {
      const flieArr = file.name.split('.');
      const fileaccept = this.accept.split(',');
      const suffix = flieArr[flieArr.length - 1];
      // 获取类型结果
      const result = fileaccept.some(function (item) {
        return item.slice(1) === suffix;
      });
      this.uploadUrl = api.uploadFile('enterpriseArchivesBatchImport');
      return new Promise((resolve, reject) => {
        if (this.fileList.length >= Number(this.maxNum)) {
          this.$message.warning(`最大上传数量为${this.maxNum}`);
          reject(new Error(`最大上传数量为${this.maxNum}`));
        } else if (!result) {
          this.$message.warning('上传格式不正确');
          reject(new Error('上传格式不正确'));
        } else if (file.size > this.maxSize * 1024 * 1024) {
          // 判断文件大小是否超标
          const errorMsg = `${file.name}超过${this.maxSize}M大小的限制!`;
          this.$message.warning(errorMsg);
          reject(new Error(errorMsg));
        } else {
          resolve();
        }
      });
    },

    customRequestPic(data) {
      const formData = new FormData();
      formData.append('file', data.file);
      formData.append('type', this.active);
      this.uploadfilePic(formData, data);
    },
    Utf8ArrayToStr(array) {
      let out, i, c;
      let char2, char3;

      out = '';
      const len = array.length;
      i = 0;
      while (i < len) {
        c = array[i++];
        switch (c >> 4) {
          case 0:
          case 1:
          case 2:
          case 3:
          case 4:
          case 5:
          case 6:
          case 7:
            // 0xxxxxxx
            out += String.fromCharCode(c);
            break;
          case 12:
          case 13:
            // 110x xxxx 10xx xxxx
            char2 = array[i++];
            out += String.fromCharCode(((c & 0x1f) << 6) | (char2 & 0x3f));
            break;
          case 14:
            // 1110 xxxx 10xx xxxx 10xx xxxx
            char2 = array[i++];
            char3 = array[i++];
            out += String.fromCharCode(
              ((c & 0x0f) << 12) | ((char2 & 0x3f) << 6) | ((char3 & 0x3f) << 0)
            );
            break;
        }
      }
      return out;
    },

    // 文件上传
    async uploadfilePic(formData, data) {
      const that = this;
      this.uploadLoading = true;
      const [res, err] = await request(
        {
          url: this.uploadUrl.replace('/api', ''),
          method: 'post',
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          data: { file: data.file, type: this.active },
          responseType: 'arraybuffer',
        },
        {
          errorCustom: false,
        },
        (res) => {
          if (
            res &&
            res?.data &&
            res?.data?.code &&
            res?.data?.code != 10000 &&
            res?.data?.msg
          ) {
            that.$message.info(res.data.msg);
            return [res?.data];
          }
          return [res?.data];
        }
      );
      this.uploadLoading = false;
      if (err) {
        data.onError();
        return;
      }
      try {
        const decoder = new TextDecoder('utf-8');
        const decodedString = decoder.decode(res);
        const response = JSON.parse(decodedString);
        console.log(response, 'response');
        if (response?.code) {
          if (response.code === '10000') {
            this.upLoadInfo = response.data;
            console.log(this.upLoadInfo, 'this.upLoadInfo');
            data.onSuccess(data.file, data.file);
            this.$message.success('上传成功');
            this.loadData();
          } else {
            if (response?.msg) {
              this.$message.info(response?.msg);
            }
            data.onError();
          }
        } else {
          this.downLoad(data, res);
        }
      } catch {
        this.downLoad(data, res);
      }
    },
    downLoad(data, res) {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
      };
      resolveBlob(
        new Blob([
          res,
          {
            type: 'application/vnd.ms-excel;charset=utf-8',
          },
        ]),
        mimeMap.xlsx,
        data.file.name,
        '.xlsx'
      );
      data.onError();
    },
    handlechange(info) {
      const { file, fileList } = info;
      console.log(fileList, 'fileList');
      this.fileList = fileList.filter((q) => q.status !== 'error');
    },
    // 删除
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      this.fileList.splice(index, 1);
    },
    // 下载模板
    async downloadTemplate(isEnterpriseNameTemplate = false, title) {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
      };
      const [res] = await api.downloadInformation(
        {
          type: this.active,
          isEnterpriseNameTemplate: isEnterpriseNameTemplate,
        },
        'enterpriseArchivesBatchImport'
      );
      if (!res.code) {
        resolveBlob(res, mimeMap.xlsx, title || '导入模板', '.xlsx');
      }
    },
    async loadData() {
      this.loading = true;
      const [res] = await getEnterpriseLog({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
      });
      this.loading = false;
      if (res && res.data) {
        this.tableData = res.data;
        this.tablePage.total = res.total;
      }
    },
    // downLoad(row) {
    //   if (row.filePath) {
    //     const mimeMap = {
    //       xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
    //     };
    //     resolveBlob(row.filePath, mimeMap.xlsx, '导入模板', '.xlsx');
    //   }
    // },
    async uploadItem() {
      if (this.fileList.length >= 1) {
        if (this.active == '1') {
          this.$refs.configParameter &&
            this.$refs.configParameter.openDialog(
              {
                ...(this.dict?.type || {}),
                parkList: this.parkList,
                labelList: this.labelList,
              },
              (this.upLoadInfo?.enterpriseList || []).map((q) => {
                return {
                  ...q,
                  labelId: q.labelId ? q.labelId.split(',') : [],
                };
              })
            );
          return;
        }
        const params = {
          type: this.active,
          ...this.upLoadInfo,
          batch: this.upLoadInfo?.importBatch,
        };
        this.uploadLoading = true;
        const [res] = await saveOrUpdateEnterprise(params);
        this.uploadLoading = false;
        if (res && res.code == '10000') {
          this.$message.success('导入成功');
          this.fileList = [];
          this.loadData();
        }
      } else {
        this.$message.info('请上传文件后在进行导入');
      }
    },
    changeTab() {
      this.fileList = [];
    },
    // 获得所属园区
    async getParkList() {
      const [res] = await getParkList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.parkList = res.data;
      }
    },
    // 获得企业标签
    async getLabelList() {
      const [res] = await getLabelList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.labelList = res.data;
      }
    },
    async okSuccess(tableData) {
      const params = {
        type: this.active,
        ...this.upLoadInfo,
        batch: this.upLoadInfo?.importBatch,
        enterpriseList: tableData.map((q) => {
          return {
            ...q,
            labelId:
              q.labelId && Array.isArray(q.labelId) ? q.labelId.join(',') : '',
          };
        }),
      };
      this.exportLoading = true;
      const [res] = await saveOrUpdateEnterprise(params);
      console.log(res, 'res');
      this.exportLoading = false;
      if (res && res.code == '10000') {
        this.$message.success('导入成功');
        this.fileList = [];
        this.$refs.configParameter && this.$refs.configParameter.handleCancel();
        this.loadData();
      }
    },
    async downLoadItem(row) {
      console.log(row, 'row');
      if (row.filePath) {
        saveAs(row.filePath, row.fileName || '操作记录文件.xlsx');
      }
    },
  },
};
</script>

<style scoped lang="less">
.wrap {
  padding: 10px;
}
.card {
  border-radius: 5px;
  background: #fff;
  position: relative;
  .back-btn {
    position: absolute;
    right: 20px;
    top: 5px;
    color: rgba(170, 169, 169, 0.508);
    z-index: 1;
  }
}
.tab-wrap {
  width: 100%;
}
.upload-wrap {
  width: 100%;
  display: grid;
  place-content: center;
  padding: 15px 0;
  position: relative;
  .upload-box {
    width: 500px;
    height: 300px;
    .tip-text {
      margin-top: 15px;
    }
  }
  .upload-tips {
    width: 500px;
    display: flex;
    left: 50%;
    transform: translateX(-50%);
    right: 0;
    justify-content: flex-end;
    position: absolute;
    top: 280px;
  }
}
.btn-wrap {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
  gap: 30px;
}
.h3-title {
  font-size: ;
}
</style>
