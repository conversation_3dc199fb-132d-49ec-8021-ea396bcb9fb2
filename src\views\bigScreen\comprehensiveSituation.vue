<template>
  <div>
    <screen ref="screen" @changeSize="changeSize" :url="url"></screen>
    <div class="search-box" :style="styleSearch" v-show="showSelect">
      <a-select
        v-model="parkModelValue"
        style="width: 100%"
        :autoClearSearchValue="true"
        :getPopupContainer="getPopupContainer"
        :dropdownClassName="'screen-select'"
        :notFoundContent="null"
        :allowClear="false"
        :filterOption="filterOption"
        :showSearch="true"
        :showArrow="false"
        @select="selectPark"
        placeholder="快速搜索园区"
        @blur="parkModelValue = undefined"
      >
        <a-select-option
          v-for="(item, index) in parkCompanyList"
          :value="item.value"
          :key="index"
        >
          {{ item.label }}
        </a-select-option>
      </a-select>
      <a-select
        v-model="modelValue"
        style="width: 100%"
        :getPopupContainer="getPopupContainer"
        :autoClearSearchValue="true"
        :dropdownClassName="'screen-select'"
        :notFoundContent="null"
        :allowClear="false"
        :filterOption="false"
        :showSearch="true"
        @search="handleSearch"
        :showArrow="false"
        @select="select"
        placeholder="快速搜索企业"
        @blur="modelValue = undefined"
      >
        <a-select-option
          v-for="(item, index) in companyList"
          :value="item.value"
          :key="index"
        >
          {{ item.label }}
        </a-select-option>
      </a-select>
    </div>
  </div>
</template>

<script>
import { debounce } from 'lodash';
import { getParkAndEnterpriseList } from '@/api/basicData/index.js';
import screen from '@/views/bigScreen/components/screen';
import getBigScreenUrl from '@/global/bigScreen/bigScreenUrl';
export default {
  name: 'comprehensiveSituation',
  components: { screen },
  data() {
    return {
      url: getBigScreenUrl.call(this),
      styleSearch: {},
      modelValue: undefined,
      parkModelValue: undefined,
      companyList: [],
      parkCompanyList: [],
      showSelect: false,
    };
  },
  mounted() {
    document.title = '综合态势';
    this.handleParkSearch('');
    this.$nextTick(() => {
      setTimeout(() => {
        this.showSelect = true;
      }, 11500);
    });
  },
  methods: {
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    changeSize(entry) {
      if (entry) {
        const { width, bottom, height, left } = entry.contentRect;
        const right = (width * 0.23 + 40).toFixed(2);
        this.styleSearch = {
          right: right + 'px',
          top: height - 35 + 'px',
        };
      }
    },
    handleSearch: debounce(async function (val) {
      if (!val) {
        this.companyList = [];
        return;
      }
      const [res] = await getParkAndEnterpriseList({
        limit: 100,
        pageNum: 1,
        orgName: val,
        dimensionType: '2',
      });
      if (res && res.data) {
        this.companyList = res.data
          .map((q) => {
            return {
              ...q,
              label: q.name,
              value: q.id,
            };
          })
          .slice(0, 40);
      } else {
        this.companyList = [];
      }
    }, 500),
    select(val) {
      if (val) {
        const params = this.companyList.find((q) => q.value === val);
        params.jumapUrl && window.open(params.jumapUrl);
        this.modelValue = undefined;
      }
    },
    handleParkSearch: debounce(async function (val) {
      // if (!val) {
      //   this.parkCompanyList = [];
      //   return;
      // }
      const [res] = await getParkAndEnterpriseList({
        limit: 100,
        pageNum: 1,
        orgName: val,
        dimensionType: '1',
      });
      if (res && res.data) {
        this.parkCompanyList = res.data
          .map((q) => {
            return {
              ...q,
              label: q.name,
              value: q.id,
            };
          })
          .slice(0, 40);
      } else {
        this.parkCompanyList = [];
      }
    }, 500),
    selectPark(val) {
      if (val) {
        const params = this.parkCompanyList.find((q) => q.value === val);
        params.jumapUrl && window.open(params.jumapUrl);
        this.parkModelValue = undefined;
      }
    },
    getPopupContainer(trigger) {
      return trigger;
    },
  },
};
</script>

<style scoped lang="less">
.search-box {
  position: fixed;
  width: 400px;
  height: 35px;
  z-index: 9999999999999;
  display: flex;
  gap: 10px;
  /deep/.ant-select-dropdown {
    z-index: 99999999999999999 !important;
  }
  /deep/ .ant-select {
    height: 100%;
  }
  /deep/ .ant-select-search__field {
    color: #fff;
  }
  /deep/.ant-select-selection {
    height: 100%;
    border: none;
    opacity: 0.7;
    background: linear-gradient(
        332deg,
        rgba(0, 170, 163, 0.8) -1%,
        rgba(0, 146, 139, 0) 26%
      ),
      linear-gradient(
        170deg,
        rgba(0, 170, 164, 0.8) -5%,
        rgba(0, 187, 180, 0) 68%
      ),
      rgba(0, 0, 0, 0.41);
    border: 1px solid rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(6px);
  }
  /deep/ .ant-select-selection__placeholder {
    font-family: HarmonyOS Sans SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 24px;
    letter-spacing: 0em;
    color: #ffffff;
  }
  /deep/ .ant-select-selection-selected-value {
    color: #fff;
  }
}
</style>
