export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'bucketName',
      title: 'Bucket名称',
      props: {
        placeholder: '请输入Bucket名称搜索',
      },
    },
    {
      field: 'serverType',
      title: '存储类型',
      element: 'a-select',
      props: {
        options: [
          { value: 'oss', label: 'oss' },
          { value: 'minio', label: 'minio' },
        ],
      },
    },
    {
      field: 'status',
      title: '状态',
      element: 'a-select',
      props: {
        options: [
          { value: 'NORMAL', label: '启用' },
          { value: 'ABNORMAL', label: '停用' },
        ],
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { bucketName: '', serverType: '', status: '' },
}; // 表头
export const tableColumn = () => [
  { field: 'bucketName', title: 'Bucket名称', align: 'center' },
  { field: 'serverType', title: '存储类型', align: 'center' },
  {
    title: '状态',
    slots: { default: 'status' },
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 280,
  },
];
export const getDefaultBucket = () => {
  return {
    bucketName: '',
    proxyUsername: '',
    isUsedProxy: false,
    serverType: 'oss',
    proxyHost: '',
    proxyPassword: '',
    accessId: '',
    proxyPort: '',
    endpoint: '',
    publicEndpoint: '',
    proxyWorkstation: '',
    proxyDomain: '',
    accessKey: '',
    remark: '',
  };
};
