<template>
  <div class="page-layout">
    <div v-if="!hidePageHeader" class="page-header">
      <a-breadcrumb>
        <a-breadcrumb-item :key="index" v-for="(item, index) in breadcrumb">
          <span v-if="item.type === 'M' || index === breadcrumbLen - 1">{{
            item.title
          }}</span>
          <router-link :to="{ path: item.path }" v-else>
            {{ item.title }}
          </router-link>
        </a-breadcrumb-item>
      </a-breadcrumb>
      <div v-if="hasTitle" class="page-header-title">
        <div class="page-title">
          <a-icon
            v-if="showBack"
            type="arrow-left"
            style="margin-right: 8px; cursor: pointer"
            @click="onClickBack"
          />{{ pageTitle }}
        </div>
        <div class="page-title-extra">
          <slot name="title-extra"></slot>
        </div>
      </div>
    </div>
    <div class="page-container">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    hasTitle: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // 面包屑
      breadcrumb: [],
    };
  },
  computed: {
    // 是否隐藏面包屑和标题
    hidePageHeader() {
      return this.$route.meta.hideHeader;
    },
    // 标题
    pageTitle() {
      // 如果某页面有自定义标题需求，请在params或者query中设置title字段
      return this.title || this.$route.meta.title;
    },
    // 是否展示标题返回按钮
    showBack() {
      return this.$route.meta.showBack;
    },
    // 面包屑长度
    breadcrumbLen() {
      return this.breadcrumb.length;
    },
  },
  mounted() {
    this.getRouteBreadcrumb();
  },
  methods: {
    getRouteBreadcrumb() {
      let routes = this.$route.matched;
      let breadcrumb = [];

      routes
        // 排除掉空路径和互动中的特殊处理（站点顶级维度）的情况
        .filter((item) => !!item.path && item.path !== '/:siteId')
        .forEach((route) => {
          const { path, meta, name } = route;
          breadcrumb.push({
            title: meta.title,
            path: this.handlePathParams(path),
            type: meta.type,
            name,
          });
        });
      this.breadcrumb = breadcrumb;
    },
    handlePathParams(path) {
      const { params } = this.$route;
      Object.keys(params).forEach((key) => {
        path = path.replace(':' + key, params[key]);
      });
      return path;
    },
    onClickBack() {
      this.$router.back();
    },
  },
};
</script>

<style lang="less" scoped>
.page-layout {
  margin: 16px 24px 20px;
  .page-header {
    .page-header-title {
      margin: 16px 0 24px;
      display: flex;
      .page-title {
        font-size: 20px;
        line-height: 20px;
        color: #333;
      }
      .page-title-extra {
        flex: 1;
      }
    }
  }
  .page-container {
    position: relative;
  }
}
</style>
