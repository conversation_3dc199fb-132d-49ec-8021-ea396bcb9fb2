<template>
  <a-upload
    :multiple="true"
    :fileList="fileList"
    :accept="accept"
    class="upload-box"
    :before-upload="beforeUpload"
    @change="handlechange"
    :customRequest="customRequestPic"
    :showUploadList="false"
  >
    <a-button :loading="loading">
      {{ text }}
    </a-button>
  </a-upload>
</template>

<script>
import { resolveBlob } from '@/utils/common/fileDownload';
import { request } from '@/utils/request/requestTkb';
import * as api from '@/api/basicData';
export default {
  props: {
    text: {
      type: String,
      default: '',
    },
  },
  components: {},
  data() {
    return {
      loading: false,
      fileList: [],
      accept: '.xlsx',
      uploadUrl: '',
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handlechange(info) {
      const { file, fileList } = info;
      console.log(fileList, 'fileList');
      this.fileList = fileList.filter((q) => q.status !== 'error');
    },
    // 删除
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      this.fileList.splice(index, 1);
    },

    // 上传限制
    beforeUpload(file) {
      const flieArr = file.name.split('.');
      const fileaccept = this.accept.split(',');
      const suffix = flieArr[flieArr.length - 1];
      // 获取类型结果
      const result = fileaccept.some(function (item) {
        return item.slice(1) === suffix;
      });
      this.uploadUrl = api.uploadFile('accountNumberVerification');
      return new Promise((resolve, reject) => {
        if (this.fileList.length >= Number(this.maxNum)) {
          this.$message.warning(`最大上传数量为${this.maxNum}`);
          reject(new Error(`最大上传数量为${this.maxNum}`));
        } else if (!result) {
          this.$message.warning('上传格式不正确');
          reject(new Error('上传格式不正确'));
        } else if (file.size > this.maxSize * 1024 * 1024) {
          // 判断文件大小是否超标
          const errorMsg = `${file.name}超过${this.maxSize}M大小的限制!`;
          this.$message.warning(errorMsg);
          reject(new Error(errorMsg));
        } else {
          resolve();
        }
      });
    },

    customRequestPic(data) {
      const formData = new FormData();
      formData.append('file', data.file);
      formData.append('type', this.active);
      this.uploadfilePic(formData, data);
    },
    Utf8ArrayToStr(array) {
      let out, i, c;
      let char2, char3;

      out = '';
      const len = array.length;
      i = 0;
      while (i < len) {
        c = array[i++];
        switch (c >> 4) {
          case 0:
          case 1:
          case 2:
          case 3:
          case 4:
          case 5:
          case 6:
          case 7:
            // 0xxxxxxx
            out += String.fromCharCode(c);
            break;
          case 12:
          case 13:
            // 110x xxxx 10xx xxxx
            char2 = array[i++];
            out += String.fromCharCode(((c & 0x1f) << 6) | (char2 & 0x3f));
            break;
          case 14:
            // 1110 xxxx 10xx xxxx 10xx xxxx
            char2 = array[i++];
            char3 = array[i++];
            out += String.fromCharCode(
              ((c & 0x0f) << 12) | ((char2 & 0x3f) << 6) | ((char3 & 0x3f) << 0)
            );
            break;
        }
      }
      return out;
    },
    // 文件上传
    async uploadfilePic(formData, data) {
      const that = this;
      this.loading = true;
      const res = await request(
        {
          url: this.uploadUrl.replace('/api', ''),
          method: 'post',
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          data: { file: data.file },
          responseType: 'arraybuffer',
        },
        {
          errorCustom: false,
        },
        (res) => {
          if (
            res &&
            res?.data &&
            res?.data?.code &&
            res?.data?.code != 10000 &&
            res?.data?.msg
          ) {
            that.$message.info(res.data.msg);
            return res?.data;
          }
          return res?.data;
        }
      );
      this.loading = false;
      try {
        const decoder = new TextDecoder('utf-8');
        const decodedString = decoder.decode(res);
        const response = JSON.parse(decodedString);
        if (response?.code) {
          if (response.code === '10000') {
            data.onSuccess(data.file, data.file);
            this.$message.success('上传成功');
          } else {
            if (response?.msg) {
              this.$message.info(response?.msg);
            }
            data.onError();
          }
        } else {
          this.downLoad(data, res);
        }
        this.fileList = [];
      } catch {
        this.downLoad(data, res);
        this.fileList = [];
      }
    },

    downLoad(data, res) {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
      };
      resolveBlob(
        new Blob([
          res,
          {
            type: 'application/vnd.ms-excel;charset=utf-8',
          },
        ]),
        mimeMap.xlsx,
        data.file.name,
        '.xlsx'
      );
      data.onError();
    },
  },
};
</script>

<style scoped lang="less"></style>
