<template>
  <!-- 添加或修改菜单对话框 -->
  <a-modal
    :title="title"
    :visible="visible"
    :loading="loading"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
    width="700px"
    @ok="submitForm"
    @cancel="closeModal"
  >
    <a-form-model ref="form" :model="form" :rules="rules" v-bind="formLayout">
      <a-form-model-item label="字典类型" prop="dictType">
        <a-select
          v-model="form.dictType"
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          :options="dictTypeOptions"
          placeholder="请选择字典类型"
        >
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="字典名称" prop="dictName">
        <a-input v-model="form.dictName" placeholder="请输入字典名称" />
      </a-form-model-item>
      <a-form-model-item label="字典编码" prop="dictCode">
        <a-input v-model="form.dictCode" placeholder="请输入字典编码" />
      </a-form-model-item>
      <a-form-model-item label="字典状态" prop="status">
        <a-radio-group v-model="form.status" :options="statusOptions">
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="备注" prop="remark">
        <a-input
          type="textarea"
          v-model="form.remark"
          placeholder="请输入备注"
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import { getType, addType, updateType } from '@/api/system/dict/type';
import { dictTypeOptions, initForm } from './constant';
import { statusOptions2String } from '@/views/system/constant/system';

export default {
  props: {
    visible: Boolean,
    dictId: String,
    dictType: String,
  },
  computed: {
    title() {
      return `${
        this.form.dictId || (this.loading && this.dictId) ? '编辑' : '新增'
      }字典`;
    },
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.dictId) {
          this.getDetail();
        } else {
          this.form = initForm({
            dictType: this.dictType,
          });
        }
      } else {
        this.$refs.form && this.$refs.form.resetFields();
      }
    },
  },
  data() {
    return {
      loading: false,
      // 表单参数
      form: initForm(),
      // 表单校验
      rules: {
        dictType: [
          {
            required: true,
            message: '字典类型不能为空',
            trigger: 'blur',
            whitespace: true,
          },
        ],
        dictName: [
          {
            required: true,
            message: '字典名称不能为空',
            trigger: 'blur',
            whitespace: true,
          },
        ],
        dictCode: [
          {
            required: true,
            message: '字典编码不能为空',
            trigger: 'blur',
            whitespace: true,
          },
        ],
      },
      // 状态数据字典
      dictTypeOptions,
      statusOptions: statusOptions2String,
      formLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
      },
    };
  },
  methods: {
    /**
     * 获取菜单详情
     */
    async getDetail() {
      this.loading = true;
      const [result, error] = await getType({ dictId: this.dictId });
      this.loading = false;
      if (error) {
        this.form = initForm({
          dictType: this.dictType,
        });
        return;
      }
      this.form = result.data || {};
    },
    /**
     * 提交按钮
     */
    submitForm() {
      if (this.loading) return;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const [, error] = this.form.dictId
            ? await updateType(this.form)
            : await addType(this.form);
          this.loading = false;
          if (error) return;
          this.$message.success(`${this.title}成功`);
          this.$emit('ok');
          this.closeModal();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .ant-form-item {
  margin-bottom: 16px !important;
}
</style>
