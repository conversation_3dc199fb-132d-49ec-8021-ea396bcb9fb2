<template>
  <PortraitCard :span="24" title="孵化机构" :canExpand="true" v-bind="$attrs">
    <a-row>
      <a-col :span="12" class="detail">
        <span class="title" style="width: 190px"> 历年孵化机构数量合计： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.incubationInstitutionsSum"
            >暂无数据</span
          >
          <template v-else
            ><span class="num">{{
              pictureInfo.incubationInstitutionsSum || '-'
            }}</span
            ><span class="unit">个</span></template
          >
        </span>
      </a-col>
      <a-col :span="12" class="detail">
        <span class="title"> 总孵化面积：</span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.incubationAreaTotal"
            >暂无数据</span
          >
          <template v-else>
            <span class="num">{{ pictureInfo.incubationAreaTotal || '-' }}</span
            ><span class="unit">平方米</span>
          </template>
        </span>
      </a-col>
    </a-row>
    <a-row :gutter="12" style="margin-top: 12px">
      <a-col :span="6">
        <ChartsCard
          chartTitle="近4年孵化机构数量统计"
          :options="charts1Options"
          height="220px"
        />
      </a-col>
      <a-col :span="6">
        <CustomizePieChart
          chartTitle="载体类别统计"
          :options="charts2Options"
          height="220px"
        />
      </a-col>
      <a-col :span="6">
        <CustomizePieChart
          chartTitle="载体级别统计"
          :options="charts3Options"
          height="220px"
        />
      </a-col>
      <a-col :span="6">
        <CustomizePieChart
          chartTitle="建设情况统计"
          :options="charts4Options"
          height="220px"
        />
      </a-col>
    </a-row>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
import ChartsCard from '../ChartsCard.vue';
import CustomizePieChart from '../CustomizePieChart.vue';
import { usePieCharts, useLineCharts } from '../chartHooks';
export default {
  components: {
    PortraitCard,
    ChartsCard,
    CustomizePieChart,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    charts1: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts2: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts3: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts4: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [[], []],
      }),
    },
  },
  data() {
    return {
      charts1Options: {},
      charts2Options: {},
      charts3Options: {},
      charts4Options: {},
    };
  },

  watch: {
    charts1: {
      deep: true,
      handler() {
        this.handleInitCharts1();
      },
    },
    charts2: {
      deep: true,
      handler() {
        this.handleInitCharts2();
      },
    },
    charts3: {
      deep: true,
      handler() {
        this.handleInitCharts3();
      },
    },
    charts4: {
      deep: true,
      handler() {
        this.handleInitCharts4();
      },
    },
  },
  methods: {
    handleInitCharts1() {
      const unit = '个';
      if (!this.charts1?.data) return;
      const { xAxis = [], data = [] } = this.charts1;
      this.charts1Options = useLineCharts({
        xAxis,
        unit,
        series: [
          { name: '近4年孵化机构数量统计', data: data || [], color: '#38d3b6' },
        ],
      });
    },
    handleInitCharts2() {
      if (!this.charts2?.data) return;
      const { data = [] } = this.charts2;
      this.charts2Options = usePieCharts({ data, direction: 'bottom' });
    },
    handleInitCharts3() {
      if (!this.charts3?.data) return;
      const { data = [] } = this.charts3;
      this.charts3Options = usePieCharts({ data, direction: 'bottom' });
    },
    handleInitCharts4() {
      if (!this.charts4?.data) return;
      const { data = [] } = this.charts4;
      this.charts4Options = usePieCharts({ data, direction: 'bottom' });
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .title {
    text-align: right;
    color: #999999;
    width: 148px;
    line-height: 26px;
  }

  .info {
    text-align: left;
    .num {
      font-family: D-DIN;
      font-size: 24px;
      font-weight: bold;
      line-height: 24px;
      color: #333333;
      z-index: 0;
    }

    .unit {
      margin-left: 4px;
      font-family: PingFang SC;
      font-size: 12px;
      color: #333333;
    }
  }
}
</style>
