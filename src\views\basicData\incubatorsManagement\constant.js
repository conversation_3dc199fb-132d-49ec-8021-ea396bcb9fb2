// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    width: 60,
    fixed: 'left',
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    field: 'date',
    title: '日期',
    width: 110,
  },
  {
    field: 'address',
    title: '载体名称',
    minWidth: 150,
  },
  {
    field: 'name',
    title: '运营主体',
    width: 100,
  },
  {
    field: 'person',
    title: '运营时间',
    width: 130,
  },
  {
    field: 'person',
    title: '总孵化面积',
    width: 130,
  },
  {
    field: 'person',
    title: '载体类别',
    width: 110,
  },
  {
    field: 'person',
    title: '载体级别',
    width: 110,
  },
  {
    field: 'person',
    title: '建设情况',
    width: 110,
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 160,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'date',
    title: '日期',
    element: 'slot',
    slotName: 'year',
    rules: [{ required: true, message: '请输入日期' }],
  },
  {
    field: 'code',
    title: '载体名称',
    element: 'a-input',
  },
  {
    field: 'category',
    title: '载体类别',
    element: 'a-select',
    props: {
      placeholder: '请输入名称',
      options: [],
      showSearch: true,
      optionFilterProp: 'children',
    },
  },
  {
    field: 'level',
    title: '载体级别',
    element: 'a-select',
    props: {
      options: [],
      showSearch: true,
      optionFilterProp: 'children',
    },
  },
  {
    field: 'code',
    title: '建设情况',
    element: 'a-select',
  },
  {
    field: 'code',
    title: '运营主体',
    element: 'a-input',
  },
];

export const initFormValue = () => {
  return {
    name: '',
    date: undefined,
    state: '',
    code: '',
    workTimes: ['', ''],
    imgUrl: '',
    xmlUrl: '',
  };
};
