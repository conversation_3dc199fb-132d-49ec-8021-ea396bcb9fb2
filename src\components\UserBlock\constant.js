import { aseDecrypt } from '@/utils/common/auth';
import moment from 'moment';
// 状态
export const statusEnum = {
  ENABLED: '0',
  DISABLED: '1',
  LOCKED: '2',
};
export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'userName',
      title: '用户名',
      props: {
        placeholder: '请输入用户名',
      },
    },
    {
      field: 'nickName',
      title: '姓名',
      props: {
        placeholder: '请输入姓名',
      },
    },
    {
      field: 'phonenumber',
      title: '手机号码',
      props: {
        placeholder: '请输入手机号码',
      },
    },
    {
      field: 'status',
      title: '状态',
      element: 'a-select',
      props: {
        options: [
          {
            value: '',
            label: '全部',
          },
          ...statusOptions,
        ],
      },
    },
  ],
  gridCol: { xs: 8, sm: 8, md: 8, lg: 8, xl: 8, xxl: 8 },
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: {
    userName: '',
    nickName: '',
    phonenumber: '',
    status: '',
  },
};
export const statusOptions = [
  {
    value: statusEnum.ENABLED,
    label: '启用',
    color: 'success',
  },
  {
    value: statusEnum.DISABLED,
    label: '停用',
    color: 'default',
  },
  {
    value: statusEnum.LOCKED,
    label: '锁定',
  },
];
export const tableColumns = [
  // {
  //   field: 'userId',
  //   type: 'checkbox',
  // },
  {
    title: '用户ID',
    field: 'userId',
    width: '150px',
    key: 'userId',
  },
  {
    title: '用户名',
    field: 'userName',
    width: '200px',
    key: 'userName',
  },
  {
    title: '姓名',
    field: 'nickName',
    width: '200px',
    key: 'nickName',
  },
  {
    title: '手机号码',
    field: 'phonenumber',
    width: '120px',
    key: 'phonenumber',
    formatter: ({ cellValue }) => {
      return cellValue ? aseDecrypt(cellValue) : '--';
    },
  },
  {
    title: '状态',
    field: 'status',
    key: 'status',
    width: '80px',
    slots: { default: 'status' },
  },
  {
    title: '创建时间',
    field: 'createTime',
    minWidth: '170px',
    key: 'createTime',
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '锁定时间',
    field: 'lockedTime',
    width: '170px',
    key: 'lockedTime',
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '解锁时间',
    field: 'unlockingTime',
    width: '170px',
    key: 'unlockingTime',
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '锁定原因',
    field: 'lockedReason',
    minWidth: '170px',
    key: 'lockedReason',
  },
  {
    title: '操作',
    field: 'operation',
    key: 'operation',
    fixed: 'right',
    showOverflow: false,
    width: '200px',
    slots: { default: 'operation' },
  },
];

export const headerList = [
  { title: '新增', icon: 'plus', type: 'primary' },
  { title: '删除', icon: 'delete', type: 'danger' },
  { title: '导出', icon: 'vertical-align-bottom', type: 'primary' },
];

export const userStatusEnum = {
  NORMAL: 'NORMAL',
  STOP_USING: 'STOP_USING',
  LOCKED: 'LOCKED',
};
export const userStatusOptions = [
  { value: userStatusEnum.NORMAL, label: '正常' },
  { value: userStatusEnum.STOP_USING, label: '停用' },
  { value: userStatusEnum.LOCKED, label: '锁定' },
];

export const initFormData = (data = {}) => {
  return {
    userId: '',
    merchantId: '',
    userName: '',
    nickName: '',
    password: '',
    phonenumber: '',
    email: '',
    jobNumber: '',
    avatar: '',
    idCard: '',
    // userType:,
    sex: undefined,
    organizeId: '',
    status: statusEnum.ENABLED,
    remark: '',
    roleIds: [],
    positionIds: [],
    ...data,
  };
};
export const initForm2UserMerchantRole = () => {
  return {
    merchantId: undefined,
    roleId: undefined,
  };
};
export const formRule2UserMerchantRole = {
  merchantId: [
    {
      required: true,
      message: '请选择授权商户',
      trigger: 'change',
    },
  ],
  roleId: [
    {
      required: true,
      message: '请选择授权角色',
      trigger: 'change',
    },
  ],
};

export const formRules = {
  nickName: [
    {
      required: true,
      message: '请输入姓名',
      trigger: 'change',
      whitespace: true,
    },
  ],
  organizeId: [
    {
      required: true,
      message: '请选择组织',
      trigger: 'change',
    },
  ],
  phonenumber: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          try {
            let reg = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
            if (reg.test(value)) {
              callback();
            } else {
              callback(new Error('数据格式错误'));
            }
          } catch (err) {
            callback(new Error('数据格式错误'));
          }
        } else {
          callback();
        }
      },
    },
  ],
  email: [
    {
      required: true,
      message: '请输入邮箱',
      trigger: 'change',
    },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
        }
        try {
          // eslint-disable-next-line no-useless-escape
          let reg =
            /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
          if (reg.test(value)) {
            callback();
          } else {
            callback(new Error('数据格式错误'));
          }
        } catch (err) {
          callback(new Error('数据格式错误'));
        }
      },
    },
  ],
  userName: [
    {
      required: true,
      message: '请输入用户名',
      trigger: 'change',
      minLength: 3,
      maxLength: 20,
      whitespace: true,
    },
    {
      min: 3,
      message: '请输入至少3个字符',
      trigger: 'change',
      whitespace: true,
    },
  ],
};

// 锁用户相关表单信息
export const initForm2LockUser = () => {
  return {
    lockDuration: undefined,
    reason: '',
  };
};
export const formRule2LockUser = {
  lockDuration: [
    {
      required: true,
      message: '请输入锁定时间',
      trigger: 'change',
    },
  ],
};

// 重置用户密码相关表单信息
export const initForm2ResetPsw = () => {
  return {
    newPasswordTwice: '',
    newPassword: '',
  };
};
// 密码格式验证正则
export const PASSWORD_REGEX =
  /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,}/;
// /^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$/;
// /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,}/
export const PASSWORD_REGEX_DESC =
  '必须是包含大小写字母、数字和特殊符号的8位以上组合';
