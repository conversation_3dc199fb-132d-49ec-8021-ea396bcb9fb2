<template>
  <BuseCrud
    ref="crud"
    title="目标列表"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalCancel="modalCancelHandler"
    @modalConfirm="modalConfirmHandler"
    @modalSubmit="modalSubmit"
    @loadData="loadData"
    @handleCreate="rowAdd"
    @rowEdit="rowEdit"
  >
    <template slot="defaultHeader">
      <a-button type="default" class="mr10" @click="exportData">导出</a-button>
    </template>
    <template slot="year">
      <BuseRangePicker
        type="year"
        v-model="filterParams.year"
        :needShowSecondPicker="() => false"
        format="YYYY"
      />
    </template>
    <template slot="yearSlot" slot-scope="{ params }">
      <a-input v-if="viewModel" v-model="params.year" disabled></a-input>
      <BuseRangePicker
        v-else
        type="year"
        v-model="params.year"
        :needShowSecondPicker="() => false"
        format="YYYY"
      />
    </template>
    <template slot="achieveTarget" slot-scope="{ params }">
      <a-input-number
        v-model="params.achieveTarget"
        placeholder="请输入目标"
        :disabled="viewModel"
        :step="initAchieveTarget * 0.05"
        :min="0"
        :precision="0"
        style="width: 90%; margin-right: 20px"
      >
      </a-input-number>
      <span>{{ achieveTargetUnit }}</span>
    </template>
    <template slot="targetAssignment" slot-scope="{ row, operationType }">
      <TargetAssignment
        ref="TargetAssignment"
        v-bind="{ row, operationType }"
      ></TargetAssignment>
    </template>
  </BuseCrud>
</template>

<script>
import TargetAssignment from './components/targetAssignment.vue';
import { getUser } from '@/api/system/user';
import { getTargetTableColumns } from './targetManagement';
import moment from 'moment';
// import { downloadInformation } from '@/api/digitalOperation/taskManagement/taskList.js';
import { resolveBlob } from '@/utils/common/fileDownload';
import {
  pageList,
  managementAdd,
  cenaAdd,
  cenaInfo,
  downloadInformation,
  getLastTargetEnum,
} from '@/api/digitalOperation/targetPerformance/targetManagement/index.js';
import { enumList } from '@/api/digitalOperation/targetPerformance/indexEnumeration/index.js';
import { checkNumber } from '@/utils/index';

export default {
  name: 'TargetManagement',
  components: {
    TargetAssignment,
  },
  data() {
    return {
      checkNumber,
      menuShow: true,
      tableData: [],
      filterParams: {
        year: undefined,
        oneTargetId: undefined,
        twoTargetId: undefined,
      },
      achieveTargetUnit: '个', //目标单位
      // unitOptions: [
      //   { value: '个', label: '个' },
      //   { value: '%', label: '%' },
      //   { value: '亿元', label: '亿元' },
      // ],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      oneTargetList: [], //一级指标
      twoTargetList: [], //二级指标
      okBtnShow: false, //是否展示ok按钮
      userInfo: null,
      viewModel: false, //是否是查看
      cenaInfo: undefined,
      initAchieveTarget: 20,
    };
  },
  created() {
    this.tableColumn = getTargetTableColumns.call(this);
  },
  computed: {
    filterOptions() {
      return {
        params: this.filterParams,
        config: [
          {
            title: '目标年份',
            field: 'year',
            element: 'slot',
            slotName: 'year',
          },
          {
            title: '一级指标',
            field: 'oneTargetId',
            element: 'a-select',
            props: {
              options: this.oneTargetList,
            },
            on: {
              change: this.oneTargetChange,
            },
          },
          {
            title: '二级指标',
            field: 'twoTargetId',
            element: 'a-select',
            props: {
              options: this.twoTargetList,
            },
            on: {
              change: this.twoTargetChange,
            },
          },
        ],
      };
    },
    modalConfig() {
      return {
        addBtn: true,
        viewBtn: false,
        addBtnText: '新增年度总目标',
        editBtnText: '编辑总目标',
        // viewBtnText: '查看总目标',
        submitText: this.okBtnShow ? '确认分配' : '确定',
        okText: '保存草稿',
        okBtn: this.okBtnShow,
        submitBtn: true,
        delBtn: false,
        formConfig: [
          {
            field: 'year',
            title: '年份',
            element: 'slot',
            slotName: 'yearSlot',
            props: {
              disabled: this.viewModel,
            },
            rules: [
              {
                required: true,
                message: '请选择年份',
                validator: (rule, value, callback) => {
                  if (value.startValue) {
                    callback();
                  } else {
                    callback('请选择年份');
                  }
                },
              },
            ],
          },
          {
            title: '一级指标',
            field: 'oneTargetId',
            element: 'a-select',
            rules: [{ required: true, message: '请选择一级指标' }],
            props: {
              disabled: this.viewModel,
              options: this.oneTargetList,
            },
            on: {
              change: this.oneTargetChangeModel,
            },
          },
          {
            title: '二级指标',
            field: 'twoTargetId',
            element: 'a-select',
            rules: [{ required: true, message: '请选择二级指标' }],
            props: {
              disabled: this.viewModel,
              options: this.twoTargetList,
            },
            on: {
              change: this.twoTargetChange,
            },
          },
          {
            title: '总计划完成目标',
            field: 'achieveTarget',
            element: 'slot',
            slotName: 'achieveTarget',
            rules: [
              {
                required: true,
                message: '请输入总计划完成目标',
                trigger: 'change',
              },
              { validator: checkNumber, trigger: '请输入正整数' },
            ],
          },
          {
            title: '指标内涵',
            field: 'targetConnotation',
            element: 'a-textarea',
            props: {
              rows: 5,
              disabled: this.viewModel,
              placeholder: '请对目标添加说明描述，不超过200个字',
            },
          },
        ],
        crudPermission: [
          {
            type: 'UPDATE',
            condition: (row) => {
              return row.isAllocation === '0';
            },
          },
        ],
        customOperationTypes: [
          {
            title: '目标分配',
            typeName: 'targetAssignment',
            slotName: 'targetAssignment',
            showForm: false,
            event: (row) => {
              this.okBtnShow = true;
              this.targetAssignmentView(row);
            },
            condition: (row) => {
              //TODO:目标分配条件判断
              return row.isAllocation === '0';
            },
          },
          {
            title: '查看完成情况',
            typeName: 'completionStatus',
            event: (row) => {
              this.$router.push({
                path: '/taskTarget/targetPerformance/completionStatus',
                query: {
                  id: row.id,
                },
              });
            },
            condition: (row) => {
              //TODO:目标分配条件判断
              return row.isAllocation === '1';
            },
          },
          {
            title: '查看总目标',
            typeName: 'viewTarget',
            event: (row) => {
              this.viewModel = true;
              this.rowViewTarget(row);
            },
            condition: () => {
              return true;
            },
          },
        ],
      };
    },
  },
  mounted() {
    this.getEnumList();
    this.loadData();
  },

  methods: {
    async loadData() {
      // this.loading = true;
      let p = this.filterParamsHandle();
      const [res, err] = await pageList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    filterParamsHandle() {
      console.log(this.filterParams.year);
      let p = {
        ...this.filterParams,
        year: this.filterParams.year?.startValue
          ? this.filterParams.year.startValue.format('YYYY-MM-DD')
          : '',
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      };
      return p;
    },
    //一级二级指标查询
    async getEnumList() {
      const [res, err] = await enumList();
      if (err) return;
      let data = res?.data;
      data = JSON.parse(
        JSON.stringify(data)
          .replace(/bpmTargetEnumTwoList/g, 'children')
          .replace(/targetName/g, 'label')
          .replace(/id/g, 'value')
      );
      this.oneTargetList = data;
      console.log(this.oneTargetList, 'this.oneTargetList');
    },
    //列表页一级指标change
    oneTargetChange(val) {
      this.filterParams.twoTargetId = undefined;
      this.filterTwoTarget(val);
    },
    //新增时一级指标change
    oneTargetChangeModel(val) {
      this.$refs.crud.$refs.modalView.formParams.twoTargetId = undefined;
      this.filterTwoTarget(val);
    },
    async twoTargetChange(val) {
      // console.log(val, '8888', this.$refs.crud.$refs.modalView);
      const currentBiz = this.twoTargetList.filter((item) => {
        return item.value === val;
      });
      this.achieveTargetUnit = currentBiz[0] ? currentBiz[0].targetUnit : '个';
      const { formParams } = this.$refs?.crud?.$refs?.modalView || {};
      if (!formParams?.year || !val) return;
      const [res, err] = await getLastTargetEnum({
        year: formParams.year.startValue.format('YYYY'),
        twoTargetId: val,
      });
      if (err) return;
      this.$refs.crud.$refs.modalView.formParams.achieveTarget = parseInt(
        res?.data?.referenceData || 0
      );
      this.initAchieveTarget = parseInt(res?.data?.referenceData || 0);
    },
    filterTwoTarget(val) {
      const currentBiz = this.oneTargetList.filter((item) => {
        return item.value === val;
      });
      console.log('currentBiz[0]', currentBiz[0]);
      this.twoTargetList = currentBiz[0] ? currentBiz[0].children : [];
    },
    //目标管理分配
    async cenaAdd(params) {
      const [, err] = await cenaAdd(params);
      if (err)
        return this.$message.error(
          `${params.status == '0' ? '保存草稿失败' : '分配失败'}`
        );
      this.$message.success(
        `${params.status == '0' ? '保存草稿成功' : '分配成功'}`
      );
      this.loadData();
    },
    //查询是否有草稿
    async getCenaInfo(row) {
      const [res, err] = await cenaInfo(row.id);
      if (err) return this.$message.error('查询草稿失败');

      if (!res.data.dateSelection) {
        this.cenaInfo = { cenaInfoAll: null, ...row };
      } else {
        this.cenaInfo = { cenaInfoAll: { ...res.data }, ...row };
      }
      console.log(this.cenaInfo, '处理');
      this.$refs.crud.switchModalView(true, 'targetAssignment', this.cenaInfo);
    },

    //目标管理新增
    async managementAdd(params, type) {
      const [, err] = await managementAdd(params);
      if (err)
        return this.$message.error(
          `${type === 'update' ? '编辑' : '新增'}失败`
        );
      this.$message.success(`${type === 'update' ? '编辑' : '新增'}成功`);
      this.loadData();
    },
    targetAssignmentView(row) {
      this.getCenaInfo(row);
    },
    modalCancelHandler() {},

    async modalSubmit(value) {
      console.log(this.achieveTargetUnit, '目标单位');
      console.log(value, 'value');
      if (!this.userInfo) {
        const userId = this.$store.state.base.user.userId;
        const [res, err] = await getUser(userId);
        if (err) {
          this.$message.error('获取用户信息异常');
          return;
        }
        this.userInfo = res.data.user;
      }

      const { crudOperationType } = value;
      console.log('操作名称', crudOperationType);
      if (crudOperationType === 'add' || crudOperationType === 'update') {
        let p = {
          ...value,
          achieveTarget: value.achieveTarget + this.achieveTargetUnit, //计划总完成目标入参拼接
          department: this.userInfo.organizeId, //部门id
          departmentName: this.userInfo.organizeName, //部门名称
          year: value.year?.startValue
            ? value.year.startValue.format('YYYY')
            : '',
        };
        crudOperationType === 'update' ? (p.id = value.id) : '';
        delete p.crudOperationType;
        console.log('新增编辑入参', p);
        this.managementAdd(p, crudOperationType); //新增
      } else if (crudOperationType === 'viewTarget') {
        console.log(333);
      } else {
        const flag = await this.modalConfirmHandler(value, '1');
        return flag;
      }
    },
    async modalConfirmHandler(value, status) {
      console.log(status, 'status');
      const formData = await this.$refs.TargetAssignment.getAllFormData();
      console.log(formData, 'formData');
      if (!formData) return false;
      let timeParams = formData.timeNodeDataForCustom.map((item) => {
        return item ? item.format('YYYY-MM-DD') : '';
      });

      let p = {
        managementId: value.id,
        dateSelection: formData.timeNodeData.dateSelection,
        customTime: timeParams ? timeParams.join(',') : '',
        status: status || '0', //草稿
        bpmCenaList: formData.areaDataParams,
      };
      console.log(p, '000ppppp');
      this.cenaAdd(p);
    },
    rowAdd() {
      this.viewModel = false;
      this.okBtnShow = false;
      this.$refs.crud.switchModalView(true);
    },
    //总计划完成目标回显
    achieveTargetHandle(value) {
      const { achieveTarget } = value;
      let index = null;
      if (achieveTarget.indexOf('个') > -1) {
        return (index = achieveTarget.indexOf('个'));
      } else if (achieveTarget.indexOf('%') > -1) {
        return (index = achieveTarget.indexOf('%'));
      } else if (achieveTarget.indexOf('亿元') > -1) {
        return (index = achieveTarget.indexOf('亿元'));
      }
      return index;
    },
    modelOpen(value, crudOperationType) {
      let index = this.achieveTargetHandle(value);
      this.achieveTargetUnit = value.achieveTarget.slice(
        index,
        value.achieveTarget.length
      );
      this.$refs.crud.switchModalView(true, crudOperationType, {
        year: this.viewModel ? value.year : { startValue: moment(value.year) },
        oneTargetId: value.oneTargetId,
        twoTargetId: value.twoTargetId,
        achieveTarget: index > -1 ? value.achieveTarget.slice(0, index) : '',
        targetConnotation: value.targetConnotation,
        department: value.department,
        departmentName: value.departmentName,
        id: value.id,
      });
    },
    rowViewTarget(value) {
      console.log('查看', value);
      this.oneTargetChangeModel(value.oneTargetId); //二级指标回显
      this.modelOpen(value, 'viewTarget');
    },
    rowEdit(value) {
      this.viewModel = false;
      console.log('编辑', value);
      this.oneTargetChangeModel(value.oneTargetId); //二级指标回显
      this.modelOpen(value, 'UPDATE');
    },
    // 导出
    async exportData() {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
      let p = this.filterParamsHandle();
      const [res] = await downloadInformation(p);
      resolveBlob(res, mimeMap.xlsx, '目标列表', '.xls');
    },
  },
};
</script>

<style lang="less" scoped></style>
