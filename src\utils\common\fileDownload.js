import axios from 'axios';
import moment from 'moment';
import { getToken } from '@/utils/common/auth';
import { getBaseUrl } from '@/utils/common/util.js';
/**
 * 文件下载
 * @param {*} opt.url
 * @param {*} opt.params
 * @param {*} opt.name
 * @param {*} opt.mimeType
 */
export function downLoadFile(opt) {
  const baseURL = process.env.VUE_APP_USE_BUILD_TYPE
    ? getBaseUrl()
    : process.env.VUE_APP_BASE_API;
  axios({
    method: 'get',
    url: baseURL + opt.url,
    params: opt.params,
    headers: { Authorization: getToken() },
    responseType: 'blob',
  })
    .then((res) => {
      if (res.headers.status === 'error') {
        //   reject('服务端错误')
        return;
      }
      const blob = new Blob([res.data], { type: opt.mimeType });
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob);
      } else {
        var link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = opt.name;
        link.click();
        window.URL.revokeObjectURL(link.href);
      }
      // resolve('ok')
    })
    .catch(() => {
      // reject(res)
    });
}

/**
 * 下载 excel 文件
 * @param {*} actualUrl
 * @param {*} params
 * @param {*} fileName
 * @param {*} fileExt
 */

const baseUrl = process.env.VUE_APP_USE_BUILD_TYPE
  ? getBaseUrl()
  : process.env.VUE_APP_BASE_API;

const mimeMap = {
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  zip: 'application/zip',
};

export function downloadExcel(actualUrl, params, fileName, fileExt = '.xls') {
  const url = `${baseUrl}${actualUrl}`;
  axios({
    method: 'get',
    url,
    params,
    responseType: 'blob',
    headers: { Authorization: getToken() },
  }).then((res) => {
    resolveBlob(res, mimeMap.xlsx, fileName, fileExt);
  });
}

/**
 * 解析blob响应内容并下载
 * @param {*} res blob响应内容
 * @param {String} mimeType MIME类型
 */
export function resolveBlob(res, mimeType, name = '', fileExt) {
  const aLink = document.createElement('a');
  const blob = new Blob([res], { type: mimeType });
  const date = moment();
  const fileName = `${name} [${date.format('YYYY-MM-DD')}]${fileExt}`;
  aLink.href = URL.createObjectURL(blob);
  aLink.setAttribute('download', fileName); // 设置下载文件名称
  document.body.appendChild(aLink);
  aLink.click();
  document.body.removeChild(aLink);
}

export function exportFile(content, customFileName) {
  let filename = content.filename || customFileName;
  let URL = window.URL || window.webkitURL;
  let objectUrl = URL.createObjectURL(content);
  let a = document.createElement('a');
  a.href = objectUrl;
  a.download = filename; //这步要注意 filename要写成 '文件名.xlsx'
  document.body.appendChild(a);
  a.click();
  a.remove();
}
