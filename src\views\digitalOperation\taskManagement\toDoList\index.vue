<template>
  <BuseCrud
    ref="crud"
    title="我的待办"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @rowView="rowView"
  >
    <template slot="defaultHeader">
      <a-button @click="exportHandle">导出</a-button>
    </template>
  </BuseCrud>
</template>

<script>
import { getTableColumn } from './toDoList';
import { getTaskStatus } from '../utils/index';

import {
  taskList,
  downloadInformation,
} from '@/api/digitalOperation/taskManagement/taskList.js';
import { resolveBlob } from '@/utils/common/fileDownload';
export default {
  name: 'ToDoList',

  data() {
    return {
      tableData: [],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      taskStatus: [],
      params: {
        taskName: undefined,
        content: undefined,
        transactorName: undefined,
        taskStatus: undefined,
        transactorTime: undefined,
        tag: '3', //列表标签 1:任务列表     2:我的批示   3:我的待办
      },
    };
  },
  created() {
    this.tableColumn = getTableColumn();
  },
  computed: {
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            title: '任务名称',
            field: 'taskName',
          },
          {
            title: '任务内容',
            field: 'content',
          },
          {
            title: '办理人',
            field: 'transactorName',
          },
          {
            title: '任务状态',
            field: 'taskStatus',
            element: 'a-select',
            props: {
              options: this.taskStatus,
            },
          },
          {
            title: '办理时限',
            field: 'transactorTime',
            element: 'a-range-picker',
          },
        ],
      };
    },
    modalConfig() {
      return {
        editBtn: false,
        delBtn: false,
        addBtn: false,
      };
    },
  },

  mounted() {
    this.loadData();
    getTaskStatus().then((res) => {
      this.taskStatus = res;
    });
  },

  methods: {
    async loadData() {
      this.loading = true;
      let p = this.paramsHandle();
      const [res, err] = await taskList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    paramsHandle() {
      let startTime = this.params.transactorTime?.[0]?.format('YYYY-MM-DD');
      let endTime = this.params.transactorTime?.[1]?.format('YYYY-MM-DD');
      let p = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
        startTime,
        endTime,
      };
      delete p.transactorTime;
      return p;
    },
    // 导出
    async exportHandle() {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
      let p = this.paramsHandle();
      const [res] = await downloadInformation(p);
      resolveBlob(res, mimeMap.xlsx, '我的待办', '.xls');
    },
    rowView(row) {
      this.$router.push({
        path: '/taskTarget/taskManagement/instructionDetail',
        query: {
          id: row.id,
          detailPageType: 'toDoList', //我的待办
        },
      });
    },
    handleAdd() {},
  },
};
</script>

<style lang="less" scoped></style>
