<!-- 饼图 -->
<template>
  <div class="pile-emissions">
    <div class="h4-title" v-if="title">{{ title }}</div>
    <div ref="initChart" class="carbon-echart"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
  components: {},
  name: 'pile-emissions',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      required: true,
    },
    subtext: {
      type: String,
      default: '',
    },
    position: {
      type: String,
      default: 'bottom',
    },
    total: {
      type: Number,
      default: 50,
    },
  },
  data() {
    return {
      color: [
        '#00FFAA',
        '#9AFF0D',
        '#69B7FF',
        '#EE6666',
        '#FF944D',
        '#FA5151',
        '#F538E2',
        '#9A60B4',
        '#EA7CCC',
      ],
    };
  },
  watch: {
    list: {
      handler(val) {
        // if (val && val.length) {
        this.$nextTick(() => {
          this.setOption();
        });
        // }
      },
      immediate: true, // 立即执行
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initObj.resize);
  },
  methods: {
    initChart() {
      this.initObj = echarts.init(this.$refs.initChart);
      window.addEventListener('resize', this.initObj.resize);
    },
    showLoading() {
      this.initObj.showLoading({
        text: '加载中...',
        color: '#00B578',
        textColor: '#00B578',
        maskColor: 'rgba(255, 255, 255, 0.2)',
        zlevel: 0,
      });
    },
    hideLoading() {
      this.initObj.hideLoading();
    },
    setOption() {
      let that = this;
      that.initObj.setOption({
        // width: "80%",
        // height: "80%",
        graphic: {
          type: 'image',
          id: 'backgroundImage',
          // 设置图片路径
          style: {
            // 这里是你的背景图路径
            // image: 'https://picsum.photos/200/300',
            image: require('@/assets/images/materialScreen/pie-bg.png'),
            x: -7, // 图片左上角的 x 坐标
            y: 10, // 图片左上角的 y 坐标
            width: 155, // 图片宽度
            height: 155, // 图片高度
            fill: '#fff',
          },
        },
        color: this.color,
        tooltip: {
          // 触发类型：坐标轴触发align
          trigger: 'item',
          backgroundColor: 'rgba(229,237,250,0.5)', // 通过设置rgba调节背景颜色与透明度
          borderWidth: '0',
          textStyle: {
            color: that.textColor,
          },
          formatter: function (info) {
            //
            let str = `<div style="text-align: left; color:#000;font-weight:700" >${info.data.name}</div>`;
            const arr = [
              {
                label: '数量',
                value: Number(info.value).toFixed(2).toLocaleString(),
              },
              { label: '占比', value: info.percent + '%' },
            ];
            arr.forEach((item, index) => {
              str += `<div style="
				background-color: rgba(255, 255, 255, 0.8);
				height: 32px;
				display: flex;
				justify-content: space-between;
				padding: 8px;
				border-radius: 4px;
				margin: 4px 0;"
      >
      <span style="color: #4e5969; font-size: 12px">${item.label}</span>
      <span style="color: #1d2129; font-size: 13px;margin-left:30px">${item.value}</span></div>`;
            });
            return str;
          },
          axisPointer: {
            //坐标轴指示器，坐标轴触发有效，
            type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
            shadowStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(7,185,185,0)', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(0,181,120,0.12)', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        },
        legend: {
          type: 'scroll',
          pageIconSize: 8,
          pageButtonItemCount: 5,
          pageIconColor: '#223b97',
          pageTextStyle: {
            color: '#fff',
            fontSize: 12,
          },
          orient: 'vertical', //horizintal  -   vertical
          // width: 300,
          // height: 1000,
          right: '0%',
          top: 'center',
          //   padding: [0, 50, 20, 10],
          //   right: 10,
          icon: 'roundRect',
          itemGap: 5,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#fff',
            width: '10%',
            rich: {
              b: {
                // color: "rgba(255,255,255,1)",
                padding: 6,
                fontSize: 10,
                width: 40,
              },
              d: {
                color: '#fff',
                padding: 6,
                fontSize: 10,
                width: 20,
                // fontWeight: 600,
              },
            },
          },
          formatter: function (name) {
            const item = that.list.filter((ele) => {
              if (ele.name == name) {
                return true;
              }
              return false;
            });
            let total = 0;
            for (let i = 0; i < that.list.length; i++) {
              total += that.list[i].value;
            }
            let per = ((item[0].value / total) * 100).toFixed(2);
            if (isNaN(per)) {
              per = '0';
            }
            const arr = ['{b|' + item[0].name + '}', '{d|' + per + '%}'];
            return arr.join(' ');
          },
          data: this.list.map((ele, index) => {
            return {
              name: ele.name,
              textStyle: {
                rich: {
                  b: {
                    // color: "rgba(255,255,255,1)",
                    padding: 6,
                    fontSize: 10,
                    width: 40,
                  },
                  d: {
                    color: that.color[index],
                    padding: 6,
                    fontSize: 10,
                    width: 20,
                    fontWeight: 600,
                  },
                },
              },
            };
          }),
        },
        series: [
          {
            type: 'pie',
            startAngle: 270, //起始角度
            center: ['25%', '45%'],
            radius: ['45%', '70%'],
            avoidLabelOverlap: true,
            roseType: 'area',
            label: {
              normal: {
                show: false,
                // formatter: `<div>123</div>`,
                formatter: '{d|{d}%}',
                // position: 'inner',
                rich: {
                  d: {
                    color: '#4E5969',
                    lineHeight: 20,
                    fontSize: 12,
                  },
                },
              },
            },
            labelLine: {
              show: true,
              emphasis: {
                show: true,
              },
              tooltip: {
                show: true,
              },
            },
            itemStyle: {
              borderWidth: 5,
              borderColor: 'rgba(0, 17, 94)',
            },
            data: this.list,
          },
        ],
      });
    },
  },
};
</script>

<style lang="less" scoped>
.pile-emissions {
  // margin-top: 50px;
  height: 220px;
  width: 100%;
  display: flex;
  flex-direction: column;
  .carbon-echart {
    width: 100%;
    flex: 1;
    height: 100%;
    display: flex;
    text-align: center;
  }
  .h4-title {
    font-family: AlibabaPuHuiTi;
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0em;
    color: #ffffff;
    position: relative;
    padding-left: 25px;
    &::after {
      content: '';
      position: absolute;
      width: 25px;
      height: 25px;
      left: 0;
      top: -3px;
      background-image: url('@/assets/images/materialScreen/icon-triangle.png');
      background-repeat: no-repeat;
      background-size: 100% auto;
    }
  }
  // .carbon-echart-half {
  // 	width: 450px;
  // 	height: 278px;
  // }
}
</style>
