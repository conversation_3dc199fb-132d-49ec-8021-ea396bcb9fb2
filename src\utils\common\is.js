const toString = Object.prototype.toString;

export function is(val, type) {
  return toString.call(val) === `[object ${type}]`;
}

export function isDef(val) {
  return typeof val !== 'undefined';
}

export function isUnDef(val) {
  return !isDef(val);
}

export function isDate(val) {
  return is(val, 'Date');
}

export function isNull(val) {
  return val === null;
}

export function isNullAndUnDef(val) {
  return isUnDef(val) && isNull(val);
}

export function isNullOrUnDef(val) {
  return isUnDef(val) || isNull(val);
}

export function isNumber(val) {
  return is(val, 'Number');
}

export function isString(val) {
  return is(val, 'String');
}

export function isBoolean(val) {
  return is(val, 'Boolean');
}

export function isRegExp(val) {
  return is(val, 'RegExp');
}

export const isServer = typeof window === 'undefined';

export const isClient = !isServer;
