import { statusEnum2String } from '@/views/system/constant/system';
import moment from 'moment';

export const statusOptions = [
  {
    value: 'NORMAL',
    label: '生效',
  },
  {
    value: 'ABNORMAL',
    label: '失效',
  },
];
export const statusEnum = {
  NORMAL: 'NORMAL',
  ABNORMAL: 'ABNORMAL',
};
export const getStatusLabel = (value) => {
  return statusOptions.find((item) => item.value === value)?.label;
};
export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'roleName',
      title: '角色名称',
      props: {
        placeholder: '请输入角色名称',
      },
    },
    {
      field: 'status',
      title: '角色状态',
      element: 'a-select',
      props: {
        options: [{ value: '', label: '全部' }, ...statusOptions],
        showSearch: true,
        optionFilterProp: 'children',
      },
    },
    {
      field: 'dateRange',
      title: '创建时间',
      element: 'a-range-picker',
      defaultValue: ['', ''],
      props: {
        showTime: {
          defaultValue: [
            moment('00:00:00', 'HH:mm:ss'),
            moment('23:59:59', 'HH:mm:ss'),
          ],
        },
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { roleName: '', status: '', dateRange: ['', ''] },
};
export const tableColumns = [
  {
    title: '角色编号',
    field: 'roleId',
    key: 'roleId',
  },
  {
    title: '角色名称',
    field: 'roleName',
    key: 'roleName',
  },
  {
    title: '状态',
    field: 'status',
    key: 'status',
    slots: { default: 'status' },
    width: 80,
    // formatter: ({ cellValue }) => {
    //   return getStatusLabel(cellValue) || '--';
    // },
  },
  {
    title: '创建时间',
    field: 'createTime',
    key: 'createTime',
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '操作',
    field: 'operation',
    key: 'operation',
    width: 150,
    slots: { default: 'operation' },
  },
];

// 应用树的对应关系
export const appReplaceFields = {
  children: 'children',
  title: 'label',
  key: 'id',
};
// 应用菜单树的对应关系
export const menuReplaceFields = {
  children: 'children',
  title: 'label',
  key: 'id',
};
export const formRules = {
  roleName: [
    {
      required: true,
      message: '角色名称不能为空',
      trigger: 'blur',
      whitespace: true,
    },
  ],
  roleKey: [
    {
      required: true,
      message: '权限字符不能为空',
      trigger: 'blur',
      whitespace: true,
    },
  ],
};

export const initForm = () => {
  return {
    roleId: undefined,
    roleName: undefined,
    roleKey: undefined,
    roleSort: 0,
    status: statusEnum2String.ENABLED,
    appIds: [], //选中列表
    remark: undefined,
  };
};
