export const queryList = ({ limit, pageNum }) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const list = [
        {
          id: 10001,
          address: '软件园',
          name: '朗新产业园',
          number: 5,
          person: '张三',
          date: '2023',
        },
        {
          id: 10002,
          address: '国有园区',
          name: '朗新产业园',
          number: 8,
          person: '李四',
          date: '2023',
        },
      ];
      resolve([
        {
          total: list.length,
          data: list.slice((pageNum - 1) * limit, pageNum * limit),
        },
        undefined,
      ]);
    }, 100);
  });
};
