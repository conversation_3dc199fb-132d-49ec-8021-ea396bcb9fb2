<!-- 饼图 -->
<template>
  <div class="charts-container">
    <h3 v-if="chartTitle">{{ chartTitle }}</h3>
    <div
      ref="initChart"
      v-show="showCharts"
      class="carbon-echart charts"
      :style="{ height: height, width: '100%' }"
    ></div>
    <a-empty
      :style="{
        height: height,
        margin: '0',
        paddingTop: 'calc((' + height + ' / 2) - 30px)',
      }"
      v-show="!showCharts"
      :image="simpleImage"
    />
    <div class="legend-wrap">
      <div
        class="legend-item"
        :class="{
          default: !selectLegendList.find((q) => q == item.name),
        }"
        v-for="(item, index) in legendList"
        :key="index"
        @click="triggerSelect(item)"
      >
        <div class="roundRect" :style="{ backgroundColor: item.color }"></div>
        <div class="name">
          {{ item.name }}
        </div>
        <div class="pre">{{ item.per }}%</div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import { Empty } from 'ant-design-vue';
export default {
  components: {},
  name: 'pile-emissions',
  props: {
    options: {
      type: Object,
      default: () => {},
    },
    chartTitle: {
      type: String,
    },
    height: {
      type: String,
      default: '160px',
    },
  },
  data() {
    return {
      colorList: [
        '#3868fc',
        '#cb20f2',
        '#01b8b9',
        '#f68a00',
        '#f82525',
        '#24cf86',
        '#fc7948',
        '#e771c5',
        '#349867',
        '#f9c14e',
        '#8f55ab',
        '#68b8d9',
      ],
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE, //空图片
      initObj: null,
      selectLegendList: [],
    };
  },
  computed: {
    showCharts() {
      return !!this.options?.series?.[0]?.data?.length;
    },
    legendList() {
      return (
        this.options?.legend?.data?.map((item, index) => {
          let total = 0;
          for (let i = 0; i < this.dataList.length; i++) {
            total += this.dataList[i].value;
          }
          const value =
            this.dataList.find((q) => q.name == item.name)?.value || 0;
          let per = ((value / total) * 100).toFixed(2);
          if (isNaN(per)) {
            per = '0';
          }
          return {
            name: item.name,
            color: this.colorList[index],
            per: per,
          };
        }) || []
      );
    },
    dataList() {
      let arr = this.options?.series?.flatMap((item, index) => {
        if (item.name == '外圈') {
          return item.data.flatMap((int) => {
            if (int.name) {
              return [
                {
                  name: int.name,
                  value: int.value,
                },
              ];
            } else {
              return [];
            }
          });
        } else {
          return [];
        }
      });
      return arr || [];
    },
  },
  watch: {
    options: {
      handler(val) {
        this.$nextTick(() => {
          this.setOption();
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart();
  },
  updated() {
    this?.initObj?.resize();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initObj.resize);
    this.initObj && this.initObj.off('legendselectchanged');
  },
  methods: {
    initChart() {
      const that = this;
      this.initObj = echarts.init(this.$refs.initChart);
      window.addEventListener('resize', this.initObj.resize);
      this.setOption();
      this.initObj.on('legendselectchanged', function (params) {
        that.selectLegendList = Object.entries(params.selected).reduce(
          (cur, nex) => {
            if (nex[1]) {
              cur.push(nex[0]);
            }
            return cur;
          },
          []
        );
      });
    },
    showLoading() {
      this.initObj.showLoading({
        text: '加载中...',
        color: '#00B578',
        textColor: '#00B578',
        maskColor: 'rgba(255, 255, 255, 0.2)',
        zlevel: 0,
      });
    },
    hideLoading() {
      this.initObj.hideLoading();
    },
    setOption() {
      this.initObj.setOption({
        ...this.options,
        legend: {
          top: 0,
          itemWidth: 0,
          formatter: () => '',
        },
      });
      this.selectLegendList = this.dataList.map((q) => q.name);
      this.initObj.dispatchAction({
        type: 'legendAllSelect',
      });
    },
    triggerSelect(item) {
      if (this.initObj) {
        this.initObj.dispatchAction({
          type: 'legendToggleSelect',
          name: item.name,
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.charts-container {
  width: 100%;
  background: rgba(249, 249, 249, 0.6);
  padding: 15px;
  border-radius: 8px;
  position: relative;
  h3 {
    padding-left: 19px;
    font-family: PingFang SC;
    font-size: 12px;
    color: #333333;
    background-image: url('@/assets/images/portraits/1-004.png');
    background-size: 14px 13px;
    background-repeat: no-repeat;
    background-position: 0 2px;
  }
  .legend-wrap {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    max-height: 85px;
    overflow-y: auto;
    padding: 0 10px;
    .legend-item {
      font-size: 10px;
      font-weight: 600;
      color: #333333;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      .roundRect {
        width: 6px;
        height: 15px;
        border-radius: 2px;
      }
    }
    .default {
      .roundRect {
        background-color: #cfcfcf !important;
      }
      .name {
        color: #cfcfcf !important;
      }
      .per {
        color: #cfcfcf !important;
      }
    }
  }
}
</style>
