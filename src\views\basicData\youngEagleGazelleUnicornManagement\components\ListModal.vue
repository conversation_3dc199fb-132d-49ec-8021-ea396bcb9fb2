<template>
  <a-modal
    width="600px"
    :title="
      modelTitle === 'add' ? '新增' : modelTitle === 'see' ? '详情' : '编辑'
    "
    :visible="visible"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="ruleForm"
        :config="formConfig"
        :params="formValue"
        :preview="preview"
      >
        <!-- 年份插槽 -->
        <template #year>
          <BuseRangePicker
            type="year"
            :needShowSecondPicker="() => false"
            v-model="formValue.year"
            placeholder="请选择年份"
            format="YYYY"
            :disableDateFunc="disableDateFunc"
          />
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment';
import { initFormValue } from '../constant';
import { addEnterCompany, updateEnterCompany } from '@/api/basicData';
import { institutionsMixin } from '../../mixins/institutionsMixin';
import FuzzySelect from '@/components/FuzzySelect/index.vue';

export default {
  props: ['visible', 'detail', 'preview', 'isLook', 'modelTitle', 'dictData'],
  mixins: [institutionsMixin],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.companyList = [];
          if (!this.detail) {
            this.formValue = initFormValue();
            return;
          }
          this.formValue.enterpriseName = this.detail.enterpriseName;
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
            year: {
              endOpen: false,
              endValue: null,
              startOpen: true,
              startValue: moment(this.detail.year),
            },
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: {
        year: { startValue: '' },
        enterpriseName: '',
        unifiedCreditCode: '',
        code: undefined,
      },
      formConfig: [
        {
          field: 'year',
          title: '年份',
          element: 'slot',
          slotName: 'year',
          rules: [
            { required: true, message: '请选择年份' },
            {
              validator: (rule, value, callback) => {
                console.log(value, 'value');
                if (value && value.startValue) {
                  callback();
                } else {
                  callback('请选择年份');
                }
              },
              trigger: 'blur',
            },
          ],
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          element: (h, item, params) => {
            return (
              <FuzzySelect
                value={params?.enterpriseName}
                onChangeSelect={(val) => {
                  if (val) {
                    this.formValue = {
                      ...this.formValue,
                      enterpriseName: val.name,
                      unifiedCreditCode: val.unifiedCreditCode,
                      enterpriseId: val.unifiedCreditCode,
                    };
                  } else {
                    this.$refs.tableComponent.setForm({
                      ...this.formValue,
                      enterpriseName: '',
                      unifiedCreditCode: '',
                      enterpriseId: '',
                    });
                  }
                }}
                disabled={this.modelTitle == 'see'}
              />
            );
          },
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          props: {
            placeholder: '请输入统一社会信用代码',
            disabled: true,
          },
        },
        {
          field: 'code',
          title: '称号',
          element: 'a-select',
          props: {
            options: this.dictData?.enterprise_designation_code || [],
          },
          rules: [{ required: true, message: '请输入称号' }],
        },
      ],
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      console.log(2222);
      this.formConfig[0].props.options = this.stateSelect;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        console.log(valid);
        if (valid) {
          const params = {
            ...this.formValue,
            year: moment(this.formValue.year?.startValue).format('YYYY'),
          };
          if (this.modelTitle === 'add') {
            this.addEnterCompany(params);
          } else {
            this.updateEnterCompany(params);
          }
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 调用父组件方法
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    // 新增
    async addEnterCompany(params) {
      const [, err] = await addEnterCompany(params);
      if (err) return;
      this.$message.success('新增成功!');
      this.$emit('loadData');
      this.handleCancel();
    },
    // 编辑
    async updateEnterCompany(params) {
      const [, err] = await updateEnterCompany(params);
      if (err) return;
      this.$message.success('编辑成功!');
      this.$emit('loadData');
      this.handleCancel();
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
