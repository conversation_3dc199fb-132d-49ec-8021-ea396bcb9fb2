<template>
  <a-spin
    :spinning="loading"
    class="portrait"
    :style="
      '--parent-width:' + parentWidth + 'px;--parent-scale:' + parentScale
    "
  >
    <div class="portrait-cnt">
      <div class="portrait-cnt-header">
        <h2></h2>
        <div class="time-area">
          {{ nowDate }}<br />
          {{ nowTime }}
        </div>
      </div>
      <div class="portrait-cnt-back" @click="$router.go(-1)">返回</div>
      <div class="portrait-cnt-bottom">
        <div class="portrait-cnt-bottom-left">
          <div class="page-title signal-line">无锡太湖湾科创城</div>
          <div class="sub-title signal-line">
            行政区划：{{ pictureInfo.adminArea }}
          </div>
          <div class="sub-title signal-line">
            载体面积：{{ pictureInfo.carrierArea }} 平方公里
          </div>
          <div class="sub-title signal-line">
            企业数量：{{ pictureInfo.companyNum }} 家
          </div>
          <div class="page-description">
            <h3>主导产业</h3>
            <p>
              {{ pictureInfo.leadIndustry }}
            </p>
          </div>
        </div>
        <div class="portrait-cnt-bottom-right">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-card :title="'经济发展'" class="portrait-card">
                <div class="portrait-card-cnt income">
                  <div class="income-list">
                    <div class="income-card">
                      本期累计应税收入
                      <span class="income-number">
                        {{ pictureInfo.currentTaxableIncome }}
                      </span>
                      <span class="income-unit"> 万元 </span>
                    </div>
                    <div class="income-card">
                      同期累计应税收入
                      <span class="income-number">
                        {{ pictureInfo.sameTaxableIncome }}
                      </span>
                      <span class="income-unit"> 万元 </span>
                    </div>
                    <div class="income-card">
                      上月累计应税收入
                      <span class="income-number">
                        {{ pictureInfo.lastTaxableIncome }}
                      </span>
                      <span class="income-unit"> 万元 </span>
                    </div>
                  </div>
                  <div class="income-charts">
                    <h3 class="income-charts-title">应税收入趋势分析</h3>
                    <div id="income" style="width: 670px; height: 274px"></div>
                  </div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card
                :title="'企业行业对比'"
                class="portrait-card portrait-card-8"
              >
                <div class="portrait-card-cnt">
                  <div class="proportion-charts">
                    <div
                      id="proportion"
                      style="width: 100%; height: 362px"
                    ></div>
                  </div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card
                :title="'企业产业对比'"
                class="portrait-card portrait-card-8"
              >
                <div class="portrait-card-cnt">
                  <div class="proportion-charts">
                    <div
                      id="proportion2"
                      style="width: 100%; height: 362px"
                    ></div>
                  </div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card
                :title="'企业应税收入排名'"
                class="portrait-card portrait-card-8"
              >
                <div class="portrait-card-cnt rank">
                  <div
                    v-for="(
                      item, index
                    ) in pictureInfo.companyTaxableIncome.slice(0, 7)"
                    :key="index"
                    :class="[
                      'rank-item',
                      'signal-line',
                      [0, 1, 2].includes(index)
                        ? 'rank-item_' + (index + 1)
                        : '',
                    ]"
                    :data-index="'0' + (index + 1)"
                    :data-num="item.value"
                    :title="item.key"
                    @click="handleCompanyClick(item)"
                  >
                    {{ item.key }}
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
    <!-- <img
      style="position: fixed; top: -100px,width: 100px; height: 100px"
      id="charts-bg"
      src="@/assets/images/portraits/17.png"
      alt=""
    /> -->
    <a-modal
      :visible="visible"
      :title="null"
      :closable="false"
      width="100%"
      wrapClassName="full-modal"
      :footer="null"
      @cancel="visible = false"
    >
      <portraits-detail
        v-if="visible"
        :pageName="'businessPortraits'"
        :pictureId="pictureId"
        @close="handleModalClose"
      ></portraits-detail>
    </a-modal>
  </a-spin>
</template>

<script>
import moment from 'moment';
import portraitsDetail from '@/views/portraits/components/portraitsDetail';
// import pannel from '@/views/portraits/components/pannel';
import * as api from '@/api/portraits';
import { throttle } from 'xe-utils';

export default {
  name: 'index',
  components: {
    //  pannel,
    portraitsDetail,
  },
  data() {
    return {
      pictureInfo: { companyTaxableIncome: [] },
      pictureInfo2: [],
      barCharts: {},
      pictureId: '',
      visible: false,
      sectorPie: {},
      industryPie: {},
      loading: true,
      parentWidth: 1920,
      parentScale: 1,
      nowDate: moment().format('YYYY-MM-DD'),
      nowTime: moment().format('dddd HH:mm:ss'),
    };
  },
  computed: {
    sectorData() {
      let companyIndustryPro = this.pictureInfo?.companyIndustryPro || [];
      return companyIndustryPro
        .map((item) => {
          return {
            value: Number(item.value),
            name: item.key,
          };
        })
        .filter((x) => x.value)
        .sort((a, b) => b.value - a.value);
    },
    industryData() {
      let companyIndustryPro = this.pictureInfo2 || [];
      return companyIndustryPro
        .map((item) => {
          return {
            value: Number(item.value),
            name: item.key,
          };
        })
        .filter((x) => x.value)
        .sort((a, b) => b.value - a.value);
    },
  },
  mounted() {
    this.getData();
    setTimeout(this.resize, 100);
    window.onresize = throttle(this.resize, 300);
  },
  methods: {
    handleModalClose() {
      this.visible = false;
    },
    resize() {
      this.parentWidth =
        document.querySelector('.portrait')?.parentNode?.offsetWidth || 1920;

      this.parentScale = this.parentWidth / 1920;
      console.log(this.parentWidth);
    },
    moment,
    // 查询页面数据信息
    async getData() {
      const _this = this;
      const params = {};
      this.loading = true;
      const [res, err] = await api.queryTaikePicture(params);
      this.loading = false;
      if (err) return;
      this.pictureInfo = res.data;
      this.incomeChart();
      this.proportionChart();
      const [res2, err2] = await api.queryIndustryPie(params);
      if (err2) return;
      this.pictureInfo2 = res2.data;
      this.proportion2Chart();

      // 监听鼠标悬停事件
      this.sectorPie?.on('mouseover', function (params) {
        // 修改另一个饼图的强调状态
        if (params.seriesIndex === 0) {
          _this.sectorPie?.dispatchAction({
            type: 'highlight',
            seriesIndex: 1,
            dataIndex: params.dataIndex,
          });
        } else if (params.seriesIndex === 1) {
          _this.sectorPie?.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: params.dataIndex,
          });
        }
      });

      // 监听鼠标移出事件
      this.sectorPie?.on('mouseout', function (params) {
        // 取消修改另一个饼图的强调状态
        _this.sectorPie?.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
        });
        _this.sectorPie?.dispatchAction({
          type: 'downplay',
          seriesIndex: 1,
        });
      });

      let industryCurrentIndex = -1;
      let sectorCurrentIndex = -1;

      setInterval(function () {
        _this.nowDate = moment().format('YYYY-MM-DD');
        _this.nowTime = moment().format('dddd HH:mm:ss');
      }, 1000);
      setInterval(function () {
        const industryDataLen = _this?.industryData?.length;
        const sectorDataLen = _this?.sectorData?.length;
        // 取消之前高亮的图形
        _this.industryPie?.dispatchAction({
          type: 'downplay',
          seriesIndex: 1,
          dataIndex: industryCurrentIndex,
        });
        industryCurrentIndex = (industryCurrentIndex + 1) % industryDataLen;
        // 高亮当前图形
        _this.industryPie?.dispatchAction({
          type: 'highlight',
          seriesIndex: 1,
          dataIndex: industryCurrentIndex,
        });
        _this.sectorPie?.dispatchAction({
          type: 'downplay',
          batch: [
            {
              seriesIndex: 1,
              dataIndex: sectorCurrentIndex,
            },
            {
              seriesIndex: 0,
              dataIndex: sectorCurrentIndex,
            },
          ],
        });
        sectorCurrentIndex = (sectorCurrentIndex + 1) % sectorDataLen;
        _this.sectorPie?.dispatchAction({
          type: 'highlight',
          batch: [
            {
              seriesIndex: 1,
              dataIndex: sectorCurrentIndex,
            },
            {
              seriesIndex: 0,
              dataIndex: sectorCurrentIndex,
            },
          ],
        });
      }, 1500);
    },
    handleCompanyClick(item) {
      console.log(item);
      if (item?.enterpriseId) {
        this.pictureId = item?.enterpriseId;
        this.visible = true;
      } else {
        this.$message.warn('暂无对应企业画像');
      }
    },
    // 收入折线图
    incomeChart() {
      let taxableIncomeAnalysis = this.pictureInfo.taxableIncomeAnalysis;
      const dataList = [];
      const xList = [];
      taxableIncomeAnalysis.forEach((item) => {
        xList.push(item.key);
        dataList.push(item.value);
      });
      // 基于准备好的dom，初始化echarts实例  这个和上面的main对应
      let myChart = this.$echarts.init(document.getElementById('income'));
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0c1d27',
          formatter:
            '<span class="income-tooltip-date">{b}</span> <br /> <span class="income-tooltip-cnt">应税收入 <span class="income-tooltip-value">{c} </span> 万元</span>',
          className: 'income-tooltip',
          padding: [12, 16, 12, 16],
        },
        grid: {
          left: 80,
          right: 20,
          top: 40,
          bottom: 30,
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            data: xList,
            boundaryGap: true, // 将 boundaryGap 设置为 true

            axisLine: {
              lineStyle: {
                color: 'rgba(94, 151, 222, 1)', // x轴线颜色
                width: 2, // x轴线宽度
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#8796A8', // X轴文字颜色
              margin: '15',
            },
          },
        ],
        yAxis: [
          {
            show: true,
            splitLine: {
              show: true, // 是否显示参考线
              lineStyle: {
                color: 'rgba(94, 152, 222, 0.5)', // 参考线颜色
                type: 'dashed', // 参考线线型，可以是 solid、dashed、dotted 等
              },
            },
            type: 'value',
            name: '单位：万元',
            nameTextStyle: {
              color: '#8796A8',
              align: 'left',
            },
            axisLabel: {
              color: '#8796A8',
            },
          },
        ],
        series: [
          {
            type: 'line',
            data: dataList,
            showSymbol: false, // 不显示折线上的点
            smooth: true,
            lineStyle: {
              // 设置线条的style等
              normal: {
                color: '#87e5ff',
              },
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  // 渐变颜色
                  {
                    offset: 0,
                    color: 'rgba(135, 229, 255, 0.2936)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(135, 229, 255, 0)',
                  },
                ],
                global: false,
              },
            },
          },
        ],
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: 0,
            realtime: true,
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
    },
    // 占比饼图1
    proportionChart() {
      const _this = this;
      let colorList = [
        '#87c1ff',
        '#4a9cff',
        '#6cff8e',
        '#ffe45f',
        '#ffac46',
        '#ff6f3f',
        '#61a5e8',
        '#7ecf51',
        '#eecb5f',
        '#e3935d',
        '#e16757',
        '#ffdf25',
      ];

      this.sectorPie = this.$echarts.init(
        document.getElementById('proportion')
      );
      let option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            let str = params.data.name + ' : ' + params.data.value + '%';
            return str;
          },
        },
        legend: {
          top: '290',
          left: 'center',
          icon: 'circle',
          itemWidth: 8, // 图标宽度
          itemHeight: 8, // 图标高度
          formatter: function (params) {
            let percent = _this.sectorData.find(
              (x) => x.name === params
            )?.value;
            let str = params + (percent ? '  ' + percent + '%' : '');
            return str;
          },
          textStyle: {
            color: 'rgba(205, 225, 255, 0.8)',
            fontSize: 14,
          },
        },
        grid: {
          containLabel: true, // 让图表内容完全包含在容器内
        },
        series: [
          {
            name: '详情',
            type: 'pie',
            radius: [0, '56%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            // padAngle: 3,
            top: '-15%',
            left: 0,
            right: 0,
            bottom: 0,
            // hoverAnimation: false, // 禁用鼠标浮动时的动画效果
            // silent: true, // 禁用鼠标事件
            labelLine: {
              show: false,
            },
            data: this.sectorData,
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorList[params.dataIndex % colorList.length];
                },
              },
            },
          },
          {
            type: 'pie',
            radius: [0, '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            top: '-15%',
            left: 0,
            right: 0,
            bottom: 0,
            emphasis: {
              scale: true, // 设置强调状态下的半径缩放比例
              scaleSize: 6, // 设置强调状态下的半径缩放比例
              // label: {
              //   show: true,
              //   fontSize: 20,
              //   fontWeight: 'bold',
              //   formatter: function (params) {
              //     let str = params.data.name + '  ' + params.data.value + '%';
              //     return str;
              //   },
              // },
            },
            labelLine: {
              show: false,
            },
            data: this.sectorData,
            itemStyle: {
              normal: {
                color: 'rgba(0,0,0,0.5)',
              },
            },
          },
        ],
      };
      this.sectorPie.setOption(option);
    },
    // 占比饼图2
    proportion2Chart() {
      const _this = this;
      let colorList = [
        '#87c1ff',
        '#4a9cff',
        '#6cff8e',
        '#ffe45f',
        '#ffac46',
        '#ff6f3f',
        '#61a5e8',
        '#7ecf51',
        '#eecb5f',
        '#e3935d',
        '#e16757',
        '#ffdf25',
      ];
      this.industryPie = this.$echarts.init(
        document.getElementById('proportion2')
      );
      let option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            let str = params.data.name + ' : ' + params.data.value + '%';
            return str;
          },
        },
        legend: {
          top: '290',
          left: 'center',
          icon: 'circle',
          itemWidth: 8, // 图标宽度
          itemHeight: 8, // 图标高度
          formatter: function (params) {
            let percent = _this.industryData.find(
              (x) => x.name === params
            )?.value;
            let str = params + (percent ? '  ' + percent + '%' : '');
            return str;
          },
          textStyle: {
            color: 'rgba(205, 225, 255, 0.8)',
            fontSize: 14,
          },
        },
        grid: {
          containLabel: true, // 让图表内容完全包含在容器内
        },
        series: [
          {
            name: '详情',
            type: 'pie',
            radius: ['43%', '56%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            padAngle: 3,
            top: '-15%',
            left: 0,
            right: 0,
            bottom: 0,
            hoverAnimation: false, // 禁用鼠标浮动时的动画效果
            silent: true, // 禁用鼠标事件
            labelLine: {
              show: false,
            },
            data: this.industryData,
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorList[params.dataIndex % colorList.length];
                },
              },
            },
          },
          {
            type: 'pie',
            radius: [0, '54%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            top: '-15%',
            left: 0,
            right: 0,
            bottom: 0,
            emphasis: {
              scale: true, // 设置强调状态下的半径缩放比例
              scaleSize: -8, // 设置强调状态下的半径缩放比例
              label: {
                show: true,
                fontSize: 16,
                color: '#fff',
                fontWeight: 'bold',
                formatter: function (params) {
                  let str = params.data.name + ' ' + params.data.value + '%';
                  return str;
                },
              },
            },
            labelLine: {
              show: false,
            },
            data: this.industryData,
            itemStyle: {
              normal: {
                color: function () {
                  return 'rgba(0,0,0,0.5)';
                },
              },
            },
          },
        ],
      };
      this.industryPie.setOption(option);
    },
  },
};
</script>

<style scoped lang="scss">
.portrait {
  --parent-width: 1920px;
  --parent-scale: 1;
  width: 1920px;
  // aspect-ratio: 16/9;
  // min-height: 2080px;
  height: 1080px;
  background: url('@/assets/images/portraits/bg.png');
  background-repeat: no-repeat;
  background-size: 120%;
  background-position: 5% 5%;
  transform: translate(-50%, -50%) scale(var(--parent-scale))
    translate(50%, 50%);
  &-cnt {
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    & > div {
      margin-bottom: 30px;
    }
    &-header {
      width: 100%;
      height: 83px;
      background: url('@/assets/images/portraits/5.png');
      background-size: 100% 100%;
      position: relative;
      h2 {
        font-family: Alimama ShuHeiTi;
        font-size: 32px;
        font-weight: bold;
        line-height: 73px;
        text-align: center;
        letter-spacing: 0px;
        color: #ffffff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .time-area {
        position: absolute;
        right: 0;
        top: 0;
        height: 43px;
        padding: 20px 0;
        width: 110px;

        font-family: AlibabaPuHuiTi;
        font-size: 13.47px;
        font-weight: 500;
        line-height: 18.64px;
        letter-spacing: 0em;

        /* 二级文字 */
        color: rgba(255, 255, 255, 0.8);
      }
    }
    &-back {
      width: 132px;
      height: 32px;
      line-height: 32px;
      background: url('@/assets/images/portraits/7.png');
      background-size: 100% 100%;
      color: #ffffff;
      font-family: Alimama ShuHeiTi;
      font-size: 16px;
      font-weight: bold;
      text-align: center;
      letter-spacing: 0em;
      cursor: pointer;
      margin: 0 40px 30px auto;
    }
    &-bottom {
      width: 1610px;
      height: 731px;
      margin: 0 auto;
      display: flex;
      &-left {
        width: 374px;
        height: 100%;
        margin-right: 60px;
        background-color: rgba(5, 35, 68, 0.3);
        background-image: url('@/assets/images/portraits/6.png');
        background-size: 100% 100%;
        font-family: HarmonyOS Sans SC;
        font-size: 14px;
        font-weight: normal;
        line-height: 150%;
        letter-spacing: 0px;
        color: rgba(205, 225, 255, 0.8);
      }
      &-right {
        width: 1186px;
        height: 100%;
      }
    }
  }
  .page-title {
    margin: 244px auto 11px;
    width: 326px;
    height: 44px;
    line-height: 44px;
    font-family: HarmonyOS Sans SC;
    font-weight: 500;
    letter-spacing: 0em;
    color: #ffffff;
    padding: 0;
    padding-left: 56px;
    background-image: url('@/assets/images/portraits/1.png');
    background-size: 100% 100%;
  }
  .sub-title {
    margin: 0 auto 11px;
    width: 326px;
    height: 32px;
    line-height: 32px;
    background: linear-gradient(
      90deg,
      rgba(81, 159, 255, 0.3) 0%,
      rgba(121, 186, 255, 0) 100%
    );
    padding-left: 12px;
  }
  .page-description {
    margin: 32px auto 12px;
    width: 326px;
    height: 24px;
    h3 {
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: 0px;
      padding: 0;
      padding-left: 36px;
      position: relative;
      /* 纯白 */
      color: #ffffff;
      &::before {
        content: '';
        display: inline-block;
        width: 28px;
        height: 16px;
        background-image: url('@/assets/images/portraits/4.png');
        background-size: 100% 100%;
        position: absolute;
        left: 0;
        bottom: 2px;
      }
    }
    p {
      text-indent: 2em;
      font-family: HarmonyOS Sans SC;
      font-size: 14px;
      font-weight: normal;
      line-height: 150%;
      letter-spacing: 0px;

      /* 文字/主要 */
      color: rgba(205, 225, 255, 0.8);
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 9; /* 设置最多显示10行 */
      line-clamp: 9; /* 标准语法，部分浏览器可能尚不支持 */
    }
  }

  .portrait-card {
    margin-top: 24px;
    ::v-deep .ant-card-head-wrapper {
      background: url('@/assets/images/portraits/3.png');
      background-size: 100% 100%;
      padding-left: 77px;
      font-family: Alimama ShuHeiTi;
      font-size: 18px;
      font-weight: bold;
      line-height: 18px;
      letter-spacing: 0px;
      height: 35px;
      // & .ant-card-head-title {
      //   padding: 12px 0 20px;
      // }
    }
    ::v-deep .ant-card-head {
      min-height: 35px;
    }
    &-8 {
      ::v-deep .ant-card-head-wrapper {
        background: url('@/assets/images/portraits/9.png');
        background-size: 100% 100%;
      }
    }
  }
  .ant-card,
  ::v-deep .ant-card-head {
    background: transparent;
    border: none;
    color: #ffffff;
    padding: 0;
  }
  ::v-deep .ant-card-body {
    padding: 0;
  }
  .signal-line {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .income {
    display: flex;
    padding-left: 12px;
    margin: 24px 0 24px;
    &-list {
      width: 468px;
    }
    &-card {
      width: 468px;
      height: 98px;
      background: url('@/assets/images/portraits/10.png');
      background-size: 100% 100%;
      padding-left: 98px;
      font-family: HarmonyOS Sans SC;
      font-size: 14px;
      font-weight: normal;
      line-height: 98px;
      letter-spacing: 0em;

      /* 二级文字-80% */
      color: rgba(205, 225, 255, 0.8);
      &:not(:last-child) {
        margin-bottom: 12px;
      }
    }
    &-number {
      font-size: 28px;
      font-weight: bold;
      letter-spacing: 0em;
      color: #fff;
    }
    &-unit {
      color: #fff;
    }
    &-charts {
      width: 670px;
      margin-left: 24px;
      &-title {
        height: 30px;
        line-height: 30px;
        width: 100%;
        background: url('@/assets/images/portraits/11.png');
        background-size: 100% 100%;
        padding-left: 26px;
        font-family: PingFang SC;
        font-size: 16px;
        font-weight: 500;
        letter-spacing: 0px;

        color: #ffffff;
      }
    }
  }

  .rank {
    width: 380px;
    height: 352px;
    padding: 50px 16px 16px;
    background: url('@/assets/images/portraits/12.png');
    background-size: 100% 100%;
    &-item {
      // padding-left: 44px;
      height: 40px;
      width: 100%;
      line-height: 40px;
      font-family: HarmonyOS Sans SC;
      font-size: 12px;
      letter-spacing: 0em;
      color: #ffffff;
      border-top: 0;
      border-left: 0;
      border-right: 0;

      /* 设置下边框宽度 */
      border-bottom-width: 5px;
      background: linear-gradient(
        270deg,
        rgba(0, 200, 255, 0) 0%,
        rgba(174, 237, 255, 0.3) 26%,
        rgba(174, 237, 255, 0.3) 75%,
        rgba(0, 200, 255, 0) 100%
      );
      background-size: 100% 2px; /* 调整大小以适应边框宽度 */
      background-repeat: no-repeat;
      background-position: left bottom; /* 确保渐变位于元素底部 */

      /* 如果需要的话，可以调整padding-bottom以确保渐变与内容之间没有间隙 */
      padding-bottom: 5px; /* 这里的值应等于border-bottom-width */
      padding-right: 110px;
      position: relative;

      cursor: pointer;
      &::before {
        content: attr(data-index);
        display: inline-block;
        width: 40px;
        height: 24px;
        line-height: 24px;
        margin-right: 4px;
        text-align: center;
        background-image: url('@/assets/images/portraits/13.png');
        background-size: 100% 100%;
        color: rgba(205, 225, 255, 0.8);
        font-family: HarmonyOS Sans SC;
        font-size: 16px;
        font-weight: bold;
      }
      &::after {
        content: attr(data-num);
        font-family: HarmonyOS Sans SC;
        font-size: 16px;
        font-weight: bold;
        line-height: 40px;
        letter-spacing: 0em;
        color: #86bded;
        position: absolute;
        right: 22px;
      }
      &_1 {
        &::before {
          color: #ffe45f;
          background-image: url('@/assets/images/portraits/14.png');
        }
      }
      &_2 {
        &::before {
          color: #ffac46;
          background-image: url('@/assets/images/portraits/15.png');
        }
      }
      &_3 {
        &::before {
          color: #4ffd83;
          background-image: url('@/assets/images/portraits/16.png');
        }
      }
    }
  }
}
#proportion2,
#proportion {
  background-image: url('@/assets/images/portraits/17.png');
  background-size: 280px;
  background-repeat: no-repeat;
  background-position: center 8px;
  position: relative;
}
#proportion::after {
  --width: 80px;
  --height: 80px;
  content: '';
  display: block;
  position: absolute;
  top: calc(50% - var(--height) / 2 - 26px);
  left: calc(50% - var(--width) / 2);
  width: var(--width);
  height: var(--height);
  background-image: url('@/assets/images/portraits/18.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

::v-deep .income-tooltip {
  background: linear-gradient(
    113deg,
    rgb(0 251 255 / 0%) 0%,
    rgba(0, 132, 255, 0.3) 100%
  );
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(115deg, rgba(0, 251, 255, 0) 77%, #00fbff 109%)
    1;
  backdrop-filter: blur(20px);
  &-date {
    font-family: HarmonyOS Sans SC;
    font-size: 16px;
    font-weight: bold;
    line-height: 16px;
    letter-spacing: 0em;
    color: #ffffff;
  }
  &-cnt {
    padding-left: 12px;
    position: relative;
    font-family: HarmonyOS Sans SC;
    font-weight: bold;
    line-height: 16px;
    text-align: right;
    letter-spacing: 0em;
    color: rgba(205, 225, 255, 0.8);
    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      background: #6cff8e;
      border-radius: 50%;
      position: absolute;
      top: 5.5px;
      left: 0;
    }
  }
  &-value {
    font-size: 16px;
    color: #87e5ff;
  }
}

::v-deep .ant-spin-blur,
::v-deep .ant-spin-container {
  height: 100%;
}
::v-deep .ant-spin-spinning {
  min-height: 1080px;
}
</style>
