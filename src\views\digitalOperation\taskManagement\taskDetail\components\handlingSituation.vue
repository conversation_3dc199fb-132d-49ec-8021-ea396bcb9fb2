<template>
  <BuseCrud
    ref="crud"
    title="办理信息"
    :loading="loading"
    :tableColumn="tableColumn"
    :tableData="taskTransactionInfoResList"
    :modalConfig="modalConfig"
    @loadData="loadData"
  >
    <template slot="attachment" slot-scope="{ row }">
      <downLoadTemplate :attachment="row.attachment" />
    </template>
  </BuseCrud>
</template>

<script>
import { revocation } from '@/api/digitalOperation/taskManagement/taskList.js';
import downLoadTemplate from '@/components/downLoadTemplate/index.vue';

export default {
  name: 'ManagementTkHandlingSituation',
  components: { downLoadTemplate },
  props: {
    taskTransactionInfoResList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      tableData: [],
      taskStatus: [],
      loading: false,
    };
  },
  created() {
    this.tableColumn = [
      {
        title: '序号',
        type: 'seq',
      },
      {
        title: '办理部门',
        field: 'departmentName',
      },
      {
        title: '办理人',
        field: 'transactorName',
      },
      {
        title: '提交时间',
        field: 'createTime',
      },
      {
        title: '办理情况',
        field: 'transactionCase',
      },
      {
        title: '任务进度（%）',
        field: 'schedule',
      },
      {
        title: '附件',
        field: 'attachment',
        slots: {
          default: 'attachment',
        },
      },
    ];
  },
  computed: {
    filterOptions() {
      if (this.params.state === '0') {
        return {
          params: this.params,
          config: [
            {
              field: 'category',
              title: '类别',
              element: 'a-cascader',
              props: {
                options: this.categories,
              },
              on: {
                change: this.categoriesChange,
              },
            },
            {
              field: 'rateTime',
              title: '创建时间',
              element: 'a-range-picker',
            },
            {
              field: 'author',
              title: '作者',
              element: 'a-cascader',
              props: {
                options: this.authors,
                changeOnSelect: true,
                loadData: this.loadAuthorsData,
              },
            },
            {
              field: 'article',
              title: '文章',
            },
            {
              field: 'articleType',
              title: '所属类型',
              element: 'a-select',
              props: {
                options: this.articleTypes,
              },
            },
          ],
        };
      } else {
        return {
          params: this.params,
          config: [
            {
              field: 'category',
              title: '类别',
              element: 'a-cascader',
              props: {
                options: this.categories,
              },
            },
            {
              field: 'rateTime',
              title: '发布时间',
              element: 'a-range-picker',
            },
            {
              field: 'author',
              title: '作者',
              element: 'a-cascader',
              props: {
                options: this.authors,
                changeOnSelect: true,
                loadData: this.loadAuthorsData,
              },
            },
            {
              field: 'article',
              title: '文章',
            },
            {
              field: 'articleType',
              title: '所属类型',
              element: 'a-select',
              props: {
                options: this.articleTypes,
              },
            },
          ],
        };
      }
    },
    modalConfig() {
      return {
        viewBtn: false,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        menu: this.$route.query.detailPageType == 'toDoList', //撤回按钮只有我的待办才显示
        customOperationTypes: [
          {
            title: (row, h) => {
              console.log(h);
              let flag = row.ifRevocation == '1' || row.id !== this.newId;
              return (
                <a-button type="link" props={{ disabled: flag }}>
                  撤回
                </a-button>
              );
            },
            typeName: 'revocation',
            event: (row) => {
              let that = this;
              this.$confirm({
                content: `确认要撤回吗？`,
                onOk() {
                  return new Promise((resolve) => {
                    that.revocation(row);
                    resolve();
                  });
                },
                cancelText: '取消',
              });
            },
            condition: (row) => {
              console.log(row);
              return true;
            },
          },
        ],
      };
    },
    newId() {
      return this.taskTransactionInfoResList[0].id;
    },
  },

  mounted() {
    console.log('办理情况', this.taskTransactionInfoResList);
  },

  methods: {
    async revocation(row) {
      const [, err] = await revocation(row.id);
      if (err) return;
      this.$message.success('撤回成功');
      this.$emit('refresh');
    },

    loadData() {},
  },
};
</script>

<style lang="less" scoped></style>
