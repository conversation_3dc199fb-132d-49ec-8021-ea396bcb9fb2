<template>
  <a-modal
    width="378px"
    :bodyStyle="{ padding: '0' }"
    :visible="visible"
    :footer="null"
    :maskClosable="false"
    :closable="true"
    centered
    @cancel="handleCancel"
  >
    <LoginForm ref="LoginForm" @success="afterLogin" />
  </a-modal>
</template>
<script>
import LoginForm from './LoginForm.vue';

export default {
  components: { LoginForm },
  props: {
    visible: Boolean,
  },
  watch: {
    visible(value) {
      if (value) {
        this.$refs?.LoginForm?.getCode();
      } else {
        this.$refs?.LoginForm?.resetFields();
      }
    },
  },
  methods: {
    handleCancel() {
      this.$emit('update:visible', false);
    },
    // 登录之后操作
    async afterLogin() {
      this.handleCancel();
      // 获取重定向信息跳转到之前页面
      // await this.$store.dispatch("base/GetInfo");
      // this.$store.commit("setting/setMenuData");
      const redirect = this.$route.query.redirect || '';
      if (redirect && redirect.indexOf('http') > -1) {
        window.location.href = redirect;
      } else {
        this.$router.replace('/');
      }
    },
  },
};
</script>
<style lang="less" scoped>
.bd-code-box {
  width: 100px;
  height: 40px;
  margin-left: 16px;
  .bd-code-img {
    width: 100px;
    height: 40px;
  }
}
.form-item-code {
  display: flex;
}
.reset-password {
  text-align: right;
  cursor: pointer;
  margin-bottom: 12px;
  color: #1677ff;
}
.login-box-wrapper {
  padding: 32px 30px;
  .login-box-header {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .login-box-title {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      text-align: center;
      line-height: 22px;
      margin: 8px 0 0;
    }
  }
  .login-box-form {
    margin-top: 35px;
    .login-form-item {
      margin-bottom: 24px;
    }
    .login-box-form-btn {
      border-radius: 20px;
      width: 100%;
      height: 40px;
      background: #84bf41;
      border-color: #84bf41;
    }
  }
}
</style>
