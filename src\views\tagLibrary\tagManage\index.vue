<template>
  <page-layout>
    <div class="tag-manage">
      <!--        目录-->
      <tag-tree @getNodeInfo="getNodeInfo" :tagList="tagList"></tag-tree>
      <!--        标签内容区-->
      <div class="tag-manage-right">
        <TableLayout
          v-bind="tableProps"
          :tableColumn="tableColumn"
          ref="page"
          @getTableData="getTable"
          :params.sync="searchForm"
        >
          <template #operation="{ row }">
            <BtnGroup :row="row" :btns="columnButtons" :maxCount="5" />
          </template>
        </TableLayout>
      </div>
    </div>

    <BuseModal
      ref="modal"
      :modalConfig="modal.modalConfig"
      :formConfig="formConfig"
      :submit="handleModalSubmit"
      v-model="modal.formParams"
    ></BuseModal>
  </page-layout>
</template>

<script>
import TagTree from '@/views/tagLibrary/tagManage/components/tagTree.vue';
import * as api from '@/api/tagLibrary';
import TableLayout from '@/layouts/TableLayout.vue';
import BtnGroup from '@/components/BtnGroup';
import BuseModal from '@/components/BuseModal';
import { confirmFunc } from '@/utils';
import { saveAs } from 'file-saver';

export default {
  name: 'index',
  components: { TagTree, TableLayout, BtnGroup, BuseModal },
  data() {
    return {
      limit: 10,
      pageNum: 1,
      currentPage: 1,
      total: 0,
      pages: 0,
      catalogInfo: {},
      loading: false,
      labelCol: {
        span: 2,
      },
      wrapperCol: {
        span: 14,
      },
      searchForm: {
        labelName: '',
      },
      typeList: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '0',
          label: '初始化标签',
        },
        {
          value: '1',
          label: '自定义标签',
        },
      ],
      classList: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '1',
          label: '园区',
        },
        {
          value: '2',
          label: '企业',
        },
      ],
      tagList: [],
      modal: {
        modalConfig: {
          props: {
            title: '编辑',
            width: '400px',
          },
          formProps: {
            layout: 'vertical',
            defaultColSpan: 24,
          },
        },
        formParams: {},
      },

      dict: {
        labelClass: [
          {
            value: '3',
            label: '全部',
          },
          {
            value: '1',
            label: '园区',
          },
          {
            value: '2',
            label: '企业',
          },
        ],
        dirId: [],
      },
      selectRow: null,
    };
  },
  computed: {
    formConfig() {
      return [
        {
          field: 'labelName',
          title: '标签名称',
          rules: [
            { required: true, message: '请输入标签名称', trigger: 'change' },
          ],
        },
        {
          field: 'labelClass',
          title: '标签属性',
          element: 'a-radio-group',
          props: {
            options: [
              {
                value: '1',
                label: '园区',
              },
              {
                value: '2',
                label: '企业',
              },
            ],
          },
          rules: [
            { required: true, message: '请选择标签属性', trigger: 'change' },
          ],
        },
        {
          field: 'dirId',
          title: '标签类别',
          element: 'a-tree-select',
          props: {
            treeData: this.dict.dirId,
          },
          rules: [
            { required: true, message: '请选择标签类别', trigger: 'change' },
          ],
        },
      ];
    },
    tableProps() {
      return {
        tableApiList: [
          {
            api: api.getListLabelWareHouse,
            params: {
              dirId: this.catalogInfo.id,
            },
          },
        ],
        filterConfig: [
          {
            field: 'labelName',
            title: '标签名称',
          },
        ],
        // dataHandler,
        tableButtons: this.creatTableButtons,
      };
    },

    tableColumn() {
      return [
        {
          field: 'labelName',
          title: '标签名称',
        },
        {
          field: 'labelClass',
          title: '标签属性',
          formatter: ({ cellValue }) =>
            this.dict.labelClass?.find((item) => item.value === cellValue)
              ?.label,
        },
        // {
        //   field: 'dirName',
        //   title: '标签类别',
        // },
        ...(this.catalogInfo?.id !== '8121751641706234876522'
          ? [
              {
                field: 'operation',
                title: '操作',
                width: 140,
                fixed: 'right',
                slots: { default: 'operation' },
              },
            ]
          : []),
      ];
    },
  },

  mounted() {
    this.getData();
  },

  methods: {
    getTable(data) {
      this.tagList = data;
    },
    async queryLabelDir() {
      // 初始化时为根目录添加操作按钮
      // this.addAttrToTree(this.treeData);
      const [res, err] = await api.queryLabelDir({});
      if (err) return;
      let list = JSON.parse(res.data[0].dirData);
      this.dict.dirId = (list?.[0]?.children || []).map((x) => ({
        ...x,
        value: x.key,
        label: x.title,
      }));
    },
    handleAdd() {
      this.modal.modalConfig.props.title = '新增标签';
      this.selectRow = null;
      this.$refs.modal.open();
    },
    handleEdit(row) {
      this.modal.modalConfig.props.title = '编辑标签';
      this.selectRow = row;
      this.$refs.modal.open({ params: row });
    },
    async handleModalSubmit() {
      let method = this.selectRow
        ? 'updateLabelWareHouse'
        : 'saveLabelWareHouse';
      const [res, err] = await api?.[method]({
        ...this.modal.formParams,
        id: this.selectRow?.id,
        dirName: this.dict.dirId.find(
          (x) => x.value === this.modal.formParams.dirId
        )?.label,
      });
      if (err) return;
      this.$message.success('保存成功');
      this.getData();
    },

    creatTableButtons() {
      return [
        {
          label: '新增标签',
          event: this.handleAdd,
          props: {
            type: 'primary',
          },
          show: () => this.catalogInfo?.id !== '8121751641706234876522',
        },
        // {
        //   label: '导出',
        //   event: this.handleExport,
        //   props: {
        //     type: 'primary',
        //   },
        //   show: () => true,
        // },
      ];
    },
    columnButtons() {
      return [
        {
          label: '编辑',
          event: this.handleEdit,
          show: () => this.catalogInfo.id !== '8121751641706234876522',
        },
        {
          label: '删除',
          event: this.handleDelete,
          show: () => this.catalogInfo.id !== '8121751641706234876522',
        },
      ];
    },
    async handleExport() {
      const res = await confirmFunc('确定导出吗？');
      if (!res) return;
      const [res2, err2] = await api.downloadAllInformation(this.searchForm);
      if (err2) return;
      saveAs(res2, '标签.xlsx');
    },
    async handleDelete({ id }) {
      const res = await confirmFunc('确定要删除吗？');
      if (!res) return;
      const [_, err2] = await api.deleteLabelWareHouse({
        id,
      });
      if (err2) return;
      this.$message.success('删除成功');
      this.getData();
    },
    // 查询数据
    async getData() {
      await this.queryLabelDir();
      // console.log(123123);
      if (!this.catalogInfo.id) {
        return;
      }
      this.$refs.page.loadData({
        dirId: this.catalogInfo.id,
      });
    },
    // 搜索
    // 点击目录更新页面标签数据
    getNodeInfo(data) {
      this.catalogInfo = data;
      this.getData();
    },
  },
};
</script>

<style scoped lang="scss">
.tag-manage {
  display: flex;
  flex-direction: row;
  height: 80vh;
  background: white;
  padding: 10px;

  .tag-manage-left {
    width: 20%;
    height: 100%;
    border-right: #d9d9d9 1px solid;
    padding: 10px;
  }

  .tag-manage-right {
    width: 80%;
    padding: 20px;

    .catalog-content {
      margin-top: 30px;
      /*max-height: 60vh;*/
      overflow: auto;

      .catalog-name {
        padding: 5px;
        background: #1677ffdb;
        font-size: 15px;
        color: white;
        border-radius: 5px;
      }

      .tag-card {
        margin-bottom: 10px;
        height: 100px;
        border: #d9d9d9 1px solid;
        border-radius: 5px;
        display: flex;
        flex-direction: row;
        align-items: center;
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);

        p {
          margin-bottom: unset;
          color: #898989;
        }

        .line {
          border-color: #7cb305;
          height: 80px;
        }

        .icon {
          width: 10%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          p {
            /*color: blue;*/
          }
        }

        .details {
          width: 70%;
          display: flex;
          flex-direction: row;

          .details-left,
          .details-right {
            .upper {
              padding: 3px;

              .code {
                font-weight: 700;
                color: #0000ff;
              }

              .title {
                color: #545353;
                font-weight: 700;
              }
            }

            .lower {
              padding: 3px;

              .title {
                color: #545353;
                font-weight: 700;
              }
            }
          }

          .details-left {
            width: 60%;

            .upper {
              display: flex;
              flex-direction: row;

              .code {
                width: 30%;
                font-weight: 700;
                color: #0000ff;
              }

              .tag-name {
                display: flex;
                width: 70%;

                .title {
                  width: 30%;
                  text-align: right;
                }

                .info {
                  width: 70%;
                  color: #ff9900;
                  font-weight: 700;
                }
              }
            }
          }

          .details-right {
            width: 40%;
          }
        }

        .online {
          width: 20%;
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          cursor: pointer;

          p {
            text-align: center;
            font-weight: 700;
            margin-top: 5px;
          }

          img {
            width: 40px;
            height: 40px;
            max-width: 100%;
            height: auto;
          }

          .picture:hover {
            transition-duration: 0.5s;
            transform: scale(1.2);
            transition-duration: 0.5s;
          }
        }
      }

      .more-button {
        margin: 20px 0;
        text-align: center;
        cursor: pointer;
      }

      .more-button:hover {
        color: #1994ff;
      }

      .more-loading {
        text-align: center;
        border-radius: 4px;
        margin-bottom: 20px;
        padding: 30px 50px;
        margin: 20px 0;
      }
    }
  }
}
</style>
