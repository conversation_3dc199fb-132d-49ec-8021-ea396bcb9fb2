# ScrollTable 组件 API 文档

## 组件概述

ScrollTable 是一个高性能的无限循环滚动表格组件，实现了以下核心功能：

- ✅ 无限向下循环滚动，视觉呈现持续流动效果
- ✅ 始终保持指定数量的可见行（默认10行）
- ✅ 队列更新机制：上边缘检测和下边缘预加载
- ✅ 循环使用数据源，无缝衔接
- ✅ 自动滚动控制：持续平滑滚动，鼠标交互暂停
- ✅ 支持人工滚动干预，自然恢复逻辑
- ✅ 原生实现，高性能优化
- ✅ 虚拟缓冲区和内存回收机制

## Props 属性

### 基础配置

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| height | String | '150px' | 否 | 表格容器高度 |
| tableTitle | String | '' | 否 | 表格标题，为空时不显示 |
| columns | Array | [] | 是 | 列配置数组 |
| tableData | Array | [] | 是 | 数据源数组 |

### 滚动控制

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| scrollSpeed | Number | 1 | 否 | 每次滚动的像素数，建议1-3 |
| scrollInterval | Number | 50 | 否 | 滚动间隔时间（毫秒），建议30-100 |
| visibleRowCount | Number | 10 | 否 | 可见行数，影响显示效果 |
| bufferSize | Number | 5 | 否 | 缓冲区大小，影响性能 |
| rowHeight | Number | 40 | 否 | 行高（像素），用于计算滚动 |
| autoScroll | Boolean | true | 否 | 是否启用自动滚动 |

## 列配置 (columns)

每个列配置对象支持以下属性：

### 基础属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| title | String | - | 是 | 列标题 |
| field | String | - | 是 | 数据字段名 |
| width | Number | - | 否 | 固定列宽（像素） |
| minWidth | Number | 80 | 否 | 最小列宽（像素） |
| align | String | 'left' | 否 | 内容对齐：left/center/right |
| headerAlign | String | 'left' | 否 | 表头对齐：left/center/right |

### 高级属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| type | String | 'text' | 否 | 列类型：text/html |
| formatter | Function | - | 否 | 自定义格式化函数 |

### formatter 函数

```javascript
formatter: ({ cellValue, row }) => {
  // cellValue: 当前单元格的值
  // row: 当前行的完整数据对象
  return '格式化后的内容';
}
```

## 方法

组件暴露以下方法（通过 ref 调用）：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| startAutoScroll | - | void | 开始自动滚动 |
| stopAutoScroll | - | void | 停止自动滚动 |
| pauseAutoScroll | - | void | 暂停自动滚动 |
| resumeAutoScroll | - | void | 恢复自动滚动 |

## 事件

目前组件主要通过内部状态管理，暂不对外暴露事件。

## 样式定制

### CSS 类名

| 类名 | 说明 |
|------|------|
| .table-container | 表格外层容器 |
| .infinite-scroll-table | 滚动表格容器 |
| .table-header | 表头区域 |
| .header-row | 表头行 |
| .header-cell | 表头单元格 |
| .table-body-container | 表体容器 |
| .table-body | 表体内容 |
| .table-row | 数据行 |
| .table-cell | 数据单元格 |

### 自定义样式示例

```css
/* 自定义表头样式 */
.my-table :deep(.table-header) {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

/* 自定义行样式 */
.my-table :deep(.table-row:hover) {
  background-color: #e6f7ff;
  transform: scale(1.02);
}

/* 自定义滚动条 */
.my-table :deep(.table-body-container::-webkit-scrollbar-thumb) {
  background: #1890ff;
}
```

## 使用示例

### 基础用法

```vue
<template>
  <ScrollTable
    :height="'400px'"
    :tableTitle="'数据列表'"
    :columns="columns"
    :tableData="data"
  />
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { title: 'ID', field: 'id', width: 80 },
        { title: '名称', field: 'name', minWidth: 120 },
        { title: '状态', field: 'status', width: 100, type: 'html',
          formatter: ({ cellValue }) => `<span class="status-${cellValue}">${cellValue}</span>`
        }
      ],
      data: [
        { id: 1, name: '项目A', status: 'active' },
        { id: 2, name: '项目B', status: 'inactive' },
        // ...
      ]
    }
  }
}
</script>
```

### 高级配置

```vue
<template>
  <ScrollTable
    ref="scrollTable"
    :height="'500px'"
    :tableTitle="'高级配置示例'"
    :columns="columns"
    :tableData="data"
    :scrollSpeed="2"
    :scrollInterval="30"
    :visibleRowCount="12"
    :bufferSize="8"
    :rowHeight="45"
    :autoScroll="autoScrollEnabled"
  />
</template>

<script>
export default {
  data() {
    return {
      autoScrollEnabled: true,
      columns: [
        {
          title: '排名',
          field: 'rank',
          width: 80,
          type: 'html',
          align: 'center',
          formatter: ({ cellValue }) => `<div class="rank-badge">TOP${cellValue}</div>`
        },
        {
          title: '公司名称',
          field: 'company',
          minWidth: 150,
          align: 'left'
        },
        {
          title: '收入(万元)',
          field: 'revenue',
          width: 120,
          align: 'right',
          formatter: ({ cellValue }) => Number(cellValue).toLocaleString()
        }
      ],
      data: [
        { rank: 1, company: '阿里巴巴', revenue: 5000.5 },
        { rank: 2, company: '腾讯', revenue: 4500.3 },
        // ...
      ]
    }
  },
  methods: {
    toggleScroll() {
      if (this.autoScrollEnabled) {
        this.$refs.scrollTable.stopAutoScroll();
      } else {
        this.$refs.scrollTable.startAutoScroll();
      }
      this.autoScrollEnabled = !this.autoScrollEnabled;
    }
  }
}
</script>
```

## 性能建议

### 数据量控制
- 建议数据量控制在 50-200 条之间
- 超过 500 条数据时考虑分页或服务端分页

### 滚动参数优化
- **平滑滚动**: scrollSpeed=1, scrollInterval=50
- **快速滚动**: scrollSpeed=2, scrollInterval=30  
- **慢速滚动**: scrollSpeed=0.5, scrollInterval=80

### 列配置优化
- 避免过多的 HTML 类型列（建议不超过总列数的 30%）
- 合理设置列宽，避免频繁的布局重排
- 复杂的 formatter 函数建议使用缓存

### 移动端适配
- 移动端建议降低滚动速度：scrollSpeed=0.5
- 减少可见行数：visibleRowCount=6-8
- 简化列配置，避免过宽的表格

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+  
- ✅ Safari 12+
- ✅ Edge 79+
- ❌ IE 11 及以下（不支持）

## 技术实现

### 核心技术栈
- Vue 2.x 组件
- CSS Transform 动画
- 虚拟滚动算法
- 事件节流优化

### 内存管理
- 动态缓冲区管理
- 及时清理定时器
- 避免内存泄漏

### 性能优化
- 使用 CSS Transform 替代 DOM 操作
- 事件节流处理（16ms/60fps）
- 虚拟化渲染减少 DOM 节点
