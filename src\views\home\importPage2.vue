<template>
  <page-layout>
    <div class="upload">
      <a-upload-dragger
        :multiple="true"
        :fileList="fileList"
        :before-upload="beforeUpload"
        :action="uploadUrl"
        :headers="headers"
        @change="handlechange"
      >
        <div class="ant-upload-text">点击上传/ 拖拽到此区域</div>
      </a-upload-dragger>
      <div class="upload-tips">
        <div class="tips-left">请上传Excel文件，大小在5m以内</div>
        <span class="downLoad" @click="downloadTemplate"
          >点击下载导入模板.xlsx</span
        >
      </div>
    </div>
    <PageWrapper
      :loading="loading"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      @handleCreate="handleCreate"
    >
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
      </template>
      <!-- 编辑弹窗 -->
      <ListModal
        :visible="visible"
        :detail="modalData"
        @handleCancel="handleCancel"
      />
    </PageWrapper>
  </page-layout>
</template>

<script>
import { importTableColumn, defaultFilterConfig } from './constant';
import * as api from '@/api/basicData';
import { resolveBlob } from '@/utils/common/fileDownload';
import ListModal from './components/ListModal.vue';
import { getBaseUrl } from '@/utils/common/util.js';
import { getToken } from '@/utils/common/auth';
import { notification } from 'ant-design-vue';

export default {
  components: { ListModal },
  dicts: ['my_notify_rule'],
  props: {
    pageName: {
      type: String,
      default: function () {
        return '';
      },
    },
  },
  data() {
    return {
      loading: false,
      uploadUrl: '',
      fromPage: '',
      headers: {
        Authorization: '',
        responseType: 'blob',
      },
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: importTableColumn(),
      tableData: [],
      visible: false,
      modalData: null,
      fileList: [],
      disabled: false,
      multiple: true,
      accept: '.xls,.xlsx',
      importApi: '',
      maxNum: 1,
      maxSize: 5,
      actionBaseUrl: process.env.VUE_APP_USE_BUILD_TYPE
        ? getBaseUrl()
        : process.env.VUE_APP_BASE_TKB,
      action: `/base/taxableMarket/importData`,
      exportPageName: '',
      // headers: {
      //   Authorization: getToken(),
      // },
    };
  },
  mounted() {},
  created() {
    if (!this.pageName) {
      this.fromPage = this.$route.query.pageName;
      this.exportPageName =
        this.$route.query.exportPageName || this.$route.query.pageName;
    } else {
      this.fromPage = this.pageName;
    }

    this.headers.Authorization = getToken();
    this.loadData();
  },
  methods: {
    // 字典加载完成
    onDictReady() {
      this.filterOptions.config[1].props.options =
        this.dict.type.my_notify_rule;
    },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const [res, err] = await api.getRecordList(
        {
          limit: this.tablePage.pageSize,
          pageNum: this.tablePage.currentPage,
        },
        this.fromPage
      );
      this.loading = false;

      if (err) return;
      // 设置数据
      this.tablePage.total = res.data.total;
      this.tableData = res.data.records;
    },
    // 创建按钮点击事件
    handleCreate() {
      this.visible = true;
    },
    // 编辑按钮点击事件
    onClickEdit(row) {
      this.visible = true;
      this.modalData = row;
    },
    // 关闭弹窗
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
      this.modalData = null;
    },
    // 查看详情
    onClickDetail() {
      // this.$router.push('./second/detail');
    },
    // 上传限制
    beforeUpload(file) {
      const flieArr = file.name.split('.');
      const fileaccept = this.accept.split(',');
      const suffix = flieArr[flieArr.length - 1];
      // 获取类型结果
      const result = fileaccept.some(function (item) {
        return item.slice(1) === suffix;
      });
      this.uploadUrl = api.uploadFile(this.fromPage);
      console.log('this.uploadUrl22', this.uploadUrl);
      return new Promise((resolve, reject) => {
        if (this.fileList.length >= Number(this.maxNum)) {
          this.$message.warning(`最大上传数量为${this.maxNum}`);
          reject(new Error(`最大上传数量为${this.maxNum}`));
        } else if (!result) {
          this.$message.warning('上传格式不正确');
          reject(new Error('上传格式不正确'));
        } else if (file.size > this.maxSize * 1024 * 1024) {
          // 判断文件大小是否超标
          const errorMsg = `${file.name}超过${this.maxSize}M大小的限制!`;
          this.$message.warning(errorMsg);
          reject(new Error(errorMsg));
        } else {
          resolve();
        }
      });
    },
    handlechange(info) {
      const { file, fileList } = info;
      this.fileList = fileList;
      this.loadData();
      if (file.status === 'done') {
        if (file.response.code) {
          if (file.response.code === '10000') {
            notification.success({ message: '上传成功！' });
          } else {
            notification.error({ message: file.response.msg });
          }
        } else {
          const fileReader = new FileReader();
          fileReader.readAsDataURL(info.file.originFileObj);
          const mimeMap = {
            xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
          };
          resolveBlob(new Blob(file.response), mimeMap.xlsx, '错误', '.xlsx');
        }
      }
    },
    // 删除
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      this.fileList.splice(index, 1);
    },
    // 下载模板
    async downloadTemplate() {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
      };
      const [res] = await api.downloadTemplate({}, this.exportPageName);
      resolveBlob(res, mimeMap.xlsx, '导入模板', '.xlsx');
    },
  },
};
</script>
<style scoped>
/deep/.page-wrapper-container {
  margin: 0 !important;
}
/deep/.ant-upload.ant-upload-drag {
  width: 400px;
  height: 200px;
  margin-bottom: 20px;
}
/deep/.ant-upload-list {
  margin-bottom: 20px;
  width: 400px;
}
.downLoad {
  color: #1677ff;
  cursor: pointer;
}
.upload-tips {
  width: 400px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
</style>
