import moment from 'moment';
export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'positionName',
      title: '岗位名称',
      props: {
        placeholder: '请输入岗位名称',
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { positionName: '' },
};
export const tableColumns = [
  { field: '', title: '', type: 'checkbox', width: 70, fixed: 'left' },
  {
    title: '岗位名称',
    dataIndex: 'positionName',
    field: 'positionName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    field: 'createTime',
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    field: 'operation',
    slots: {
      default: 'operation',
    },
  },
];
export const replaceFields = {
  children: 'children',
  title: 'label',
  key: 'id',
};
export const formLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 14,
  },
};

export const rules = {
  positionName: [
    {
      required: true,
      whitespace: true,
      message: '名称不能为空',
      trigger: 'blur',
    },
  ],
  organizeId: [
    {
      required: true,
      message: '组织ID不能为空',
      trigger: 'blur',
    },
  ],
};

export const tableUserColumns = [
  { title: '序号', scopedSlots: { customRender: 'order' } },
  {
    title: '用户账号',
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '用户昵称',
    dataIndex: 'nickName',
    key: 'nickName',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    scopedSlots: { customRender: 'operation' },
  },
];
export const initFormData = (data) => {
  return {
    positionName: '',
    merchantId: '',
    remark: '',
    positionCode: '',
    organizeId: undefined,
    ...data,
  };
};
