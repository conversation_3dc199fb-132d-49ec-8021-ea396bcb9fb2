<template>
  <a-modal
    width="600px"
    :title="
      modelTitle === 'add' ? '新增' : modelTitle === 'see' ? '详情' : '编辑'
    "
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="ruleForm"
        :config="formConfig"
        :params="formValue"
        :preview="preview"
      >
        <!-- 年份插槽 -->
        <template #year>
          <BuseRangePicker
            type="year"
            :needShowSecondPicker="() => false"
            v-model="formValue.year"
            placeholder="请选择年份"
            format="YYYY"
            :disableDateFunc="disableDateFunc"
          />
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment';
import { initFormValue } from '../constant';
import { saveTalent, editTalentId, getListEnterprise } from '@/api/basicData';
import { institutionsMixin } from '../../mixins/institutionsMixin';
import FuzzySelect from '@/components/FuzzySelect/index.vue';

export default {
  props: [
    'visible',
    'detail',
    'preview',
    'isLook',
    'modelTitle',
    'carrierCategory',
    'dictData',
  ],
  mixins: [institutionsMixin],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.companyList = [];
          if (!this.detail) return;
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
            year: {
              endOpen: false,
              endValue: null,
              startOpen: true,
              startValue: this.detail.year ? moment(this.detail.year) : '',
            },
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      options: [],
      loading: false,
      businessName: [],
      formValue: initFormValue(),
      companyList: [],
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  computed: {
    formConfig() {
      const formConfig = [
        {
          field: 'year',
          title: '年份',
          element: 'slot',
          slotName: 'year',
          rules: [
            { required: true, message: '请选择年份' },
            {
              validator: (rule, value, callback) => {
                console.log(value, 'value');
                if (value && value.startValue) {
                  callback();
                } else {
                  callback('请选择年份');
                }
              },
              trigger: 'blur',
            },
          ],
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          element: (h, item, params) => {
            return (
              <FuzzySelect
                value={params?.enterpriseName}
                onChangeSelect={(val) => {
                  if (val) {
                    this.formValue = {
                      ...this.formValue,
                      enterpriseName: val.name,
                      unifiedCreditCode: val.unifiedCreditCode,
                      enterpriseId: val.unifiedCreditCode,
                    };
                  } else {
                    this.$refs.tableComponent.setForm({
                      ...this.formValue,
                      enterpriseName: '',
                      unifiedCreditCode: '',
                      enterpriseId: '',
                    });
                  }
                }}
                disabled={this.modelTitle == 'see'}
              />
            );
          },
          rules: [{ required: true, message: '请输入企业名称' }],
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          props: {
            placeholder: '请输入统一社会信用代码',
            disabled: true,
          },
        },
        {
          field: 'talentName',
          title: '姓名',
          element: 'a-input',
          props: {
            placeholder: '请输入姓名',
            maxLength: 30,
          },
          rules: [{ required: true, message: '请输入姓名' }],
        },
        {
          field: 'level',
          title: '入选级别',
          element: 'a-select',
          props: {
            placeholder: '请选择入选级别',
            options: this.dictData?.level_type || [],
          },
          rules: [{ required: true, message: '请选择入选级别' }],
        },
        {
          field: 'projectManType',
          title: '人才/项目类别',
          element: 'a-select',
          props: {
            placeholder: '请选择人才/项目类别',
            options: this.dictData?.project_man_type || [],
            showSearch: true,
            optionFilterProp: 'children',
          },
          rules: [{ required: true, message: '请选择人才/项目类别' }],
        },
        {
          field: 'projectName',
          title: '项目名称',
          element: 'a-input',
          props: {
            placeholder: '请输入项目名称',
            maxLength: 30,
          },
        },
        {
          field: 'money',
          title: '项目资金(万元)',
          props: {
            placeholder: '请输入项目资金',
            min: 0,
            max: 99999999999,
            step: 0.11,
          },
          element: 'a-input-number',
          rules: [
            {
              pattern: /^(0|[1-9]\d*)(\.\d{1,2})?$/,
              trigger: 'blur',
              message: '请输入大于或等于零的数字，最多保留两位小数。',
            },
          ],
        },
        {
          field: 'teamNumbers',
          title: '团队成员',
          element: 'a-input',
          props: {
            placeholder: '请输入团队成员',
            maxLength: 30,
          },
        },
        {
          field: 'remark',
          title: '备注',
          element: 'a-input',
          props: {
            placeholder: '请输入备注',
            maxLength: 30,
          },
        },
      ];
      return formConfig;
    },
  },
  mounted() {
    console.log(this.detail, 'detail');
    // this.getListEnterprise();
  },
  methods: {
    async getListEnterprise(val) {
      const [res, err] = await getListEnterprise({ enterpriseName: val });
      if (err) return;
      this.companyList = res.data.map((item) => {
        return {
          value: item.id,
          label: item.enterpriseName,
        };
      });
      console.log(this.companyList, 'res-----');
    },
    handleSearch(val) {
      this.getListEnterprise(val);
    },
    // 下拉字典加载完成
    onDictReady() {
      this.formConfig[0].props.options = this.stateSelect;
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        console.log(valid);
        if (valid) {
          if (this.modelTitle === 'add') {
            this.saveTalent();
          } else {
            this.editTalentId();
          }
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 调用父组件方法
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    // 新增
    async saveTalent() {
      const [, err] = await saveTalent({
        ...this.formValue,
        year: this.formValue.year?.startValue
          ? moment(this.formValue.year?.startValue).format('yyyy')
          : '',
      });
      if (err) return;
      this.$message.success('新增成功!');
      this.handleCancel();
      this.$emit('loadData');
    },
    // 编辑
    async editTalentId() {
      const [, err] = await editTalentId({
        ...this.formValue,
        year: this.formValue.year?.startValue
          ? moment(this.formValue.year?.startValue).format('yyyy')
          : '',
      });
      if (err) return;
      this.$message.success('编辑成功!');
      this.handleCancel();
      this.$emit('loadData');
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
