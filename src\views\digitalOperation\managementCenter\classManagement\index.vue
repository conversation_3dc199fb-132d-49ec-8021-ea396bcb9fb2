<template>
  <page-layout>
    <div class="class-management">
      <h4>太科办知识库</h4>
      <a-tree
        :tree-data="treeData"
        class="custom-tree"
        :defaultExpandAll="true"
        :autoExpandParent="true"
        @select="getNodeInfo"
      >
        <template slot="icon" slot-scope="item">
          <div class="node-container">
            <span class="node-title">{{ item.title }}</span>
            <a-icon
              class="icon"
              id="plus"
              type="plus-circle"
              @click="addCatalog(item, 'add')"
            ></a-icon>
            <a-icon
              class="icon"
              id="delete"
              type="delete"
              @click="deleteCatalog(item)"
            ></a-icon>
          </div>
        </template>
      </a-tree>
      <!--        弹框-->
      <a-modal
        :visible="visible"
        :title="visibleTitle"
        @ok="submitCatalog"
        @cancel="visible = false"
      >
        <a-form-model ref="formRef" :model="formState">
          <a-form-model-item
            prop="firstCategory"
            label="一级类别"
            :required="true"
            :rules="[{ required: true, message: '请输入一级类别' }]"
          >
            <a-input
              v-model.trim="formState.firstCategory"
              :disabled="disabled"
              placeholder="请输入一级类别"
              allow-clear
            />
          </a-form-model-item>
          <a-form-model-item
            prop="secondaryCategory"
            label="二级类别"
            :required="true"
            :rules="[{ required: true, message: '请输入二级类别' }]"
          >
            <a-input
              v-model.trim="formState.secondaryCategory"
              placeholder="请输入二级类别"
              allow-clear
            />
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </div>
  </page-layout>
</template>

<script>
// import { getUUID } from '@/utils/index';
import { Modal } from 'ant-design-vue';
import {
  categoryList,
  categoryAdd,
  categoryDelete,
} from '@/api/digitalOperation/managementCenter/classManagement/index.js';
export default {
  name: 'tagTree',

  data() {
    return {
      formState: {
        firstCategory: '', //一级类别
        secondaryCategory: '', //二级类别
      },
      visibleTitle: '', //新增弹框标题
      nodeDetail: {}, //节点详情
      operate: '', //节点操作类型
      visible: false,
      disabled: false, //一级类别是否可编辑
      // replaceFields: {
      //   children: 'tkbCategoryRes',
      //   title: 'categoryName',
      //   key: 'id',
      // },
      nodeLevel: '', //获取点击节点的等级
      parentNodeInfo: '', //获取点击节点的父节点信息
      treeData: [
        {
          title: '政策文件',
          key: '20231115180728587000003222511539',
          parentId: null,
          children: [
            {
              children: null,
              key: '20231115180732657000005222511533',
              parentId: '20231115180728587000003222511539',
              scopedSlots: { title: 'icon' },
              title: '主机',
            },
          ],
        },
      ],
    };
  },
  mounted() {
    // this.addAttrToTree(this.treeData);
    this.categoryList();
  },
  methods: {
    //类别查询
    async categoryList() {
      const [res, err] = await categoryList();
      console.log('类别查询', res, err);
      if (err) return;
      let data = res?.data;
      data = JSON.parse(
        JSON.stringify(data)
          .replace(/tkbCategoryRes/g, 'children')
          .replace(/categoryName/g, 'title')
          .replace(/id/g, 'key')
      );
      this.treeData = data;
      this.addAttrToTree(this.treeData);
    },
    //类别新增
    async categoryAdd(params) {
      const [res, err] = await categoryAdd(params);
      console.log(res, 1, err, 2);
      if (err) {
        if (err.code === '30000') {
          return this.$message.error(err.message);
        }
        return this.$message.error('新增失败');
      }
      this.$message.success('新增成功');
      this.categoryList();
    },
    //类别删除
    async categoryDelete(id) {
      const [, err] = await categoryDelete({ id });
      if (err) return this.$message.error('删除失败');
      this.$message.success('删除成功');
      this.categoryList();
    },
    //新增接口入参数据处理
    addParamsHandle() {
      if (this.nodeLevel == 1) {
        return [
          {
            categoryName: this.formState.firstCategory,
            categoryType: '0',
            parentId: '',
          },
          {
            categoryName: this.formState.secondaryCategory,
            categoryType: '1',
            parentId: '',
          },
        ];
      } else {
        return [
          {
            categoryName: this.formState.secondaryCategory,
            categoryType: '1',
            parentId: this.parentNodeInfo.key,
          },
        ];
      }
    },
    //处理数据
    addAttrToTree(jsonData) {
      console.log(jsonData, 'jsonData');
      if (jsonData) {
        for (let i = 0; i < jsonData.length; i++) {
          this.$set(jsonData[i], 'scopedSlots', { title: 'icon' });
          if (Object.prototype.hasOwnProperty.call(jsonData[i], 'key')) {
            this.addAttrToTree(jsonData[i].children);
          }
        }
      } else {
        jsonData = [];
      }
      return jsonData;
    },
    // 编辑类别
    addCatalog(item, type) {
      this.visible = true;
      this.operate = type;
      this.$refs.formRef && this.$refs.formRef.clearValidate();
      //获取点击节点的等级
      this.nodeLevel = this.getNodeLevel(item);
      //获取点击节点的父节点信息
      this.parentNodeInfo = this.findParentNodeInfo(this.treeData, item.key);

      if (type === 'add') {
        this.visibleTitle = '新增类别';
        //新增二级节点时
        if (this.nodeLevel == 2) {
          this.formState = {
            firstCategory: this.parentNodeInfo.title, //一级类别
            secondaryCategory: '', //二级类别
          };

          this.disabled = true;
        } else {
          this.formState = {
            firstCategory: '', //一级类别
            secondaryCategory: '', //二级类别
          };
          this.disabled = false;
        }
      }
    },
    // 删除类别
    deleteCatalog(item) {
      // 类别下有子类别，不可删除
      if (item.children && item.children.length !== 0) {
        this.$message.error(
          '该类别下有文件，不可直接删除，请先删除里面的文件！'
        );

        return;
      }

      let that = this;
      Modal.confirm({
        title: `确定删除类别名为‘${item.title}’的类别吗?`,
        icon: 'warning',
        onOk() {
          return new Promise((resolve, reject) => {
            setTimeout(Math.random() > 0.5 ? resolve : reject, 1000);
            // let node = that.findParentNode(that.treeData, item.key);
            // node.children = node.children.filter((a) => a.key !== item.key);
            // TODO调用保存接口保存删除后的类别结构
            that.categoryDelete(item.key);
          }).catch(() => console.log('删除失败'));
        },
        onCancel() {},
      });
    },
    // 新增提交
    submitCatalog() {
      this.$refs.formRef.validate((valid) => {
        console.log(valid);
        if (valid) {
          this.visible = false;
          let params = this.addParamsHandle();
          this.categoryAdd(params);
        }
      });
    },

    // 点击节点获取节点信息
    getNodeInfo(selectedKeys) {
      if (selectedKeys.length === 0) {
        return;
      }
      let clickedNode = this.findNodeByKey(this.treeData, selectedKeys[0]);
      let parent = this.findParentNode(this.treeData, selectedKeys[0]);
      let level = this.getNodeLevel(parent);
      // 该节点为叶子节点时传参给父组件更新页面标签信息
      if (level === 2) {
        let nodeInfo = {
          id: clickedNode.key,
          name: clickedNode.title,
          treeData: this.treeData,
        };
        this.$emit('getNodeInfo', nodeInfo);
      }
      this.nodeDetail = clickedNode;
    },

    // 获取当前节点信息
    findNodeByKey(nodes, key) {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].key === key) {
          return nodes[i];
        }
        if (nodes[i].children) {
          const foundNode = this.findNodeByKey(nodes[i].children, key);
          if (foundNode) {
            return foundNode;
          }
        }
      }
      return null;
    },
    // 获取父亲节点node信息
    findParentNode(nodes, key) {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].children) {
          if (nodes[i].children.some((child) => child.key === key)) {
            return nodes[i];
          }
          const foundNode = this.findParentNode(nodes[i].children, key);
          if (foundNode) {
            return foundNode;
          }
        }
      }
      return null;
    },
    // 获取父亲节点item信息
    findParentNodeInfo(nodes, key) {
      console.log(key);
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].children) {
          if (nodes[i].children.some((child) => child.key === key)) {
            console.log(nodes[i], 'nodes[i]');
            return nodes[i];
          }
        }
      }
      return null;
    },
    // 判断当前节点层级
    getNodeLevel(node, level = 0) {
      if (node && node.key) {
        return this.getNodeLevel(
          this.findParentNode(this.treeData, node.key),
          level + 1
        );
      } else {
        return level;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.class-management {
  overflow: auto;
  background-color: #fff;
  height: 90vh;
  padding: 16px 24px;
  h4 {
    font-size: 16px;
    color: #222;
    line-height: 32px;
    height: 32px;
    min-width: 120px;
    margin-bottom: 12px;
  }
  .custom-tree .icon {
    margin-left: 5px;
  }

  #plus {
    color: #409eff;
  }

  #edit {
    color: #67c23a;
  }

  #delete {
    color: #f56c6c;
  }
}
</style>
