import { request } from '@/utils/request/requestTkb';
/**
 *
 * @param {*} businessFlag 业务标识  1：工业企业消防安全  2:消防安全检查记录
 * @returns
 */
//工业企业消防安全和消防安全查看Excel List接口
export function getExcelData(query) {
  return request({
    url: `/industrial/enterprise/excel/list/${query.businessFlag}`,
    method: 'GET',
  });
}
//工业企业消防安全分页接口
export function pageList(data) {
  return request({
    url: '/industrial/enterprise/new/page/list',
    method: 'post',
    data,
  });
}
//工业企业消防查看历史记录接口
export function enterpriseHistory(id) {
  return request({
    url: `/industrial/enterprise/list/${id}`,
    method: 'GET',
  });
}

//工业企业消防查看详情接口
export function enterpriseInfo(id) {
  return request({
    url: `/industrial/enterprise/info/${id}`,
    method: 'GET',
  });
}

//工业企业消防新增接口
export function enterpriseAdd(data) {
  return request({
    url: '/industrial/enterprise/add',
    method: 'post',
    data,
  });
}
//工业企业消防查看详情中保存接口
export function addOrUpdate(data) {
  return request({
    url: '/industrial/enterprise/addOrUpdate',
    method: 'post',
    data,
  });
}
//工业企业消防删除接口

export function enterpriseDelete(id) {
  return request({
    url: `/industrial/enterprise/delete/${id}`,
    method: 'post',
  });
}

//工业企业消防列表维护

export function excelAddOrUpdate(data) {
  return request({
    url: `/industrial/enterprise/excel/addOrUpdate`,
    method: 'post',
    data,
  });
}
