<template>
  <a-card
    title="租户列表"
    :bordered="false"
    style="height: 100%"
    :bodyStyle="{
      padding: '16px',
      overflow: 'auto',
    }"
  >
    <a
      v-if="treeData && treeData.length"
      slot="extra"
      href="javascript:;"
      @click="() => (showAll = !showAll)"
    >
      <a-icon v-if="showAll" type="shrink" />
      <a-icon v-else type="arrows-alt" />
      {{ showAll ? '收起全部' : '展开全部' }}
    </a>
    <div v-if="treeData && treeData.length > 0">
      <a-input-search v-model="filterWord" allowClear />
      <a-spin :spinning="treeLoading">
        <a-tree
          v-if="showTree"
          ref="tree"
          show-icon
          :expandedKeys="expandedKeys"
          style="margin-left: -12px; margin-top: 8px"
          :selectedKeys="[merchantId]"
          :default-expand-all="showAll"
          :treeData="treeData"
          :replaceFields="replaceFields"
          :filterTreeNode="filterTreeNode"
          @select="handleTreeSelect"
          @expand="handleNodeExpand"
        >
          <!-- <a-icon slot="switcherIcon" type="down" /> -->
          <a-icon slot="bank" type="shop" />
          <a-icon slot="team" type="team" />
          <a-icon type="user" slot="user" />
        </a-tree>
      </a-spin>
    </div>
    <div v-else>
      <a-empty :image="simpleImage" />
    </div>
  </a-card>
</template>

<script>
import { Empty } from 'ant-design-vue';
import { getAllMerchantTree } from '@/api/system/merchant';
import { recursionDataSlide } from './utils';
import { handleTreeToArray } from '@/utils/common/tree';

export default {
  props: {
    merchantId: {
      type: String,
      default: '',
    },
    merchantName: {
      type: String,
      default: '',
    },
  },
  computed: {
    //树结构的菜单--》平铺
    treeToArrayList() {
      return handleTreeToArray(this.treeData);
    },
  },
  watch: {
    // 根据名称筛选部门树
    filterWord(val) {
      if (val) {
        this.filterTreeNode(this.$refs.tree);
      }
    },
    // 展开所有
    showAll(val) {
      if (val) {
        this.expandedKeys = this.treeToArrayList.map((item) => item.id);
      } else {
        this.expandedKeys = [];
      }
    },
  },
  data() {
    return {
      // 遮罩层
      loading: true,

      // 树相关参数
      treeLoading: false,
      filterWord: '',
      // 树相关数据
      showTree: false,
      treeData: [], //部门下拉列表
      expandedKeys: [], //部门下拉列表

      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE, //空图片
      replaceFields: {
        children: 'children',
        title: 'label',
        key: 'id',
      },
      showAll: false,
    };
  },
  mounted() {
    this.getAllMerchantTree();
  },
  methods: {
    /** 查询部门下拉树结构 */
    async getAllMerchantTree(refresh = true) {
      this.treeLoading = true;
      const [result, error] = await getAllMerchantTree();
      if (!error) {
        let treeData = result?.data || [];
        // 如果需要写默认值的话
        if (refresh) {
          const selectKey = treeData.length > 0 ? treeData[0]?.id : '';
          const merchantName = treeData.length > 0 ? treeData[0]?.label : '';

          this.$emit('update:merchantId', selectKey);
          this.$emit('update:merchantName', merchantName);
        }
        const data = recursionDataSlide(treeData);
        this.treeData = data;
        this.showTree = true;
        this.showAll = true;
      } // 获取树数据完成
      this.$emit('getTreeDone', {
        array: this.treeToArrayList || [],
        tree: this.treeData || [],
      });
      this.treeLoading = false;
    },
    // 树组件 => 筛选部门
    handleTreeSelect(selectedKeys, info) {
      if (Array.isArray(selectedKeys) && selectedKeys.length) {
        const merchantName = info.node.dataRef.label;
        const selectedKey = selectedKeys[0];
        // 更新选择数据
        this.$emit('update:merchantId', selectedKey);
        this.$emit('update:merchantName', merchantName);
        // 完成选择
        this.$emit('afterChooseMerchant');
      }
    },
    /**
     * 节点点击展开
     */
    async handleNodeExpand(data) {
      this.expandedKeys = data;
    },
    // 筛选节点
    filterTreeNode(node) {
      if (this.filterWord) {
        return node.title && node.title.indexOf(this.filterWord) !== -1;
      } else return false;
    },
    //刷新树数据
    refresh(isInit) {
      this.getAllMerchantTree(isInit);
    },
  },
};
</script>
