<!--
 * @Author: @17550589859 <EMAIL>
 * @Date: 2024-11-25 10:45:38
 * @LastEditors: @17550589859 <EMAIL>
 * @LastEditTime: 2025-04-08 17:23:29
 * @FilePath: \management-tk\src\views\login\login.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="login-container" ref="loginContainer">
    <div class="login-wrap">
      <div class="top-title">
        <img src="@/assets/images/logo_title.png" alt="" />
      </div>
      <LoginForm class="login-box" @success="afterLogin"></LoginForm>
    </div>
    <div v-if="copyright" class="copyright">
      {{ copyright }}
    </div>
  </div>
</template>

<script>
import { throttle } from 'xe-utils';
import LoginForm from '@/components/Login/LoginForm.vue';
import { initRoute } from '@/router/guards';

export default {
  name: 'LoginPage',
  components: { LoginForm },
  computed: {
    copyright() {
      return process.env.VUE_APP_COPY_RIGHT;
    },
    containerStyle() {
      return {
        '--parent-width': this.parentWidth + 'px',
        '--parent-scale': this.parentScale,
        '--window-height': this.windowHeight + 'px',
      };
    },
  },
  data() {
    return {
      parentWidth: 1920,
      parentScale: 1,
      windowHeight: window.innerHeight,
      resizeObserver: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      // 使用 ResizeObserver 代替 resize 事件
      this.resizeObserver = new ResizeObserver(this.handleResize);
      this.resizeObserver.observe(this.$refs.loginContainer.parentNode);
      this.handleResize();
    });
  },
  beforeDestroy() {
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }
  },
  methods: {
    async afterLogin() {
      try {
        await this.$store.dispatch('base/GetInfo');
        const redirect = this.$route.query.redirect || '';

        if (redirect && redirect.indexOf('http') > -1) {
          window.location.href = redirect;
        } else {
          await initRoute(this.$store);
          const hasFirstMenu = this.$store.state.setting.sysFirstMenuPath;
          this.$router.replace(hasFirstMenu || '/401');
        }
      } catch (error) {
        console.error('Login error:', error);
        this.$router.replace('/');
      }
    },
    // 优化 resize 处理
    handleResize: throttle(function (entries) {
      if (!this.$refs.loginContainer) return;

      requestAnimationFrame(() => {
        const container = this.$refs.loginContainer;
        const parentNode = container.parentNode;
        this.parentWidth = parentNode?.offsetWidth || 1920;
        this.parentScale = this.parentWidth / 1920;
        this.windowHeight = window.innerHeight;
      });
    }, 16), // 约60fps
  },
};
</script>

<style lang="less" scoped>
html,
body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.login-container {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-image: url('@/assets/images/page_bg.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #060b1f;
  will-change: transform; // 提示浏览器优化转换
  transform: translateZ(0); // 强制创建新的图层

  .login-wrap {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    transform: translateY(-10%);
    min-width: 593px;
    will-change: transform;

    .top-title {
      img {
        width: 300px;
        height: auto;
        will-change: transform;
      }
    }
  }

  .copyright {
    position: absolute;
    bottom: 16px;
    width: 100%;
    text-align: center;
  }
}

// 预加载背景图
@media (min-width: 1px) {
  body::after {
    position: absolute;
    width: 0;
    height: 0;
    overflow: hidden;
    z-index: -1;
    content: url('@/assets/images/page_bg.jpg');
  }
}
</style>
