<template>
  <PortraitCard :span="24" title="基本信息" :canExpand="true">
    <template #title>
      最近更新时间：{{ pictureInfo.updateTime || '-' }}
      <a-button
        type="primary"
        size="small"
        style="margin-left: 24px; margin-right: 8px"
        @click="updateHistory.visible = true"
        >更新记录</a-button
      >
    </template>
    <template #default="{ showAll }">
      <a-row v-if="!showAll">
        <a-col :span="8" class="detail">
          <div class="title">公司名称：</div>
          <div class="info">
            {{ pictureInfo.name || '-' }}
          </div>
        </a-col>
        <a-col :span="4" class="detail">
          <div class="title">法定代表人：</div>
          <div class="info">
            {{ pictureInfo.legalPerson || '-' }}
          </div>
        </a-col>
        <a-col :span="8" class="detail">
          <div class="title" style="width: 150px">统一社会信用代码：</div>
          <div class="info" style="width: 180px">
            {{ pictureInfo.unifiedCreditCode || '-' }}
          </div>
        </a-col>
        <a-col :span="4" class="detail">
          <div class="title" style="width: 50px">电话：</div>
          <div class="info" style="width: 120px">
            {{ pictureInfo.phone || '-' }}
          </div>
        </a-col>
        <a-col :span="24" class="detail" style="margin-top: 16px">
          <div class="title">
            <p>公司地址：</p>
          </div>
          <div class="info">
            <p>{{ pictureInfo.address || '-' }}</p>
          </div>
        </a-col>
      </a-row>
      <a-row v-else>
        <a-col
          :span="6"
          v-for="(item, index) in baseList"
          :key="index"
          class="detail"
          style="margin-bottom: 8px"
        >
          <div class="title">{{ item.title }}：</div>
          <div class="info">
            {{ pictureInfo[item.field] || '-' }}
          </div>
        </a-col>
      </a-row>
      <a-modal
        :visible="updateHistory.visible"
        :title="null"
        :closable="false"
        :footer="null"
        width="1000px"
        @cancel="updateHistory.visible = false"
      >
        <updateRecord
          :dataDict="dataDict"
          :rowData="{ unifiedCreditCode: pictureInfo.pictureId }"
        ></updateRecord>
      </a-modal>
    </template>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
import UpdateRecord from '@/views/basicData/enterpriseArchives/components/updateRecord.vue';
export default {
  components: {
    PortraitCard,
    UpdateRecord,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    dataDict: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      showAll: false,
      updateHistory: { visible: false },
      baseList: [
        { title: '企业名称', field: 'name' },
        { title: '统一社会信用代码', field: 'unifiedCreditCode' },
        { title: '企业地址', field: 'address' },
        { title: '登记状态', field: 'statusLabel' },
        { title: '所属园区', field: 'parkName' },
        { title: '位置经纬度', field: 'coordinates' },
        { title: '是否规上', field: 'regulateName' },
        { title: '企业性质', field: 'natureName' },
        { title: '标签', field: 'labelNames' },
        { title: '企业简介', field: 'synopsis' },
        { title: '法定代表人', field: 'legalPerson' },
        { title: '注册资本', field: 'registerCapital' },
        { title: '实缴资本', field: 'realCapital' },
        { title: '成立日期', field: 'registerDate' },
        { title: '所属省份', field: 'province' },
        { title: '所属城市', field: 'city' },
        { title: '所属区县', field: 'area' },
        { title: '电话', field: 'phone' },
        { title: '更多电话', field: 'otherPhone' },
        { title: '邮箱', field: 'email' },
        { title: '更多邮箱', field: 'otherEmail' },
        { title: '企业(机构)类型', field: 'enterpriseType' },
        { title: '纳税人识别号', field: 'identNum' },
        { title: '工商注册号', field: 'businessRegisNum' },
        { title: '组织机构代码', field: 'oibCode' },
        { title: '参保人数', field: 'insuredNum' },
        { title: '参保人员所属年度', field: 'reportInsuredNum' },
        { title: '营业期限', field: 'businessTerm' },
        { title: '国标行业门类', field: 'industryCategory' },
        { title: '国标行业大类', field: 'industryBigCategory' },
        { title: '国标行业中类', field: 'industryMiddleCategory' },
        { title: '国标行业小类', field: 'industrySmallCategory' },
        { title: '企业规模', field: 'enterpriseScale' },
        { title: '曾用名', field: 'oldEnterpriseName' },
        { title: '英文名', field: 'engName' },
        { title: '官网', field: 'officialWebsite' },
        { title: '通信地址', field: 'commAddr' },
        { title: '经营范围', field: 'rangeExperience' },
        { title: '登记机关', field: 'regisAuthority' },
        { title: '纳税人资质', field: 'taxpayerAptitude' },
        { title: '最新年报月份', field: 'reportTime' },
        { title: '企业经营状态', field: 'operateStatus' },
      ],
    };
  },
  methods: {
    toDetail(path) {
      if (path) {
        this.$router.push({
          path,
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;

  .title {
    width: 98px;
    text-align: right;
    color: #999999;
  }

  .info {
    width: calc(100% - 98px);
  }
}
</style>
