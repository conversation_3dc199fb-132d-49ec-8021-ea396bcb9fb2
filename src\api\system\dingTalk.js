import request from '@/utils/request';

// 活动预警群配置查询接口
export function dingTalkList(query) {
  return request({
    url: `${process.env.VUE_APP_ACT}/api/admin/activity/dingTalk`,
    method: 'GET',
    params: query,
  });
}

// 活动预警群配置添加
export function editDingTalk(query) {
  return request({
    url: `${process.env.VUE_APP_ACT}/api/admin/activity/adjustDingTalk`,
    method: 'POST',
    data: query,
  });
}
// 删除报警群
export function deleteDingTalk(query) {
  return request({
    url: `${process.env.VUE_APP_ACT}/api/admin/activity/dingTalk`,
    method: 'DELETE',
    params: query,
  });
}
