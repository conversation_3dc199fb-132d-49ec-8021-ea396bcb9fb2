<template>
  <a-col :span="span">
    <a-card
      :class="[
        'portrait-card',
        !diyHidden && canExpand && !showAll ? 'portrait-card-hidden' : '',
      ]"
    >
      <template #title>
        <div class="portrait-card-title">
          <slot name="leftTitle">
            <span>{{ title }}</span>
          </slot>

          <span style="font-size: 14px; color: #333333">
            <slot name="title" :showAll="showAll" />
            <a-button
              v-if="detailUrl"
              type="default"
              size="small"
              class="button-right"
              @click="toDetail(detailUrl)"
              >详情</a-button
            >
            <a-button
              v-if="canExpand && showExpandBtn"
              type="primary"
              size="small"
              :class="['button-show', showAll ? '' : 'button-show-up']"
              @click="showAll = !showAll"
              >{{ showAll ? '收起' : '展开' }}
            </a-button>
          </span>
        </div>
      </template>
      <div class="portrait-card-cnt">
        <slot :showAll="showAll" />
      </div>
    </a-card>
  </a-col>
</template>
<script>
export default {
  props: {
    span: {
      type: Number,
      default: 24,
    },
    title: {
      type: String,
      default: '-',
    },
    canExpand: {
      type: Boolean,
      default: false,
    },
    showExpandBtn: {
      type: Boolean,
      default: true,
    },
    diyHidden: {
      type: Boolean,
      default: false,
    },
    detailUrl: {
      type: String,
      default: '',
    },
    pictureId: {
      type: String,
      default: '',
    },
    pageName: {
      type: String,
      default: function () {
        return this.$route.query?.pageName || '';
      },
    },
  },
  data() {
    return {
      showAll: false,
    };
  },
  methods: {
    changeShowAll() {
      this.showAll = !this.showAll;
    },
    toDetail(path) {
      if (path) {
        this.$router.push({
          path,
          query: {
            pictureId:
              this.pageName == 'businessPortraits' ? this.pictureId : '',
          },
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.portrait-card {
  ::v-deep .ant-card-head {
    padding: 0;
    border: none;
  }
  ::v-deep .ant-card-head-wrapper {
    background: url('@/assets/images/portraits/1-3.png');
    background-size: 1210px 60px;
    background-position: 0 0;
    padding-left: 56px;
    padding-right: 24px;
    font-family: PingFang SC;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0em;
    color: #333333;
    height: 60px;
  }

  ::v-deep .ant-card-body {
    padding: 8px 24px 24px 24px;
  }

  &.portrait-card-hidden {
    ::v-deep & > .ant-card-body {
      height: 0;
      overflow: hidden;
      padding: 0;
    }
  }
}
.ant-card {
  background-color: #fff;
  border: none;
  border-radius: 8px;
  color: #333333;
  padding: 0;
  overflow: hidden;
}

.portrait-card-title {
  display: flex;
  justify-content: space-between;
  ::v-deep .ant-btn-sm {
    &.button-right {
      position: relative;
      padding-right: 20px;
      &::after {
        content: '';
        display: block;
        width: 4.6px;
        height: 7.3px;
        position: absolute;
        top: calc(50% - 3.6px);
        right: 9.4px;
        background-repeat: no-repeat;
        background-image: url('@/assets/images/portraits/1-005.png');
        background-size: 100% 100%;
      }
    }
    &.button-show {
      position: relative;
      padding-right: 20px;
      margin-left: 8px;
      &::after {
        content: '';
        display: block;
        width: 7.3px;
        height: 4.6px;
        position: absolute;
        top: calc(50% - 2.3px);
        right: 9.4px;
        background-repeat: no-repeat;
        background-image: url('@/assets/images/portraits/1-006.png');
        background-size: 100% 100%;
        transition: all 0.25s ease-in-out;
        transform: rotate(0deg);
      }
      &.button-show-up {
        &::after {
          transform: rotate(180deg);
        }
      }
    }
  }
}
</style>
