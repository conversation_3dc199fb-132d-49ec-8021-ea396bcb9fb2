<template>
  <PortraitCard :span="24" title="重大项目" :canExpand="true" v-bind="$attrs">
    <a-row>
      <a-col :span="6" class="detail">
        <span class="title" style="width: 180px"> 历年重大项目数量合计： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.projectCount">暂无数据</span>
          <template v-else
            ><span class="num">{{ pictureInfo.projectCount || '-' }}</span
            ><span class="unit">个</span></template
          >
        </span>
      </a-col>
      <a-col :span="6" class="detail">
        <span class="title"> 总占地面积：</span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.floorSpace">暂无数据</span>
          <template v-else>
            <span class="num">{{ pictureInfo.floorSpace || '-' }}</span
            ><span class="unit">亩</span>
          </template>
        </span>
      </a-col>
      <a-col :span="6" class="detail">
        <span class="title" style="width: 91px"> 计划投资：</span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.investment">暂无数据</span>
          <template v-else>
            <span class="num">{{ pictureInfo.investment || '-' }}</span
            ><span class="unit">万元</span>
          </template>
        </span>
      </a-col>
      <a-col :span="6" class="detail">
        <span class="title" style="width: 91px"> 已完成投资：</span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.completeInvestment"
            >暂无数据</span
          >
          <template v-else>
            <span class="num">{{ pictureInfo.completeInvestment || '-' }}</span
            ><span class="unit">万元</span>
          </template>
        </span>
      </a-col>
    </a-row>
    <a-row :gutter="12" style="margin-top: 12px">
      <a-col :span="6">
        <CustomizePieChart
          chartTitle="历年项目级别统计"
          :options="charts1Options"
          height="220px"
        />
      </a-col>
      <a-col :span="6">
        <ChartsCard
          chartTitle="近4年重大项目数量统计"
          :options="charts2Options"
          height="220px"
        />
      </a-col>
      <a-col :span="6">
        <ChartsCard
          chartTitle="近4年重大项目占地面积统计"
          :options="charts3Options"
          height="220px"
        />
      </a-col>
      <a-col :span="6">
        <ChartsCard
          chartTitle="近4年重大项目投资情况"
          :options="charts4Options"
          height="220px"
        />
      </a-col>
    </a-row>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
import ChartsCard from '../ChartsCard.vue';
import CustomizePieChart from '../CustomizePieChart.vue';
import { usePieCharts, useLineCharts } from '../chartHooks';
// import ScrollTable from '../ScrollTable.vue';
export default {
  components: {
    PortraitCard,
    ChartsCard,
    CustomizePieChart,
    // ScrollTable,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    charts1: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts2: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts3: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts4: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [[], []],
      }),
    },
  },
  data() {
    return {
      charts1Options: {},
      charts2Options: {},
      charts3Options: {},
      charts4Options: {},
    };
  },
  watch: {
    charts1: {
      deep: true,
      handler() {
        this.handleInitCharts1();
      },
    },
    charts2: {
      deep: true,
      handler() {
        this.handleInitCharts2();
      },
    },
    charts3: {
      deep: true,
      handler() {
        this.handleInitCharts3();
      },
    },
    charts4: {
      deep: true,
      handler() {
        this.handleInitCharts4();
      },
    },
  },
  methods: {
    handleInitCharts1() {
      if (!this.charts1?.data) return;
      const { data = [] } = this.charts1;
      this.charts1Options = usePieCharts({ data, direction: 'bottom' });
    },
    handleInitCharts2() {
      const unit = '个';
      if (!this.charts2?.data) return;
      const { xAxis = [], data = [] } = this.charts2;
      this.charts2Options = useLineCharts({
        xAxis,
        unit,
        series: [
          { name: '近4年重大项目数量统计', data: data || [], color: '#38d3b6' },
        ],
      });
    },
    handleInitCharts3() {
      const unit = '亩';
      if (!this.charts1?.data) return;
      const { xAxis = [], data = [] } = this.charts3;
      this.charts3Options = useLineCharts({
        xAxis,
        unit,
        series: [
          {
            name: '近4年重大项目占地面积统计',
            data: data || [],
            color: '#ffab04',
          },
        ],
      });
    },
    handleInitCharts4() {
      const unit = '万元';
      if (!this.charts2?.data) return;
      const { xAxis = [], data = [] } = this.charts4;
      this.charts4Options = useLineCharts({
        xAxis,
        unit,
        series: [
          { name: '计划投资', data: data[0] || [], color: '#5184f8' },
          { name: '已完成投资', data: data[1] || [], color: '#56d8a0' },
        ],
      });
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .title {
    text-align: right;
    color: #999999;
    width: 148px;
    line-height: 26px;
  }

  .info {
    text-align: left;
    .num {
      font-family: D-DIN;
      font-size: 24px;
      font-weight: bold;
      line-height: 24px;
      color: #333333;
      z-index: 0;
    }

    .unit {
      margin-left: 4px;
      font-family: PingFang SC;
      font-size: 12px;
      color: #333333;
    }
  }
}
</style>
