<template>
  <page-layout>
    <a-row type="flex" justify="space-between" :gutter="[16, 0]">
      <a-col :span="4">
        <OrganizationTree
          ref="orgaizationTree"
          :organizeId.sync="organizeId"
          :organizeName.sync="organizeName"
          @getTreeDone="getTreeDone"
          @afterChooseMerchant="afterChooseMerchant"
        />
      </a-col>
      <a-col :span="20" :style="{ margin: '-16px 0 -60px 0' }">
        <PageWrapper
          :title="`组织列表${organizeName ? '- ' + organizeName : ''}`"
          createText=""
          :loading="loading"
          :tablePage="tablePage"
          :filterOptions="filterOptions"
          :tableColumn="tableColumns"
          :tableData="tableData"
          @loadData="queryList"
          @handleReset="handleReset"
          @handleCreate="handleAdd"
        >
          <template #defaultHeader>
            <a-button
              v-hasPermi="['system:organize:export']"
              style="margin-right: 8px"
              @click="openExproVisible = true"
            >
              导入
            </a-button>
            <a-button
              v-hasPermi="['system:organize:add']"
              type="primary"
              :loading="loading"
              @click="handleAdd"
            >
              新增
            </a-button>
          </template>
          <template #status="{ row }">
            {{ getStatusLabelSecond(row.status) }}
          </template>
          <template #operation="{ row }">
            <a-button
              v-if="row.parentId !== '0'"
              icon="edit"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleEdit(row)"
            >
              编辑
            </a-button>
            <a-button
              v-if="row.parentId !== '0'"
              icon="delete"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleDelete(row)"
            >
              删除
            </a-button>
          </template>
        </PageWrapper>
      </a-col>
    </a-row>
    <EditModal
      :visible.sync="editModalVisible"
      :organizeId="editOrganizeId"
      :organizationTree="organizationTree"
      :parentId="organizeId"
      @queryList="queryList"
      @refreshList="refresh"
    />
    <ExportExl
      :visible.sync="openExproVisible"
      :organizeId="organizeId"
      :organizeName="organizeName"
      @refresh="refresh"
    />
  </page-layout>
</template>
<script>
import { getList, deleteOrganize } from '@/api/system/organization';
import EditModal from './components/EditModal.vue';
import ExportExl from './components/ExportExl.vue';
import OrganizationTree from '@/components/SystemTreeSelect/OrganizationTree.vue';
import { Modal } from 'ant-design-vue';
import { getStatusLabelSecond } from '@/views/system/constant/system';
import { filterOptions, tableColumns } from './constant';

export default {
  components: { EditModal, ExportExl, OrganizationTree },
  data() {
    return {
      // 菜单树
      // 菜单树选项
      organizationNameFilter: '',
      showAll: false, //展示所有
      loading: false, //数据加载
      showTree: false, //展示树
      treeLoading: false, //树加载
      openExproVisible: false, //导入
      organizationTree: [], //组织树
      expandedKeys: [], //展开的key
      // 筛选条件
      filterOptions,
      // 表格数据
      tableColumns,
      tableData: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      // 是否显示弹出层
      editModalVisible: false,
      editOrganizeId: '', //选择的key
      organizeId: '', //组织架构id
      organizeName: '', //组织架构名称
    };
  },
  computed: {
    merchantId() {
      return this.$store.state?.base?.merchant?.merchantId;
    },
  },
  methods: {
    getStatusLabelSecond,
    /**
     * 获取树完成之后保存数据
     */
    getTreeDone(data) {
      this.organizationTree = data?.tree || [];
      this.queryList();
    },
    /**
     * 查询应用详情
     */
    async queryList() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [result, error] = await getList({
        ...params,
        parentId: this.organizeId,
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
      });
      this.loading = false;
      if (error) return;
      this.tableData = result?.data || [];
      this.tablePage.total = result.count;
    },
    // 重置
    async handleReset() {
      this.organizeId = this.organizationTree[0]?.id;
      this.organizeName = this.organizationTree[0]?.label;
      this.afterChooseMerchant();
    },

    /**
     * 选择组织后
     */
    async afterChooseMerchant() {
      this.filterOptions.params = {
        organizeName: '',
        status: '',
      };
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      this.queryList();
    },
    /**
     * 菜单新增删除后的刷新操作
     */
    refresh() {
      this.filterOptions.params = {
        organizeName: '',
        status: '',
      };
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      this.$refs?.orgaizationTree?.refresh();
    },
    /**
     * 新增按钮操作
     */
    handleAdd() {
      this.editOrganizeId = '';
      this.editModalVisible = true;
    },
    /**
     * 修改按钮操作
     */
    handleEdit(row) {
      this.editOrganizeId = row.organizeId;
      this.editModalVisible = true;
    },
    /**
     * 删除按钮操作
     */
    handleDelete(row) {
      if (this.loading) return;
      Modal.confirm({
        title: '警告',
        content:
          '删除组织后，将同步删除下级组织以及组织下的岗位和成员，请谨慎操作！',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.loading = true;
          const [, error] = await deleteOrganize(row.organizeId);
          this.loading = false;
          if (error) return;
          this.$message.success('删除成功');
          this.refresh();
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.table-title {
  font-weight: 500;
  font-size: 16px;
}
.tree-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 48px;
}
</style>
