<template>
  <a-modal
    width="600px"
    :title="
      modelTitle === 'add' ? '新增' : modelTitle === 'see' ? '详情' : '编辑'
    "
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="ruleForm"
        :config="formConfig"
        :params="formValue"
        :preview="preview"
      >
        <template #sale>
          <a-input
            placeholder="请输入销售收入"
            v-model="formValue.area"
            class="model-unit"
          />
          <span class="unit">元</span>
        </template>
        <template #saleSame>
          <a-input
            placeholder="请输入销售收入"
            v-model="formValue.area"
            class="model-unit"
          />
          <span class="unit">元</span>
        </template>
        <template #wareHouse>
          <a-input
            placeholder="请输入销售收入"
            v-model="formValue.area"
            class="model-unit"
          />
          <span class="unit">元</span>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import { initFormValue } from '../constant';
export default {
  props: ['visible', 'detail', 'preview', 'isLook', 'modelTitle'],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
      formConfig: [
        {
          field: 'date',
          title: '日期',
          element: 'a-month-picker',
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'code',
          title: '统一社会信用代码',
          element: 'a-input',
          rules: [
            { required: true, validator: this.checkCode, trigger: 'change' },
          ],
        },
        {
          field: 'name',
          title: '企业名称',
          element: 'a-select',
          props: {
            showSearch: true,
            filterOption: this.filterOption,
            options: [
              { value: '1', label: '应税收入' },
              { value: '2', label: '工业产值' },
            ],
          },
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'sale',
          title: '销售收入（本期数）',
          element: 'slot',
          slotName: 'sale',
        },
        {
          field: 'saleSame',
          title: '销售收入（同期数）',
          element: 'slot',
          slotName: 'saleSame',
        },
        {
          field: 'wareHouse',
          title: '净入库金额',
          element: 'slot',
          slotName: 'wareHouse',
        },
      ],
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      console.log(2222);
      this.formConfig[0].props.options = this.stateSelect;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        console.log(valid);
        if (valid) {
          console.log(this.formValue);
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 信用代码规则校验
    async checkCode(rule, value) {
      if (!value) {
        return Promise.reject(`请输入内容`);
      }
      if (!this.validateInput(value)) {
        return Promise.reject('请输入18位数字和字母组合');
      } else {
        if (value.length > 18) {
          return Promise.reject('不能多于18位数字');
        } else if (value.length < 18) {
          return Promise.reject('不能少于18位数字');
        } else {
          return Promise.resolve();
        }
      }
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
