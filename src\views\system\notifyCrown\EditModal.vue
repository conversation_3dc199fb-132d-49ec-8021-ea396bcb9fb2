<template>
  <a-modal
    :title="title"
    :visible="visible"
    :loading="loading"
    okText="确定"
    cancelText="取消"
    :maskClosable="false"
    width="700px"
    @ok="submitForm"
    @cancel="closeModal"
  >
    <a-form-model ref="form" :model="form" :rules="rules" v-bind="formLayout">
      <a-form-model-item label="报警群名" prop="groupName">
        <a-input v-model="form.groupName" placeholder="请输入报警群名" />
      </a-form-model-item>
      <a-form-model-item label="钉钉验证信息" prop="secret">
        <a-input
          type="textarea"
          v-model="form.secret"
          placeholder="请输入钉钉验证信息"
        />
      </a-form-model-item>
      <a-form-model-item label="登录TOKEN" prop="accessToken">
        <a-input
          type="textarea"
          v-model="form.accessToken"
          placeholder="请输入登录TOKEN"
        />
      </a-form-model-item>
      <a-form-model-item label="手机号" prop="phones">
        <a-input
          v-model="form.phones"
          placeholder="请输入手机号"
          :maxLength="11"
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import { editDingTalk } from '@/api/system/dingTalk';
import { initForm, formRules } from './constant.js';

export default {
  props: {
    visible: Boolean,
    contractForm: Object,
  },
  data() {
    return {
      loading: false,
      // 表单参数
      form: initForm(),
      // 表单校验
      rules: formRules,
      formLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
      },
    };
  },
  computed: {
    title() {
      return `${
        this.form.dingNotifyId ||
        (this.loading && this.contractForm.dingNotifyId)
          ? '编辑'
          : '新增'
      }报警群配置`;
    },
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.contractForm?.dingNotifyId) {
          this.form = {
            ...initForm(),
            ...this.contractForm,
          };
        } else {
          this.form = initForm();
        }
      } else {
        this.$refs?.form?.resetFields();
      }
    },
  },

  methods: {
    /** 提交按钮 */
    submitForm: function () {
      if (this.loading) return;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const [, error] = await editDingTalk(this.form);
          this.loading = false;
          if (error) return;
          this.$message.success(`${this.title}成功`);
          this.$emit('ok');
          this.closeModal();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .ant-form-item {
  margin-bottom: 24px;
}
</style>
