<template>
  <div>
    <a-modal
      width="600px"
      :visible="visible"
      title="提交结果"
      @cancel="cancelHandler"
      @ok="okHandler"
    >
      <a-form :form="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="进度">
          <a-input
            suffix="%"
            type="number"
            v-decorator="[
              'schedule',
              {
                rules: [
                  { required: true, message: '请输入进度' },
                  { validator: checkNumber, trigger: '请输入正整数' },
                ],
              },
            ]"
            placeholder="请输入进度"
          ></a-input>
        </a-form-item>
        <a-form-item label="办理情况">
          <a-textarea
            placeholder="请输入办理情况"
            :auto-size="{ minRows: 3, maxRows: 5 }"
            v-decorator="[
              'transactionCase',
              {
                rules: [{ required: true, message: '请输入办理情况' }],
              },
            ]"
          />
        </a-form-item>
        <a-form-item label="附件">
          <Upload
            v-model="attachment"
            :accept="'.doc'"
            :showUploadList="true"
            :maxCount="1"
            :fileListTemp="fileListTemp"
            @setNewsImg="setNewsImg"
            :maxSize="4"
          />
          <span class="upload-mark-text"
            >支持doc格式附件，附件大小不能超过4M</span
          >
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import Upload from '@/components/Uploads/index.vue';
import { operation } from '@/api/digitalOperation/taskManagement/todoList.js';
import { checkNumber } from '@/utils/index';

export default {
  name: 'submitResultModal',
  components: { Upload },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    taskDetail: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      checkNumber,
      fileList: [],
      attachment: '',
      fileListTemp: [],
      form: this.$form.createForm(this, { name: 'submitResult' }),
    };
  },

  mounted() {},
  watch: {
    visible() {
      this.form.resetFields();
      this.fileListTemp = [];
      this.attachment = '';
    },
  },
  methods: {
    async operation(value) {
      let p = {
        ...value,
        attachment: this.attachment,
        taskId: this.taskDetail.id,
        departmentName: this.taskDetail.departmentName,
        tag: '2', //操作按钮标识: 1:发起任务  2:提交结果  3:确认办结  4:归档  5:终止
      };
      console.log('p', p);
      const [, err] = await operation(p);
      if (err) return;
      this.$message.success('提交结果成功');
      this.$emit('refresh');
      this.$emit('close');
    },

    setNewsImg(val) {
      console.log('val', val);
      this.attachment = val.fileName;
    },
    cancelHandler() {
      this.$emit('close');
    },
    handleChange() {},
    okHandler() {
      this.form.validateFields((err, values) => {
        if (err) return;
        console.log(
          '🚀 ~ file: termination.vue:55 ~ this.form.validateFields ~ reason:',
          values
        );
        //提交结果请求
        this.operation(values);
        //this.$emit('close');
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
