<template>
  <div class="building-safety-wrapper">
    <a-tabs default-active-key="1" class="tab-box">
      <a-tab-pane key="1" tab="小微工程审批">
        <EngineeringSupervision />
      </a-tab-pane>
      <a-tab-pane key="2" tab="建筑工地项目统计" class="mr0">
        <ProjectStatistics />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import EngineeringSupervision from '../../../smallMicroEngineering/engineeringSupervision.vue';
import ProjectStatistics from './projectStatistics';
export default {
  name: 'BuildingSafety',
  components: {
    ProjectStatistics,
    EngineeringSupervision,
  },

  data() {
    return {};
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="less" scoped>
.building-safety-wrapper {
  margin: 24px;
  /deep/.ant-tabs-bar {
    margin-bottom: 0;
  }
  .mr0 {
    margin: 0;
  }
  /deep/.bd3001-page-wrapper-container {
    margin: 0;
  }
}
</style>
