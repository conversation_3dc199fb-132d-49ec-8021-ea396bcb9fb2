<template>
  <PortraitCard :span="24" title="企业称号" v-bind="$attrs" :canExpand="true">
    <div v-if="pictureInfo.titles && pictureInfo.titles.length">
      <div
        v-for="(item, index) in pictureInfo.titles"
        :class="[
          'titles-item',
          item.lightUp ? 'titles-item-' + ((index % 4) + 1) : '',
        ]"
        :key="index"
      >
        {{ item.content }}
      </div>
    </div>
    <a-row v-else>
      <a-col :span="6" class="detail">
        <div class="title" style="padding-right: 1em">暂无</div>
      </a-col>
    </a-row>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
export default {
  components: {
    PortraitCard,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style lang="less" scoped>
.titles-item {
  padding: 8px 16px 8px 48px;
  border-radius: 8px;
  background-color: #f9f9f9;
  background-image: url('@/assets/images/portraits/1-205.png');
  background-repeat: no-repeat;
  background-size: 24px 24px;
  background-position: 16px 8px;
  margin-right: 12px;
  margin-bottom: 12px;
  float: left;
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.02em;

  color: #999999;
  &-1 {
    color: #5ecb82;
    background-color: rgba(21, 212, 78, 0.08);
    background-image: url('@/assets/images/portraits/1-201.png');
  }
  &-2 {
    color: #03adfd;
    background-color: rgba(5, 184, 255, 0.08);
    background-image: url('@/assets/images/portraits/1-202.png');
  }
  &-3 {
    color: #fdb203;
    background-color: rgba(253, 178, 3, 0.12);
    background-image: url('@/assets/images/portraits/1-203.png');
  }
  &-4 {
    color: #6f36ff;
    background-color: rgba(111, 54, 255, 0.08);
    background-image: url('@/assets/images/portraits/1-204.png');
  }
}
</style>
