import { request } from '@/utils/request/requestTkb';
/**
 * 分页资源列表
 */
export function getResList(data) {
  return request({
    url: '/gov/resPool/getResList',
    method: 'post',
    data,
  });
}
/**
 * 发布资源
 */
export function articleSave(data) {
  return request({
    url: '/gov/resPool/articleSave',
    method: 'post',
    data,
  });
}

/**
 * 上架资源
 */
export function onShelf(data) {
  return request({
    url: '/gov/resPool/onShelf',
    method: 'post',
    params: data,
  });
}

/**
 * 下架资源
 */
export function offShelf(data) {
  return request({
    url: '/gov/resPool/offShelf',
    method: 'post',
    params: data,
  });
}

/**
 * 查看资源详情
 */
export function getResDetail(data) {
  return request({
    url: '/gov/resPool/getResDetail',
    method: 'post',
    params: data,
  });
}

/**
 * 删除资源
 */
export function removeResource(data) {
  return request({
    url: '/gov/resPool/removeResource',
    method: 'post',
    params: data,
  });
}

/**
 * 推送资源
 */
export function push(data) {
  return request({
    url: '/gov/resPool/push',
    method: 'post',
    data,
  });
}

/**
 * 查询全部资源类型
 */
export function getAllResType(data) {
  return request({
    url: '/gov/resoureceType/getAllResType',
    method: 'get',
    data,
  });
}

export function pushPublicBatch(data) {
  return request({
    url: '/gov/resPool/pushPublicBatch',
    method: 'post',
    data,
  });
}

export function batchPush(data) {
  return request({
    url: '/gov/resPool/batchPushRes',
    method: 'post',
    data,
  });
}
export function pushResByLabel(data) {
  return request({
    url: '/gov/resPool/pushResByLabel',
    method: 'post',
    data,
  });
}
