function getTargetTableColumns() {
  return [
    {
      title: '目标年份',
      field: 'year',
    },
    {
      title: '一级指标',
      field: 'oneTargetName',
    },
    {
      title: '二级指标',
      field: 'twoTargetName',
    },
    {
      title: '总计划完成目标',
      field: 'achieveTarget',
    },
    {
      title: '总完成情况',
      field: 'performanceUnit',
    },
    {
      title: '完成率',
      field: 'finishingRate',
      formatter({ cellValue }) {
        return cellValue ? `${cellValue}%` : `0%`;
      },
    },
    {
      title: '目标分配情况',
      field: 'isAllocation',
      formatter({ cellValue }) {
        return cellValue === '0' ? '未分配' : '已分配';
      },
    },
  ];
}

function getTargetCompletionTableColumns() {
  return [
    {
      title: '目标年份',
      field: 'year',
    },
    {
      title: '一级指标',
      field: 'oneTargetName',
    },
    {
      title: '二级指标',
      field: 'twoTargetName',
    },
    {
      title: '总计划完成目标',
      field: 'achieveTarget',
    },
    {
      title: '分配园区',
      field: 'parkName',
    },
    {
      title: '目标分配量',
      field: 'assignedAmount',
      formatter: ({ row }) => {
        return `${row.assignedAmount}${row.targetUnit}`;
      },
    },
    {
      title: '当前完成情况',
      field: 'completeSchedule',
      formatter: ({ row }) => {
        return row.completeSchedule !== '/'
          ? `${row.completeSchedule}${row.targetUnit}`
          : row.completeSchedule;
      },
    },
    {
      title: '完成进度',
      field: 'finishingRateUnit',
    },
  ];
}
function getCompletionStatusList() {
  return [
    {
      label: '全部',
      value: 'all',
    },
    {
      label: '>=80%',
      value: '100',
    },
    {
      label: '60%~80%',
      value: '80',
    },
    {
      label: '40%~60%',
      value: '60',
    },
    {
      label: '20%~40%',
      value: '40',
    },
    {
      label: '<20%',
      value: '20',
    },
  ];
}
export {
  getTargetTableColumns,
  getCompletionStatusList,
  getTargetCompletionTableColumns,
};
