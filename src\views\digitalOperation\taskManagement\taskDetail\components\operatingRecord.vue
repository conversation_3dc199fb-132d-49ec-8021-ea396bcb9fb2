<template>
  <BuseCrud
    ref="crud"
    title="操作记录"
    :loading="loading"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
  >
  </BuseCrud>
</template>

<script>
import { getTableColumn } from './taskDetail';
import { getDicts } from '@/api/system/dict/data';
export default {
  name: 'OperationRecord',
  props: {
    taskOperatingRecordResList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      tableData: [],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      operationStatus: [],
    };
  },
  beforeCreate() {},
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        menu: false,
      };
    },
  },
  created() {
    this.tableColumn = getTableColumn.call(this);
    this.getOperationStatus();
  },
  mounted() {},
  watch: {
    taskOperatingRecordResList(val) {
      this.tableData = val;
    },
  },
  methods: {
    async getOperationStatus() {
      const [res, err] = await getDicts(['operation_status']);
      if (err) return;
      this.operationStatus = res.data.map((item) => {
        return {
          value: item.dictValue,
          label: item.dictLabel,
        };
      });
      this.tableData = this.taskOperatingRecordResList;
    },
    loadData() {},
  },
};
</script>

<style lang="less" scoped></style>
