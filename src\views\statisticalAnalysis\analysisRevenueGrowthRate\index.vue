<template>
  <page-layout>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tableColumn="tableColumn"
      :tablePage="tablePage"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
      @handleReset="handleReset"
    >
      <template slot="defaultTitle">
        <span></span>
      </template>
      <template slot="defaultHeader">
        <div class="flex-row-10">
          <a-button :loading="exportLoading" @click="exportData">导出</a-button>
          <a-button type="link" @click="openDrawer">高级筛选</a-button>
        </div>
      </template>
      <template #growRate>
        <a-input-group compact>
          <a-row>
            <a-col :span="12">
              <a-select
                style="width: 100%"
                :allowClear="true"
                v-model="filterParams.symbol"
                placeholder="请选择"
                @change="changeSymbol"
              >
                <a-select-option
                  :value="item.value"
                  v-for="(item, index) in symbolOptions"
                  :key="index"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="12">
              <a-input-number
                :allowClear="true"
                style="width: 100%"
                v-model="filterParams.growRate"
                :formatter="(value) => `${value}%`"
                :parser="(value) => value.replace('%', '')"
              />
            </a-col>
          </a-row>
        </a-input-group>
      </template>
    </BuseCrud>
    <a-drawer
      title="高级筛选"
      placement="right"
      :closable="true"
      :visible="visible"
      @close="() => (visible = false)"
      width="800px"
    >
      <AutoFilters
        :gridCol="{ xs: 24, sm: 24, md: 24, lg: 24, xl: 24, xxl: 24 }"
        :formCol="{ labelCol: 12, wrapperCol: 12 }"
        class="filter-box"
        :config="autoFilterOptions.config"
        :params="autoFilterOptions.params"
        @handleReset="handleReset"
        @loadData="loadData"
      >
      </AutoFilters>
    </a-drawer>
    <a-modal
      :visible="modalVisible"
      :title="null"
      :closable="false"
      width="100%"
      wrapClassName="full-modal"
      :footer="null"
      @cancel="visible = false"
    >
      <portraits-detail
        v-if="modalVisible"
        pageName="businessPortraits"
        :pictureId="pictureId"
        @close="() => (modalVisible = false)"
      ></portraits-detail>
    </a-modal>
  </page-layout>
</template>

<script>
import portraitsDetail from '@/views/portraits/components/portraitsDetail';
import {
  filterOption,
  initParams,
  disabledStartDate,
  disabledEndDate,
} from '@/utils';
import {
  queryBusinessPictureOperate,
  getGrowRateAnalysisPage,
  downloadGrowRateAnalysis,
} from '@/api/statisticalAnalysis/index.js';
import { getParkList, getLabelList } from '@/api/basicData/index.js';
import moment from 'moment';
import { resolveBlob } from '@/utils/common/fileDownload';
import { isIntervalGreaterThanOneYear } from '@/utils';
export default {
  props: {},
  components: { portraitsDetail },
  dicts: [
    'enterprise_regulate_status',
    'enterprise_nature_status',
    'enterprise_register_status',
    'enterprise_attr',
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      filterParams: {
        startTime: moment().subtract(12, 'month').format('YYYY-MM'),
        endTime: moment().subtract(1, 'month').format('YYYY-MM'),
        parkIds: [],
        regulate: undefined,
        nature: undefined,
        corporateLabel: [],
        symbol: undefined,
        growRate: '',
        enterpriseAttr: [],
      },
      operationType: '',
      dataDict: {},
      visible: false,
      autoFilterParams: {
        name: undefined,
        unifiedCreditCode: undefined,
        address: undefined,
        status: undefined,
      },
      exportLoading: false,
      dateList: [],
      symbolOptions: [
        {
          label: '>',
          value: '01',
        },
        {
          label: '≥',
          value: '02',
        },
        {
          label: '=',
          value: '03',
        },
        {
          label: '<',
          value: '04',
        },
        {
          label: '≤',
          value: '05',
        },
      ],
      pictureId: '',
      modalVisible: false,
      parkList: [],
      labelList: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'parkIds',
            element: 'a-select',
            title: '所属园区',
            defaultValue: [],
            props: {
              placeholder: '请选择所属园区',
              options: this.parkList,
              filterOption: filterOption,
              mode: 'multiple',
              maxTagCount: 1,
              maxTagTextLength: 4,
            },
          },
          {
            field: 'regulate',
            element: 'a-select',
            title: '是否规上',
            props: {
              placeholder: '请选择是否规上',
              options: this.dict.type.enterprise_regulate_status,
              filterOption: filterOption,
            },
          },
          {
            field: 'nature',
            element: 'a-select',
            title: '企业性质',
            props: {
              placeholder: '请选择企业性质',
              options: this.dict.type.enterprise_nature_status,
              filterOption: filterOption,
            },
          },
          {
            field: 'corporateLabel',
            element: 'a-select',
            title: '企业标签',
            props: {
              placeholder: '请选择企业标签',
              options: this.labelList,
              filterOption: filterOption,
              mode: 'multiple',
            },
          },
          {
            field: 'enterpriseAttr',
            element: 'a-select',
            defaultValue: [],
            title: '企业属性',
            props: {
              placeholder: '请选择企业属性',
              options: this.dict.type?.enterprise_attr || [],
              mode: 'multiple',
            },
          },
          {
            field: 'startTime',
            title: '开始月份',
            defaultValue: moment().subtract(1, 'months').format('YYYY-MM'),
            element: 'a-month-picker',
            props: {
              allowClear: false,
              placeholder: '请选择开始月份',
              format: 'YYYY-MM',
              valueFormat: 'YYYY-MM',
              disabledDate: this.disabledStartDate,
            },
          },
          {
            field: 'endTime',
            title: '结束月份',
            defaultValue: moment().subtract(1, 'months').format('YYYY-MM'),
            element: 'a-month-picker',
            props: {
              allowClear: false,
              placeholder: '请选择结束月份',
              format: 'YYYY-MM',
              valueFormat: 'YYYY-MM',
              disabledDate: this.disabledEndDate,
            },
          },
          {
            field: 'growRate',
            title: '营业增长率',
            element: 'slot',
            slotName: 'growRate',
            props: {
              placeholder: '请输入营业增长率',
            },
          },
        ],
        params: this.filterParams,
        formCol: { labelCol: 10, wrapperCol: 14 },
        // showCount: 7,
      };
    },
    tableColumn() {
      return [
        {
          type: 'checkbox',
          width: 80,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          width: 80,
        },
        {
          field: 'name',
          title: '企业名称',
          minWidth: 120,
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          minWidth: 140,
        },
        {
          field: 'address',
          title: '企业地址',
          minWidth: 120,
        },
        {
          field: 'parkName',
          title: '所属园区',
          minWidth: 120,
        },
        {
          field: 'status',
          title: '登记状态',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.dict?.type?.enterprise_register_status?.find(
              (q) => q.value == cellValue
            )?.label;
          },
        },
        {
          title: '营业增长率(%)',
          algin: 'center',
          children: this.dateList.map((item, index) => {
            return {
              field: `growRate${index}`,
              title: item,
              minWidth: 120,
            };
          }),
        },
      ];
    },
    modalConfig() {
      return {
        addBtn: false,
        submitBtn: false,
        editBtn: false,
        delBtn: false,
        viewBtn: false,
        menuFixed: 'right',
        menuWidth: 100,
        customOperationTypes: [
          {
            title: '企业画像',
            showForm: false,
            typeName: 'routerTo',
            event: (row) => {
              this.pictureId = row.unifiedCreditCode;
              this.modalVisible = true;
            },
          },
        ],
      };
    },
    autoFilterOptions() {
      return {
        config: [
          {
            field: 'name',
            title: '企业名称',
            props: {
              placeholder: '请输入择企业名称',
            },
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              placeholder: '请输入统一社会信用代码',
            },
          },
          {
            field: 'address',
            title: '企业地址',
            props: {
              placeholder: '请输入企业地址',
            },
          },
          {
            field: 'status',
            element: 'a-select',
            title: '登记状态',
            props: {
              placeholder: '请选择登记状态',
              options: this.dict.type.enterprise_register_status,
            },
          },
        ],
        params: this.autoFilterParams,
      };
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.loadData();
    // this.getGrowRateAnalysisPage();
    this.getParkList();
    this.getLabelList();
  },
  methods: {
    handleReset() {
      this.filterParams = initParams(this.filterOptions.config);
      this.autoFilterParams = initParams(this.autoFilterOptions.config);
      this.loadData();
    },
    async getGrowRateAnalysisPage() {
      const [res, err] = await queryBusinessPictureOperate();
      console.log(res, err);
    },
    async loadData() {
      if (
        isIntervalGreaterThanOneYear(
          this.filterParams.startTime,
          this.filterParams.endTime
        )
      ) {
        this.$message.error('时间间隔不能超过一年');
        return;
      }
      this.loading = true;
      const [res] = await getGrowRateAnalysisPage({
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
        ...this.filterParams,
        ...this.autoFilterParams,
        growRate: this.filterParams.symbol
          ? this.filterParams.growRate || 0
          : '',
      });
      this.loading = false;
      if (res && res?.data) {
        const { dateList, growRateAnalysisResList } = res.data;
        this.tableData = growRateAnalysisResList?.records || [];
        this.tablePage.total = growRateAnalysisResList?.total || 0;
        this.dateList = dateList || [];
      }
    },
    // 打开弹窗
    btnClickHandler(operationType, row) {
      this.operationType = operationType;
      this.$refs.crud.switchModalView(true, operationType, row);
    },
    openDrawer() {
      this.visible = true;
    },
    async exportData() {
      const list = this.$refs.crud.getCheckboxRecords();
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
      this.exportLoading = true;
      const [res] = await downloadGrowRateAnalysis({
        ...this.filterParams,
        ids: list.map((q) => q.unifiedCreditCode),
      });
      this.exportLoading = false;
      resolveBlob(res, mimeMap.xlsx, '目标列表', '.xlsx');
    },
    changeSymbol(val) {
      if (this.filterParams.symbol) {
        this.filterParams.growRate = this.filterParams.growRate || 0;
      } else {
        this.filterParams.growRate = '';
      }
    },
    // 获取园区列表
    async getParkList() {
      const [res] = await getParkList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.parkList = res.data;
      }
    },
    async getLabelList() {
      const [res] = await getLabelList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.labelList = res.data;
      }
    },
    disabledStartDate(val) {
      return disabledStartDate(val, this.filterParams.endTime);
    },
    disabledEndDate(val) {
      return disabledEndDate(val, this.filterParams.startTime);
    },
    // disabledEndDate(val) {
    //   // 假设 val 是结束时间
    //   const startDate = this.filterParams.startTime;
    //   if (startDate) {
    //     // 获取开始时间的时间戳
    //     const startNum = new Date(
    //       moment(startDate).format('YYYY-MM-DD')
    //     ).getTime();
    //     // 获取传入的结束时间的时间戳
    //     const now = new Date(moment(val).format('YYYY-MM-DD')).getTime();

    //     // 检查结束时间是否早于开始时间
    //     if (now < startNum) {
    //       return true; // 结束时间早于开始时间，禁用
    //     }

    //     // 检查结束时间与开始时间的间隔是否超过一年
    //     const oneYearLater = moment(startDate)
    //       .add(1, 'years')
    //       .toDate()
    //       .getTime();
    //     if (now > oneYearLater) {
    //       return true; // 结束时间超过开始时间一年，禁用
    //     }
    //   }
    //   return false;
    // },
    // disabledStartDate(val) {
    //   const endDate = this.filterParams.endTime;
    //   if (endDate) {
    //     const endNum = new Date(moment(endDate).format('YYYY-MM-DD')).getTime();
    //     const now = new Date(moment(val).format('YYYY-MM-DD')).getTime();

    //     // 检查开始时间是否晚于结束时间
    //     if (now > endNum) {
    //       return true; // 开始时间晚于结束时间，禁用
    //     }

    //     // 检查开始时间与结束时间的间隔是否超过一年
    //     const oneYearEarlier = new Date(
    //       endNum - 365 * 24 * 60 * 60 * 1000
    //     ).getTime();
    //     if (now < oneYearEarlier) {
    //       return true; // 开始时间早于结束时间前一年，禁用
    //     }
    //   }
    //   return false;
    // },
  },
};
</script>

<style scoped lang="less"></style>
