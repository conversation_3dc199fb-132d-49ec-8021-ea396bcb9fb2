<template>
  <page-layout>
    <table-component
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :parentData="params"
      :pageName="pageName"
    ></table-component>
  </page-layout>
</template>

<script>
import { institutionsMixin } from '../mixins/institutionsMixin';
import tableComponent from '@/views/basicData/components/tableComponent';
import moment from 'moment';

export default {
  name: 'innovationPlatformInstitutions',
  components: { tableComponent },
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'innovationPlatformInstitutions',
      platformTypeList: [],
      platformLevelList: [],
      creationCompleteList: [],
      params: {
        platformName: undefined,
        subjectOperation: undefined,
        platformType: undefined,
        level: undefined,
        creationComplete: undefined,
        rateTime: undefined,
      },
      tableColumn: [
        {
          field: '',
          title: '',
          type: 'checkbox',
          fixed: 'left',
          width: 70,
        },
        {
          field: '',
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: 70,
        },
        {
          field: 'rateTime',
          title: '日期',
          width: 200,
          formatter: ({ cellValue }) => {
            return cellValue ? moment(cellValue).format('YYYY') : '';
          },
        },
        {
          field: 'platformName',
          title: '平台名称',
          width: 200,
        },
        {
          field: 'constructionTheme',
          title: '建设主体',
          width: 200,
        },
        {
          field: 'subjectOperation',
          title: '运营主体',
          width: 200,
        },
        {
          field: 'serviceTime',
          title: '运营时间',
          width: 200,
        },
        {
          field: 'functionalOrientation',
          title: '功能定位（平方米）',
          width: 200,
        },
        {
          field: 'platformType',
          title: '平台类型',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.platformTypeList);
          },
        },
        {
          field: 'level',
          title: '平台级别',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.platformLevelList);
          },
        },
        {
          field: 'creationComplete',
          title: '建设情况',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.creationCompleteList);
          },
        },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        //筛选控件配置
        config: [
          {
            field: 'platformName',
            title: '平台名称',
          },
          {
            field: 'subjectOperation',
            title: '运营主体',
          },
          {
            field: 'platformType',
            title: '平台类型',
            element: 'a-select',
            props: {
              showSearch: true,
              optionFilterProp: 'children',
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.platformTypeList,
            },
          },
          {
            field: 'level',
            title: '平台级别',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.platformLevelList,
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
          {
            field: 'creationComplete',
            title: '建设情况',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.creationCompleteList,
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
          {
            field: 'rateTime',
            title: '日期',
            element: 'slot',
            slotName: 'dateYear',
            rules: [{ required: true, message: '请选择年份' }],
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        submitBtn: true,
        okBtn: false,
        addBtn: false,
        viewBtn: true,
        editBtn: true,
        delBtn: false,
        menu: true,
        menuWidth: 200,
        menuFixed: 'right',
        formConfig: [
          {
            field: 'rateTime',
            title: '年份',
            element: 'slot',
            slotName: 'dateYearForPop',
            rules: [{ required: true, message: '请选择年份' }],
          },
          {
            field: 'serviceTime',
            title: '运营时间',
            element: 'a-date-picker',
            echoFormatter: (value) => {
              return moment(value);
            },
            rules: [{ required: true, message: '请输入运营时间' }],
          },
          {
            field: 'platformName',
            title: '平台名称',
            rules: [{ required: true, message: '请输入平台名称' }],
          },
          {
            field: 'constructionTheme',
            title: '建设主体',
            rules: [{ required: true, message: '请输入建设主体' }],
          },
          {
            field: 'subjectOperation',
            title: '运营主体',
            rules: [{ required: true, message: '请输入运营主体' }],
          },
          {
            field: 'functionalOrientation',
            title: '功能定位',
            props: {
              suffix: '平方米',
            },
            rules: [{ required: true, message: '请输入功能定位' }],
          },
          {
            field: 'platformType',
            title: '平台类型',
            element: 'a-select',
            rules: [{ required: true, message: '请选择平台类型' }],
            props: {
              options: this.platformTypeList,
              showSearch: true,
              optionFilterProp: 'children',
            },
            previewFormatter: (value) => {
              return this.translateValue(value, this.platformTypeList);
            },
          },
          {
            field: 'level',
            title: '平台级别',
            element: 'a-select',
            rules: [{ required: true, message: '请选择平台级别' }],
            props: {
              options: this.platformLevelList,
              showSearch: true,
              optionFilterProp: 'children',
            },
            previewFormatter: (value) => {
              return this.translateValue(value, this.platformLevelList);
            },
          },
          {
            field: 'creationComplete',
            title: '建设情况',
            element: 'a-select',
            rules: [{ required: true, message: '请选择建设情况' }],
            props: {
              options: this.creationCompleteList,
              showSearch: true,
              optionFilterProp: 'children',
            },
            previewFormatter: (value) => {
              return this.translateValue(value, this.creationCompleteList);
            },
          },
        ],
      };
    },
  },
  created() {
    this.getCodeByType('PLATFORM_TYPE').then((res) => {
      this.platformTypeList = res;
    });
    this.getCodeByType('PLATFORM_LEVEL').then((res) => {
      this.platformLevelList = res;
    });
    this.getCodeByType('CREATION_COMPLETE').then((res) => {
      this.creationCompleteList = res;
    });
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.mr-10 {
  margin-right: 10px;
}
</style>
