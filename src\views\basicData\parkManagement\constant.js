// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    fixed: 'left',
    width: 60,
  },
  {
    type: 'seq',
    title: '序号',
    fixed: 'left',
    width: 60,
  },
  {
    field: 'type',
    title: '园区类别',
    minWidth: 180,
    formatter: ({ cellValue }) => {
      return cellValue === '1'
        ? '国有园区'
        : cellValue === '2'
        ? '民营园区'
        : '龙头企业园区';
    },
  },
  {
    field: 'parkName',
    title: '园区名称',
    minWidth: 180,
  },
  {
    field: 'address',
    title: '主要地址',
    width: 180,
  },
  {
    field: 'parkNameDetail',
    title: '详细园区名称',
    width: 180,
  },
  {
    field: 'addressDetail',
    title: '详细地址',
    width: 180,
  },
  {
    field: 'totalArea',
    title: '总规划面积(m²)',
    width: 180,
  },
  {
    field: 'completedArea',
    title: '已建成面积(m²)',
    width: 180,
  },
  {
    field: 'isCompleted',
    title: '建成状况',
    width: 180,
    formatter: ({ cellValue }) => {
      return cellValue === '1' ? '已建成' : cellValue === '0' ? '未建成' : '';
    },
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 180,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'parkName',
    title: '园区名称',
    props: {
      placeholder: '请输入名称',
    },
  },
  {
    field: 'type',
    title: '园区类别',
    element: 'a-select',
    // slotName: 'state',
    props: {
      options: [],
    },
  },
  {
    field: 'isCompleted',
    title: '建设情况',
    element: 'a-select',
    props: {
      options: [
        { value: '', label: '全部' },
        { value: '1', label: '已建成' },
        { value: '0', label: '未建成' },
      ],
    },
  },
];

export const initFormValue = () => {
  return {
    parkName: '',
    type: undefined,
    address: '',
    parkNameDetail: '',
    addressDetail: '',
    totalArea: '',
    isCompleted: '',
    completedArea: '',
  };
};

export const formConfig = [
  {
    field: 'parkName',
    title: '园区名称',
    // itemProps: {
    //   help: '请输入真实姓名',
    // },
    props: {
      placeholder: '请输入园区名称',
    },
    rules: [{ required: true, message: '请输入园区名称' }],
  },
  {
    field: 'type',
    title: '园区类别',
    element: 'a-select',
    props: {
      options: [
        { value: '1', label: '国有园区' },
        { value: '2', label: '民营园区' },
        { value: '3', label: '龙头企业园区' },
      ],
      showSearch: true,
      optionFilterProp: 'children',
    },
    rules: [{ required: true, message: '请输入园区类别' }],
  },
  {
    field: 'address',
    title: '主要地址',
    element: 'a-input',
    rules: [{ required: true, message: '请输入主要地址' }],
  },
  {
    field: 'parkNameDetail',
    title: '详细园区名称',
    element: 'a-input',
    rules: [{ required: true, message: '请输入' }],
  },
  {
    field: 'addressDetail',
    title: '详细地址',
    element: 'a-input',
    rules: [{ required: true, message: '请输入' }],
  },
  {
    field: 'totalArea',
    title: '总规划面积',
    element: 'slot',
    slotName: 'area',
    rules: [{ required: true, validator: checkNum, trigger: 'change' }],
  },
  {
    field: 'completedArea',
    title: '总建成面积',
    element: 'slot',
    slotName: 'buildArea',
    rules: [{ required: true, validator: checkNum, trigger: 'change' }],
  },
  {
    field: 'isCompleted',
    title: '建成状况',
    element: 'a-select',
    rules: [{ required: true, message: '请输入' }],
    props: {
      options: [
        { value: '1', label: '已建成' },
        { value: '0', label: '未建成' },
      ],
    },
  },
];

export const initManFormValue = () => {
  return {
    parkId: '',
    name: '',
    phone: '',
    businessRange: '',
    intro: '',
    ratingPre: '',
    greenPre: '',
    publicService: undefined,
    floorSpace: '',
    carrierArea: '',
    honor: '',
    population: '',
  };
};
export const initFormLabelValue = () => {
  return {
    labelId: [],
  };
};
// 数字规则校验
async function checkNum(rule, value) {
  if (!value) {
    return Promise.reject(`请输入内容`);
  }
  if (isNaN(Number(value))) {
    return Promise.reject('请输入数字');
  } else {
    if (value.indexOf('.') !== -1) {
      let len = value.toString().split('.')[1].length;
      if (len !== 0) {
        if (len > 2) {
          return Promise.reject('只能保留两位小数');
        } else {
          return Promise.resolve();
        }
      } else {
        return Promise.reject('请输入正确数字格式');
      }
    } else {
      return Promise.resolve();
    }
  }
}
