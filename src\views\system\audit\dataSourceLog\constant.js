import moment from 'moment';

export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'userId',
      title: '数据库账号',
      props: {
        placeholder: '请输入数据库账号',
      },
    },
    {
      field: 'ipAddress',
      title: '执行状态',
      element: 'a-select',
      props: {
        placeholder: '请输入执行状态',
      },
    },
    {
      field: 'time',
      title: '操作时间',
      element: 'a-date-picker',
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { userId: '', ipAddress: '', time: '' },
};

// 表头
export const tableColumn = [
  { field: 'processName', title: '数据库账号' },
  { field: 'processName', title: '报文' },
  { field: 'processKey', title: '影响行数' },
  { field: 'processKey', title: '执行时长' },
  { field: 'processKey', title: '执行状态' },
  { field: 'processKey', title: 'IP地址' },
  {
    field: 'updateTime',
    title: '操作时间',
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '详情',
    slots: { default: 'operate' },
  },
];
