<template>
  <page-layout>
    <a-row
      type="flex"
      justify="space-between"
      class="page-wrapper-row-flex"
      :gutter="[16, 0]"
    >
      <a-col :span="4">
        <OrganizationTree
          ref="orgaizationTree"
          :organizeId.sync="organizeId"
          :organizeName.sync="organizeName"
          @getTreeDone="getTreeDone"
          @afterChooseMerchant="afterChooseMerchant"
        ></OrganizationTree>
      </a-col>
      <a-col :span="20">
        <PageWrapper
          ref="vxeRef"
          style="margin: 0"
          :title="(organizeName && `用户列表 - ${organizeName}`) || '用户列表'"
          createText=""
          :loading="tableLoading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="tableColumns"
          :tableData="tableData"
          @loadData="loadData"
          @handleReset="handleReset"
          @handleCreate="onClickHeaderBtn('add')"
        >
          <template #defaultHeader>
            <a-button
              v-hasPermi="['system:user:add']"
              type="primary"
              :loading="tableLoading"
              @click="onClickHeaderBtn('add')"
            >
              新增用户
            </a-button>
          </template>
          <!-- 创建按钮区域插槽 -->
          <!-- <template #defaultHeader>
            <a-button
              ghost
              type="danger"
              :style="{ marginRight: '8px' }"
              icon="delete"
              @click="onClickHeaderBtn('delete')"
            >
              批量删除
            </a-button>
          </template> -->
          <template #status="{ row }">
            <a-switch
              :checked="row.status === userStatusEnum.NORMAL"
              checked-children="启用"
              :un-checked-children="
                row.status === userStatusEnum.LOCKED ? '锁定' : '停用'
              "
              :disabled="row.status === userStatusEnum.LOCKED"
              @change="handleSwitchChange(row)"
            />
          </template>
          <template #operation="{ row }">
            <!-- <a-button
              icon="eye"
              type="link"
              @click="handleOperation('showDetail', row)"
              style="padding: 0; margin-right: 8px"
            >
              查看
            </a-button> -->
            <a-button
              v-hasPermi="['system:user:edit']"
              v-if="loginUserId !== row.userId"
              icon="edit"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleOperation('modify', row)"
            >
              编辑
            </a-button>
            <a-button
              v-hasPermi="['system:user:delete']"
              v-if="loginUserId !== row.userId"
              icon="delete"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleOperation('delete', row)"
            >
              删除
            </a-button>
            <a-button
              v-hasPermi="['system:user:lock']"
              icon="lock"
              v-if="
                loginUserId !== row.userId &&
                row.status !== userStatusEnum.LOCKED
              "
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleOperation('lock', row)"
            >
              锁定
            </a-button>
            <a-button
              v-hasPermi="['system:user:lock']"
              icon="key"
              v-if="
                loginUserId !== row.userId &&
                row.status === userStatusEnum.LOCKED
              "
              type="link"
              @click="handleOperation('unLock', row)"
              style="padding: 0; margin-right: 8px"
            >
              解锁
            </a-button>

            <a-button
              v-hasPermi="['system:user:resetPassword']"
              icon="reload"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleOperation('reset', row)"
            >
              重置密码
            </a-button>
          </template>
        </PageWrapper>
      </a-col>
    </a-row>

    <!-- 新增用户 -->
    <AddUser
      :visible.sync="showAdd"
      :organizeTreeList="organizationTreeList"
      :userId="userDetail.userId"
      :organizeId="organizeId"
      @ok="loadData"
    />
    <!-- 修改密码 -->
    <ChangePassword
      :visible.sync="showReset"
      :userId="userDetail.userId"
      @ok="loadData"
    />
    <!-- 锁定用户 -->
    <LockUser
      :visible.sync="showLock"
      :userId="userDetail.userId"
      @ok="loadData"
    />
    <!-- 查看用户信息 -->
    <UserDetail :visible.sync="showDetailVisible" :userId="userDetail.userId" />
  </page-layout>
</template>

<script>
import {
  listUser,
  delUser,
  unlockUser,
  changeUserStatus,
} from '@/api/system/user';
import { Modal, Empty } from 'ant-design-vue';
import AddUser from './components/AddUser.vue'; //新增用户
import UserDetail from './components/UserDetail.vue'; //查看用户信息
import ChangePassword from './components/ChangePassword.vue'; //修改密码
import LockUser from './components/LockUser.vue'; //锁定用户
import { tableColumns, userStatusEnum, filterOptions } from './constant';
import OrganizationTree from '@/components/SystemTreeSelect/OrganizationTree.vue';
import { rsaCode } from '@/utils/common/auth';
import moment from 'moment';
export default {
  components: {
    AddUser,
    UserDetail,
    ChangePassword,
    OrganizationTree,
    LockUser,
  },
  data() {
    return {
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE, //空图片
      userStatusEnum, //用户状态
      // 左侧树相关数据
      organizeId: '', //默认选择数据
      organizeName: '', //组织名称
      organizationTreeList: [],
      organizationArrayList: [],

      // 表格展示
      tableLoading: false, //列表loading
      filterOptions, //搜索配置
      tableColumns, //表格表头
      tableData: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },

      // 弹窗相关数据
      showReset: false, //展示重置密码
      showAdd: false, //展示新增用户
      showLock: false, // 展示锁用户
      userDetail: false, // 展示锁用户
      showDetailVisible: false, //查看数据
      handleLoading: false,
      // 切换租户
      showChangeMerchant: false, //切换租户的吐槽
      //编辑用户组
      editUserRoles: false,
    };
  },
  computed: {
    merchantId() {
      return this.$store.state?.base?.merchant?.merchantId;
    },
    loginUserId() {
      return this.$store.state?.base?.user?.userId;
    },
  },
  created() {},
  methods: {
    /**
     * 获取树完成之后保存数据
     */
    getTreeDone(data) {
      this.organizationTreeList = data?.tree || [];
      this.organizationArrayList = data?.array || [];
      this.loadData();
    },
    /**
     * 选择组织后
     */
    async afterChooseMerchant() {
      this.filterOptions.params = {
        userName: '',
        nickName: '',
        phonenumber: '',
        status: '',
      };
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      this.loadData();
    },
    /**
     * 数据加载
     */
    async loadData() {
      this.tableLoading = true;
      const { createTime, phonenumber, ...params } = this.filterOptions.params;

      let timeData = {};
      // 如果存在数据
      if (createTime && createTime[0] && createTime[1]) {
        timeData = {
          beginTime: moment(createTime[0]).valueOf(),
          endTime: moment(createTime[1]).valueOf(),
        };
      }
      const [result, error] = await listUser({
        ...params,
        page: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
        ...timeData,
        phonenumber: phonenumber ? rsaCode(phonenumber) : '',
        organizeId: this.organizeId || '',
      });
      this.tableLoading = false;
      if (error) return;
      this.tableData = result.data || [];
      this.tablePage.total = result.count;
    },
    /**
     * 操作按钮
     * @param {String} type 操作类型
     * @param {Object} record 表单数据
     */
    async handleOperation(type, record) {
      this.userDetail = record;
      switch (type) {
        case 'modify': {
          // 修改
          this.showAdd = true;
          break;
        }
        case 'delete': // 删除
          this.onDeleteUser(record);
          break;
        case 'reset': // 重置密码
          this.showReset = true;
          break;
        case 'lock': // 重置密码
          this.showLock = true;
          break;
        case 'unLock': // 重置密码
          this.unlockUser(record);
          break;
        case 'block': // 重置密码
          this.editUserRoles = true;
          break;
        case 'showDetail': // 查看
          this.showDetailVisible = true;
          break;
        default:
          break;
      }
    },
    /**
     * 解锁用户
     * @param {Object} data 表单数据
     */
    async unlockUser(data) {
      const { userId, lockDuration, reason } = data;
      const [, error] = await unlockUser({ userId, lockDuration, reason });
      if (error) {
        this.$message.error(`解锁失败`);
      } else {
        this.$message.success(`解锁成功`);
      }
      this.loadData();
    },
    /**
     * 修改用户状态
     * @param {Object} record 表单数据
     */
    handleSwitchChange(record) {
      if (this.handleLoading) return;
      const text = record.status === userStatusEnum.NORMAL ? '停用' : '启用';
      Modal.confirm({
        title: '警告',
        content: '确认要"' + text + '""' + record.userName + '"用户吗?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.handleLoading = true;
          const [, error] = await changeUserStatus(
            record.userId,
            record.status === userStatusEnum.NORMAL
              ? userStatusEnum.STOP_USING
              : userStatusEnum.NORMAL
          );
          this.handleLoading = false;
          if (error) return;
          this.$message.success(`${text}成功`);
          this.loadData();
        },
      });
    },
    /**
     * 增 删 导出按钮点击
     * @param {String} type 操作类型
     */
    async onClickHeaderBtn(type) {
      switch (type) {
        case 'add':
          this.userDetail = {};
          this.showAdd = true;
          break;
        case 'delete':
          // 无需传值 使用选中项selectTableList中数据
          this.onDeleteUser();
          break;
        default:
          break;
      }
    },
    /**
     * 删除用户
     * @param {Object} record 表单数据
     */
    async onDeleteUser(data) {
      if (this.handleLoading) return;
      const deleteList = data ? [data] : this.$refs.vxeRef.getCheckboxRecords();
      if (deleteList.length === 0) {
        this.$message.warn('请选择需要删除数据');
        return;
      }
      const userIds = deleteList.map((item) => item.userId);
      const userName = deleteList.map((item) => item.userName).join(',');

      Modal.confirm({
        title: '警告',
        content: '是否确认删除用户名为"' + userName + '"的数据项?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.handleLoading = true;
          const [, error] = await delUser(userIds);
          this.handleLoading = false;
          if (error) return;
          this.loadData();
          this.selectTableList = [];
          this.multiple = false;
          this.$message.success('删除成功！');
        },
      });
    },
    async handleReset() {
      this.filterOptions.params = {
        userName: '',
        nickName: '',
        phonenumber: '',
        status: '',
      };
      this.organizeId = this.organizationTreeList?.[0]?.id;
      this.organizeName = this.organizationTreeList?.[0]?.label;
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      this.loadData();
    },
  },
};
</script>

<style scoped lang="less">
.table-title {
  color: #000;
  font-size: 18px;
  font-weight: 500;
}
.page-wrapper-row-flex {
  display: flex;
  align-items: stretch;
}
.show-merchantName {
  margin-bottom: 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
