<template>
  <div class="empty-container">
    空页面路由配置meta中配置：
    <br />
    meta: { hideHeader: true }
    <p>hooks：获取dict：my_application_type</p>
    <a-select v-model="form.jobGroup" style="width: 600px" placeholder="请选择">
      <a-select-option
        v-for="dict in my_application_type"
        :key="dict.value"
        :value="dict.value"
        >{{ dict.label }}</a-select-option
      >
    </a-select>
  </div>
</template>

<script setup name="User">
import { getCurrentInstance, reactive } from 'vue';
const { proxy } = getCurrentInstance();
const { my_application_type } = proxy.useDict('my_application_type');
const form = reactive({
  jobGroup: '',
});
</script>

<style scoped lang="less">
.empty-container {
  width: 100%;
  min-height: calc(100vh - 64px);
}
</style>
