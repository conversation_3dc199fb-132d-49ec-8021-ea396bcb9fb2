{"name": "management-taike", "version": "0.0.1", "description": "中台前端-赋能平台子应用后管模板-Vue2x版本", "author": "68260 <daih<PERSON><PERSON><PERSON>@bangdao-tech.com>", "homepage": "https://gitlab.bangdao-tech.com/bangdao-templates/background-admin-template", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:scene": "vue-cli-service build --mode scene", "lint": "vue-cli-service lint --fix", "generate": "node lib/generate.js"}, "dependencies": {"@bangdao/buse-components": "0.2.23", "@bangdao/pro-components": "2.0.2", "@riophae/vue-treeselect": "^0.4.0", "@tinymce/tinymce-vue": "3.0.1", "ant-design-vue": "1.7.8", "autofit.js": "^3.0.7", "axios": "^1.3.4", "core-js": "3.31.0", "crypto-js": "4.1.1", "d3": "^7.8.5", "echarts": "^5.5.0", "fast-deep-equal": "3.1.3", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "js-cookie": "2.2.1", "jsencrypt": "3.2.1", "moment": "^2.29.4", "nprogress": "0.2.0", "tinymce": "5.1.0", "vue": "2.7.14", "vue-clipboard2": "^0.3.3", "vue-contextmenu": "^1.5.11", "vue-pdf": "^4.3.0", "vue-router": "3", "vuex": "3.6.2", "vxe-table": "3.10.10", "vxe-table-plugin-antd": "1.11.3", "xe-utils": "3.5.6", "xlsx": "0.17.2"}, "devDependencies": {"@babel/core": "7.21.3", "@babel/eslint-parser": "7.21.3", "@babel/preset-env": "^7.24.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "7.32.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "8.7.1", "less": "3.13.1", "less-loader": "6.2.0", "lint-staged": "11.2.6", "prettier": "2.8.7", "style-resources-loader": "1.3.2", "terser-webpack-plugin": "^5.3.9", "vue-cli-plugin-style-resources-loader": "0.1.5", "webpack": "5.76.3"}, "gitHooks": {"pre-commit": "lint-staged"}}