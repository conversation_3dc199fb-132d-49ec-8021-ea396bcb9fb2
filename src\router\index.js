/** @format */
import Vue from 'vue';
import Router from 'vue-router';
import { constantRoutes } from './config';

Vue.use(Router);

// 去除重复路由跳转报错提示：Error: Avoided redundant navigation to current location
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};

const router = new Router({
  mode: 'hash',
  scrollBehavior: () => ({ x: 0, y: 0 }),
  routes: constantRoutes,
  base: '/tkb-platform-front',
});
export default router;

export function useRouter() {
  return router;
}
export function useRoute() {
  return router.app.$route;
}
