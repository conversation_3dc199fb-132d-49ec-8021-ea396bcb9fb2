import request from '@/utils/request';

// 新增
export function add(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/app/add',
    method: 'POST',
    data: query,
  });
}
//更新
export function update(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/app/update',
    method: 'POST',
    data: query,
  });
}
// 删除
export function deleteApp(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/app/delete',
    method: 'GET',
    params: query,
  });
}
// 获取app列表
export function getList(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/app/list',
    method: 'GET',
    params: query,
  });
}
// 应用详情
export function getSingle(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/app/single',
    method: 'GET',
    params: query,
  });
}
