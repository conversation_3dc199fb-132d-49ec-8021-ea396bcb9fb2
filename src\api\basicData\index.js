import { request } from '@/utils/request/requestTkb';
// 公共操作日志列表
export function getOperationLog(query) {
  return request({
    url: '/common/operation/log',
    method: 'post',
    data: query,
  });
}
// 企业日志记录列表
export function getEnterpriseLog(query) {
  return request({
    url: '/common/enterprise/log',
    method: 'post',
    data: query,
  });
}
// 获取企业列表
export function getEnterpriseList(query) {
  return request({
    url: '/common/dropdown/enterprise/list',
    method: 'post',
    data: query,
  });
}
// 获取所有园区、企业列表
export function getParkAndEnterpriseList(query) {
  return request({
    url: '/screen/situation/getParkAndEnterpriseList',
    method: 'post',
    data: query,
  });
}
// 获取企业列表全部企业信息
export function getEnterpriseListAll(query) {
  return request({
    url: '/common/dropdown/enterprise',
    method: 'post',
    data: query,
  });
}
// 获取园区信息列表
export function getParkList(query) {
  return request({
    url: '/common/dropdown/park/list',
    method: 'post',
    data: query,
  });
}
// 获取标签列表
export function getLabelList(query) {
  return request({
    url: '/common/dropdown/label/list',
    method: 'post',
    data: query,
  });
}

//查询所有企业
export function getListEnterprise(query) {
  return request({
    url: '/base/enterprise/listEnterprise',
    method: 'post',
    data: query,
  });
}

// 太科城信息管理基础信息
export function baseInformation(query) {
  return request({
    url: '/base/tkcInformation/baseInformation',
    method: 'GET',
    params: query,
  });
}
// 太科城信息管理指标信息
export function metricInformation(query) {
  return request({
    url: '/base/tkcInformation/metricInformation',
    method: 'POST',
    data: query,
  });
}
// 太科城信息管理指标信息
export function codeByType(query) {
  return request({
    url: '/base/code/codeByType',
    method: 'GET',
    params: query,
  });
}
// 太科城信息管理新增
export function saveInformation(query) {
  return request({
    url: '/base/tkcInformation/saveInformation',
    method: 'POST',
    data: query,
  });
}
// 太科城信息管理编辑
export function editInformation(query) {
  return request({
    url: '/base/tkcInformation/editInformation',
    method: 'POST',
    data: query,
  });
}
// 太科城信息管理删除
export function deleteInformation(query) {
  return request({
    url: '/base/tkcInformation/deleteInformation',
    method: 'POST',
    data: query,
  });
}

// 太科城人才企业数量、社区常住人口数量 分页查询
export function getInformationByTape(query) {
  return request({
    url: '/base/tkcInformation/getInformationByTape',
    method: 'POST',
    data: query,
  });
}

// 园区管理列表
export function parkPages(query) {
  return request({
    url: '/base/sciencePark/parkPages',
    method: 'POST',
    data: query,
  });
}
// 新增园区管理列表
export function savePark(query) {
  return request({
    url: '/base/sciencePark/savePark',
    method: 'POST',
    data: query,
  });
}
// 编辑园区管理列表
export function editPark(query) {
  return request({
    url: '/base/sciencePark/editPark',
    method: 'POST',
    data: query,
  });
}
// 园区管理管理弹框保存
export function saveManageDetail(query) {
  return request({
    url: '/base/sciencePark/saveManageDetail',
    method: 'POST',
    data: query,
  });
}
// 园区管理管理删除
export function deletePark(query) {
  return request({
    url: '/base/sciencePark/deleteInformation',
    method: 'POST',
    data: query,
  });
}
// 园区管理管理信息
export function parkManageDetail(query) {
  return request({
    url: '/base/sciencePark/parkManageDetail',
    method: 'GET',
    params: query,
  });
}
// 企业管理管理导出
export function downloadEnterTemplate(query) {
  return request({
    url: '/base/enterprise/downloadTemplate',
    method: 'GET',
    params: query,
  });
}
// 企业管理列表
export function pageEnterprise(query) {
  return request({
    url: '/base/enterprise/pageEnterprise',
    method: 'POST',
    data: query,
  });
}
// 企业管理删除
export function deleteEnterprise(query) {
  return request({
    url: '/base/enterprise/deleteInformation',
    method: 'POST',
    data: query,
  });
}
// 企业管理新增
export function saveEnterprise(query) {
  return request({
    url: '/base/enterprise/saveEnterprise',
    method: 'POST',
    data: query,
  });
}
// 企业管理编辑
export function editEnterprise(query) {
  return request({
    url: '/base/enterprise/editEnterprise',
    method: 'POST',
    data: query,
  });
}
// 企业管理所属园区
export function parkList(query = {}) {
  return request({
    url: '/base/sciencePark/parkList',
    method: 'POST',
    data: query,
  });
}
// 重点企业
export function pageKeyEmphasis(query) {
  return request({
    url: '/base/emphasis/pageEmphasis',
    method: 'POST',
    data: query,
  });
}
// 重点企业新增
export function saveEmphasis(query) {
  return request({
    url: '/base/emphasis/saveEmphasis',
    method: 'POST',
    data: query,
  });
}
// 重点企业编辑
export function editEmphasis(query) {
  return request({
    url: '/base/emphasis/editEmphasis',
    method: 'POST',
    data: query,
  });
}
// 重点企业删除
export function deleteEmphasis(query) {
  return request({
    url: '/base/emphasis/deleteEmphasis',
    method: 'POST',
    data: query,
  });
}
// 独立企业园区
export function pageParkEnterprise(query) {
  return request({
    url: '/base/independent/pageParkEnterprise',
    method: 'POST',
    data: query,
  });
}
// 独立企业园区新增
export function saveIndepEnterprise(query) {
  return request({
    url: '/base/independent/saveEnterprise',
    method: 'POST',
    data: query,
  });
}
// 独立企业园区编辑
export function editIndepEnterprise(query) {
  return request({
    url: '/base/independent/editEnterprise',
    method: 'POST',
    data: query,
  });
}
// 独立企业园区删除
export function deleteIndepEnterprise(query) {
  return request({
    url: '/base/independent/deleteEnterprise',
    method: 'POST',
    data: query,
  });
}
// 独立企业园区模板导出
export function downloadIndepTemplate(query) {
  return request({
    url: '/base/independent/downloadTemplate',
    method: 'GET',
    params: query,
  });
}
// 独立企业园区导入
export function importIndepData(query) {
  return request({
    url: '/base/independent/importData',
    method: 'POST',
    data: query,
  });
}
// 上市公司
export function companyPage(query) {
  return request({
    url: '/base/listed/companyPage',
    method: 'POST',
    data: query,
  });
}
// 上市公司新增
export function saveCompany(query) {
  return request({
    url: '/base/listed/saveCompany',
    method: 'POST',
    data: query,
  });
}
// 上市公司编辑
export function editCompany(query) {
  return request({
    url: '/base/listed/editCompany',
    method: 'POST',
    data: query,
  });
}
// 上市公司删除
export function deleteCompany(query) {
  return request({
    url: '/base/listed/deleteCompany',
    method: 'POST',
    data: query,
  });
}
// 上市公司下载
export function downloadCompanyTemplate(query) {
  return request({
    url: '/base/listed/downloadTemplate',
    method: 'GET',
    data: query,
  });
}
// 上市公司导入
export function importCompanyData(query) {
  return request({
    url: '/base/listed/excelAdd',
    method: 'POST',
    data: query,
  });
}
// 上市公司导出
export function downloadCompanyInformation(query) {
  return request({
    url: '/base/listed/downloadInformation',
    method: 'POST',
    data: query,
  });
}
// 营收月报
export function taxablePage(query) {
  return request({
    url: '/base/taxableMarket/taxablePage',
    method: 'POST',
    data: query,
  });
}
// 营收月报新增
export function saveTaxable(query) {
  return request({
    url: '/base/taxableMarket/saveTaxable',
    method: 'POST',
    data: query,
  });
}
// 营收月报编辑
export function editTaxable(query) {
  return request({
    url: '/base/taxableMarket/editTaxable',
    method: 'POST',
    data: query,
  });
}
// 营收月报删除
export function deleteTaxable(query) {
  return request({
    url: '/base/taxableMarket/deleteTaxable',
    method: 'POST',
    data: query,
  });
}
// 营收月报导入
export function importTaxData(query) {
  return request({
    url: '/base/taxableMarket/importData',
    method: 'POST',
    data: query,
  });
}
// 营收月报导出
export function downloadTaxInformation(query) {
  return request({
    url: '/base/taxableMarket/downloadInformation',
    method: 'POST',
    data: query,
  });
}
// 企业名称
export function companyNameList(query) {
  return request({
    url: '/base/enterprise/companyNameList',
    method: 'POST',
    data: query,
  });
}
// 科技创新
export function pageTechnologyEmphasis(query) {
  return request({
    url: '/base/technology/pageEmphasis',
    method: 'POST',
    data: query,
  });
}
// 科技创新新增
export function addCompany(query) {
  return request({
    url: '/base/technology/addCompany',
    method: 'POST',
    data: query,
  });
}
// 科技创新编辑
export function updateCompany(query) {
  return request({
    url: '/base/technology/updateCompany',
    method: 'POST',
    data: query,
  });
}
// 科技创新删除
export function batchDelete(query) {
  return request({
    url: '/base/technology/batchDelete',
    method: 'POST',
    data: query,
  });
}
// 科技创新导出
export function exportTechnology(query) {
  return request({
    url: '/base/technology/export',
    method: 'POST',
    data: query,
  });
}
// 科技创新导入
export function importExl(query) {
  return request({
    url: '/base/technology/importExl',
    method: 'POST',
    data: query,
  });
}
// 科技创新下载模板
export function downTechnology(query) {
  return request({
    url: '/base/technology/downloadTemplate',
    method: 'GET',
    params: query,
  });
}
// 瞪羚独角兽企业
export function pageEnterEmphasis(query) {
  return request({
    url: '/base/enterpriseName/pageEmphasis',
    method: 'POST',
    data: query,
  });
}
// 瞪羚独角兽企业类别
export function companyType(query) {
  return request({
    url: '/base/enterpriseName/companyType',
    method: 'GET',
    params: query,
  });
}
// 瞪羚独角兽企业新增
export function addEnterCompany(query) {
  return request({
    url: '/base/enterpriseName/addCompany',
    method: 'POST',
    data: query,
  });
}
// 瞪羚独角兽企业编辑
export function updateEnterCompany(query) {
  return request({
    url: '/base/enterpriseName/updateCompany',
    method: 'POST',
    data: query,
  });
}
// 瞪羚独角兽企业删除
export function batchEnterDelete(query) {
  return request({
    url: '/base/enterpriseName/batchDelete',
    method: 'POST',
    data: query,
  });
}
// 瞪羚独角兽企业导出
export function downloadEnter(query) {
  return request({
    url: '/base/enterpriseName/downloadInformation',
    method: 'POST',
    data: query,
  });
}
// 瞪羚独角兽企业下载模板
export function pageEnterDown(query) {
  return request({
    url: '/base/enterpriseName/pageEmphasis',
    method: 'GET',
    params: query,
  });
}
// 瞪羚独角兽企业上传
export function importEnterData(query) {
  return request({
    url: '/base/enterpriseName/importData',
    method: 'POST',
    data: query,
  });
}
// 人才管理
export function talentPage(query) {
  return request({
    url: '/base/talent/talentPage',
    method: 'POST',
    data: query,
  });
}
// 人才管理新增
export function saveTalent(query) {
  return request({
    url: '/base/talent/saveTalent',
    method: 'POST',
    data: query,
  });
}
// 人才管理编辑
export function editTalentId(query) {
  return request({
    url: '/base/talent/editTalent',
    method: 'POST',
    data: query,
  });
}
// 人才管理删除
export function deleteTalentId(query) {
  return request({
    url: '/base/talent/deleteTalent',
    method: 'POST',
    data: query,
  });
}
// 人才管理导入
export function importTalentData(query) {
  return request({
    url: '/base/talent/importData',
    method: 'POST',
    data: query,
  });
}
// 人才管理导出
export function downloadTalent(query) {
  return request({
    url: '/base/talent/downloadInformation',
    method: 'POST',
    data: query,
  });
}
// 人才管理下载模板
export function downloadTalentTemplate(query) {
  return request({
    url: '/base/talent/downloadTemplate',
    method: 'GET',
    params: query,
  });
}
// 研发机构管理
export function metricResearch(query) {
  return request({
    url: '/base/research/metricInformation',
    method: 'POST',
    data: query,
  });
}
// 研发机构管理新增
export function addResearch(query) {
  return request({
    url: '/base/research/add',
    method: 'POST',
    data: query,
  });
}
// 研发机构管理编辑
export function editResearch(query) {
  return request({
    url: '/base/research/editInformation',
    method: 'POST',
    data: query,
  });
}
// 研发机构管理删除
export function deleteResearch(query) {
  return request({
    url: '/base/research/deleteInformation',
    method: 'POST',
    data: query,
  });
}
// 研发机构管理导入
export function importResearch(query) {
  return request({
    url: '/base/research/importData',
    method: 'POST',
    data: query,
  });
}
// 研发机构管理导出
export function downloadResearch(query) {
  return request({
    url: '/base/research/downloadInformation',
    method: 'POST',
    data: query,
  });
}
// 研发机构管理下载模板
export function downloadResearchTemplate(query) {
  return request({
    url: '/base/research/downloadTemplate',
    method: 'GET',
    params: query,
  });
}
// PCT专利列表
export function pagePctEmphasis(query) {
  return request({
    url: '/base/pct/pageEmphasis',
    method: 'POST',
    data: query,
  });
}
// PCT专利列表新增
export function savePctEmphasis(query) {
  return request({
    url: '/base/pct/saveEmphasis',
    method: 'POST',
    data: query,
  });
}
// PCT专利列表编辑
export function editPctEmphasis(query) {
  return request({
    url: '/base/pct/editEmphasis',
    method: 'POST',
    data: query,
  });
}
// PCT专利列表删除
export function deletePctEmphasis(query) {
  return request({
    url: '/base/pct/deleteEmphasis',
    method: 'POST',
    data: query,
  });
}
// PCT专利列表导出
export function downloadPct(query) {
  return request({
    url: '/base/pct/downloadInformation',
    method: 'POST',
    data: query,
  });
}
// PCT专利列表下载模板
export function downloadPctTemplate(query) {
  return request({
    url: '/base/pct/downloadTemplate',
    method: 'GET',
    params: query,
  });
}
// PCT专利导入
export function importPctData(query) {
  return request({
    url: '/base/pct/importData',
    method: 'POST',
    data: query,
  });
}
// 发明专利列表
export function pagePatentEmphasis(query) {
  return request({
    url: '/base/patent/pageEmphasis',
    method: 'POST',
    data: query,
  });
}
// 发明专利列表新增
export function savePatentEmphasis(query) {
  return request({
    url: '/base/patent/saveEmphasis',
    method: 'POST',
    data: query,
  });
}
// 发明专利列表编辑
export function editPatentEmphasis(query) {
  return request({
    url: '/base/patent/editEmphasis',
    method: 'POST',
    data: query,
  });
}
// 发明专利列表删除
export function deletePatentEmphasis(query) {
  return request({
    url: '/base/patent/deleteEmphasis',
    method: 'POST',
    data: query,
  });
}
// 发明专利列表导出
export function downloadPatent(query) {
  return request({
    url: '/base/patent/downloadInformation',
    method: 'POST',
    data: query,
  });
}
// 发明专利列表下载模板
export function downloadPatentTemplate(query) {
  return request({
    url: '/base/patent/downloadTemplate',
    method: 'GET',
    params: query,
  });
}
// 发明专利导入
export function importPatentData(query) {
  return request({
    url: '/base/patent/importData',
    method: 'POST',
    data: query,
  });
}
// 专精特新小巨人
export function pageProEmphasis(query) {
  return request({
    url: '/base/littleGiant/getAscertainmentSituationPageList',
    method: 'POST',
    data: query,
  });
}
// 专精特新小巨人新增
export function saveProEmphasis(query) {
  return request({
    url: '/base/littleGiant/saveLittleGiant',
    method: 'POST',
    data: query,
  });
}
// 专精特新小巨人编辑
export function editProEmphasis(query) {
  return request({
    url: '/base/littleGiant/editLittleGiant',
    method: 'POST',
    data: query,
  });
}
// 专精特新小巨人删除
export function deleteProEmphasis(query) {
  return request({
    url: '/base/littleGiant/deleteLittleGiant',
    method: 'POST',
    data: query,
  });
}
// 专精特新小巨人导出
export function downloadPro(query) {
  return request({
    url: '/base/profession/downloadInformation',
    method: 'POST',
    data: query,
  });
}
// 专精特新小巨人下载模板
export function downloadProTemplate(query) {
  return request({
    url: '/base/profession/downloadTemplate',
    method: 'GET',
    params: query,
  });
}
// 专精特新小巨人导入
export function importProData(query) {
  return request({
    url: '/base/profession/importData',
    method: 'POST',
    data: query,
  });
}
// 专精特新小巨人领域行业
export function profession(query) {
  return request({
    url: '/base/profession/profession',
    method: 'GET',
    params: query,
  });
}
// 标签列表
export function labelList(query) {
  return request({
    url: '/label/warehouse/labelList',
    method: 'GET',
    params: query,
  });
}
// 企业标签修改
export function editLabel(query) {
  return request({
    url: '/base/enterprise/editLabel',
    method: 'POST',
    data: query,
  });
}
// 园区标签修改
export function editParkLabel(query) {
  return request({
    url: '/base/sciencePark/editLabel',
    method: 'POST',
    data: query,
  });
}

// 基本信息列表分页查询
export function getListInfo(query, pageName) {
  const map = {
    cityPlanInstitutions: '/base/plan/metricInformation',
    incubatorsInstitutions: '/base/incubation/metricInformation',
    innovationPlatformInstitutions: '/base/Innovate/metricInformation',
    majorProgramInstitutions: '/base/major/metricInformation',
    ledgerManage: '/base/account/pageEmphasis',
    overseasManage: '/base/overseas/researchPage',
    riskInvesmentManage: '/base/vc/pageEmphasis',
    majorInnovationManage: '/base/creative/pageEmphasis',
  };
  const url = map[pageName];
  return request({
    url: url,
    method: 'POST',
    data: query,
  });
}

// 新增接口
export function addInfo(query, pageName) {
  const map = {
    cityPlanInstitutions: '/base/plan/add',
    incubatorsInstitutions: '/base/incubation/add',
    innovationPlatformInstitutions: '/base/Innovate/add',
    majorProgramInstitutions: '/base/major/add',
    ledgerManage: '/base/account/saveEnterprise',
    overseasManage: '/base/overseas/saveResearch',
    riskInvesmentManage: '/base/vc/saveEmphasis',
    majorInnovationManage: '/base/creative/saveEnterprise',
  };
  const url = map[pageName];
  return request({
    url: url,
    method: 'POST',
    data: query,
  });
}

// 编辑接口
export function editInfo(query, pageName) {
  const map = {
    cityPlanInstitutions: '/base/plan/editInformation',
    incubatorsInstitutions: '/base/incubation/editInformation',
    innovationPlatformInstitutions: '/base/Innovate/editInformation',
    majorProgramInstitutions: '/base/major/editInformation',
    ledgerManage: '/base/account/editEnterprise',
    overseasManage: '/base/overseas/editResearch',
    riskInvesmentManage: '/base/vc/editEmphasis',
    majorInnovationManage: '/base/creative/editEnterprise',
  };
  const url = map[pageName];
  return request({
    url: url,
    method: 'POST',
    data: query,
  });
}

// 批量删除接口
export function deleteInfo(query, pageName) {
  const map = {
    cityPlanInstitutions: '/base/plan/deleteInformation',
    incubatorsInstitutions: '/base/incubation/deleteInformation',
    innovationPlatformInstitutions: '/base/Innovate/deleteInformation',
    majorProgramInstitutions: '/base/major/deleteInformation',
    ledgerManage: '/base/account/deleteInformation',
    overseasManage: '/base/overseas/deleteResearch',
    riskInvesmentManage: '/base/vc/deleteEmphasis',
    majorInnovationManage: '/base/creative/deleteInformation',
  };
  const url = map[pageName];
  return request({
    url: url,
    method: 'POST',
    data: query,
  });
}

// 上传记录
export function getRecordList(query, pageName) {
  const map = {
    cityPlanInstitutions: 'plan_information',
    incubatorsInstitutions: 'incubation_information',
    innovationPlatformInstitutions: 'creative_platform',
    majorProgramInstitutions: 'major_project',
    ledgerManage: 'account',
    overseasManage: 'overseas_information',
    riskInvesmentManage: 'vc',
    majorInnovationManage: 'creative_platform',
    monthlyReportManagement: 'taxable_information',
    parkManage: 'park_information',
    enterpriseManage: 'enterprise_information',
    keyEenterManage: 'emphasis_information',
    independentManage: 'independent_information',
    companiesManage: 'company_information',
    technologyManagement: 'innovate',
    youngManagement: 'enterprise_name',
    talent: 'talent_information',
    institutionsManagement: 'research_information',
    pctManage: 'pct_apply',
    patentManage: 'InventionPatent',
    professionManage: 'ascertainment_situation',
    basicInformation: 'base_information',
    qualification: 'qualification_information',
    copyrights: 'software_copyright',
    trademarkInformation: 'trademark_information',
    patentInformation: 'patent_information',
    worksCopyright: 'copyright_information',
    listingBackupManagement: 'enterprise_listing_reserve',
    patentForInvention: 'enterprise_invention_patent',
    pctPatent: 'tkb_pct_patent',
    powerConsumption: 'tkb_qty_usage',
    tkbSafeDanger: 'tkb_safe_danger',
  };
  const obj = {
    recordType: map[pageName],
  };
  const data = Object.assign({}, query, obj);
  return request({
    url: `/base/uploadRecord/recordList`,
    method: 'POST',
    data: data,
  });
}

// 模板下载
export function downloadTemplate(query, pageName) {
  const map = {
    cityPlanInstitutions: '/base/plan/downloadTemplate',
    incubatorsInstitutions: '/base/incubation/downloadTemplate',
    innovationPlatformInstitutions: '/base/Innovate/downloadTemplate',
    majorProgramInstitutions: '/base/major/downloadTemplate',
    ledgerManage: '/base/account/downloadTemplate',
    overseasManage: '/base/overseas/downloadTemplate',
    riskInvesmentManage: '/base/vc/downloadTemplate',
    majorInnovationManage: '/base/creative/downloadTemplate',
    monthlyReportManagement: '/base/taxableMarket/downloadTemplate',
    parkManage: '/base/sciencePark/downloadTemplate',
    enterpriseManage: '/base/enterprise/downloadTemplate',
    keyEenterManage: '/base/emphasis/downloadTemplate',
    independentManage: '/base/independent/downloadTemplate',
    companiesManage: '/base/listed/downloadTemplate',
    technologyManagement: '/base/technology/downloadTemplate',
    youngManagement: '/base/enterpriseName/downloadTemplate',
    talent: '/base/talent/downloadTemplate',
    institutionsManagement: '/base/research/downloadTemplate',
    pctManage: '/base/pct/downloadTemplate',
    patentManage: '/base/patent/downloadTemplate',
    professionManage: '/base/littleGiant/downloadTemplate',
    basicInformation: '/import/enterprisebasic/downloadTemplate',
    qualification: '/import/qualified/downloadTemplate',
    copyrights: '/import/software/downloadTemplate',
    trademarkInformation: '/import/trademark/downloadTemplate',
    patentInformation: '/import/patent/downloadTemplate',
    worksCopyright: '/import/copyright/downloadTemplate',
    listingBackupManagement: '/enterprise/listing/reserve/template/export',
    patentForInvention: '/invention/patent/template/export',
    pctPatent: '/pct/patent/template/export',
    powerConsumption: '/qty/usage/template/export',
    tkbSafeDanger: '/danger/safe/template/export',
  };
  const url = map[pageName];
  return request({
    url: url,
    method: 'GET',
    headers: {
      'content-type': 'application/x-excel',
    },
    responseType: 'blob',
    params: query,
  });
}

// 文件上传
export function uploadFile(pageName) {
  const map = {
    cityPlanInstitutions: '/base/plan/importData',
    incubatorsInstitutions: '/base/incubation/importData',
    innovationPlatformInstitutions: '/base/Innovate/importData',
    majorProgramInstitutions: '/base/major/importData',
    ledgerManage: '/base/account/importData',
    overseasManage: '/base/overseas/importData',
    riskInvesmentManage: '/base/vc/importData',
    majorInnovationManage: '/base/creative/importData',
    parkManage: '/base/sciencePark/importData',
    youngManagement: '/base/enterpriseName/importData',
    enterpriseManage: '/base/enterprise/importData',
    keyEenterManage: '/base/emphasis/importData',
    independentManage: '/base/independent/importData',
    companiesManage: '/base/listed/excelAdd',
    technologyManagement: '/base/technology/importData',
    talent: '/base/talent/importData',
    institutionsManagement: '/base/research/importData',
    pctManage: '/base/pct/importData',
    patentManage: '/base/patent/importData',
    professionManage: '/base/littleGiant/excelAdd',
    basicInformation: '/import/enterprisebasic/importData',
    qualification: '/import/qualified/importData',
    copyrights: '/import/software/importData',
    trademarkInformation: '/import/trademark/importData',
    patentInformation: '/import/patent/importData',
    worksCopyright: '/import/copyright/importData',
    monthlyReportManagement: '/base/taxableMarket/excelAdd',
    enterpriseArchivesBatchImport: '/qcc/basic/enterprise/import',
    listingBackupManagement: '/enterprise/listing/reserve/import',
    patentForInvention: '/invention/patent/import',
    pctPatent: '/pct/patent/import',
    powerConsumption: '/qty/usage/import',
    accountNumberVerification: '/qcc/basic/enterprise/unifiedCreditCode',
    tkbSafeDanger: '/danger/safe/import',
  };
  const url = map[pageName];
  return url;
}

// 批量导出数据
export function downloadInformation(query, pageName) {
  const map = {
    cityPlanInstitutions: '/base/plan/downloadInformation',
    incubatorsInstitutions: '/base/incubation/downloadInformation',
    innovationPlatformInstitutions: '/base/Innovate/downloadInformation',
    majorProgramInstitutions: '/base/major/downloadInformation',
    ledgerManage: '/base/account/downloadInformation',
    overseasManage: '/base/overseas/downloadResearchInformation',
    riskInvesmentManage: '/base/vc/downloadInformation',
    majorInnovationManage: '/base/creative/downloadInformation',
    parkManage: '/base/sciencePark/downloadInformation',
    youngManagement: '/base/enterpriseName/downloadInformation',
    enterpriseManage: 'base/enterprise/downloadInformation',
    keyEenterManage: '/base/emphasis/downloadInformation',
    independentManage: '/base/independent/downloadInformation',
    companiesManage: '/base/listed/downloadInformation',
    monthlyReportManagement: '/base/taxableMarket/downloadInformation',
    technologyManagement: '/base/technology/export',
    talent: '/base/talent/downloadInformation',
    institutionsManagement: 'base/research/downloadInformation',
    pctManage: '/base/pct/downloadInformation',
    patentManage: '/base/patent/downloadInformation',
    professionManage: '/base/littleGiant/downloadInformation',
    enterpriseArchivesBatchImport: '/qcc/basic/enterprise/template/export',
    patentForInvention: '/invention/patent/export',
    pctPatent: '/pct/patent/export',
    powerConsumption: '/qty/usage/export',
    tkbSafeDanger: '/danger/safe/template/export',
  };
  const url = map[pageName];
  return request({
    url: url,
    method: 'POST',
    data: query,
    responseType: 'blob',
  });
}

// 批量导出数据
export function downloadAllInformation(query, pageName) {
  const map = {
    cityPlanInstitutions: '/base/plan/downloadAllInformation',
    incubatorsInstitutions: '/base/incubation/downloadAllInformation',
    innovationPlatformInstitutions: '/base/Innovate/downloadAllInformation',
    majorProgramInstitutions: '/base/Innovate/downloadAllInformation',
    ledgerManage: '/base/account/downloadAllInformation',
    overseasManage: '/base/overseas/downloadResearchInformation',
    riskInvesmentManage: '/base/vc/downloadAllInformation',
    majorInnovationManage: '/base/creative/downloadAllInformation',
    parkManage: '/base/sciencePark/downloadAllInformation',
    youngManagement: '/base/enterpriseName/downloadAllInformation',
    enterpriseManage: '/base/enterprise/downloadAllInformation',
    keyEenterManage: '/base/emphasis/downloadAllInformation',
    independentManage: '/base/independent/downloadAllInformation',
    companiesManage: '/base/listed/downloadAllInformation',
    monthlyReportManagement: '/base/taxableMarket/downloadAllInformation',
    technologyManagement: '/base/technology/downloadAllInformation',
    talent: '/base/talent/downloadAllInformation',
    institutionsManagement: 'base/research/downloadAllInformation',
    pctManage: '/base/pct/downloadAllInformation',
    patentManage: '/base/patent/downloadAllInformation',
    professionManage: '/base/profession/downloadAllInformation',
  };
  const url = map[pageName];
  return request({
    url: url,
    method: 'POST',
    data: query,
    responseType: 'blob',
  });
}
// 企业基本信息分页查询
export function pageInfoEnterprise(query) {
  return request({
    url: '/base/enterpriseInfo/pageEnterprise',
    method: 'post',
    data: query,
  });
}
// 修改企业基本信息分页查询
export function updateEnterprise(query) {
  return request({
    url: '/base/enterpriseInfo/updateEnterprise',
    method: 'post',
    data: query,
  });
}
// 新增企业基本信息分页查询
export function addEnterprise(query) {
  return request({
    url: '/base/enterpriseInfo/addEnterprise',
    method: 'post',
    data: query,
  });
}
// 删除企业基本信息分页查询
export function delEnterprise(query) {
  return request({
    url: '/base/enterpriseInfo/deleteEnterprise',
    method: 'post',
    data: query,
  });
}
// 导出企业基本信息分页查询
export function exportEnterprise(query) {
  return request({
    url: '/base/enterpriseInfo/export',
    method: 'post',
    data: query,
    responseType: 'blob',
  });
}
// 导出企业基本信息分页查询
export function queryEnterpriseById(query) {
  return request({
    url: `/base/enterpriseInfo/queryEnterpriseById/${query.id}`,
    method: 'get',
    data: query,
  });
}
// 企业模版导出
export function exportEnterpriseTemplate(query) {
  return request({
    url: '/qcc/basic/enterprise/template/export',
    method: 'post',
    data: query,
    responseType: 'blob',
  });
}
// 导入数据保存或更新
export function saveOrUpdateEnterprise(query) {
  return request({
    url: '/qcc/basic/enterprise/saveOrUpdate',
    method: 'post',
    data: query,
  });
}
// 导入数据失败 功能重试
export function pageReserveList(query) {
  return request({
    url: '/qcc/basic/enterprise/failed/retry',
    method: 'post',
    data: query,
  });
}
// 上市后备企业列表
export function retryEnterprise(query) {
  return request({
    url: '/enterprise/listing/reserve/list',
    method: 'post',
    data: query,
  });
}
// 新增编辑上市后备企业列表
export function addEditOReserveUpdate(query) {
  return request({
    url: '/enterprise/listing/reserve/addOrUpdate',
    method: 'post',
    data: query,
  });
}
// 删除上市后备企业列表
export function delReserve(query) {
  return request({
    url: '/enterprise/listing/reserve/delete',
    method: 'post',
    data: query,
  });
}
// 删除上市后备企业列表
export function exportReserve(query) {
  return request({
    url: '/enterprise/listing/reserve/template/export',
    method: 'get',
    data: query,
    responseType: 'blob',
  });
}
// 导出
export function exportReserveList(query) {
  return request({
    url: '/enterprise/listing/reserve/export',
    method: 'POST',
    data: query,
    responseType: 'blob',
  });
}
// 企业发明专利列表
export function getPatentList(query) {
  return request({
    url: '/invention/patent/list',
    method: 'post',
    data: query,
  });
}
// 删除企业发明专利列表
export function delPatentList(query) {
  return request({
    url: '/invention/patent/delete',
    method: 'post',
    data: query,
  });
}
// pct发明专利
export function getPctPatentList(query) {
  return request({
    url: '/pct/patent/list',
    method: 'post',
    data: query,
  });
}
// 删除pct发明专利
export function delPctPatentList(query) {
  return request({
    url: '/pct/patent/delete',
    method: 'post',
    data: query,
  });
}
// 用电量查询列表
export function getPowerPatentList(query) {
  return request({
    url: '/qty/usage/list',
    method: 'post',
    data: query,
  });
}
// 用电量物理删除
export function delPowerPatentList(query) {
  return request({
    url: '/qty/usage/delete',
    method: 'post',
    data: query,
  });
}
// 同步数据分页
export function syncLogList(query) {
  return request({
    url: '/sync/log/list',
    method: 'post',
    data: query,
  });
}
// 手动触发同步档案信息操作
export function syncLogExecute(query) {
  return request({
    url: '/sync/log/execute',
    method: 'post',
    data: query,
  });
}
// 同步刷新最新信息
export function refreshLogExecute(query) {
  return request({
    url: '/sync/refresh',
    method: 'get',
    data: query,
  });
}
