<template>
  <div>
    <div class="file-type-item">
      <img :src="FileImgs[fileType]" alt="" />
      <div>
        <div class="item-file-name">{{ item.fileName }}</div>
        <span
          v-if="fileType == 'png' || fileType == 'jpg' || fileType == 'gif'"
          class="preview"
          @click="handlePreview()"
          >预览</span
        >
        <span
          v-if="fileType == 'png' || fileType == 'jpg' || fileType == 'gif'"
          class="link"
          @click="urlDownLoad"
          >下载</span
        >
        <a
          v-else
          class="link"
          target="_blank"
          :href="url"
          :download="getFileName(url)"
          >下载
        </a>
      </div>
    </div>
    <a-modal
      :visible="previewVisible"
      :footer="null"
      @cancel="handleCancel"
      :width="1200"
    >
      <img alt="example" style="width: 100%" :src="previewImage" />
      <pdf v-if="fileType == 'pdf'" ref="pdfdom" :url="previewImage"></pdf>
    </a-modal>
  </div>
</template>

<script>
import pdf from '@/components/filePreview/pdf.vue';
import { urlDownLoad } from '@/utils/common/util.js';
import FileImgs from '@/assets/config/art_detail.js';
import { getFileName, getFileType } from '@/utils/index';
export default {
  name: 'FileTypeItem',
  components: { pdf },
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      FileImgs, //文件类型图片们
      previewVisible: false,
      previewImage: '',
      fileType: '', //png.gif
      url: '',
      getFileName,
      getFileType,
    };
  },
  beforeCreate() {},
  mounted() {
    console.log(this.item, 'item,');
    this.imgTypeHandle();
  },

  methods: {
    handlePreview() {
      this.previewVisible = true;
      this.previewImage = this.url;
    },
    handleCancel() {
      this.previewVisible = false;
    },
    urlDownLoad() {
      let fileName = getFileName(this.url);
      urlDownLoad(this.url, fileName);
    },
    //处理fileType
    imgTypeHandle() {
      this.url = this.baseImgUrl + this.item.fileUrl;
      this.fileType = getFileType(this.url);
      console.log(this.url, this.fileType, '00000');
    },
  },
};
</script>

<style lang="less" scoped>
.file-type-item {
  display: flex;
  .item-file-name {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: #333333;
  }
  img {
    margin-right: 16px;
    width: 40px;
    height: 40px;
  }
  span {
    font-size: 12px;
    font-weight: normal;
    line-height: 18px;
    color: #1890ff;
  }
  .preview {
    margin-right: 16px;
    cursor: pointer;
  }
  .link {
    font-size: 12px;
    color: #1890ff;
    cursor: pointer;
  }
}
.article-right-main-item:not(:last-child) {
  margin-bottom: 18px;
}
/deep/.ant-modal-close {
  top: -12px;
  right: -12px;
}
</style>
