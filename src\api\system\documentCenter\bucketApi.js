import request from '@/utils/request';
/**
 * 1 bucket数据管理
 *
 */
// 1、查询bucket配置列表
export function documentBucketList(query) {
  return request({
    url: `/api/authority/admin/upload/config/list`,
    method: 'GET',
    params: query,
  });
}

// 2、新增bucket配置
export function documentBucketAdd(data) {
  return request({
    url: `/api/authority/admin/upload/config/add`,
    method: 'POST',
    data: data,
  });
}
// 3、编辑bucket配置信息
export function documentBucketEdit(data) {
  return request({
    url: `/api/authority/admin/upload/config/edit`,
    method: 'POST',
    data: data,
  });
}
// 4、设为默认配置
export function documentBucketAsDefault(configId) {
  return request({
    url: `/api/authority/admin/upload/config/asDefault/${configId}`,
    method: 'GET',
  });
}
// 5、切换状态
export function documentBucketChangeStatus(data) {
  return request({
    url: `/api/authority/admin/upload/config/changeStatus`,
    method: 'POST',
    data: data,
  });
}
// 6、移除bucket配置信息
export function documentBucketRemove(configId) {
  return request({
    url: `/api/authority/admin/upload/config/remove/${configId}`,
    method: 'GET',
  });
}
