import request from '@/utils/request';
/**
 * 1 bucket数据管理
 *
 */

// 1、批量上传文件
export function documentBatchUpload(data) {
  return request({
    url: `/api/authority/admin/upload/batch`,
    method: 'POST',
    data: data,
  });
}
// 2、查询列表
export function documentManageList(query) {
  return request({
    url: `/api/authority/admin/upload/record/manage/list`,
    method: 'GET',
    params: query,
  });
}
// 3、删除
export function documentManageRemove(recordId) {
  return request({
    url: `/api/authority/admin/upload/record/manage/remove/${recordId}`,
    method: 'GET',
  });
}
// 4、批量删除
export function documentManageRemoveBatch(data) {
  return request({
    url: `/api/authority/admin/upload/record/manage/removeBatch`,
    method: 'POST',
    data,
  });
}
