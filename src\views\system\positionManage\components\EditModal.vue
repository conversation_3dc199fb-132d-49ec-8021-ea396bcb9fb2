<template>
  <!-- 添加或修改菜单对话框 -->
  <a-modal
    :title="`${positionId ? '编辑' : '新增'}岗位`"
    :visible="visible"
    :loading="loading"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
    width="700px"
    @ok="submitForm"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="form" :rules="rules" v-bind="formLayout">
        <a-form-model-item label="租户名称" prop="menuType">
          {{ merchantName }}
        </a-form-model-item>
        <a-form-model-item label="所属组织" prop="organizeId">
          <treeselect
            style="line-height: 35px"
            v-model="form.organizeId"
            :options="treeSelect"
            placeholder="请选择所属组织"
            :default-expand-level="10"
          />
        </a-form-model-item>
        <a-form-model-item label="岗位名称" prop="positionName">
          <a-input
            v-model="form.positionName"
            :max-length="20"
            placeholder="请输入岗位名称"
          >
          </a-input>
        </a-form-model-item>
        <a-form-model-item label="岗位描述" prop="remark">
          <a-input
            type="textarea"
            v-model="form.remark"
            :max-length="200"
            placeholder="请输入岗位描述"
          >
          </a-input>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import {
  addPoition,
  updatePosition,
  getPoitionDetail,
} from '@/api/system/position';
import { getTreeList } from '@/api/system/organization';
import { recursionDataSlide } from '@/utils';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { initFormData, formLayout, rules } from '../constant.js';

export default {
  components: {
    Treeselect,
  },
  props: {
    visible: Boolean,
    organizeId: String,
    positionId: String,
    treeSelect: Array,
  },
  data() {
    return {
      loading: false,
      formLayout,
      rules,
      form: initFormData({
        organizeId: this.organizeId,
      }),
    };
  },
  watch: {
    visible(val) {
      if (val) {
        // 获取组织架构树

        this.getTreeOrganization();
        // 初始化数据
        this.form = initFormData({
          organizeId: this.organizeId,
        });
        // 如果存在数据--》编辑
        if (this.positionId) {
          this.getDetail();
        }
      } else {
        this.$refs.form && this.$refs.form.resetFields();
      }
    },
  },
  computed: {
    merchantId() {
      return this.$store.state?.base?.merchant?.merchantId;
    },
    merchantName() {
      return this.$store.state?.base?.merchant?.merchantName;
    },
  },
  methods: {
    /**
     *  🌲获取组织架构树🌲
     */
    async getTreeOrganization() {
      this.treeLoading = true;
      const [result, error] = await getTreeList({
        merchantId: this.merchantId,
      });
      this.treeLoading = false;
      if (error) return;
      const data = result?.data?.length ? recursionDataSlide(result.data) : [];

      this.selectedKeys = [data[0].id];
      this.showTree = true;
      this.organizationTree = data;
    },
    /**
     * 获取详情
     */
    async getDetail() {
      this.loading = true;
      const [result, error] = await getPoitionDetail(this.positionId);
      this.loading = false;
      if (error) return;
      this.form = {
        ...this.form,
        ...(result?.data || {}),
      };
    },
    /**
     * 提交按钮
     */
    submitForm() {
      if (this.loading) return;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const queryParams = {
            ...this.form,
            merchantId: this.merchantId,
          };
          const [, error] = this.form.positionId
            ? await updatePosition(queryParams)
            : await addPoition(queryParams);
          this.loading = false;
          if (error) return;
          this.$message.success(`${this.positionId ? '编辑' : '新增'}岗位成功`);
          this.$emit('ok');
          this.closeModal();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
