<!DOCTYPE html>
<html lang="en" class="beauty-scroll">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= process.env.VUE_APP_NAME %></title>
    <!-- require cdn assets css -->
    <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.css) { %>
      <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" />
    <% } %>
  </head> 

  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <% if (process.env.VUE_APP_PROJECT && process.env.VUE_APP_BZT_VERSION) { %>
      <script>
        !(function(win,b,d,a){win[a]||(win[a]={});win[a].config={
          project: '<%= process.env.VUE_APP_PROJECT %>',
        };with(b){with(body){with(insertBefore(createElement("script"),firstChild)){setAttribute("crossorigin","",src=d)}}}})(window,document,"https://oss-static.bangdao-tech.com/biz-track/<%= process.env.VUE_APP_BZT_VERSION %>/biz-track.min.js","_bzt");
      </script>
    <% } %>
    <div id="popContainer" class="beauty-scroll" style="height: 100vh; overflow-y: scroll">
      <div id="app"></div>
    </div>
    <!-- require cdn assets js -->
    <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.js) { %>
      <script type="text/javascript" src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
    <% } %>
    <!-- built files will be auto injected -->
  </body>
</html>
