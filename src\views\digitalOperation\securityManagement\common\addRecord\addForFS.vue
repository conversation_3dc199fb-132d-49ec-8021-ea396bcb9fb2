<template>
  <div class="add-record-container">
    <DynamicForm
      ref="dynamicForm"
      :defaultColSpan="8"
      :config="formConfig"
      :params="params"
    >
      <template slot="uploadFile" slot-scope="{ params }">
        <!-- <Upload
          v-model="params.attachment"
          :listType="'text'"
          :showUploadList="true"
          @setNewsImg="setNewsImg"
          :maxSize="4"
        /> -->
        <div class="file">
          <uploadFiles
            :disabled="disabled"
            v-model="params.attachment"
            :accept="'.png, .pdf, .jpg, .word, .excel'"
            @setAnnexList="setAnnexList"
            :showDefaultUploadList="true"
            :maxCount="5"
            :fileListTemp="params.attachment"
            :maxSize="4"
          ></uploadFiles>
        </div>
      </template>
    </DynamicForm>
    <vxe-grid
      ref="vxeRef"
      :autoResize="true"
      :columns="tableColumns"
      :data="tableData"
    >
      <template slot="checkResult" slot-scope="{ row }">
        <a-radio-group
          :disabled="isViewAndTag"
          v-model="row.checkSituation"
          :default-value="row.checkSituation"
        >
          <a-radio value="1"> 是 </a-radio>
          <a-radio value="0"> 否 </a-radio>
        </a-radio-group>
      </template>
      <template slot="remark" slot-scope="{ row }">
        <a-textarea :disabled="isViewAndTag" v-model="row.remark"></a-textarea>
      </template>
      <template
        slot="attachments"
        slot-scope="{ row }"
        v-if="row.checkSituation == '0'"
      >
        <uploadPics
          :disabled="isViewAndTag"
          v-model="row.attachments"
          :accept="'.png, .jpg'"
          :maxCount="1"
          @setNewsImgs="(fileLists) => setNewsImg(fileLists, row)"
          :fileListTemp="row.attachments"
          :maxSize="10"
        />
      </template>

      <template slot="operation" slot-scope="{ row }">
        <span
          class="edit"
          @click="editHandle(row)"
          v-if="row.tag !== '0' && !isViewAndTag"
          >编辑</span
        >
      </template>
    </vxe-grid>
    <a-row type="flex" justify="end">
      <a-col :span="6">
        <a-button :disabled="isViewAndTag" @click="addHandle"
          >列表维护</a-button
        >
        <a-button
          :disabled="isViewAndTag"
          type="primary"
          class="submit-btn"
          @click="submitHandler"
          >保存</a-button
        >
        <a-button @click="goBack" style="margin-left: 10px"> 返回 </a-button>
      </a-col>
    </a-row>
    <addListForFS
      v-if="isVisible"
      :isVisible="isVisible"
      @cancelModal="cancelModal"
      :rowData="editRowData"
      :addOrEdit="addOrEdit"
      :serialNumber="serialNumber"
    />
  </div>
</template>

<script>
import uploadPics from '@/components/Uploads/uploadPics.vue';
import uploadFiles from '@/components/Uploads/uploadFilesNew.vue';
import addListForFS from '../addList/addListForFS.vue';
import {
  randerImgs,
  imgToStringHandle,
  oneImgsHandle,
  oneImgsRander,
} from '@/utils/index';
import { getTableColumn } from './addForFS.js';
import {
  fireInfo,
  addOrUpdate,
} from '@/api/digitalOperation/securityManagement/parkSafety/fireSafety.js';
import { getExcelData } from '@/api/digitalOperation/securityManagement/parkSafety/industrialEnterprise';
export default {
  name: 'ManagementTkAddForFS',
  components: {
    uploadPics,
    uploadFiles,
    addListForFS,
  },

  data() {
    return {
      checkResult: true,
      tableData: null,
      params: {
        reviewedOrganizeId: undefined,
        reviewedOrganizeName: undefined,
        inspectTime: undefined,
        address: undefined,
        inspectPrincipalName: undefined,
        inspectName: undefined,
        // checkResult: undefined,
        reviewTime: undefined,
        reviewResult: undefined,
        attachment: [],
        principalName: undefined,
        principalPhone: undefined,
        inspectionResult: undefined,
      },
      isViewAndTag: false,
      userAccountFilterList: [],
      row: '',
      disabled: false,
      isVisible: false,
      editRowData: {},
      addOrEdit: '',
      serialNumber: '',
    };
  },

  computed: {
    formConfig() {
      return [
        {
          title: '被检查单位',
          field: 'reviewedOrganizeName',
          // defaultValue: this.params.reviewedOrganizeName,
          rules: [
            {
              required: true,
              message: '请输入被检查单位',
            },
          ],
          props: {
            disabled: true,
          },
        },
        {
          title: '检查时间',
          field: 'inspectTime',
          element: 'a-date-picker',
          rules: [
            {
              required: true,
              message: '请输入检查时间',
            },
          ],
          props: {
            disabled: this.disabled,
          },
        },
        {
          title: '地址',
          field: 'address',
          rules: [
            {
              required: true,
              message: '请输入地址',
            },
          ],
          props: {
            disabled: this.disabled,
          },
        },
        {
          title: '责任人',
          field: 'principalName',
          rules: [
            {
              required: true,
              message: '请输入责任人',
            },
          ],
          props: {
            disabled: this.disabled,
          },
        },
        {
          title: '责任人电话',
          field: 'principalPhone',
          rules: [
            {
              required: true,
              message: '请输入责任人电话',
            },
          ],
          props: {
            disabled: this.disabled,
          },
        },
        {
          title: '检查人员',
          field: 'inspectName',
          rules: [
            {
              required: true,
              message: '请输入检查人员',
            },
          ],
          props: {
            disabled: this.disabled,
          },
        },
        {
          title: '检查单位负责人',
          field: 'inspectPrincipalName',
          props: {
            disabled: this.disabled,
          },
          rules: [
            {
              required: true,
              message: '请输入检查单位负责人',
            },
          ],
          colProps: {
            span: 10,
          },
        },
        {
          title: '检查结果',
          field: 'inspectionResult',
          element: 'a-radio-group',
          defaultValue: this.params.inspectionResult,
          rules: [
            {
              required: true,
              message: '请选择检查结果',
            },
          ],
          props: {
            disabled: this.disabled,
            options: [
              { label: '合格', value: '1' },
              { label: '不合格', value: '0' },
            ],
          },
          on: {
            change: (value) => {
              this.checkResult = value === '1' ? true : false;
            },
          },
        },
        {
          title: '复查时间',
          field: 'reviewTime',
          element: 'a-date-picker',
          show: !this.checkResult,
          rules: [
            {
              required: !this.checkResult ? true : false,
              message: '请选择复查时间',
            },
          ],
          props: {
            disabled: this.disabled,
          },
        },
        {
          title: '复查结果',
          field: 'reviewResult',
          element: 'a-radio-group',
          defaultValue: this.params.reviewResult,
          rules: [
            {
              required: !this.checkResult ? true : false,
              message: '请选择复查结果',
            },
          ],
          props: {
            disabled: this.disabled,
            options: [
              { label: '合格', value: '1' },
              { label: '不合格', value: '0' },
            ],
          },
          show: !this.checkResult,
        },
        {
          title: '上传附件',
          field: 'attachment',
          element: 'slot',
          slotName: 'uploadFile',
          props: {
            disabled: this.disabled,
          },
          colProps: {
            span: 24,
          },
          itemProps: {
            labelCol: {
              span: 2,
            },
            wrapperCol: { span: 10 },
          },
        },
      ];
    },
  },
  created() {
    this.tableColumns = getTableColumn.call(this);
    this.row = JSON.parse(localStorage.getItem('addForFSInfo'));
  },

  mounted() {
    this.getBaseInfo();
    this.rowHandel();
  },

  methods: {
    //查看详情
    async fireInfo() {
      const [res, err] = await fireInfo(this.$route.query.id);
      if (err) return;
      let type = this.$route.query.type;
      var mergedArray = this.tableData.map(function (item1) {
        var item2 = res.data.find(function (item2) {
          return item1.id == item2.industrialEnterpriseExcelId;
        });
        //将查询到的两个表拼接在一起
        if (item2) {
          //查看详情时展示右半边数据
          if (type == 'view') {
            return Object.assign({}, item1, item2);
          } else {
            //新增时只取对应的id值
            return Object.assign({}, item1, {
              checkSituation: null,
              findTheProblem: null,
              industrialEnterpriseExcelId: item2.industrialEnterpriseExcelId,
              industrialEnterpriseId: item2.industrialEnterpriseId,
              rectifyComments: null,
            });
          }
        } else {
          return item1;
        }
      });
      this.tableData = mergedArray;
      this.tableData.forEach((item) => {
        item.attachments = oneImgsRander(item.attachments);
      });
      this.serialNumber =
        Number(this.tableData[this.tableData.length - 1].serialNumber) + 1;
    },
    //单个图片
    setNewsImg(fileList, row) {
      console.log('单个图片', fileList);
      row.attachments = fileList;
    },
    addHandle() {
      this.editRowData = {};
      this.addOrEdit = 'add';
      this.isVisible = true;
    },
    //编辑
    editHandle(row) {
      this.editRowData = { ...row };
      console.log('this.editRowData', this.editRowData);
      this.addOrEdit = 'edit';
      this.isVisible = true;
    },
    //保存
    async addOrUpdate(p) {
      const [, err] = await addOrUpdate(p);
      if (err) return this.$message.error('保存失败');
      this.$message.success('保存成功');
      this.goBack();
    },
    goBack() {
      this.$router.push({
        name: 'parkSecurity',
        query: {
          type: 'FS',
        },
      });
    },
    cancelModal(type) {
      type == 'refresh' ? this.getBaseInfo() : '';
      this.isVisible = false;
    },
    rowHandel() {
      console.log(this.row, 'this.row');
      const {
        parkId,
        parkName,
        reviewedOrganizeId,
        reviewedOrganizeName,
        inspectTime,
        attachments,
        address,
        inspectPrincipalName,
        inspectName,
        reviewTime,
        reviewResult,
        attachment,
        principalName,
        principalPhone,
        inspectionResult,
      } = this.row;
      this.params.parkId = parkId;
      this.params.parkName = parkName;
      this.params.reviewedOrganizeId = reviewedOrganizeId;
      this.params.reviewedOrganizeName = reviewedOrganizeName;
      if (this.$route.query.type == 'view') {
        this.params = {
          reviewedOrganizeId,
          reviewedOrganizeName,
          inspectTime,
          attachments,
          address,
          inspectPrincipalName,
          inspectName,
          reviewTime,
          reviewResult,
          attachment,
          principalName,
          principalPhone,
          inspectionResult,
        };
        this.params.attachment = randerImgs(attachments);
        this.disabled = true;
        if (inspectionResult == '0') {
          this.checkResult = false;
        }
        this.row.tag !== '0'
          ? (this.isViewAndTag = true)
          : (this.isViewAndTag = false); //标识  0:A端录入   1:B端录入
      }
    },
    getBaseInfo() {
      getExcelData({ businessFlag: 2 }).then(([res, err]) => {
        if (err) {
          return;
        }
        this.tableData = res.data;
        this.fireInfo();
      });
    },
    setAnnexList(fileList) {
      this.params.attachment = fileList;
    },
    submitHandler() {
      this.$refs.dynamicForm.validate((result) => {
        if (!result) {
          this.$message.error('请先填写完整表单');
          return;
        }
        let flag = true;
        //获取表格数据和表单数据
        console.log(this.params, 'this.params');
        console.log(this.tableData, 'this.tableData');
        let fireInspectionList = this.tableData.map((item, index) => {
          if (item.checkSituation == '0') {
            if (!item.attachments.length) {
              flag = false;
              this.$message.error(`请上传列表第${index + 1}行的图片`);
              return;
            }
          }
          return {
            checkSituation: item.checkSituation,
            industrialEnterpriseExcelId: item.id,
            remark: item.remark,
            attachments: oneImgsHandle(item.attachments),
          };
        });
        if (!flag) return;
        const { type, id } = this.$route.query;
        let p = {
          id: type == 'add' && this.row.inspectTime ? undefined : id,
          ...this.params,
          reviewTime:
            type === 'add'
              ? this.params.reviewTime?.format('YYYY-MM-DD HH:mm:ss')
              : this.params.reviewTime,
          inspectTime:
            type == 'add'
              ? this.params.inspectTime.format('YYYY-MM-DD HH:mm:ss')
              : this.params.inspectTime,
          attachment: imgToStringHandle(this.params.attachment),
          fireInspectionList,
        };
        console.log(p, 'ppppp');
        this.addOrUpdate(p);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.add-record-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  .submit-btn {
    margin-left: 12px;
    margin-top: 24px;
  }
  .edit {
    color: #165dff;
    cursor: pointer;
  }
}
.file {
  display: flex;
  .item {
    margin-left: 10px;
  }
  /deep/.ant-upload-list {
    display: flex;
    flex-wrap: wrap;
  }
  /deep/.ant-upload-list-picture .ant-upload-list-item,
  /deep/.ant-upload-list-picture-card .ant-upload-list-item {
    height: auto;
    border: none;
  }
  /deep/.ant-upload-list-item-card-actions.picture {
    right: -8px;
  }
  /deep/.ant-form-horizontal > span {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  /deep/ .ant-upload-list-item-thumbnail {
    display: none;
  }
  /deep/.ant-upload-list-picture .ant-upload-list-item-name,
  /deep/.ant-upload-list-picture-card .ant-upload-list-item-name {
    padding-left: 0;
  }
}
</style>
