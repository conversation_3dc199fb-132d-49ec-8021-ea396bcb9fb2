import { renderTaskStates } from '../utils/index';

function getTableColumn() {
  const _this = this;
  return [
    {
      type: 'checkbox',
    },
    {
      title: '任务名称',
      field: 'taskName',
    },
    {
      title: '任务内容',
      field: 'content',
    },
    {
      title: '任务类型',
      field: 'taskType',
      formatter({ row }) {
        //1:活动请示 2:总结报告 3:合同 4:调研报告 5:数据表格
        const taskType = _this.taskTypes || {
          1: '活动请示',
          2: '总结报告',
          3: '合同',
          4: '调研报告',
          5: '数据表格',
        };
        return taskType[row.taskType] || '其他';
      },
    },
    {
      title: '阅读状态',
      field: 'ifRead',
      formatter({ cellValue }) {
        return cellValue == '1' ? '已读' : '未读';
      },
    },
    {
      title: '办理人',
      field: 'transactorName',
    },
    {
      title: '创建人',
      field: 'creatorName',
    },
    {
      title: '任务状态',
      field: 'taskStatus',
      slots: {
        default(row) {
          const result = renderTaskStates.call(this, row);
          if (typeof result === 'string') return result;
          return (
            <span style={{ color: result.colorValue }}>{result.text}</span>
          );
        },
      },
    },
    {
      title: '办理时限',
      field: 'transactorTime',
    },
    {
      title: '创建时间',
      field: 'createTime',
    },
    {
      title: '完成时间',
      field: 'finishTime',
    },
  ];
}

export { getTableColumn };
