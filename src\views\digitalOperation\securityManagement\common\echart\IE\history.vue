<template>
  <div class="history-echart-box">
    <h2>历史每月检查情况</h2>
    <BaseChart
      class="chart-box"
      v-if="historyResultData.axisData.length > 0"
      :axisData="historyResultData.axisData"
      :seriesData="historyResultData.seriesData"
      :yAxis="yAxis"
      :tooltipFormatter="tooltipFormatter"
      unit="符合率"
    />
    <echartEmptyBox v-else />
  </div>
</template>

<script>
import echartEmptyBox from '@/components/echartEmptyBox/index.vue';
import BaseChart from '@/components/chart/lineChart.vue';
import { getTemplateBase } from '../echart';
// import { historyResultMock } from '../mockData';
import { industryMonthly } from '@/api/digitalOperation/securityManagement/parkSafety/echart/IE.js';
export default {
  name: 'ManagementTkActions',
  components: {
    BaseChart,
    echartEmptyBox,
  },

  data() {
    return {
      historyResultData: {
        axisData: [],
        seriesData: [
          {
            name: '当月符合率',
            type: 'line',
            data: [],
          },
        ],
      },
    };
  },
  created() {
    this.yAxis = {
      type: 'value',
      nameGap: 40,
      nameTextStyle: {
        // 字体样式
        padding: [0, -60, 0, 0],
        color: 'rgba(0,0,0,0.45)',
        fontSize: 14, // 字体大小
      },
      scale: true,
      axisLabel: {
        formatter: '{value}%',
      },
    };
  },

  mounted() {
    this.getHistoryData();
  },

  methods: {
    async getHistoryData() {
      const [res, err] = await industryMonthly();
      if (err) return;
      let arr = [];
      res.data.forEach((item) => {
        arr.push({
          date: item.month,
          value: item.coincidenceRate,
          unit: item.unitNums,
        });
      });
      this.mockDataForRanking = arr;

      setTimeout(() => {
        this.historyResult = arr;
        this.historyResultData.axisData = arr.map((item) => {
          return item.date;
        });
        this.historyResultData.seriesData[0].data = arr.map((item) => {
          return item.value;
        });
      }, 500);
      //TODO:获取近期和历史符合率数据
    },
    tooltipFormatter(info) {
      let str = `<div style="text-align: left; color:#1D2129;" >${info[0].name}</div>`;
      info.forEach((item) => {
        str += getTemplateBase(item.marker, item.seriesName, item.value + '%');
      });
      const itemInfo = this.historyResult.filter(
        (item) => item.date === info[0].name
      )[0];
      str += getTemplateBase('', '检查单位数量', itemInfo.unit);
      return str;
    },
  },
};
</script>

<style lang="less" scoped>
.history-echart-box {
  margin: 40px 16px 16px 16px;
}
</style>
