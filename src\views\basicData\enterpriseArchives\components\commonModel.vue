<template>
  <div class="">
    <DynamicForm
      :preview="preview"
      labelAlign="right"
      ref="baseForm"
      :params="params"
      :config="baseConfig"
    >
      <template slot="latLng">
        <a-input-group size="large">
          <a-row :gutter="2">
            <a-col :span="12">
              <a-input-number
                placeholder="请输入经度"
                style="width: 100%"
                :disabled="preview"
                :step="0.000001"
                :max="1000"
                :min="-1000"
                v-model="params.latitude"
              />
            </a-col>
            <a-col :span="12">
              <a-input-number
                placeholder="请输入纬度"
                style="width: 100%"
                :disabled="preview"
                :max="1000"
                :min="-1000"
                :step="0.000001"
                v-model="params.longitude"
              />
            </a-col>
          </a-row>
        </a-input-group>
      </template>
      <template slot="previewLabel">
        <div class="tag-wrap">
          <a-tag v-for="(item, index) in params.labelId" :key="index">{{
            getLabel(item)
          }}</a-tag>
        </div>
      </template>
    </DynamicForm>
  </div>
</template>

<script>
import { initParams, filterOption } from '@/utils';
import { queryEnterpriseById } from '@/api/basicData/index.js';
import moment from 'moment';
export default {
  props: {
    operationType: {
      type: String,
      default: '',
    },
    dataDict: {
      type: Object,
    },
    rowData: {
      type: Object,
    },
    parkList: {
      type: Array,
      default: () => [],
    },
    labelList: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
  data() {
    return {
      params: {},
    };
  },
  computed: {
    preview() {
      return this.operationType == 'VIEW';
    },
    baseConfig() {
      const showTime = ['03', '04'];
      if (this.operationType == 'VIEW') {
        return [
          {
            field: 'name',
            title: '企业名称',
            props: {
              placeholder: '请输入企业名称',
              maxLength: 30,
            },
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              placeholder: '请输入统一社会信用代码',
              maxLength: 30,
            },
          },
          {
            field: 'address',
            title: '企业地址',
            props: {
              placeholder: '请输入企业地址',
              maxLength: 50,
            },
          },
          {
            field: 'status',
            title: '登记状态',
            element: 'a-select',
            props: {
              placeholder: '请选择登记状态',
              options: this.dataDict.enterprise_register_status,
            },
            previewFormatter: () => {
              return this.dataDict.enterprise_register_status?.find(
                (q) => q.value == this.params.status
              )?.label;
            },
          },
          ...(showTime.includes(this.params.status)
            ? [
                {
                  field: 'endTime',
                  title: '时间',
                  element: 'a-date-picker',
                  props: {
                    placeholder: '请选择时间',
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD',
                  },
                  rules: [{ required: true, message: '请选择时间' }],
                },
              ]
            : []),
          {
            field: 'parkId',
            title: '所属园区',
            element: 'a-select',
            props: {
              placeholder: '请选择所属园区',
              options: this.parkList,
              showSearch: true,
              filterOption: filterOption,
            },
            previewFormatter: () => {
              return this.parkList?.find((q) => q.value == this.params.parkId)
                ?.label;
            },
          },
          {
            title: '位置经纬度',
            field: 'A',
            element: 'slot',
            slotName: 'latLng',
            props: {
              placeholder: '请输入位置经纬度',
              maxLength: 20,
            },
            previewFormatter: () => {
              return `${this.params?.latitude || ''} , ${
                this.params?.longitude || ''
              }`;
            },
          },
          {
            field: 'regulate',
            title: '是否规上',
            element: 'a-select',
            props: {
              placeholder: '请选择是否规上',
              options: this.dataDict.enterprise_regulate_status,
              showSearch: true,
              filterOption: filterOption,
            },
            previewFormatter: () => {
              return this.dataDict.enterprise_regulate_status?.find(
                (q) => q.value == this.params.regulate
              )?.label;
            },
          },
          {
            field: 'nature',
            title: '企业性质',
            element: 'a-select',
            props: {
              placeholder: '请选择企业性质',
              options: this.dataDict.enterprise_nature_status,
              showSearch: true,
              filterOption: filterOption,
            },
            previewFormatter: () => {
              return this.dataDict.enterprise_nature_status?.find(
                (q) => q.value == this.params.nature
              )?.label;
            },
          },
          {
            field: 'labelId',
            title: '企业标签',
            element: 'a-select',
            defaultValue: [],
            props: {
              placeholder: '请选择企业标签',
              options: this.labelList,
              mode: 'multiple',
              maxTagCount: 2,
              maxTagTextLength: 4,
              showSearch: true,
              filterOption: filterOption,
            },
            previewSlot: 'previewLabel',
          },
          {
            field: 'synopsis',
            title: '企业简介',
            props: {
              placeholder: '请输入企业简介',
              maxLength: 30,
            },
            element: 'a-textarea',
            colProps: { span: 24 },
            // itemProps: {
            //   labelCol: {
            //     span: 2,
            //   },
            //   wrapperCol: {
            //     span: 18,
            //   },
            // },
          },
          {
            field: 'legalPerson',
            title: '法定代表人',
            props: {
              placeholder: '请输入法定代表人',
              maxLength: 30,
            },
          },
          {
            field: 'registerCapital',
            title: '注册资本',
            props: {
              placeholder: '请输入注册资本',
              maxLength: 30,
            },
          },
          {
            field: 'realCapital',
            title: '实缴资本',
            props: {
              placeholder: '请输入实缴资本',
              maxLength: 30,
            },
          },
          {
            field: 'registerDate',
            title: '成立日期',
            props: {
              placeholder: '请输入成立日期',
              maxLength: 30,
            },
          },
          {
            field: 'province',
            title: '所属省份',
            props: {
              placeholder: '请输入所属省份',
              maxLength: 30,
            },
          },
          {
            field: 'city',
            title: '所属城市',
            props: {
              placeholder: '请输入所属城市',
              maxLength: 30,
            },
          },
          {
            field: 'area',
            title: '所属区县',
            props: {
              placeholder: '请输入所属区县',
              maxLength: 30,
            },
          },
          {
            field: 'phone',
            title: '电话',
            props: {
              placeholder: '请输入电话',
              maxLength: 30,
            },
          },
          {
            field: 'otherPhone',
            title: '更多电话',
            props: {
              placeholder: '请输入更多电话',
              maxLength: 30,
            },
          },
          {
            field: 'email',
            title: '邮箱',
            props: {
              placeholder: '请输入邮箱',
              maxLength: 30,
            },
          },
          {
            field: 'otherEmail',
            title: '更多邮箱',
            props: {
              placeholder: '请输入更多邮箱',
              maxLength: 30,
            },
          },
          {
            field: 'enterpriseType',
            title: '企业(机构)类型',
            props: {
              placeholder: '请输入企业(机构)类型',
              maxLength: 30,
            },
          },
          {
            field: 'identNum',
            title: '纳税人识别号',
            props: {
              placeholder: '请输入纳税人识别号',
              maxLength: 30,
            },
          },
          {
            field: 'businessRegisNum',
            title: '注册号',
            props: {
              placeholder: '请输入注册号',
              maxLength: 30,
            },
          },
          {
            field: 'oibCode',
            title: '组织机构代码',
            props: {
              placeholder: '请输入组织机构代码',
              maxLength: 30,
            },
          },
          {
            field: 'insuredNum',
            title: '参保人数',
            props: {
              placeholder: '请输入参保人数',
              maxLength: 30,
            },
          },
          {
            field: 'reportInsuredNum',
            title: '参保人数所属年报',
            props: {
              placeholder: '请输入参保人数所属年报',
              maxLength: 30,
            },
          },
          {
            field: 'businessTerm',
            title: '营业期限',
            props: {
              placeholder: '请输入营业期限',
              maxLength: 30,
            },
          },
          {
            field: 'industryCategory',
            title: '国标行业门类',
            props: {
              placeholder: '请输入国标行业门类',
              maxLength: 30,
            },
          },
          {
            field: 'industryBigCategory',
            title: '国标行业大类',
            props: {
              placeholder: '请输入国标行业大类',
              maxLength: 30,
            },
          },
          {
            field: 'industryMiddleCategory',
            title: '国标行业中类',
            props: {
              placeholder: '请输入国标行业种类',
              maxLength: 30,
            },
          },
          {
            field: 'industrySmallCategory',
            title: '国标行业小类',
            props: {
              placeholder: '请输入国标行业小类',
              maxLength: 30,
            },
          },
          {
            field: 'enterpriseScale',
            title: '企业规模',
            props: {
              placeholder: '请输入企业规模',
              maxLength: 30,
            },
          },
          {
            field: 'oldEnterpriseName',
            title: '曾用名',
            props: {
              placeholder: '请输入曾用名',
              maxLength: 30,
            },
          },
          {
            field: 'engName',
            title: '英文名',
            props: {
              placeholder: '请输入英文名',
              maxLength: 30,
            },
          },
          {
            field: 'officialWebsite',
            title: '官网',
            props: {
              placeholder: '请输入官网',
              maxLength: 30,
            },
          },
          {
            field: 'commAddr',
            title: '通信地址',
            props: {
              placeholder: '请输入通信地址',
              maxLength: 30,
            },
          },

          {
            field: 'regisAuthority',
            title: '登记机关',
            props: {
              placeholder: '请输入登记机关',
              maxLength: 30,
            },
          },
          {
            field: 'taxpayerAptitude',
            title: '纳税人资质',
            props: {
              placeholder: '请输入纳税人资质',
              maxLength: 30,
            },
          },
          {
            field: 'reportTime',
            title: '最新年报月份',
            props: {
              placeholder: '请输入最新年报月份',
              maxLength: 30,
            },
          },
          {
            field: 'operateStatus',
            title: '企业经营状态',
            props: {
              placeholder: '请输入企业经营状态',
              maxLength: 30,
            },
          },
          {
            field: 'rangeExperience',
            title: '经营范围',
            props: {
              placeholder: '请输入经营范围',
              maxLength: 30,
            },
          },
        ];
      } else {
        return [
          {
            field: 'name',
            title: '企业名称',
            props: {
              placeholder: '请输入企业名称',
              maxLength: 30,
            },
            rules: [{ required: true, message: '请输入企业名称' }],
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              placeholder: '请输入统一社会信用代码',
              maxLength: 30,
              disabled: this.operationType != 'ADD',
            },
            rules: [
              { required: true, message: '请输入统一社会信用代码' },
              {
                pattern: /^[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/g,
                message: '统一社会信用代码格式不正确',
                trigger: 'blur',
              },
            ],
          },
          {
            field: 'status',
            title: '登记状态',
            element: 'a-select',
            props: {
              placeholder: '请选择登记状态',
              options: this.dataDict.enterprise_register_status,
            },
            rules: [{ required: true, message: '请选择登记状态' }],
            on: {
              change: (val) => {
                console.log(val, 'val');
                this.$refs.baseForm && this.$refs.baseForm.clearValidate();
              },
            },
          },
          ...(showTime.includes(this.params.status)
            ? [
                {
                  field: 'endTime',
                  title: '时间',
                  element: 'a-date-picker',
                  props: {
                    placeholder: '请选择时间',
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD',
                    disabledDate: (val) => {
                      return val.isAfter(moment());
                    },
                  },
                  rules: [{ required: true, message: '请选择时间' }],
                },
              ]
            : []),
          {
            field: 'parkId',
            title: '所属园区',
            element: 'a-select',
            props: {
              placeholder: '请选择所属园区',
              options: this.parkList,
              showSearch: true,
              filterOption: filterOption,
            },
            rules: [{ required: true, message: '请选择所属园区' }],
          },
          {
            field: 'regulate',
            title: '是否规上',
            element: 'a-select',
            props: {
              placeholder: '请选择是否规上',
              options: this.dataDict.enterprise_regulate_status,
              showSearch: true,
              filterOption: filterOption,
            },
            rules: [{ required: true, message: '请选择是否规上' }],
          },
          {
            field: 'nature',
            title: '企业性质',
            element: 'a-select',
            props: {
              placeholder: '请选择企业性质',
              showSearch: true,
              options: this.dataDict.enterprise_nature_status,
              filterOption: filterOption,
            },
            rules: [{ required: true, message: '请选择企业性质' }],
          },
          {
            field: 'labelId',
            title: '企业标签',
            element: 'a-select',
            defaultValue: [],
            props: {
              placeholder: '请选择企业标签',
              options: this.labelList,
              mode: 'multiple',
              maxTagCount: 2,
              maxTagTextLength: 4,
              showSearch: true,
              filterOption: filterOption,
            },
          },
          {
            field: 'address',
            title: '企业地址',
            props: {
              placeholder: '请输入企业地址',
              maxLength: 50,
            },
            rules: [{ required: true, message: '请输入企业地址' }],
          },
          {
            title: '位置经纬度',
            field: 'A',
            element: 'slot',
            slotName: 'latLng',
            props: {
              placeholder: '请输入位置经纬度',
              maxLength: 20,
            },
          },
        ];
      }
    },
  },
  watch: {},
  created() {},
  async mounted() {
    this.params = Object.assign(
      {
        latitude: '',
        longitude: '',
        endTime: '',
      },
      initParams(this.baseConfig)
    );
    if (this.operationType !== 'ADD') {
      const [res] = await queryEnterpriseById({
        id: this.rowData.id,
      });
      if (res && res.data) {
        this.params = {
          ...this.params,
          ...res.data,
          labelId: (res.data?.labelId?.split(',') || []).filter((q) => q),
        };
      }
    }
  },
  methods: {
    getLabel(val) {
      return this.labelList.find((q) => q.value == val)?.label || '-';
    },
  },
};
</script>

<style scoped lang="less">
.tag-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  max-height: 75px;
  overflow-y: auto;
  /* 针对VxeTable的滚动条样式 */
  &::-webkit-scrollbar {
    height: 10px;
    width: 3px;
    /* 高度 */
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    /* 滚动条常规颜色 */
    // border-radius: 4px; /* 圆角 */
  }

  /* 鼠标悬停时的滚动条样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #c5c5c5;
    /* 鼠标悬停时的颜色 */
  }
}
</style>
