<template>
  <PortraitCard
    ref="PortraitCard"
    :span="9"
    title="知识产权"
    style="width: 466px"
    v-bind="$attrs"
  >
    <a-row :gutter="12" class="IntellectualPropertyRight-wrap">
      <a-col :span="12">
        <div class="detail-picture detail-picture-1">
          <div class="title">发明专利数量</div>
          <div class="info">
            <span class="num">{{ pictureInfo.inventionPatentNum || '-' }}</span
            ><span class="unit">个</span>
          </div>
        </div>
      </a-col>
      <a-col :span="12">
        <div class="detail-picture detail-picture-2">
          <div class="title">PCT专利数量</div>
          <div class="info">
            <span class="num">{{ pictureInfo.pctPatentNum || '-' }}</span
            ><span class="unit">个</span>
          </div>
        </div>
      </a-col>
    </a-row>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
export default {
  components: {
    PortraitCard,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
  },
};
</script>
<style lang="less" scoped>
.detail-picture {
  &-1 {
    background-image: url('@/assets/images/portraits/1-001.png');
  }
  &-2 {
    background-image: url('@/assets/images/portraits/1-002.png');
  }
}
.IntellectualPropertyRight-wrap {
  padding: 0 10px;
}
</style>
