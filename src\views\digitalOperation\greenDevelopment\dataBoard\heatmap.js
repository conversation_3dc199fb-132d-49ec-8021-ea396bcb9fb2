import * as d3 from 'd3';

const parkList = [
  {
    name: '无锡（国家）软件园',
    id: '1',
  },
  {
    name: '中国物联网国际创新园',
    id: '2',
  },
  {
    name: '无锡国际生命科技园',
    id: '3',
  },
  {
    name: '新发汇融广场',
    id: '4',
  },
  {
    name: '天安智慧城',
    id: '5',
  },
  {
    name: '无锡中关村科技创新园',
    id: '6',
  },
  {
    name: '无锡北邮国吴物联生态园',
    id: '7',
  },
  {
    name: '东庄电力电子科技园',
    id: '8',
  },
  {
    name: '江大科技园',
    id: '9',
  },
  {
    name: '中科智慧信息产业园',
    id: '10',
  },
  {
    name: '金投集成电路产业园',
    id: '11',
  },

  {
    name: '亿利科创产业园',
    id: '12',
  },
  {
    name: '海冠智谷科技园（海尔）',
    id: '13',
  },
  {
    name: '净慧科创园（本码）',
    id: '14',
  },
  {
    name: '爱创科技园',
    id: '15',
  },
  {
    name: '无锡北洋清安科技园（绿野千传）',
    id: '16',
  },
  {
    name: '龙创汽车华东研发总部基地',
    id: '17',
  },
  {
    name: '朗新科技产业园',
    id: '18',
  },
  {
    name: '中电海康无锡物联网产业基地',
    id: '19',
  },
];
const offsets = {
  1: {
    x: -35,
    y: -30,
  },
  5: {
    x: -35,
    y: -30,
  },
};
const seqOffset = {
  4: {
    line: {
      x: 0,
      y: -1,
    },
    circle: {
      x: 0,
      y: 1,
    },
  },
  10: {
    line: {
      x: 0,
      y: -1,
    },
    circle: {
      x: 0,
      y: 1,
    },
  },
  7: {
    line: {
      x: 0,
      y: -1,
    },
    circle: {
      x: 0,
      y: 1,
    },
  },
  9: {
    line: {
      x: 0,
      y: -1,
    },
    circle: {
      x: 0,
      y: 1,
    },
  },
  12: {
    line: {
      x: -1,
      y: 0,
    },
    circle: {
      x: 1,
      y: 0,
    },
  },
  13: {
    line: {
      x: 0,
      y: -1,
    },
    circle: {
      x: 0,
      y: 1,
    },
  },
  14: {
    line: {
      x: 0,
      y: -1,
    },
    circle: {
      x: 0,
      y: 1,
    },
  },
  16: {
    line: {
      x: 0,
      y: -1,
    },
    circle: {
      x: 0,
      y: 1,
    },
  },
  17: {
    line: {
      x: 1,
      y: 0,
    },
    circle: {
      x: -1,
      y: 0,
    },
  },
};
const parkIdLinkId = new Map([
  ['ZZ1724675418075848617', '1'],
  ['ZZ1724674955024687104', '2'],
  ['ZZ1724675116396339200', '3'],
  ['ZZ1724675418075848601', '4'],
  ['ZZ1724675418075848602', '5'],
  ['ZZ1724675418075848603', '6'],
  ['ZZ1724675418075848604', '7'],
  ['ZZ1724675418075848605', '8'],
  ['ZZ1724675418075848606', '9'],
  ['ZZ1724675418075848607', '10'],
  ['ZZ1724675418075848608', '11'],
  ['ZZ1724675418075848609', '12'],
  ['ZZ1724675418075848610', '13'],
  ['ZZ1724675418075848611', '14'],
  ['ZZ1724675418075848612', '15'],
  ['ZZ1724675418075848613', '16'],
  ['ZZ1724675418075848614', '17'],
  ['ZZ1724675418075848615', '18'],
  ['ZZ1724675418075848616', '19'],
]);

//获取多边形中点
function getStartPoint(pathElement) {
  // 获取 path 数据
  var pathLength = pathElement.getTotalLength();

  var points = [];
  var numPoints = 100; // 可根据需要调整采样点的数量

  for (var i = 0; i < numPoints; i++) {
    var length = (i / (numPoints - 1)) * pathLength;
    var point = pathElement.getPointAtLength(length);
    points.push([point.x, point.y]);
  }

  // 计算点的平均值，即中心点
  var centroid = points.reduce(
    function (acc, point) {
      return [acc[0] + point[0], acc[1] + point[1]];
    },
    [0, 0]
  );

  centroid[0] /= numPoints;
  centroid[1] /= numPoints;

  return [centroid[0], centroid[1]];
}

class DrawSeq {
  constructor(paths, svg) {
    this.paths = paths;
    this.svg = svg;
    this.offset = -15;
  }
  draw() {
    this.paths.forEach((pathElement) => {
      const id = pathElement.getAttribute('data-id');
      if (id === '0') return;
      const startPoint = getStartPoint(pathElement);
      this.drawStartPoint(startPoint);
      const endPoint = this.drawLine(startPoint, id);
      this.drawCircle(endPoint, pathElement);
    });
  }
  drawStartPoint(startPoint) {
    //画起点
    this.svg
      .append('circle')
      .attr('cx', startPoint[0]) // 圆心的 x 坐标
      .attr('cy', startPoint[1]) // 圆心的 y 坐标
      .attr('r', 2) // 圆的半径
      .attr('fill', 'white'); // 填充颜色
  }
  drawLine(startPoint, id) {
    let x = 0,
      y = 1;
    if (seqOffset[id]?.line) {
      x = seqOffset[id]?.line.x;
      y = seqOffset[id]?.line.y;
    }
    //画线
    const endPointX = startPoint[0] + x * this.offset;
    const startPointY = startPoint[1] + x;
    const endPointY = startPoint[1] + this.offset * y;
    const lineData = [
      { x: startPoint[0], y: startPointY },
      { x: endPointX, y: endPointY },
    ];
    const lineGenerator = d3
      .line()
      .x((d) => d.x)
      .y((d) => d.y);
    this.svg
      .append('path')
      .attr('d', lineGenerator(lineData))
      .attr('stroke', 'white')
      .attr('stroke-width', 1)
      .attr('fill', 'none');

    return [endPointX, endPointY];
  }
  drawCircle(startPoint, pathElement) {
    const id = pathElement.getAttribute('data-id');
    // if (id === '12') return;
    let x = 0,
      y = -1;
    if (seqOffset[id]?.circle) {
      x = seqOffset[id]?.circle.x;
      y = seqOffset[id]?.circle.y;
    }
    const radius = 10;

    // 圆的中心坐标
    const centerX = startPoint[0] + x * radius;
    const centerY = startPoint[1] + radius * y;

    // 圆的背景色为蓝色，圆边为白色
    this.svg
      .append('circle')
      .attr('cx', centerX)
      .attr('cy', centerY)
      .attr('r', radius)
      .attr('fill', '#4198f6')
      .attr('stroke', 'white')
      .attr('stroke-width', 2);

    // 在圆中间添加数字
    this.svg
      .append('text')
      .attr('x', centerX)
      .attr('y', centerY)
      .attr('text-anchor', 'middle')
      .attr('dy', '0.3em')
      .attr('fill', 'white')
      .attr('font-size', '12px')
      .text(id);
  }
}

function DrawAddress(svg) {
  const roads = [
    {
      centerX: 260,
      centerY: 22,
      addressName: '运河西路',
    },
    {
      centerX: 380,
      centerY: 35,
      addressName: '朗诗绿色家园',
    },
    {
      centerX: 480,
      centerY: 105,
      addressName: '新安花苑',
    },
    {
      centerX: 100,
      centerY: 80,
      addressName: '菱湖大道',
    },
    {
      centerX: 240,
      centerY: 80,
      addressName: '菱湖大道',
    },
    {
      centerX: 435,
      centerY: 135,
      addressName: '530创业大厦',
    },
    {
      centerX: 315,
      centerY: 165,
      addressName: '万达广场',
    },
    {
      centerX: 580,
      centerY: 155,
      addressName: '大溪港湿地公园',
    },
    {
      centerX: 265,
      centerY: 115,
      addressName: '新发汇融广场',
    },
    {
      centerX: 165,
      centerY: 165,
      addressName: '净慧西道',
      deg: 12,
    },
    {
      centerX: 125,
      centerY: 165,
      addressName: '观山路',
      deg: 115,
    },
    {
      centerX: 50,
      centerY: 165,
      addressName: '高浪路',
      deg: 115,
    },
    {
      centerX: 800,
      centerY: 50,
      addressName: '312国道',
      deg: 25,
    },
    {
      centerX: 730,
      centerY: 80,
      addressName: '锡通高速',
      deg: 115,
    },
    {
      centerX: 820,
      centerY: 110,
      addressName: '望虞河',
      deg: 115,
    },
    {
      centerX: 750,
      centerY: 140,
      addressName: '慧海湾生态公园',
      deg: 30,
    },
  ];
  roads.forEach((road) => {
    draw(road);
  });
  function draw(addressOptions) {
    svg
      .append('text')
      .attr('x', addressOptions.centerX)
      .attr('y', addressOptions.centerY)
      .attr('text-anchor', 'middle')
      .attr('dy', '0.3em')
      .attr('fill', '#999999')
      .attr('font-size', '10px')
      .attr(
        'transform',
        `rotate(${addressOptions.deg ?? 0} ${addressOptions.centerX} ${
          addressOptions.centerY
        })`
      )
      .text(addressOptions.addressName);
  }
}

export { parkList, offsets, parkIdLinkId, DrawSeq, getStartPoint, DrawAddress };
