<template>
  <a-modal
    width="800px"
    title="编辑"
    :visible="visible"
    cancelText="取消"
    @ok="handleCreate"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <a-form-model
        ref="myRef"
        :model="ruleForm"
        :rules="rules"
        v-bind="layout"
      >
        <a-form-model-item has-feedback label="Password" prop="pass">
          <a-input v-model="ruleForm.pass" type="password" autocomplete="off" />
        </a-form-model-item>
        <a-form-model-item has-feedback label="Confirm" prop="checkPass">
          <a-input
            v-model="ruleForm.checkPass"
            type="password"
            autocomplete="off"
          />
        </a-form-model-item>
        <a-form-model-item has-feedback label="Age" prop="age">
          <a-input v-model.number="ruleForm.age" />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: ['visible'],
  data() {
    return {
      loading: false,
      ruleForm: {
        pass: '',
        checkPass: '',
        age: '',
      },
      rules: {
        pass: [
          {
            required: true,
            message: 'pass can not be null',
            trigger: 'cange',
          },
        ],
      },
      layout: {
        labelCol: { span: 4 },
        wrapperCol: { span: 14 },
      },
    };
  },
  watch: {
    visible: {
      deep: true,
      handler(val) {
        if (val) {
          this.init();
        } else {
          this.$refs.myRef.resetFields();
        }
      },
    },
  },
  methods: {
    async init() {
      //
    },
    /**
     * 新增、编辑
     */
    async handleCreate() {
      this.$refs.myRef.validate(async (valid) => {
        if (valid) {
          console.log(this.params);
        }
      });
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
  },
};
</script>

<style lang="less" scoped></style>
