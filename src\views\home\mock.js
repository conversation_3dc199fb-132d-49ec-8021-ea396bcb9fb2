export const queryList = ({ limit, pageNum }) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const list = [
        {
          id: 10001,
          name: '张三',
          state: '上学',
          age: 22,
          desc: '所有学科100分',
        },
        {
          id: 10002,
          name: '菜徐坤',
          state: '打篮球',
          age: 2,
          desc: '篮球日记篮球日记篮球日记篮球日记篮球日记篮球日记篮球日记篮球日记篮球日记篮球日记',
        },
      ];
      resolve([
        {
          total: list.length,
          data: list.slice((pageNum - 1) * limit, pageNum * limit),
        },
        undefined,
      ]);
    }, 100);
  });
};
