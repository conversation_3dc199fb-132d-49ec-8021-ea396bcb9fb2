<template>
  <div id="tableComponent">
    <BuseCrud
      ref="crud"
      :title="titleName[pageName]"
      :loading="loading"
      :tablePage="tablePage"
      :tableData="tableData"
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      @loadData="loadData"
    >
      <template slot="menu" slot-scope="{ row }">
        <span @click="() => applyBtnClickHandler(row)"
          ><a-icon type="search"></a-icon
        ></span>
      </template>
    </BuseCrud>
    <a-modal
      :visible="visible"
      :title="null"
      :closable="false"
      width="100%"
      wrapClassName="full-modal"
      :footer="null"
      @cancel="visible = false"
    >
      <!--      <a-select-->
      <!--              v-model="search"-->
      <!--              show-search-->
      <!--              placeholder="Select a person"-->
      <!--              style="width: 200px"-->
      <!--              optionFilterProp='label'-->
      <!--      >-->
      <!--        <a-select-option v-for="item in options" :key="item.value" :value="item.value" :label="item.label">-->
      <!--          {{ item.label }}-->
      <!--        </a-select-option>-->
      <!--      </a-select>-->
      <portraits-detail
        v-if="visible"
        :pageName="pageName"
        :pictureId="pictureId"
        @close="handleModalClose"
      ></portraits-detail>
    </a-modal>
  </div>
</template>

<script>
import portraitsDetail from '@/views/portraits/components/portraitsDetail';
import * as api from '@/api/portraits/index';

export default {
  name: 'tableComponent',
  components: { portraitsDetail },
  props: {
    tableColumn: {
      type: Array,
      default: function () {
        return [];
      },
    },
    modalConfig: {
      type: Object,
      default: function () {
        return {};
      },
    },
    filterOptions: {
      type: Object,
      default: function () {
        return {};
      },
    },
    pageName: {
      type: String,
      default: function () {
        return '';
      },
    },
    params: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {
      // search: '',
      // options: [
      //   {
      //     value: 1,
      //     label:'你好',
      //     a: 'gk'
      //   }
      // ],
      pictureId: '',
      loading: false,
      visible: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      titleName: {
        businessPortraits: '企业画像列表',
        parkPortraits: '园区画像列表',
      },
    };
  },
  computed: {},
  mounted() {
    this.loadData();
  },
  methods: {
    handleModalClose() {
      this.visible = false;
    },
    // 请求表格数据
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [res, err] = await api.getListInfo(
        {
          limit: this.tablePage.pageSize,
          pageNum: this.tablePage.currentPage,
          ...params,
        },
        this.pageName
      );
      this.loading = false;

      if (err) return;
      // 设置数据
      this.tablePage.total = res.data.total;
      this.tableData = res.data.records;
    },
    applyBtnClickHandler(row) {
      this.visible = true;
      this.pictureId = row.id;
    },
  },
};
</script>

<style scoped lang="scss">
.mr-8 {
  margin-right: 8px;
}
</style>
