(function () {
  'use strict';

  let Tools = tinymce.util.Tools.resolve('tinymce.util.Tools');
  let global = tinymce.util.Tools.resolve('tinymce.PluginManager');

  let defaults = {
    spaces: !1,
    isInput: !1,
    toast: null,
  };

  class WordLimit {
    constructor(editor, options) {
      this.editor = editor;
      this.options = Tools.extend(defaults, options);

      var _this = this,
        oldContent = editor.getContent(),
        WordCount = editor.plugins.wordcount,
        preCount = 0,
        _wordCount = 0;

      editor.on('input paste undo redo Keyup ', function (e) {
        //var content = editor.getContent() || e.content || '';
        var options2 = editor.getParam('wordlimit', {}, 'object');
        if (!options2.spaces) {
          _wordCount = WordCount.body.getCharacterCount();
        } else {
          _wordCount = WordCount.body.getCharacterCountWithoutSpaces();
        }

        if (options2.max && _wordCount > options2.max) {
          preCount = _wordCount;

          if (options2.isInput == !1) {
            editor.setContent(oldContent);

            if (!options2.spaces) {
              _wordCount = WordCount.body.getCharacterCount();
            } else {
              _wordCount = WordCount.body.getCharacterCountWithoutSpaces();
            }
          }

          editor.getBody().blur();
          editor.fire('wordlimit', {
            maxCount: _this.options.max,
            wordCount: _wordCount,
            preCount: preCount,
            isPaste: e.type === 'paste' || e.paste || false,
          });
        }

        oldContent = editor.getContent();
      });
    }
  }

  function Plugin() {
    global.add('wordlimit', function (editor) {
      var options = editor.getParam('wordlimit', {}, 'object');

      if (!options && !options.max) {
        return !1;
      }

      if (typeof options.toast !== 'function') {
        options.toast = function (message) {
          editor.notificationManager.open({
            text: message,
            type: 'error',
            timeout: 3000,
          });
        };
      }

      if (!editor.plugins.wordcount) {
        options.toast(
          'Please add the wordcount plugin to the plugins configuration of tinymce before the wordlimit plugin.'
        );
        return !1;
      }

      editor.on('init', function (e) {
        new WordLimit(editor, options);
      });
    });
  }

  Plugin();
})();
