import { request } from '@/utils/request/requestTkb';
/**
 * 指标枚举配置查询
 */
export function enumList(data) {
  return request({
    url: '/bpm/target/enum/list',
    method: 'get',
    params: data,
  });
}

/**
 * 指标枚举配置新增
 */
export function enumAdd(data) {
  return request({
    url: '/bpm/target/enum/add',
    method: 'post',
    data,
  });
}
/**
 * 指标枚举配置修改
 */
export function enumUpdate(data) {
  return request({
    url: '/bpm/target/enum/update',
    method: 'post',
    data,
  });
}
/**
 * 指标枚举配置删除
 */
export function enumDelete(id) {
  return request({
    url: `/bpm/target/enum/delete/${id}`,
    method: 'post',
  });
}
