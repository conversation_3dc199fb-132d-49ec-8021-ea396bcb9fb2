import { BusinessAPI, ParkAPI } from '@/api/portraits-new';

/** 企业画像 - 人才类别 */
export const talentInformationColumns = function () {
  return [
    {
      title: '年份',
      field: 'year',
      width: 120,
    },
    {
      title: '姓名',
      field: 'talentName',
      width: 120,
    },
    {
      title: '入选级别',
      field: 'level',
      width: 120,
      formatter: ({ cellValue }) => {
        return this.dict?.type?.level_type?.find((q) => q.value == cellValue)
          ?.label;
      },
    },
    {
      title: '人才/项目类别',
      field: 'projectManType',
      width: 140,
      formatter: ({ cellValue }) => {
        return this.dict?.type?.project_man_type?.find(
          (q) => q.value == cellValue
        )?.label;
      },
    },
    {
      title: '项目名称',
      field: 'projectName',
      minWidth: 160,
    },
    {
      title: '项目资金(万元)',
      field: 'money',
      width: 140,
      type: 'html',
      formatter: ({ cellValue }) =>
        `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
    },
    {
      title: '团队成员',
      field: 'teamNumbers',
      minWidth: 140,
    },
  ];
};

/** 企业画像 - 风险 */
export const riskInvestmentColumns = function () {
  return [
    {
      title: '年份',
      field: 'year',
      minWidth: 120,
    },
    {
      title: '产业类型',
      field: 'industryType',
      minWidth: 120,
      formatter: ({ cellValue }) => {
        return this.dict?.type?.enterprise_industry_type?.find(
          (q) => q.value == cellValue
        )?.label;
      },
    },
    {
      title: '投资方',
      field: 'investor',
      minWidth: 120,
    },
    {
      title: '金额(万元)',
      field: 'money',
      minWidth: 120,
      type: 'html',
      formatter: ({ cellValue }) =>
        `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
    },
    {
      title: '备注',
      field: 'remark',
      minWidth: 120,
    },
  ];
};
/** 企业画像 - 境外投资 */
export const overInvestmentColumns = function () {
  return [
    {
      title: '年份',
      field: 'year',
      minWidth: 120,
    },
    {
      title: '设立的境外投资机构名称',
      field: 'overseasName',
      minWidth: 120,
    },
    {
      title: '企业控股情况',
      field: 'enterpriseHoldingSituation',
      minWidth: 120,
    },
  ];
};

/** 企业画像 - 企业安全 */

export const enterpriseSecurityColumns = function () {
  return [
    {
      title: '检验时间',
      field: 'verifyDate',
      minWidth: 120,
    },
    {
      title: '检验结论',
      field: 'verifyResult',
      minWidth: 120,
    },
    {
      title: '信息录入时间',
      field: 'inputTime',
      minWidth: 120,
    },
    {
      title: '信息状态',
      field: 'status',
      minWidth: 120,
      formatter: ({ cellValue }) => {
        return cellValue == '0' ? '过期' : cellValue == '1' ? '正常' : '';
      },
    },
  ];
};

/** 企业画像 - 小微工程 */
export const smallEngineeringColumns = function () {
  return [
    {
      title: '接受时间',
      field: 'receiveTime',
      minWidth: 120,
    },
    {
      title: '项目名称',
      field: 'projectName',
      minWidth: 120,
    },
    {
      title: '经办人/负责人',
      field: 'approver',
      minWidth: 120,
    },
    {
      title: '建设单位',
      field: 'constructionUnit',
      minWidth: 120,
    },
    {
      title: '审批状态',
      field: 'nodeStatus',
      minWidth: 120,
      formatter: ({ cellValue }) => {
        return cellValue == 'pending'
          ? '待处理'
          : cellValue == 'pass'
          ? '通过'
          : cellValue == 'reject'
          ? '驳回'
          : '';
      },
    },
  ];
};

/** 企业画像 - 隐患整改 */
export const hiddenDangerColumns = function () {
  return [
    {
      title: '创建时间',
      field: 'createTime',
      minWidth: 120,
    },
    {
      title: '安全隐患名称',
      field: 'dangerName',
      minWidth: 120,
    },
    {
      title: '隐患类型',
      field: 'dangerType',
      minWidth: 120,
    },
    {
      title: '数据来源',
      field: 'source',
      minWidth: 120,
    },
    {
      title: '整改期限',
      field: 'deadline',
      minWidth: 120,
    },
  ];
};

export const businessTableList = function () {
  return [
    {
      title: '人才信息(本年)',
      detailUrl: '/basicData/enterpriseManagement/talentManagement',
      canExpand: true,
      columns: talentInformationColumns.call(this),
      leftWidth: '155px',
      cardList: [
        {
          img: require('@/assets/images/portraits/1-icon-charging-station.png'),
          num: '',
          label: '人才数量',
          unit: '个',
          key: 'talentAmount',
        },
        {
          img: require('@/assets/images/portraits/1-icon-photovoltaic-power-generation-green.png'),
          num: '',
          label: '项目数量',
          unit: '个',
          key: 'projectAmount',
        },
      ],
      loadDataCallback: BusinessAPI.getTalentInformation,
    },
    {
      title: '风险投资',
      detailUrl: '/basicData/enterpriseManagement/riskInvestmentManage',
      canExpand: true,
      columns: riskInvestmentColumns.call(this),
      loadDataCallback: BusinessAPI.getVcInformation,
    },
    {
      title: '境外投资机构',
      detailUrl: '/basicData/enterpriseManagement/overseasManage',
      canExpand: true,
      columns: overInvestmentColumns.call(this),
      loadDataCallback: BusinessAPI.getOverseasResearchInformation,
    },
    {
      title: '企业安全',
      detailUrl: '/greenSafety/securityManagement/parkSecurity',
      canExpand: true,
      tableTitle: '电梯安全检查记录',
      leftWidth: '260px',
      columns: enterpriseSecurityColumns.call(this),
      loadDataCallback: BusinessAPI.getEnterpriseSafetyInfo,
      cardList: [
        {
          img: require('@/assets/images/portraits/<EMAIL>'),
          num: '',
          label: '安全生产综合符合率（近期）',
          unit: '%',
          key: 'safetyProductionComprehensive',
        },
      ],
    },
    {
      title: '小微工程申报',
      detailUrl: '/greenSafety/securityManagement/buildingSafety',
      canExpand: true,
      columns: smallEngineeringColumns.call(this),
      loadDataCallback: BusinessAPI.getEicroProjectsDeclaration,
      cardList: [
        {
          img: require('@/assets/images/portraits/1-icon-charging-station-time.png'),
          num: '',
          label: '待处理数量',
          unit: '条',
          key: 'pendingNum',
        },
      ],
    },
    {
      title: '隐患整改',
      detailUrl: '/greenSafety/securityManagement/rectificationInfo',
      canExpand: true,
      columns: hiddenDangerColumns.call(this),
      loadDataCallback: BusinessAPI.getRectificationRecords,
      cardList: [
        {
          img: require('@/assets/images/portraits/1-icon-charging-station-bs.png'),
          num: '',
          label: '整改中数量',
          unit: '个',
          key: 'rectifiedNum',
        },
        {
          img: require('@/assets/images/portraits/1-icon-photovoltaic-power.png'),
          num: '',
          label: '未整改数量',
          unit: '个',
          key: 'notRectifiedNum',
        },
      ],
    },
  ];
};

/** 园区画像 - 园区安全 */

export const enterpriseSecurityColumns_park = function () {
  return [
    {
      title: '检验单位',
      field: 'maintainOrgName',
      minWidth: 120,
    },
    {
      title: '检验时间',
      field: 'verifyDate',
      minWidth: 120,
    },
    {
      title: '检验结论',
      field: 'verifyResult',
      minWidth: 120,
    },
    {
      title: '信息录入时间',
      field: 'inputTime',
      minWidth: 120,
    },
    {
      title: '信息状态',
      field: 'status',
      minWidth: 120,
      formatter: ({ cellValue }) => {
        return cellValue == '0' ? '过期' : cellValue == '1' ? '正常' : '';
      },
    },
  ];
};

/** 园区画像 - 小微工程 */
export const smallEngineeringColumns_park = function () {
  return [
    {
      title: '申报企业',
      field: 'name',
      minWidth: 120,
    },
    {
      title: '接受时间',
      field: 'receiveTime',
      minWidth: 120,
    },
    {
      title: '项目名称',
      field: 'projectName',
      minWidth: 120,
    },
    {
      title: '经办人/负责人',
      field: 'approver',
      minWidth: 120,
    },
    {
      title: '建设单位',
      field: 'constructionUnit',
      minWidth: 120,
    },
    {
      title: '审批状态',
      field: 'nodeStatus',
      minWidth: 120,
      formatter: ({ cellValue }) => {
        return cellValue == 'pending'
          ? '待处理'
          : cellValue == 'pass'
          ? '通过'
          : cellValue == 'reject'
          ? '驳回'
          : '';
      },
    },
  ];
};

/** 园区画像 - 隐患整改 */
export const hiddenDangerColumns_park = function () {
  return [
    {
      title: '创建日期',
      field: 'createTime',
      minWidth: 120,
    },
    {
      title: '单位名称',
      field: 'enterpriseName',
      minWidth: 120,
    },
    {
      title: '安全隐患名称',
      field: 'dangerName',
      minWidth: 120,
    },
    {
      title: '隐患类型',
      field: 'dangerType',
      minWidth: 120,
    },
    {
      title: '数据来源',
      field: 'source',
      minWidth: 120,
    },
    {
      title: '整改期限',
      field: 'deadline',
      minWidth: 120,
    },
  ];
};

export const parkTableList = function () {
  return [
    {
      title: '园区安全',
      detailUrl: '/greenSafety/securityManagement/parkSecurity',
      canExpand: true,
      tableTitle: '电梯安全检查记录',
      leftWidth: '260px',
      columns: enterpriseSecurityColumns_park.call(this),
      loadDataCallback: ParkAPI.getParkSafetyInfo,
      cardList: [
        {
          img: require('@/assets/images/portraits/1-2010.png'),
          num: '',
          label: '安全生产综合符合率（近期）',
          unit: '%',
          key: 'safetyProductionComprehensive',
        },
        {
          img: require('@/assets/images/portraits/1-2011.png'),
          num: '',
          label: '消防安全检查合格率',
          unit: '%',
          key: 'fireSafetyComprehensive',
        },
      ],
    },
    {
      title: '小微工程申报',
      detailUrl: '/greenSafety/securityManagement/buildingSafety',
      canExpand: true,
      columns: smallEngineeringColumns_park.call(this),
      loadDataCallback: ParkAPI.getEicroProjectsDeclaration,
      cardList: [
        {
          img: require('@/assets/images/portraits/1-icon-charging-station-time.png'),
          num: '',
          label: '待处理数量',
          unit: '条',
          key: 'pendingNum',
        },
      ],
    },
    {
      title: '隐患整改',
      detailUrl: '/greenSafety/securityManagement/rectificationInfo',
      canExpand: true,
      columns: hiddenDangerColumns_park.call(this),
      loadDataCallback: ParkAPI.getRectificationRecordsByParkId,
      cardList: [
        {
          img: require('@/assets/images/portraits/1-icon-charging-station-bs.png'),
          num: '',
          label: '整改中数量',
          unit: '个',
          key: 'rectifiedNum',
        },
        {
          img: require('@/assets/images/portraits/1-icon-photovoltaic-power.png'),
          num: '',
          label: '未整改数量',
          unit: '个',
          key: 'notRectifiedNum',
        },
      ],
    },
  ];
};
