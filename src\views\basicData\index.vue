<template>
  <page-layout>
    <PageWrapper
      title="人物名单"
      createText="创建人物"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      @handleCreate="handleCreate"
    >
      <!-- 创建按钮区域插槽 -->
      <template #defaultHeader>
        <a-button
          type="primary"
          style="margin-right: 8px"
          @click="onClickDetail"
        >
          导出
        </a-button>
      </template>
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
      </template>
      <!-- 编辑弹窗 -->
      <ListModal
        :visible="visible"
        :detail="modalData"
        @handleCancel="handleCancel"
      />
    </PageWrapper>
  </page-layout>
</template>

<script>
import moment from 'moment';
import { defaultTableColumn, defaultFilterConfig } from './constant';
import { queryList } from './mock';
import ListModal from './components/ListModal.vue';
export default {
  components: { ListModal },
  dicts: ['my_notify_rule'],
  data() {
    return {
      loading: false,
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
        params: {
          blog: '',
          name: 'test',
          state: '',
          identity: '',
          birthday: '',
          birthdayRange: ['', ''],
        }, // 筛选器结果数据
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: defaultTableColumn(),
      tableData: [],
      visible: false,
      modalData: null,
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    // 字典加载完成
    onDictReady() {
      this.filterOptions.config[1].props.options =
        this.dict.type.my_notify_rule;
    },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [res, err] = await queryList({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...params,
        birthdayRange: undefined, // 对时间范围按照后端要求处理
        timeStart: moment(params.birthdayRange[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        timeEnd: moment(params.birthdayRange[1]).format('YYYY-MM-DD HH:mm:ss'),
      });
      this.loading = false;

      if (err) return;
      // 设置数据
      this.tablePage.total = res.total;
      this.tableData = res.data;
    },
    // 创建按钮点击事件
    handleCreate() {
      this.visible = true;
    },
    // 编辑按钮点击事件
    onClickEdit(row) {
      this.visible = true;
      this.modalData = row;
    },
    // 关闭弹窗
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
      this.modalData = null;
    },
    // 查看详情
    onClickDetail() {
      // this.$router.push('./second/detail');
    },
  },
};
</script>
<style scoped>
/deep/.page-wrapper-container {
  margin: 0 !important;
}
</style>
