<template>
  <a-modal
    width="600px"
    title="管理"
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="ruleForm"
        :config="formManageConfig"
        :params="formValue"
      >
        <template #area>
          <a-input
            placeholder="请输入"
            v-model="formValue.ratingPre"
            class="model-unit"
          />
          <span class="unit">%</span>
        </template>
        <template #buildArea>
          <a-input
            placeholder="请输入"
            v-model="formValue.greenPre"
            class="model-unit"
          />
          <span class="unit">%</span>
        </template>
        <template #carrierArea>
          <a-input
            placeholder="请输入"
            v-model="formValue.carrierArea"
            class="model-unit"
          />
          <span class="unit">m²</span>
        </template>
        <template #floorSpace>
          <a-input
            placeholder="请输入"
            v-model="formValue.floorSpace"
            class="model-unit"
          />
          <span class="unit">m²</span>
        </template>
        <template #honor>
          <a-input
            placeholder="请输入"
            v-model="formValue.honor"
            class="model-unit"
            type="textarea"
          />
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import { initManFormValue } from '../constant';
import { saveManageDetail, codeByType } from '@/api/basicData';
import { institutionsMixin } from '../../mixins/institutionsMixin';

export default {
  props: ['visible', 'detail', 'isLook', 'modelTitle', 'parkId', 'parkName'],
  components: {},
  mixins: [institutionsMixin],
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          this.formValue = {
            ...initManFormValue(),
            ...this.detail,
            parkId: this.parkId,
            name: this.parkName,
            publicService: this.detail?.publicService?.split(','),
          };
        } else {
          this.formValue = initManFormValue();
        }
      },
    },
  },
  created() {
    this.codeByType();
  },
  data() {
    return {
      loading: false,
      formValue: initManFormValue(),
      // formManageConfig,
      serviceList: [],
    };
  },
  computed: {
    formManageConfig() {
      const formManageConfig = [
        {
          field: 'phone',
          title: '电话',
          props: {
            placeholder: '请输入',
          },
        },
        {
          field: 'honor',
          title: '荣誉',
          element: 'slot',
          slotName: 'honor',
          props: {
            placeholder: '输入内容请使用英文逗号进行分隔',
          },
        },
        {
          field: 'businessRange',
          title: '经营范围',
          props: {
            placeholder: '请输入',
          },
        },
        {
          field: 'intro',
          title: '简介',
          props: {
            placeholder: '请输入',
          },
        },
        {
          field: 'ratingPre',
          title: '出租率',
          element: 'slot',
          slotName: 'area',
          rules: [
            { required: true, validator: this.checkNum, trigger: 'change' },
          ],
        },
        {
          field: 'greenPre',
          title: '绿化率',
          element: 'slot',
          slotName: 'buildArea',
          rules: [
            { required: true, validator: this.checkNum, trigger: 'change' },
          ],
        },
        // {
        //   field: 'carrierArea',
        //   title: '载体面积',
        //   element: 'slot',
        //   slotName: 'carrierArea',
        //   rules: [
        //     { required: true, validator: this.checkNum, trigger: 'change' },
        //   ],
        // },
        {
          field: 'floorSpace',
          title: '占地面积',
          element: 'slot',
          slotName: 'floorSpace',
          rules: [
            { required: true, validator: this.checkNum, trigger: 'change' },
          ],
        },
        {
          field: 'publicService',
          title: '公共服务',
          element: 'a-checkbox-group',
          props: {
            placeholder: '请输入公共服务',
            options: this.serviceList,
          },
        },
        {
          field: 'population',
          title: '就业人口数量',
          element: 'a-input',
          props: {
            suffix: '个',
            type: 'number',
            placeholder: '请输入就业人口数量',
          },
        },
      ];
      return formManageConfig;
    },
  },
  methods: {
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.formValue.publicService =
            this.formValue.publicService?.join(',') || '';
          this.saveManageDetail();
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 公共服务下拉
    async codeByType() {
      const [res, err] = await codeByType({ codeType: 'park_service' });
      if (err) return;
      this.serviceList = res.data.map((item) => {
        item.label = item.name;
        return item;
      });
    },
    // 编辑
    async saveManageDetail() {
      const [, err] = await saveManageDetail(this.formValue);
      if (err) return;
      this.$message.success('编辑成功!');
      this.$emit('loadData');
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
