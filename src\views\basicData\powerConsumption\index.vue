<template>
  <page-layout>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tableColumn="tableColumn"
      :tablePage="tablePage"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
      @rowView="rowView"
      :tableOn="{
        'checkbox-change': selectChangeEvent,
        'checkbox-all': selectChangeEvent,
      }"
    >
      <!--    日期筛选器（年）-->
      <template slot="dateYear">
        <BuseRangePicker
          type="month"
          v-model="filterParams.reportYm"
          :needShowSecondPicker="() => false"
          format="YYYY-MM"
          placeholder="请选择月份"
          :disableDateFunc="disableDateFunc"
        />
      </template>
      <template slot="defaultTitle">
        <span></span>
      </template>
      <template slot="defaultHeader">
        <div class="flex-row-10">
          <!-- <a-button type="primary" @click="handleCreate">新增</a-button> -->
          <a-button @click="onClickImport">导入</a-button>
          <a-button :loading="exportLoading" @click="exportData">导出</a-button>
          <a-button
            :loading="delLoading"
            type="danger"
            @click="delAll"
            :disabled="!checkItems.length"
            >删除</a-button
          >
        </div>
      </template>
    </BuseCrud>
  </page-layout>
</template>

<script>
import moment from 'moment';
import { resolveBlob } from '@/utils/common/fileDownload';
import {
  getParkList,
  getPowerPatentList,
  delPowerPatentList,
  downloadInformation,
} from '@/api/basicData/index.js';
import { filterOption } from '@/utils';
export default {
  props: {},
  components: {},
  dicts: ['enterprise_listing_type'],
  data() {
    return {
      pageName: 'powerConsumption',
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      filterParams: {
        reportYm: { startValue: '' },
        enterpriseName: '',
        unifiedCreditCode: this.$route.query.pictureId,
        consNo: '',
      },
      exportLoading: false,
      delLoading: false,
      parkList: [],
      checkItems: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'reportYm',
            title: '日期',
            element: 'slot',
            slotName: 'dateYear',
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
            props: {
              placeholder: '请输入企业名称',
            },
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              placeholder: '请输入统一社会信用代码',
            },
            itemProps: {
              labelCol: { span: 10 },
              wrapperCol: { span: 14 },
            },
          },
          {
            field: 'consNo',
            title: '户号',
            props: {
              placeholder: '请输入户号',
            },
          },
        ],
        params: this.filterParams,
      };
    },
    tableColumn() {
      return [
        {
          type: 'checkbox',
          width: 80,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          width: 80,
          fixed: 'left',
        },
        {
          field: 'reportYm',
          title: '日期',
          minWidth: 120,
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          minWidth: 120,
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          minWidth: 120,
        },
        {
          field: 'enterpriseAddr',
          title: '地址',
          minWidth: 120,
        },
        {
          field: 'consNo',
          title: '户号',
          minWidth: 120,
        },
        {
          field: 'volLevel',
          title: '电压等级',
          minWidth: 120,
        },
        {
          field: 'conCap',
          title: '合同容量',
          minWidth: 120,
        },
        {
          field: 'eleQty',
          title: '电量',
          minWidth: 120,
        },
        {
          field: 'updateBy',
          title: '更新人',
          minWidth: 120,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          minWidth: 180,
        },
      ];
    },
    modalConfig() {
      return {
        addBtn: false,
        menu: false,
      };
    },
  },
  watch: {},
  created() {},
  mounted() {
    if (this.$route.query.pictureId) {
      this.ifFirst = true;
    }
    this.getParkList();
    this.loadData();
  },
  methods: {
    selectChangeEvent({ records }) {
      this.checkItems = records.map((item) => {
        return item.id;
      });
    },
    async loadData() {
      this.loading = true;
      const [res] = await getPowerPatentList({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...this.filterParams,
        reportYm: this.filterParams.reportYm?.startValue
          ? moment(this.filterParams.reportYm.startValue).format('YYYY-MM')
          : '',
      });
      this.loading = false;
      if (res && res.data) {
        this.checkItems = [];
        this.tableData = res.data;
        this.tablePage.total = res.total;
      }
    },
    rowDel(row) {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [res] = await delPowerPatentList({
            ids: [row.id],
          });
          if (res && res.code == '10000') {
            that.$message.success('删除成功!');
            that.loadData();
          }
        },
      });
    },
    // 打开弹窗
    btnClickHandler(operationType, row) {
      this.operationType = operationType;
      if (operationType !== 'ADD') {
        this.$refs.crud.switchModalView(true, operationType, {
          ...row,
          enterpriseId: row.name,
        });
        return;
      }
      this.$refs.crud.switchModalView(true, operationType, row);
    },
    // 获取园区列表
    async getParkList() {
      const [res] = await getParkList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.parkList = res.data;
      }
    },
    handlerSearchEnterprise(val) {
      return [];
    },
    async exportData() {
      this.exportLoading = true;
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8',
      };
      const [res] = await downloadInformation(
        {
          ids: this.$refs.crud.getCheckboxRecords().map((q) => q.id),
          ...this.filterParams,
          reportYm: this.filterParams.reportYm?.startValue
            ? moment(this.filterParams.reportYm?.startValue).format('yyyy')
            : '',
        },
        'powerConsumption'
      );
      this.exportLoading = false;
      resolveBlob(res, mimeMap.xlsx, '用电量', '.xlsx');
    },
    rowView(row) {
      this.btnClickHandler('VIEW', row);
    },
    delAll() {
      const list = this.$refs.crud.getCheckboxRecords();
      const that = this;
      if (list && list.length > 0) {
        const ids = list.map((q) => q.id);
        this.$confirm({
          title: '确认删除',
          content: () => '确认删除当前选中数据？',
          cancelText: '取消',
          okText: '确定',
          async onOk() {
            that.delLoading = true;
            const [res] = await delPowerPatentList({
              ids: ids,
            });
            that.delLoading = false;
            if (res && res.code == '10000') {
              that.$message.success('删除成功!');
              that.loadData();
            }
          },
        });
      } else {
        this.$message.info('请选择需要删除的数据');
      }
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: 'powerConsumption',
          ifTime: true,
        },
      });
    },
    // 日期格式化
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
  },
};
</script>

<style scoped lang="less"></style>
