<template>
  <div>
    <a-modal
      :maskClosable="false"
      class="add-new-custem-modal"
      :title="title"
      :visible="isVisible"
      @ok="onClickSubmit"
      @cancel="cancelModal"
      width="950px"
    >
      <a-form-model
        ref="formRef"
        :model="addForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        ><a-form-model-item
          prop="serialNumber"
          label="序号"
          :required="true"
          :rules="[{ required: true, message: '请输入序号' }]"
        >
          <a-input
            v-model="addForm.serialNumber"
            placeholder="请输入序号"
            allow-clear
            disabled
          />
        </a-form-model-item>
        <a-form-model-item
          prop="name"
          label="消防安全类别"
          :required="true"
          :rules="[{ required: true, message: '请输入消防安全类别' }]"
        >
          <a-input
            v-model="addForm.name"
            placeholder="请输入消防安全类别"
            allow-clear
          />
        </a-form-model-item>
        <a-form-model-item
          prop="content"
          label="检查内容"
          :required="true"
          :rules="[{ required: true, message: '请输入检查内容' }]"
        >
          <a-input
            v-model="addForm.content"
            placeholder="请输入检查内容"
            allow-clear
          />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { excelAddOrUpdate } from '@/api/digitalOperation/securityManagement/parkSafety/industrialEnterprise.js';
export default {
  name: 'addListForFS',
  components: {},
  props: {
    //编辑数据
    rowData: {
      type: Object,
      default: () => {},
    },
    isVisible: {
      type: Boolean,
      default: false,
    },
    addOrEdit: {
      type: String,
      default: 'add',
    },
    serialNumber: { type: Number, default: 0 },
  },
  data() {
    return {
      addEditVisible: false, //新增弹框是否开启
      title: '列表维护',
      addForm: { name: '', content: '' },
    };
  },

  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'formCommon' });
  },
  mounted() {
    this.initHandel();
  },
  methods: {
    initHandel() {
      if (this.addOrEdit === 'edit') {
        this.addForm = this.rowData;
        this.title = '编辑';
      } else {
        this.addForm.serialNumber = this.serialNumber;
        this.title = '新增';
      }
    },
    //新增
    async excelAddOrUpdate(p) {
      const [, err] = await excelAddOrUpdate(p);
      if (err) return this.$message.error(`${this.title}失败`);
      this.$message.success(`${this.title}成功`);
      this.$emit('cancelModal', 'refresh');
    },
    cancelModal() {
      this.$emit('cancelModal');
    },
    onClickSubmit() {
      this.$refs.formRef.validate((valid) => {
        console.log('valid', valid);
        if (valid) {
          console.log('提交数据1', this.addForm);
          let p = {
            businessFlag: '2', //业务标识  1：工业企业消防安全  2:消防安全检查记录
            ...this.addForm,
          };
          if (this.addOrEdit === 'edit') {
            delete p.tag;
            delete p._X_ROW_KEY;
          }
          console.log(p, 999.8);
          this.excelAddOrUpdate(p);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
