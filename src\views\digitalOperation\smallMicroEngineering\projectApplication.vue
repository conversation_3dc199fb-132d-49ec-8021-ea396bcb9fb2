<template>
  <div class="pro-application">
    <div class="application-info">
      <h2 class="title">申报信息</h2>
      <DynamicForm ref="formRef" :config="formConfig" :params="params" />
      <a-button
        type="primary"
        @click="handleSubmit"
        :loading="loading"
        :disabled="$route.query.type == 'view'"
        >提交</a-button
      >
    </div>
    <div class="flows-info">
      <h2 class="title">进度流程</h2>
      <a-steps :current="processList.length - 1">
        <a-step
          title="物业管理单位"
          :sub-title="
            processList?.[0]?.nodeStatus == 'pass'
              ? '：通过'
              : processList?.[0]?.nodeStatus == 'reject'
              ? ':不通过'
              : ''
          "
        >
          <div slot="description" v-if="processList.length > 0">
            <div>
              <div>审批人:{{ processList?.[0]?.approver }}</div>
              <div>接收时间:{{ processList?.[0]?.receiveTime }}</div>
            </div>
            <div v-if="processList?.[0]?.nodeStatus !== 'pending'">
              <div>审批意见:{{ processList?.[0]?.remarks }}</div>
              <div>审批时间:{{ processList?.[0]?.approvalTime }}</div>
            </div>
          </div>
        </a-step>
        <a-step
          title="运营管理单位"
          :sub-title="
            processList?.[1]?.nodeStatus == 'pass'
              ? '：通过'
              : processList?.[1]?.nodeStatus == 'reject'
              ? ':不通过'
              : ''
          "
        >
          <div slot="description" v-if="processList.length > 1">
            <div>
              <div>审批人:{{ processList?.[1]?.approver }}</div>
              <div>接收时间:{{ processList?.[1]?.receiveTime }}</div>
            </div>
            <div v-if="processList?.[1]?.nodeStatus !== 'pending'">
              <div>审批意见:{{ processList?.[1]?.remarks }}</div>
              <div>审批时间:{{ processList?.[1]?.approvalTime }}</div>
            </div>
          </div></a-step
        >
        <a-step
          title="太科办建设局-经办人"
          :sub-title="
            processList?.[2]?.nodeStatus == 'pass'
              ? '：通过'
              : processList?.[2]?.nodeStatus == 'reject'
              ? ':不通过'
              : ''
          "
        >
          <div slot="description" v-if="processList.length > 2">
            <div>
              <div>审批人:{{ processList?.[2]?.approver }}</div>
              <div>接收时间:{{ processList?.[2]?.receiveTime }}</div>
            </div>
            <div v-if="processList?.[2]?.nodeStatus !== 'pending'">
              <div>审批意见:{{ processList?.[2]?.remarks }}</div>
              <div>审批时间:{{ processList?.[2]?.approvalTime }}</div>
            </div>
          </div></a-step
        >
        <a-step
          title="太科办建设局-负责人"
          :sub-title="
            processList?.[3]?.nodeStatus == 'pass'
              ? '：通过'
              : processList?.[3]?.nodeStatus == 'reject'
              ? ':不通过'
              : ''
          "
        >
          <div slot="description" v-if="processList.length > 3">
            <div>
              <div>审批人:{{ processList?.[3]?.approver }}</div>
              <div>接收时间:{{ processList?.[3]?.receiveTime }}</div>
            </div>
            <div v-if="processList?.[3]?.nodeStatus !== 'pending'">
              <div>审批意见:{{ processList?.[3]?.remarks }}</div>
              <div>审批时间:{{ processList?.[3]?.approvalTime }}</div>
            </div>
          </div></a-step
        >
      </a-steps>
    </div>
  </div>
</template>

<script>
import { DynamicForm } from '@bangdao/buse-components';
import {
  approvalDetail,
  approval,
} from '@/api/digitalOperation/securityManagement/buildingSafety/index.js';
import { getOrganizeAll } from '@/views/digitalOperation/taskManagement/utils/index.js';
export default {
  name: 'ManagementTkProjectApplication',
  components: {
    DynamicForm,
  },

  data() {
    return {
      params: {
        projectName: '项目名称',
        constructionUnit: '建设单位',
        constructionScale: '建设规模',
        engineeringCost: '工程造价',
        bulidUnit: '施工单位',
        tenementUnit: '物业监管单位',
        parkSuperviseUnit: '园区监管单位',
        nodeStatus: undefined,
        remarks: undefined,
        nextApproverId: undefined,
      },
      organizeAllList: [], //下一节点人列表数据
      processList: [], //流程进度
      nextApproverId: '', //下一节点人id
      nextApprover: '', //下一节点人
      loading: false,
    };
  },
  created() {},
  computed: {
    formConfig() {
      return [
        {
          title: '项目名称',
          field: 'projectName',
          props: {
            disabled: true,
          },
        },
        {
          title: '建设单位',
          field: 'constructionUnit',
          props: {
            disabled: true,
          },
        },
        {
          title: '建设规模',
          field: 'constructionScale',
          props: {
            suffix: '平方米',
            disabled: true,
          },
        },
        {
          title: '工程造价',
          field: 'engineeringCost',
          props: {
            suffix: '万元',
            disabled: true,
          },
        },
        {
          title: '施工单位',
          field: 'bulidUnit',
          props: {
            disabled: true,
          },
        },
        {
          title: '物业监管单位',
          field: 'tenementUnit',
          props: {
            disabled: true,
          },
        },
        {
          title: '园区监管单位',
          field: 'parkSuperviseUnit',
          props: {
            disabled: true,
          },
        },
        {
          title: '审批',
          field: 'nodeStatus',
          element: 'a-radio-group',
          rules: [
            {
              required: true,
              message: '请选择审批',
            },
          ],
          props: {
            disabled: this.$route.query.type == 'view',
            options: [
              { label: '通过', value: 'pass' },
              { label: '不通过', value: 'reject' },
            ],
          },
        },
        {
          title: '审批意见',
          field: 'remarks',
          element: 'a-textarea',
          props: {
            disabled: this.$route.query.type == 'view',
          },
        },

        {
          title: '选择下一节点人员',
          field: 'nextApproverId',
          element: 'a-cascader',
          show:
            this.processList.length < 4 &&
            this.$route.query.type != 'view' &&
            this.params.nodeStatus != 'reject',
          rules: [
            {
              required: true,
              message: '请选择下一节点人员',
            },
          ],
          props: {
            options: this.organizeAllList,
          },
          on: {
            change: this.nextApproverChange,
          },
        },
      ];
    },
  },

  mounted() {
    getOrganizeAll().then((res) => {
      this.organizeAllList = res.filter((item) => {
        return item.label === '建设局';
      });
    });
    this.approvalDetail();
  },

  methods: {
    async approvalDetail() {
      const [res, err] = await approvalDetail({ id: this.$route.query.id });
      if (err) return;
      const {
        projectName,
        constructionUnit,
        constructionScale,
        engineeringCost,
        bulidUnit,
        tenementUnit,
        parkSuperviseUnit,
      } = res.data;
      this.params.projectName = projectName;
      this.params.constructionUnit = constructionUnit;
      this.params.constructionScale = constructionScale;
      this.params.engineeringCost = engineeringCost;
      this.params.bulidUnit = bulidUnit;
      this.params.tenementUnit = tenementUnit;
      this.params.parkSuperviseUnit = parkSuperviseUnit;
      const { processList } = res.data;
      processList.sort(function (a, b) {
        return Number(a.node) - Number(b.node);
      });
      this.processList = processList;
      if (this.$route.query.type == 'view') {
        const lastItem = processList[processList.length - 1];
        if (lastItem.nodeStatus == 'pending') {
          const item = processList[processList.length - 2];
          this.params.nodeStatus = item.nodeStatus;
          this.params.remarks = item.remarks;
        } else {
          this.params.nodeStatus = lastItem.nodeStatus;
          this.params.remarks = lastItem.remarks;
        }
      }
    },
    async approval(p) {
      const [, err] = await approval(p);
      this.loading = false;
      if (err) return this.$message.error('审批失败');
      this.$message.success('审批成功');
      this.$router.push({
        name: 'buildingSafety',
      });
    },

    nextApproverChange(val, val1, val2) {
      this.nextApproverId = val2[1].value; //办理人id
      this.nextApprover = val2[1].label; //办理人
    },
    handleSubmit() {
      this.$refs.formRef.validate(() => {
        if (!this.params.nodeStatus) return;
        this.loading = true;
        const { nodeStatus, remarks } = this.params;
        let p = {
          projectId: this.$route.query.id,
          nodeStatus,
          nextApprover: this.nextApprover, //下一节点审批人
          nextApproverId: this.nextApproverId, //下节点审批人id
          remarks,
        };
        console.log(p, 'this.$refs.formRef');
        this.approval(p);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.pro-application {
  background: #fff;
  margin: 24px;
  padding: 24px;
  h2 {
    align-self: self-start;
    margin-bottom: 24px;
  }
  .application-info {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .flows-info {
    width: 95%;
    margin: 60px auto;
  }
  /deep/.ant-steps-item-description {
    max-width: 240px;
  }
}
</style>
