import { request } from '@/utils/request/requestTkb';
/**
 * 推送列表
 */
export function pushList(data) {
  return request({
    url: '/gov/resPool/pushList',
    method: 'post',
    data,
  });
}
/**
 * 撤销推送资源
 */
export function cancelPush(data) {
  return request({
    url: '/gov/resPool/cancelPush',
    method: 'post',
    data,
  });
}

/**
 * 撤销推送资源
 */
export function rePushRes(data) {
  return request({
    url: '/gov/resPool/rePushRes',
    method: 'post',
    data,
  });
}

export function exportResPushInfo(data) {
  return request({
    url: '/gov/resPool/exportResPushInfo',
    method: 'post',
    data,
    responseType: 'blob',
  });
}

export function pushDetailStatistics(data) {
  return request({
    url: '/gov/resPool/pushDetailStatistics',
    method: 'get',
    params: data,
  });
}
