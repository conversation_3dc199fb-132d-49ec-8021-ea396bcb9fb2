<template>
  <a-modal
    :title="`用户信息`"
    width="500px"
    :visible="visible"
    :footer="null"
    @cancel="closeModal"
    :bodyStyle="{ padding: '24px 48px' }"
  >
    <a-spin :spinning="loading">
      <a-form-model
        v-if="!errorFlag"
        ref="form"
        :model="formInfo"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="用户名">
          {{ originData.userName }}
        </a-form-model-item>
        <a-form-model-item label="姓名" prop="nickName">
          <div v-if="editType === 'nickName'" class="input-block">
            <a-input
              placeholder="请输入用户名"
              :maxLength="25"
              v-model="formInfo.nickName"
            />
            <a-icon
              style="margin: 0 10px"
              type="check"
              @click="saveUserDetail('nickName')"
            />
            <a-icon type="close" @click="cancelEdit('nickName')" />
          </div>
          <div v-else class="input-block">
            {{ originData.nickName || '-' }}
            <a-icon
              style="margin-left: 10px"
              type="edit"
              @click="openEdit('nickName')"
            />
          </div>
        </a-form-model-item>
        <a-form-model-item label="手机号" prop="phonenumber">
          <div v-if="editType === 'phonenumber'" class="input-block">
            <a-input
              placeholder="请输入手机号"
              :maxLength="20"
              v-model="formInfo.phonenumber"
            />
            <a-icon
              style="margin: 0 10px"
              type="check"
              @click="saveUserDetail('phonenumber')"
            />
            <a-icon type="close" @click="cancelEdit('phonenumber')" />
          </div>
          <div v-else class="input-block">
            {{ originData.phonenumber || '-' }}
            <a-icon
              style="margin-left: 10px"
              type="edit"
              @click="openEdit('phonenumber')"
            />
          </div>
        </a-form-model-item>
        <a-form-model-item label="邮箱" prop="email">
          <div v-if="editType === 'email'" class="input-block">
            <a-input
              placeholder="请输入邮箱"
              :max-length="50"
              v-model="formInfo.email"
            />
            <a-icon
              style="margin: 0 10px"
              type="check"
              @click="saveUserDetail('email')"
            />
            <a-icon type="close" @click="cancelEdit('email')" />
          </div>
          <div v-else class="input-block">
            {{ originData.email || '-' }}
            <a-icon
              style="margin-left: 10px"
              type="edit"
              @click="openEdit('email')"
            />
          </div>
        </a-form-model-item>
        <!-- <a-form-model-item label="所属商户">
          {{ (merchant && merchant.merchantName) || '-' }}
        </a-form-model-item>
        <a-form-model-item label="所属机构">
          {{ (originData && originData.organizeName) || '-' }}
        </a-form-model-item> -->
        <a-form-model-item label="岗位">
          {{ (originData && originData.positionName) || '-' }}
        </a-form-model-item>
        <a-form-model-item label="角色">
          {{ (originData && originData.roleNameInfos) || '-' }}
        </a-form-model-item>
        <a-form-model-item label="密码">
          <a-button type="link" @click="resetPassword">重置密码</a-button>
        </a-form-model-item>
      </a-form-model>
      <div v-else>
        <a-result status="error" title="数据请求错误" />
      </div>
    </a-spin>
    <!-- 修改密码 -->
    <ChangePassword
      :visible.sync="changePasswordVisible"
      :userId="userId"
      @ok="changePasswordVisible = false"
    />
  </a-modal>
</template>

<script>
import { getUser, updateUser } from '@/api/system/user';
import { aseDecrypt, rsaCode } from '@/utils/common/auth';
import ChangePassword from '@/components/UserBlock/ChangePassword.vue';
export default {
  components: { ChangePassword },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.editType = '';
        this.getDetail();
      }
    },
  },
  computed: {
    userId() {
      return this.$store?.state?.base?.user?.userId;
    },
  },
  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      loading: false,
      changePasswordVisible: false,
      errorFlag: false, //页面请求报错
      rules: {
        phonenumber: [
          {
            validator: (rule, value, callback) => {
              if (value) {
                try {
                  let reg = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
                  if (reg.test(value)) {
                    callback();
                  } else {
                    callback(new Error('数据格式错误'));
                  }
                } catch (err) {
                  callback(new Error('数据格式错误'));
                }
              } else {
                callback();
              }
            },
          },
        ],
        email: [
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('不能为空'));
              }
              try {
                let reg =
                  // eslint-disable-next-line no-useless-escape
                  /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
                if (reg.test(value)) {
                  callback();
                } else {
                  callback(new Error('数据格式错误'));
                }
              } catch (err) {
                callback(new Error('数据格式错误'));
              }
            },
          },
        ],
        nickName: [
          {
            require: true,
            message: '不能为空',
            trigger: 'change',
            whitespace: true,
          },
        ],
      },
      merchant: {}, //用户的商户信息
      formInfo: {
        userName: '',
        nickName: '',
        email: '',
        phonenumber: '',
      },
      originData: {}, //初始化数据
      editType: '', //编辑数据类型
    };
  },
  mounted() {
    this.getDetail();
  },
  methods: {
    async getDetail() {
      if (this.loading) return;
      this.errorFlag = false;
      this.loading = true;
      const [result, error] = await getUser(this.userId);
      this.loading = false;
      if (error || !result.data.user) {
        this.errorFlag = true;
        return;
      }

      const {
        merchant,
        roleInfos,
        organizeId,
        email,
        positionInfos,
        phonenumber,
        status,
        ...req
      } = result.data.user;
      this.merchant = merchant;
      this.originData = {
        ...req,
        positionIds: result.data?.positionIds || [],
        organizeId: organizeId || this.organizeTreeList[0].organizeId || '',
        email: email ? aseDecrypt(email) : undefined,
        phonenumber: phonenumber ? aseDecrypt(phonenumber) : undefined,
        status: String(status),
        roleIds: result.data?.roleIds || [],
        roleNameInfos: roleInfos?.map((item) => item.roleName).join(','),
        positionName: positionInfos?.map((item) => item.positionName).join(','),
      };
      this.formInfo = {
        userName: this.originData.userName,
        nickName: this.originData.nickName,
        email: this.originData.email,
        phonenumber: this.originData.phonenumber,
      };
    },
    // 开启编辑
    openEdit(type) {
      if (this.editType) {
        this.$message.warning('请先保存当前修改');
        return;
      }
      this.editType = type;
    },
    // 关闭编辑
    cancelEdit() {
      this.editType = '';
      this.$refs.form.resetFields();
      this.formInfo = {
        userName: this.originData.userName,
        nickName: this.originData.nickName,
        email: this.originData.email,
        phonenumber: this.originData.phonenumber,
      };
    },
    // 保存数据
    async saveUserDetail(name) {
      this.loading = false;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.loading) return;
          // 修改数据
          this.loading = true;
          const { email, phonenumber, ...req } = this.originData;
          let changeData = {};
          if (name === 'email' || name === 'phonenumber') {
            changeData[name] = rsaCode(this.formInfo[name]);
          } else {
            changeData[name] = this.formInfo[name];
          }
          //重新组合数据
          const queryParams = {
            ...req,
            email: email ? rsaCode(email) : '',
            phonenumber: phonenumber ? rsaCode(phonenumber) : undefined,
            ...changeData,
          };
          console.log(queryParams);
          const [, error] = await updateUser(queryParams);
          this.loading = false;
          if (error) return;
          this.editType = '';
          this.getDetail();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    resetPassword() {
      this.changePasswordVisible = true;
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$store.dispatch('base/GetInfo');
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped>
.input-block {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
/deep/.ant-form-item {
  margin-bottom: 0;
}
</style>
