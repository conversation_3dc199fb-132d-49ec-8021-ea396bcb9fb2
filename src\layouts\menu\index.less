// .shadow {
//   box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
// }
.side-menu {
  position: relative;
  // border-right: 1px solid @g-gray-light;
  background-color: #fff;
  overflow: unset;
  z-index: 1;
  background-image: linear-gradient(to bottom, #3bfb2d08, #ffffff);
  &::after {
    background: url('@/assets/images/left-menu.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    content: '';
    width: 220px;
    height: 256px;
    position: absolute;
    bottom: 0;
    z-index: -1;
  }
  /deep/ .ant-menu {
    background: none;
  }
  /deep/ .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
  }
  /deep/.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background-image: linear-gradient(to right, @menu-color, @menu-color);
    // background-image: linear-gradient(to right, #43bdff, #1890ff);
    color: #ffffff;
    border-radius: 8px;
    &::after {
      border: none;
    }
  }
  /deep/.ant-menu-item-selected > a,
  .ant-menu-item-selected > a:hover {
    color: #ffffff;
  }
  /deep/.ant-menu-submenu-selected {
    color: rgba(0, 0, 0, 0.65);
  }
  .ant-menu-item:hover,
  .ant-menu-item-active,
  .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
  .ant-menu-submenu-active,
  .ant-menu-submenu-title:hover {
    color: @menu-color !important;
  }
  /deep/.ant-menu-vertical .ant-menu-item,
  .ant-menu-vertical-left .ant-menu-item,
  .ant-menu-vertical-right .ant-menu-item,
  .ant-menu-inline .ant-menu-item,
  .ant-menu-vertical .ant-menu-submenu-title,
  .ant-menu-vertical-left .ant-menu-submenu-title,
  .ant-menu-vertical-right .ant-menu-submenu-title,
  .ant-menu-inline .ant-menu-submenu-title {
  }
}
.menu {
  flex: 1;
  overflow-x: hidden;
  /deep/ &.ant-menu {
    padding: 24px 10px;
    border: none;
  }
  /deep/ &.ant-menu-vertical {
    border-right: 1px solid rgba(0, 0, 0, 0.15);
  }
  &.menu-collaped {
    width: 60px;
    /deep/ .ant-menu-submenu-title,
    /deep/ .ant-menu-item {
      padding: 0 calc(50% - 16px / 2) !important;
    }
  }
}


.menu-bottom {
  display: none;
  width: 100%;
  height: 38px;
  border-top: 0.5px solid @g-gray-light;
  background: #fff;
  // display: flex;
  // align-items: center;
  .icon {
    cursor: pointer;
    float: left;
    font-size: 16px;
    margin-left: 16px;
  }
  &.menu-collapsed {
    justify-content: space-around;
    .icon {
      margin-left: 0;
    }
  }
}
