<template>
  <div class="full-screen"><screen :url="url"></screen></div>
</template>

<script>
import screen from '@/views/bigScreen/components/screen';
import getBigScreenUrl from '@/global/bigScreen/bigScreenUrl';

export default {
  name: 'enterpriseBigData',
  components: { screen },
  data() {
    return {
      url: getBigScreenUrl.call(this),
    };
  },
  mounted() {
    document.title = '企业大数据';
  },
};
</script>

<style scoped></style>
