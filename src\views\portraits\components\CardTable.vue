<template>
  <PortraitCard
    :span="span"
    :title="title"
    :detailUrl="detailUrl"
    :pictureId="pictureId"
    :canExpand="canExpand"
    v-bind="$attrs"
    ref="PortraitCard"
  >
    <div class="table-wrap">
      <div
        class="table-left"
        v-if="cardList && cardList.length > 0"
        :style="{ width: leftWidth }"
      >
        <div class="card-item" v-for="(item, index) in cardList" :key="index">
          <div class="l-img">
            <img :src="item.img" alt="" />
          </div>
          <div class="r-t">
            <div class="tip">{{ item.label }}</div>
            <div class="num">
              <span>{{
                isNullShowText(tableInfo[item.key] ?? pictureInfo[item.key])
              }}</span>
              {{ item.unit }}
            </div>
          </div>
        </div>
      </div>

      <components
        v-if="rightComponent"
        :is="rightComponent"
        :style="{ width: 'calc(100% - ' + leftWidth + ')' }"
        :pictureId="pictureId"
        v-bind="$attrs"
        :loadDataCallback="loadData"
        :dataCount="tablePage.total"
        :tableData="tableData"
        :pictureInfo="pictureInfo"
        class="table-right"
        :rowHeight="45"
        :scrollInterval="60"
        :visibleRowCount="10"
      >
      </components>
    </div>
  </PortraitCard>
</template>

<script>
import { isNullShowText } from '@/utils/common';
import PortraitCard from './PortraitCard.vue';
import ScrollTable from './ScrollTable.vue';

export default {
  components: {
    PortraitCard,
    ScrollTable,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    canExpand: {
      type: Boolean,
      default: false,
    },
    span: {
      type: Number,
      default: 24,
    },
    title: {
      type: String,
      default: '',
    },
    detailUrl: {
      type: String,
      default: '',
    },
    cardList: {
      type: Array,
      default: () => [],
    },
    pictureId: {
      type: String,
      default: '',
    },
    leftWidth: {
      type: String,
      default: '200px',
    },
    rightComponent: {
      type: Object,
      default: () => ScrollTable,
    },
    loadDataCallback: {
      type: Function,
      default: () => async () => [],
    },
  },
  data() {
    return {
      tablePage: {
        pageNum: 1,
        limit: 20,
        total: 0,
      },
      tableData: [],
      loading: false,
      tableInfo: {},
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    isNullShowText,

    async loadData(args) {
      this.loading = true;
      const [res, err] = await this.loadDataCallback({
        ...this.tablePage,
        ...args,
        unifiedCreditCode: this.pictureId,
        // unifiedCreditCode: '91320214MA1MEY1L65',
      });
      this.loading = false;
      if (err || typeof res.data !== 'object') return;
      let list = [];
      if (Array.isArray(res.data)) {
        list = res.data;
      } else {
        list = Object.values(res.data).find((item) => Array.isArray(item));
      }
      list.forEach((item) => {
        // if (!this.tableData.find((q) => q.id == item.id)) {
        this.tableData.push({
          ...item,
          index: this.tableData.length,
        });
        // }
      });
      this.tableInfo = res.data;
      this.tablePage.total = res.total;
    },
  },
};
</script>

<style scoped lang="less">
.table-wrap {
  width: 100%;
  display: flex;
  flex-direction: row;
  gap: 15px;
  .table-left {
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: content-box;
    padding-right: 10px;
    overflow: hidden;
    gap: 24px;
    flex: none;
  }
  .table-right {
    flex: 1;
  }
}

.card-item {
  width: 100%;
  height: 60px;
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  .l-img {
    flex: none;
    width: 48px;
    display: grid;
    place-content: center;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .r-t {
    flex: 1;
    height: 100%;
    .tip {
      font-size: 14px;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.65);
      white-space: nowrap;
    }
    .num {
      display: flex;
      align-items: baseline;
      font-size: 14px;
      line-height: 14px;
      color: rgba(4, 15, 36, 0.65);
      span {
        font-family: D-DIN;
        font-size: 24px;
        line-height: 40px;
        color: rgba(4, 15, 36, 0.85);
        font-weight: 700;
        max-width: 80px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-block;
        margin-right: 4px;
      }
    }
  }
}
</style>
