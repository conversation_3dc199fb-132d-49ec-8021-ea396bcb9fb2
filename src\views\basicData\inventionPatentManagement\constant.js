import moment from 'moment';
// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    fixed: 'left',
    width: 60,
  },
  {
    type: 'seq',
    fixed: 'left',
    title: '序号',
    width: 60,
  },
  {
    field: 'rateTime',
    title: '日期',
    width: 110,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY') : '';
    },
  },
  {
    field: 'mainClassificationNum',
    title: '主分类号',
    minWidth: 150,
  },
  {
    field: 'enterpriseName',
    title: '企业名称',
    width: 100,
  },
  {
    field: 'address',
    title: '地址',
    width: 130,
  },
  {
    field: 'organization',
    title: '专利机构',
    width: 130,
  },
  {
    field: 'authorizationDate',
    title: '授权日期',
    width: 110,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD') : '';
    },
  },
  {
    field: 'inventName',
    title: '发明名称',
    width: 110,
  },
  {
    field: 'applicationNum',
    title: '申请号',
    width: 140,
  },
  {
    field: 'applicationDate',
    title: '申请日期',
    width: 110,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD') : '';
    },
  },
  {
    field: 'patentmanPost',
    title: '专利权人邮箱',
    width: 110,
  },
  {
    field: 'patentType',
    title: '专利类型',
    width: 140,
    formatter: ({ cellValue }) => {
      return cellValue == 1 ? '发明' : cellValue == 2 ? '其它' : '';
    },
  },
  {
    field: 'applicantType',
    title: '申请人类型',
    width: 140,
    formatter: ({ cellValue }) => {
      return getStatus(cellValue);
    },
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 160,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'rateTime',
    title: '日期',
    element: 'slot',
    slotName: 'year',
    rules: [{ required: true, message: '请输入日期' }],
  },
  {
    field: 'name',
    title: '企业名称',
    element: 'a-input',
  },
  {
    field: 'inventName',
    title: '发明名称',
    element: 'a-input',
  },
];

export const initFormValue = () => {
  return {
    rateTime: undefined,
    mainClassificationNum: '',
    enterpriseId: '',
    enterprise: {},
    enterpriseName: '',
    address: '',
    organization: '',
    authorizationDate: '',
    inventName: '',
    applicationNum: '',
    applicationDate: '',
    patentmanPost: '',
    patentType: '',
    applicantType: '',
  };
};

export const getStatus = (code) => {
  switch (code) {
    case '1':
      return '企业';
    case '2':
      return '大专院校';
    case '3':
      return '科研机构';
    case '4':
      return '个人';
    case '5':
      return '其它';
  }
};
