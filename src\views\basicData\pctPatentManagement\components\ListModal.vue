<template>
  <a-modal
    width="600px"
    :title="
      modelTitle === 'add' ? '新增' : modelTitle === 'see' ? '详情' : '编辑'
    "
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="ruleForm"
        :config="formConfig"
        :params="formValue"
        :preview="preview"
      >
        <!-- 企业 -->
        <template #nameSlot>
          <a-select
            :allowClear="true"
            :filterOption="false"
            :showSearch="true"
            placeholder="请选择"
            v-model="formValue.enterprise"
            :notFoundContent="null"
            @change="handleSearch"
            @search="handleSearch"
            :labelInValue="true"
          >
            <a-select-option
              v-for="item in companyList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment';
import { initFormValue } from '../constant';
import { savePctEmphasis, editPctEmphasis } from '@/api/basicData';
import { institutionsMixin } from '../../mixins/institutionsMixin';

export default {
  props: [
    'visible',
    'detail',
    'preview',
    'isLook',
    'modelTitle',
    'patentTypeList',
    'pctTypeList',
  ],
  mixins: [institutionsMixin],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.companyList = [];
          if (!this.detail) return;
          if (this.modelTitle !== 'see') {
            this.formValue.enterprise = {
              label: this.detail.enterpriseName,
              key: this.detail.enterpriseId,
            };
          } else {
            this.formValue.enterprise = this.detail.enterpriseName;
          }
          this.formValue.enterpriseName = this.detail.enterpriseName;
          this.formValue.enterpriseId = this.detail.enterpriseId;
          this.companyList = [
            {
              label: this.formValue.enterpriseName,
              value: this.formValue.enterpriseId,
            },
          ];
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
            ...{ enterprise: this.formValue.enterprise },
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
      formConfig: [
        {
          field: 'mon',
          title: '日期',
          element: 'a-month-picker',
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'ipoAddress',
          title: '知识产权人地址',
          element: 'a-input',
        },
        {
          field: 'enterprise',
          title: '企业名称',
          element: 'slot',
          slotName: 'nameSlot',
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'patentType',
          title: '专利类型',
          element: 'a-select',
          props: {
            options: this.patentTypeList,
            showSearch: true,
            optionFilterProp: 'children',
          },
          preview: {
            formatter: (row) => {
              const list = this.patentTypeList.filter(
                (item) => item.value === row.patentType
              );
              if (list.length !== 0) {
                return list[0].label;
              }
            },
          },
          rules: [{ required: true, message: '请选择', trigger: 'blur' }],
        },
        {
          field: 'affiliatingArea',
          title: '所属地区',
          element: 'a-input',
        },
        {
          field: 'townshipStreet',
          title: '乡镇街道',
          element: 'a-input',
        },
        {
          field: 'bcTownshipStreet',
          title: '补充乡镇街道',
          element: 'a-input',
        },
        {
          field: 'ipname',
          title: '知识产权名称',
          element: 'a-input',
        },
        {
          field: 'iponame',
          title: '知识产权人名称',
          element: 'a-input',
        },
        {
          field: 'applicationNum',
          title: '申请号',
          element: 'a-input',
        },
        {
          field: 'iptype',
          title: '知识产权类型',
          element: 'a-input',
          // element: 'a-select',
          // props: {
          //   options: [
          //     { value: '1', label: '应税收入' },
          //     { value: '2', label: '工业产值' },
          //   ],
          // },
        },
        {
          field: 'ipotype',
          title: '知识产权人类型',
          element: 'a-input',
          // element: 'a-select',
          // props: {
          //   options: [
          //     { value: '1', label: '应税收入' },
          //     { value: '2', label: '工业产值' },
          //   ],
          // },
        },
        {
          field: 'type',
          title: '类型',
          element: 'a-select',
          props: {
            options: this.pctTypeList,
            showSearch: true,
            optionFilterProp: 'children',
          },
          preview: {
            formatter: (row) => {
              const list = this.pctTypeList.filter(
                (item) => item.value === row.type
              );
              if (list.length !== 0) {
                return list[0].label;
              }
            },
          },
          rules: [{ required: true, message: '请选择' }],
        },
      ],
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      console.log(2222);
      this.formConfig[0].props.options = this.stateSelect;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      if (this.modelTitle === 'see') {
        this.handleCancel();
        return;
      }
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.formValue.enterpriseId = this.formValue.enterprise.key;
          this.formValue.enterpriseName = this.formValue.enterprise.label;
          this.formValue.rateTime = moment(
            this.formValue.rateTime?.startValue
          ).format('YYYY-MM');
          if (this.modelTitle === 'add') {
            this.savePctEmphasis();
          } else {
            this.editPctEmphasis();
          }
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 新增
    async savePctEmphasis() {
      const [, err] = await savePctEmphasis(this.formValue);
      if (err) return;
      this.$message.success('新增成功!');
      this.$emit('loadData');
    },
    // 编辑
    async editPctEmphasis() {
      const [, err] = await editPctEmphasis(this.formValue);
      if (err) return;
      this.$message.success('编辑成功!');
      this.$emit('loadData');
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
