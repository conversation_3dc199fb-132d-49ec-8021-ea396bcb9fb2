{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "strict": false,
    "noImplicitThis": false,
    "jsx": "preserve",
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowJs": true,
    "noEmit": true, // 只包含 js 文件，不编译，跳过编译检查
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "useDefineForClassFields": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },
    "typeRoots": ["./node_modules/@types", "./src/typings"],
    "lib": ["esnext", "dom"]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.js",
    "src/**/*.jsx",
    "src/**/*.vue"
  ],
  "exclude": ["node_modules"],
  "vueCompilerOptions": {
    "extensions": [".vue"],
    "target": 2.7
  }
}
