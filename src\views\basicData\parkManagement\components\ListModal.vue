<template>
  <a-modal
    width="600px"
    :title="modelTitle === 'add' ? '新增' : '编辑'"
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm ref="ruleForm" :config="formConfig" :params="formValue">
        <template #area>
          <a-input
            placeholder="请输入数字"
            v-model="formValue.totalArea"
            class="model-unit"
            type="number"
          />
          <span class="unit">m²</span>
        </template>
        <template #buildArea>
          <a-input
            placeholder="请输入数字"
            v-model="formValue.completedArea"
            class="model-unit"
            type="number"
          />
          <span class="unit">m²</span>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import { initFormValue, checkNum } from '../constant';
import { savePark, editPark } from '@/api/basicData';
export default {
  props: ['visible', 'detail', 'isLook', 'modelTitle'],
  components: {},
  dicts: ['park_type'],
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
    };
  },
  computed: {
    formConfig() {
      return [
        {
          field: 'parkName',
          title: '园区名称',
          // itemProps: {
          //   help: '请输入真实姓名',
          // },
          props: {
            placeholder: '请输入园区名称',
          },
          rules: [{ required: true, message: '请输入园区名称' }],
        },
        {
          field: 'type',
          title: '园区类别',
          element: 'a-select',
          props: {
            options: this.dict?.type?.park_type || [],
            showSearch: true,
            optionFilterProp: 'children',
          },
          rules: [{ required: true, message: '请输入园区类别' }],
        },
        {
          field: 'address',
          title: '主要地址',
          element: 'a-input',
          rules: [{ required: true, message: '请输入主要地址' }],
        },
        {
          field: 'parkNameDetail',
          title: '详细园区名称',
          element: 'a-input',
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'addressDetail',
          title: '详细地址',
          element: 'a-input',
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'totalArea',
          title: '总规划面积',
          element: 'slot',
          slotName: 'area',
          rules: [{ required: true, validator: checkNum, trigger: 'change' }],
        },
        {
          field: 'completedArea',
          title: '总建成面积',
          element: 'slot',
          slotName: 'buildArea',
          rules: [{ required: true, validator: checkNum, trigger: 'change' }],
        },
        {
          field: 'isCompleted',
          title: '建成状况',
          element: 'a-select',
          rules: [{ required: true, message: '请输入' }],
          props: {
            options: [
              { value: '1', label: '已建成' },
              { value: '0', label: '未建成' },
            ],
          },
        },
      ];
    },
  },
  methods: {
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate(async (valid) => {
        console.log(valid);
        if (valid) {
          console.log(this.formValue);
          if (this.modelTitle === 'add') {
            const [, err] = await savePark(this.formValue);
            if (err) return;
            this.$emit('loadData');
          } else {
            const [, err] = await editPark(this.formValue);
            if (err) return;
            this.$emit('loadData');
          }
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
