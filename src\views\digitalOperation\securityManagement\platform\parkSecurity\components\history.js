//获取历史记录不同类型的表格列数据
const getHistoryTableColumn = (type) => {
  const columns = {
    columnForIE: [
      {
        title: '检查时间',
        field: 'inspectTime',
        minWidth: 120,
      },
      {
        title: '信息录入时间',
        field: 'informationEntryTime',
        minWidth: 120,
      },
      {
        title: '检查人员',
        field: 'inspectName',
        minWidth: 120,
      },
      {
        title: '单位消防安全负责人',
        field: 'fireSafetyName',
        minWidth: 120,
      },
    ],
    columnForFS: [
      {
        title: '检查时间',
        field: 'inspectTime',
        minWidth: 120,
      },
      {
        title: '信息录入时间',
        field: 'informationEntryTime',
        minWidth: 120,
      },
      {
        title: '检查结果',
        field: 'inspectionResult',
        minWidth: 120,
        formatter({ cellValue }) {
          return cellValue === '1' ? '合格' : '不合格';
        },
      },
      {
        title: '复检时间',
        field: 'reviewTime',
        minWidth: 120,
      },
      {
        title: '复检结果',
        field: 'reviewResult',
        minWidth: 120,
        formatter({ cellValue }) {
          return cellValue === '1' ? '合格' : '不合格';
        },
      },
    ],
    columnForElevator: [
      {
        title: '序号',
        type: 'seq',
        width: 80,
      },
      {
        title: '检验结论',
        field: 'verifyResult',
        minWidth: 120,
      },
      {
        title: '检验时间',
        field: 'verifyDate',
        minWidth: 120,
      },
      {
        title: '下次检验时间',
        field: 'verifyDateNext',
        minWidth: 120,
      },
      {
        title: '信息录入时间',
        field: 'inputTime',
        minWidth: 120,
      },
    ],
  };
  switch (type) {
    case 'IE':
      return columns.columnForIE;
    case 'FS':
      return columns.columnForFS;
    case 'ELE':
      return columns.columnForElevator;
    default:
      return columns.columnForIE;
  }
};

export { getHistoryTableColumn };
