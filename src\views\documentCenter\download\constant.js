import moment from 'moment';

export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'fileName',
      title: '文件名称',
      props: {
        placeholder: '请输入文件名称搜索',
      },
    },
    {
      field: 'fileType',
      title: '文件类型',
      element: 'a-select',
      props: {
        options: [
          { value: '', label: '全部' },
          { value: 'COMMON', label: 'common' },
          { value: 'FILE', label: 'file' },
          { value: 'EXCEL', label: 'excel' },
          { value: 'IMAGE', label: 'image' },
          { value: 'AUDIO', label: 'audio' },
          { value: 'VIDEO', label: 'video' },
        ],
        showSearch: true,
        optionFilterProp: 'children',
      },
    },
    {
      field: 'uploadTime',
      title: '下载时间',
      element: 'a-range-picker',
      props: {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
        showTime: {
          defaultValue: [
            moment('00:00:00', 'HH:mm:ss'),
            moment('23:59:59', 'HH:mm:ss'),
          ],
        },
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { name: '', fileType: '', uploadTime: ['', ''] },
};
export const tableColumn = () => [
  { field: '', title: '', type: 'checkbox', width: 70, fixed: 'left' },
  { field: 'fileName', title: '文件名称', align: 'center' },
  { field: 'fileType', title: '文件类型', align: 'center' },
  { field: 'createTime', title: '下载时间', align: 'center' },
  { field: 'fileSize', title: '文件大小', align: 'center' },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 180,
  },
];
