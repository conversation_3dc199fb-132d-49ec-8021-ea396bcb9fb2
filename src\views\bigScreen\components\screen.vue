<template>
  <iframe
    class="full-screen"
    ref="fullScreen"
    frameborder="0"
    style="border: 0px"
    :src="url"
  ></iframe>
</template>

<script>
export default {
  name: 'screen',
  props: {
    url: {
      type: String,
      default: function () {
        return '';
      },
    },
  },
  data() {
    return {
      resizeObserver: null,
    };
  },
  mounted() {
    // 获取 DOM 元素
    const targetNode = this.$refs.fullScreen;

    // 创建 ResizeObserver 实例
    this.resizeObserver = new ResizeObserver((entries) => {
      console.log(entries, 'entries');
      for (let entry of entries) {
        console.log('元素尺寸变化:', entry.contentRect);
        this.$emit('changeSize', entry);
        // 在这里可以执行其他相应的操作
      }
    });

    // 开始观察
    if (targetNode) {
      this.resizeObserver.observe(targetNode);
    }
  },
  beforeDestroy() {
    // 在组件销毁之前停止观察
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  },
};
</script>

<style scoped lang="scss">
iframe {
  padding: 10px;
  // width: 1920px;
  // height: 1080px;
  width: 100vw;
  height: 100vh;
  /*top: 0;*/
  /*left: 0;*/
  transform-origin: top left;
  transform: scale(1, 1);
}
</style>
