import request from '@/utils/request';

// 删除
export function deleteApp(organizeId) {
  return request({
    url: `/api/authority/admin/organize/remove/${organizeId}`,
    method: 'GET',
  });
}
// 组织架构列表
export function getList(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/organize/list',
    method: 'GET',
    params: query,
  });
}
// 获取用户当前组织下拉树列表
export function getTreeList(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/organize/treeselect',
    method: 'GET',
    params: query,
  });
}
// 根据组织编号获取详细信息
export function getOrganizeDetail({ organizeId }) {
  return request({
    url: `/api/authority/admin/organize/${organizeId}`,
    method: 'GET',
  });
}
// 新增组织架构
export function addOrganize(data) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/organize/addOrganize',
    method: 'POST',
    data: data,
  });
}
// 修改组织架构
export function updateOrganize(data) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/organize/updateOrganize',
    method: 'POST',
    data: data,
  });
}
// 删除组织架构
export function deleteOrganize(organizeId) {
  return request({
    url: `/api/authority/admin/organize/remove/${organizeId}`,
    method: 'GET',
  });
}
//下载组织导入模板
export function importExcel(params) {
  return request({
    url: `/api/authority/admin/organize/importExcel`,
    method: 'POST',
    data: params,
  });
}
