import { renderTaskStates } from '../utils/index';

function getTableColumn() {
  const _this = this;
  return [
    {
      title: '任务名称',
      field: 'taskName',
    },
    {
      title: '任务内容',
      field: 'content',
    },
    {
      title: '任务类型',
      field: 'taskType',
      formatter({ cellValue }) {
        const currentBiz = _this.taskTypes.filter((item) => {
          return item.value == cellValue;
        });
        return currentBiz[0] ? currentBiz[0].label : cellValue;
      },
    },
    {
      title: '办理人',
      field: 'transactorName',
    },
    {
      title: '创建人',
      field: 'creatorName',
    },
    {
      title: '任务状态',
      field: 'taskStatus',
      slots: {
        default(row) {
          const result = renderTaskStates.call(this, row);
          if (typeof result === 'string') return result;
          return (
            <span style={{ color: result.colorValue }}>{result.text}</span>
          );
        },
      },
    },
    {
      title: '办理时限',
      field: 'transactorTime',
    },
    {
      title: '创建时间',
      field: 'createTime',
      width: '13%',
    },

    {
      title: '完成时间',
      field: 'finishTime',
      width: '13%',
    },
  ];
}

export { getTableColumn };
