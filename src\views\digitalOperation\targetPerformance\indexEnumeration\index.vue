<template>
  <page-layout>
    <div class="index-management">
      <h4>指标枚举配置</h4>
      <div>
        <a-button type="primary" @click="addPrimary(1)">新增一级指标</a-button>
      </div>
      <a-tree
        :tree-data="treeData"
        class="custom-tree"
        :defaultExpandAll="true"
        :autoExpandParent="true"
        @select="getNodeInfo"
      >
        <template slot="icon" slot-scope="item">
          <div class="node-container">
            <span class="node-title">{{ item.title }}</span>
            <a-icon
              v-if="item.children"
              class="icon"
              id="plus"
              type="plus-circle"
              @click="addSecondaryIndex(3, item)"
            ></a-icon>
            <a-icon
              class="icon"
              id="plus"
              type="edit"
              @click="editCatalog(item.children ? 2 : 4, item)"
            ></a-icon>
            <a-icon
              class="icon"
              id="delete"
              type="delete"
              @click="deleteCatalog(item)"
            ></a-icon>
          </div>
        </template>
      </a-tree>
      <!--        弹框-->
      <ModalBox
        :visible="modalVisible"
        :modalType="modalType"
        :operationType="operationType"
        :formState="formState"
        @submitHandler="submitHandler"
        @cancel="handleCancel"
      ></ModalBox>
    </div>
  </page-layout>
</template>

<script>
import ModalBox from './modal.vue';
import {
  enumList,
  enumAdd,
  enumUpdate,
  enumDelete,
} from '@/api/digitalOperation/targetPerformance/indexEnumeration/index.js';
export default {
  name: 'IndexEnumeration',
  components: {
    ModalBox,
  },

  data() {
    return {
      formState: {},
      visibleTitle: '', //新增弹框标题
      nodeDetail: {}, //节点详情
      operate: '', //节点操作类型
      modalVisible: false,
      disabled: false, //一级指标是否可编辑
      nodeLevel: '', //获取点击节点的等级
      parentNodeInfo: '', //获取点击节点的父节点信息
      treeData: [
        {
          title: '政策文件',
          key: '20231115180728587000003222511539',
          parentId: null,
          children: [
            {
              children: null,
              key: '20231115180732657000005222511533',
              parentId: '20231115180728587000003222511539',
              scopedSlots: { title: 'icon' },
              title: '主机',
            },
          ],
        },
      ],
      modalType: '1',
      operationType: 1, //1-一级新增，2一级编辑，3二级新增，4二级编辑
    };
  },
  mounted() {
    this.enumList();
  },
  methods: {
    addPrimary() {
      this.modalVisible = true;
      this.modalType = '1';
      this.operationType = 1;
    },
    handleCancel() {
      this.modalVisible = false;
    },
    //指标查询
    async enumList() {
      const [res, err] = await enumList();
      if (err) return;
      let data = res?.data;
      data = JSON.parse(
        JSON.stringify(data)
          .replace(/bpmTargetEnumTwoList/g, 'children')
          .replace(/targetName/g, 'title')
          .replace(/id/g, 'key')
      );
      this.treeData = data;
      this.addAttrToTree(this.treeData);
    },
    //指标新增
    async enumAdd(params) {
      const [, err] = await enumAdd(params);
      if (err) return this.$message.error('新增失败');
      this.$message.success('新增成功');
      this.modalVisible = false;
      this.enumList();
    },
    //指标编辑
    async enumUpdate(params) {
      const [, err] = await enumUpdate(params);
      if (err) return this.$message.error('编辑失败');
      this.$message.success('编辑成功');
      this.modalVisible = false;
      this.enumList();
    },

    //指标删除
    async enumDelete(id) {
      const [, err] = await enumDelete(id);
      if (err) return this.$message.error('删除失败');
      this.$message.success('删除成功');
      this.modalVisible = false;
      this.enumList();
    },
    submitHandler() {
      console.log(this.formState, 'formState');
      let p = {};
      switch (this.operationType) {
        case 1: //新增一级
          this.enumAdd({ oneTargetName: this.formState.targetName });
          break;
        case 2: //编辑一级
          this.enumUpdate(this.formState);
          break;
        case 3: //新增二级
          p = { ...this.formState };
          p.twoTargetName = p.targetName;
          delete p.targetName;
          this.enumAdd(p);
          break;
        case 4: //编辑二级
          this.enumUpdate(this.formState);
          break;
      }
    },
    addSecondaryIndex(type, item) {
      console.log('🚀 ~ ', type, item);
      this.modalVisible = true;
      this.operationType = 3;
      this.modalType = '1';
      this.formState = {
        targetName: '',
        targetUnit: '',
        rule: '',
        parentId: item.key,
        initialValue: undefined,
      };
    },

    //处理数据
    addAttrToTree(jsonData) {
      console.log(jsonData, 'jsonData');
      if (jsonData) {
        for (let i = 0; i < jsonData.length; i++) {
          this.$set(jsonData[i], 'scopedSlots', { title: 'icon' });
          if (Object.prototype.hasOwnProperty.call(jsonData[i], 'key')) {
            this.addAttrToTree(jsonData[i].children);
          }
        }
      } else {
        jsonData = [];
      }
      return jsonData;
    },
    // 编辑指标
    editCatalog(type, item) {
      this.modalVisible = true;
      this.operationType = type;
      //获取点击节点的等级
      this.nodeLevel = this.getNodeLevel(item);
      //获取点击节点的父节点信息
      this.parentNodeInfo = this.findParentNodeInfo(this.treeData, item.key);
      if (this.operationType === 2) {
        this.formState = {
          targetName: item.title,
          id: item.key,
        };
      } else {
        this.formState = {
          targetName: item.title,
          targetUnit: item.targetUnit,
          rule: item.rule,
          initialValue: item.initialValue,
          id: item.key,
        };
      }
    },
    // 删除指标
    deleteCatalog(item) {
      // 指标下有子指标，不可删除
      if (item.children && item.children.length !== 0) {
        this.$message.error(
          '该指标下有文件，不可直接删除，请先删除里面的文件！'
        );
        return;
      }
      let that = this;
      this.$confirm({
        title: `确定删除指标名为‘${item.title}’的指标吗?`,
        icon: 'warning',
        onOk() {
          return new Promise((resolve, reject) => {
            setTimeout(Math.random() > 0.5 ? resolve : reject, 1000);
            // let node = that.findParentNode(that.treeData, item.key);
            // node.children = node.children.filter((a) => a.key !== item.key);
            // TODO调用保存接口保存删除后的指标结构
            that.enumDelete(item.key);
          }).catch(() => console.log('删除失败'));
        },
        onCancel() {},
      });
    },

    // 点击节点获取节点信息
    getNodeInfo(selectedKeys) {
      if (selectedKeys.length === 0) {
        return;
      }
      let clickedNode = this.findNodeByKey(this.treeData, selectedKeys[0]);
      let parent = this.findParentNode(this.treeData, selectedKeys[0]);
      let level = this.getNodeLevel(parent);
      // 该节点为叶子节点时传参给父组件更新页面标签信息
      if (level === 2) {
        let nodeInfo = {
          id: clickedNode.key,
          name: clickedNode.title,
          treeData: this.treeData,
        };
        this.$emit('getNodeInfo', nodeInfo);
      }
      this.nodeDetail = clickedNode;
    },

    // 获取当前节点信息
    findNodeByKey(nodes, key) {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].key === key) {
          return nodes[i];
        }
        if (nodes[i].children) {
          const foundNode = this.findNodeByKey(nodes[i].children, key);
          if (foundNode) {
            return foundNode;
          }
        }
      }
      return null;
    },
    // 获取父亲节点node信息
    findParentNode(nodes, key) {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].children) {
          if (nodes[i].children.some((child) => child.key === key)) {
            return nodes[i];
          }
          const foundNode = this.findParentNode(nodes[i].children, key);
          if (foundNode) {
            return foundNode;
          }
        }
      }
      return null;
    },
    // 获取父亲节点item信息
    findParentNodeInfo(nodes, key) {
      console.log(key);

      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].children) {
          if (nodes[i].children.some((child) => child.key === key)) {
            console.log(nodes[i], 'nodes[i]');
            return nodes[i];
          }
        }
      }
      return null;
    },
    // 判断当前节点层级
    getNodeLevel(node, level = 0) {
      if (node && node.key) {
        return this.getNodeLevel(
          this.findParentNode(this.treeData, node.key),
          level + 1
        );
      } else {
        return level;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.index-management {
  overflow: auto;
  background-color: #fff;
  height: 90vh;
  padding: 16px 24px;
  .node-container {
    .icon {
      padding: 0 2px;
    }
  }
  h4 {
    font-size: 16px;
    color: #222;
    line-height: 32px;
    height: 32px;
    min-width: 120px;
    margin-bottom: 12px;
  }
  .custom-tree .icon {
    margin-left: 5px;
  }

  #plus {
    color: #409eff;
  }

  #edit {
    color: #67c23a;
  }

  #delete {
    color: #f56c6c;
  }
}
</style>
