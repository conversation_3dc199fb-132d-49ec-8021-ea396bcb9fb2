<template>
  <a-modal
    width="600px"
    :title="modelTitle === 'add' ? '新增' : '编辑'"
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm ref="ruleForm" :config="formBasicConfig" :params="formValue">
        <template #imgUpload>
          <Upload
            list-type="picture-card"
            :limitSize="2"
            fileType="IMAGE"
            :file-list.sync="formValue.imgUrl"
            :data="{
              fileType: 'IMAGE',
            }"
          />
        </template>
        <template #xmlUpload>
          <Upload
            list-type="text"
            :limitSize="4"
            fileType="XML"
            :file-list.sync="formValue.xmlUrl"
            :data="{
              fileType: 'IMAGE',
            }"
          />
        </template>
      </DynamicForm>
      <!-- <a-row type="flex" justify="center">
        <a-button
          type="primary"
          style="margin-right: 16px"
          @click="onClickSubmit"
          >提交</a-button
        >
        <a-button @click="onClickReset">取消</a-button>
      </a-row> -->
    </a-spin>
  </a-modal>
</template>

<script>
import Upload from '@/components/Upload';
import { initBasicFormValue, checkNumber } from '../constant';
import { editInformation } from '@/api/basicData';
export default {
  props: ['visible', 'detail', 'isLook', 'modelTitle', 'dict'],
  components: { Upload },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          this.formValue = {
            ...initBasicFormValue(),
            ...this.detail,
            idmType: '1',
          };
        } else {
          this.formValue = initBasicFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initBasicFormValue(),
    };
  },
  computed: {
    formBasicConfig() {
      return [
        {
          field: 'indicator',
          title: '基础信息',
          element: 'a-select',
          props: {
            disabled: true,
            options: this.dict?.tkc_basic || [],
          },
        },
        {
          field: 'value',
          title: '值',
          element: 'a-textarea',
          props: {
            maxLength: 200,
          },
          rules: [{ required: true, trigger: 'change', message: '请输入值' }],
        },
        {
          field: 'unit',
          title: '单位',
          element: 'a-input',
          props: {
            disabled: true,
          },
        },
      ];
    },
  },
  methods: {
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        console.log(valid);
        if (valid) {
          console.log('编辑参数', this.formValue);
          this.editInformation();
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 新增
    async editInformation() {
      const [res, err] = await editInformation(this.formValue);
      if (err) return;
      console.log('新增成功', res);
      this.$emit('loadData');
    },
  },
};
</script>
