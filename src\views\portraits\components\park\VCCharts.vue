<template>
  <a-row :gutter="12">
    <a-col :span="14" class="detail">
      <ChartsCard
        chartTitle="被投资企业产业类型(本年)"
        :options="charts1Options"
      />
    </a-col>

    <a-col :span="10" class="detail">
      <ChartsCard chartTitle="风险投资记录(近4年)" :options="charts2Options" />
    </a-col>
  </a-row>
</template>
<script>
import ChartsCard from '../ChartsCard.vue';
import { useLineCharts, usePieCharts } from '../chartHooks';
export default {
  components: {
    ChartsCard,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    charts1: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts2: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
  },
  data() {
    return {
      charts1Options: {},
      charts2Options: {},
    };
  },
  watch: {
    charts1: {
      deep: true,
      handler() {
        this.handleInitCharts1();
      },
    },
    charts2: {
      deep: true,
      handler() {
        this.handleInitCharts2();
      },
    },
  },
  methods: {
    handleInitCharts1() {
      if (!this.charts1?.data) return;
      const { data = [] } = this.charts1;
      this.charts1Options = usePieCharts({
        data,
      });
    },
    handleInitCharts2() {
      const unit = '万元';
      if (!this.charts2?.data) return;
      const { xAxis = [], data = [] } = this.charts2;
      this.charts2Options = useLineCharts({
        xAxis,
        unit,
        series: [
          { name: '风险投资记录(近4年)', data: data || [], color: '#ffa23d' },
        ],
      });
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .title {
    text-align: right;
    color: #999999;
    width: 148px;
    line-height: 26px;
  }

  .info {
    text-align: left;
    .num {
      font-family: D-DIN;
      font-size: 24px;
      font-weight: bold;
      line-height: 24px;
      color: #333333;
      z-index: 0;
    }

    .unit {
      margin-left: 4px;
      font-family: PingFang SC;
      font-size: 12px;
      color: #333333;
    }
  }
}
</style>
