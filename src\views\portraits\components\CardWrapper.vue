<template>
  <a-col :span="24">
    <a-card :class="['card-wrap-card']">
      <div class="expand-btn">
        <a-button
          v-if="canExpand"
          type="primary"
          size="small"
          :class="['button-show', showAll ? '' : 'button-show-up']"
          @click="changeShowAll"
          >{{ showAll ? '收起' : '展开' }}
        </a-button>
      </div>
      <a-row class="card-wrap-card-cnt">
        <components
          ref="cardItem"
          v-for="(item, index) in cardList"
          :key="index"
          :is="item.components"
          :pictureId="pictureId"
          :pictureInfo="pictureInfo"
          v-bind="item.props || {}"
          :pageName="pageName"
          :canExpand="true"
          :showExpandBtn="false"
          @openModal="openModal"
        />
      </a-row>
    </a-card>
  </a-col>
</template>

<script>
import CardTable from './CardTable.vue';
import IntellectualPropertyRight from './business/IntellectualPropertyRight.vue';
import EnergyInfo from './business/EnergyInfo.vue';
export default {
  props: {
    cardList: {
      type: Array,
      default: () => [],
    },
    pageName: {
      type: String,
      default: '',
    },
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    pictureId: {
      type: String,
      default: '',
    },
  },
  components: { CardTable, IntellectualPropertyRight, EnergyInfo },
  data() {
    return {
      showAll: false,
      canExpand: true,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    openModal() {},
    changeShowAll() {
      if (!this.$refs.cardItem) return;
      if (Array.isArray(this.$refs.cardItem)) {
        this.$refs.cardItem.forEach((item) => {
          console.log(item.$refs.PortraitCard.changeShowAll);
          item.$refs.PortraitCard && item.$refs.PortraitCard.changeShowAll();
        });
      } else {
        this.$refs.cardItem &&
          this.$refs.cardItem.$refs?.PortraitCard.changeShowAll();
      }
      this.showAll = !this.showAll;
    },
  },
};
</script>

<style scoped lang="less">
.card-wrap-card {
  position: relative;
  .expand-btn {
    position: absolute;
    top: 15px;
    right: 27px;
    z-index: 9;
    .button-show {
      position: relative;
      padding-right: 20px;
      margin-left: 8px;
      &::after {
        content: '';
        display: block;
        width: 7.3px;
        height: 4.6px;
        position: absolute;
        top: calc(50% - 2.3px);
        right: 9.4px;
        background-repeat: no-repeat;
        background-image: url('@/assets/images/portraits/1-006.png');
        background-size: 100% 100%;
        transition: all 0.25s ease-in-out;
        transform: rotate(0deg);
      }
      &.button-show-up {
        &::after {
          transform: rotate(180deg);
        }
      }
    }
  }
  ::v-deep .ant-card-head {
    padding: 0;
    border: none;
  }
  ::v-deep .ant-card-head-wrapper {
    background: url('@/assets/images/portraits/1-3.png');
    background-size: 1210px 60px;
    background-position: -8px 0;
    padding-left: 46px;
    padding-right: 24px;
    font-family: PingFang SC;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0em;
    color: #333333;
    height: 60px;
  }

  ::v-deep .ant-card-body {
    padding: 0 !important;
  }

  &.card-wrap-card-hidden {
    ::v-deep & > .ant-card-body {
      height: 0;
      overflow: hidden;
      padding: 0;
    }
  }
  /deep/ .portrait-card-title {
    padding-right: 70px;
  }
  /deep/ .portrait-card-cnt {
    padding: 0 10px;
  }
}
.card-wrap-card-cnt {
  display: flex;
  flex-direction: row;
}
.ant-card {
  background-color: #fff;
  border: none;
  border-radius: 8px;
  color: #333333;
  padding: 0;
  overflow: hidden;
}
</style>
