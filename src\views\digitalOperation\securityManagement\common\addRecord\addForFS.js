const tableData = [
  {
    name: '安全疏散',
    checkContent: '疏散通道是否畅通',
    checkResult: '1',
    remark: '',
  },
  {
    name: '安全疏散',
    checkContent: '安全出口是否畅通',
    checkResult: '1',
    remark: '',
  },
  {
    name: '安全疏散',
    checkContent: '是否设置影响逃生的防盗窗及栅栏',
    checkResult: '1',
    remark: '',
  },
  {
    name: '易燃易爆物品使用管理情况',
    checkContent: '是否违法储存、试用瓶装液化气',
    checkResult: '1',
    remark: '',
  },
  {
    name: '易燃易爆物品使用管理情况',
    checkContent: '是否在具有火灾、爆炸危险的场所吸烟、适用明火',
    checkResult: '1',
    remark: '',
  },
  {
    name: '易燃易爆物品使用管理情况',
    checkContent: '是否违章进行电气焊作业等情况',
    checkResult: '1',
    remark: '',
  },
  {
    name: '消火栓及灭火器配置及维护',
    checkContent: '室内消火栓是否有水',
    checkResult: '1',
    remark: '',
  },
  {
    name: '消火栓及灭火器配置及维护',
    checkContent: '水带、水枪等配件是否齐全',
    checkResult: '1',
    remark: '',
  },
  {
    name: '消火栓及灭火器配置及维护',
    checkContent: '室内消火栓是否被圈占、遮挡',
    checkResult: '1',
    remark: '',
  },
  {
    name: '消防培训',
    checkContent: '从业人员是否经过消防安全培训',
    checkResult: '1',
    remark: '',
  },
  {
    name: '消防培训',
    checkContent: '现场工作人员是否会使用消防器械',
    checkResult: '1',
    remark: '',
  },
  {
    name: '消防培训',
    checkContent: '现场工作人员是否掌握自救逃生知识',
    checkResult: '1',
    remark: '',
  },
  {
    name: '电气线路敷设及装修装饰材料',
    checkContent: '电器线路是否穿戴敷设',
    checkResult: '1',
    remark: '',
  },
  {
    name: '电气线路敷设及装修装饰材料',
    checkContent: '电气线路是否敷设在可燃材料上',
    checkResult: '1',
    remark: '',
  },
  {
    name: '电气线路敷设及装修装饰材料',
    checkContent: '是否存在室内违规停放、充电电动自行车',
    checkResult: '1',
    remark: '',
  },
  {
    name: '电气线路敷设及装修装饰材料',
    checkContent: '人员密集场所是否采用可燃、易燃材料装修',
    checkResult: '1',
    remark: '',
  },
  {
    name: '电气线路敷设及装修装饰材料',
    checkContent: '是否在建筑内使用聚氨酯泡沫彩钢板',
    checkResult: '1',
    remark: '',
  },
  {
    name: '“三合一”场所',
    checkContent: '生产储存经营场所与员工宿舍是否设置在同一连通空间',
    checkResult: '1',
    remark: '',
  },
  {
    name: '物业服务企业',
    checkContent: '对管理区域公用消防设施是否维护管理',
    checkResult: '1',
    remark: '',
  },
  {
    name: '物业服务企业',
    checkContent: '消控室值班制度是否落实',
    checkResult: '1',
    remark: '',
  },
  {
    name: '物业服务企业',
    checkContent: '消防车通道是否畅通',
    checkResult: '1',
    remark: '',
  },
  {
    name: '物业服务企业',
    checkContent: '消防车通道是否设置标识',
    checkResult: '1',
    remark: '',
  },
  {
    name: '物业服务企业',
    checkContent: '是否设置固定的消防宣传栏',
    checkResult: '1',
    remark: '',
  },
];

const getTableColumn = () => {
  return [
    {
      title: '序号',
      type: 'seq',
      width: '4%',
    },
    {
      field: 'name',
      title: '消防安全类别',
    },
    {
      title: '检查内容',
      field: 'content',
    },
    {
      title: '检查情况',
      field: 'checkSituation',
      slots: {
        default: 'checkResult',
      },
    },
    {
      title: '备注',
      field: 'remark',
      slots: {
        default: 'remark',
      },
    },
    {
      title: '上传照片',
      field: 'attachments',
      slots: {
        default: 'attachments',
      },
    },
    {
      title: '操作',
      field: 'operation',
      fixed: 'right',
      width: '4%',
      slots: { default: 'operation' },
    },
  ];
};

export { tableData, getTableColumn };
