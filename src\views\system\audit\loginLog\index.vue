<template>
  <page-layout>
    <div>
      <PageWrapper
        style="margin: 0"
        title=""
        createText=""
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        @handleReset="handleReset"
        @loadData="loadData"
      >
        <!-- table插槽 -->
        <template #operate="{ row }">
          <span class="operate-button" @click="onClickDetail(row, 'edit')">
            查看
          </span>
        </template>
      </PageWrapper>
    </div>
    <LogDetail :visible.sync="infoVisible" :infoDetail="infoDetail"></LogDetail>
  </page-layout>
</template>

<script>
import LogDetail from './LogDetail';
import { getList } from '@/api/system/log';
import { filterOptions, tableColumn } from './constant';
import moment from 'moment';
export default {
  components: { LogDetail },
  data() {
    return {
      loading: false,
      filterOptions,
      tableColumn,
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [{}],
      visible: false,
      infoVisible: false,
      infoDetail: {},
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    handleReset() {
      this.filterOptions.params = {
        userId: '',
        ip: '',
        time: ['', ''],
      };
      this.tablePage.total = 0;
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const { time, ...param } = params;
      let timeRange = {};
      if (time && time.length > 0) {
        timeRange.beginTime = moment(time[0]).valueOf();
        timeRange.endTime = moment(time[1]).valueOf();
      }
      const [res, err] = await getList({
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
        type: 'LOGIN',
        ...param,
        ...timeRange,
      });
      this.loading = false;
      if (err) return;
      // 设置数据
      this.tablePage.total = res.count;
      this.tableData = res.data;
    },
    // 编辑数据
    onClickDetail(data) {
      this.infoVisible = true;
      this.infoDetail = data;
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  padding: 24px;
  background-color: #f4f4f4;
}
// table操作按钮
.operate-button {
  display: inline-block;
  margin-right: 16px;
  padding: 8px 0;
  color: #1677ff;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  cursor: pointer;
}
</style>
