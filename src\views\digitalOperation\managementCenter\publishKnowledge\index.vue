<template>
  <page-layout>
    <div class="main">
      <a-form-model
        ref="formRef"
        :model="publishForm"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 10 }"
      >
        <a-form-model-item
          prop="articleType"
          label="文章类型"
          :required="true"
          :rules="[{ required: true, message: '' }]"
        >
          <a-radio-group
            v-model="publishForm.articleType"
            @change="articleTypeChange"
          >
            <a-radio value="0">图文</a-radio>
            <a-radio value="1">图片</a-radio>
            <a-radio value="2">文档</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item
          prop="title"
          label="标题"
          :required="true"
          :rules="[
            { required: true, message: '请输入标题' },
            {
              min: 1,
              max: 30,
              message: '最长不超过30个字',
              trigger: 'blur',
            },
          ]"
        >
          <a-input
            v-model="publishForm.title"
            placeholder="请输入标题"
            allow-clear
          />
        </a-form-model-item>
        <a-form-model-item
          prop="publishClass"
          label="类别"
          :required="true"
          :rules="[{ required: true, message: '请输入类别' }]"
        >
          <a-cascader
            v-model="publishClass"
            :options="classOptions"
            expand-trigger="hover"
            placeholder="请选择类别"
            @change="publishClassChange"
          />
        </a-form-model-item>
        <a-form-model-item
          v-if="publishForm.articleType == '0'"
          prop="richText"
          label="正文编辑"
          :required="true"
          :rules="[{ required: true, message: '请输入正文内容' }]"
        >
          <RichText
            v-model="publishForm.richText"
            :handleImageUpload="handleImageUpload"
          />
        </a-form-model-item>

        <a-form-model-item
          prop="imgUrl"
          label="上传封面"
          v-if="publishForm.articleType == '0'"
        >
          <uploadPics
            v-model="publishForm.coverPic"
            :accept="'.png, .jpg'"
            :maxCount="1"
            @setNewsImgs="setNewsImg"
            :fileListTemp="publishForm.coverPic"
            :maxSize="10"
          />
          <span class="upload-mark-text"
            >注释：只能上传1张，JPG\PNG格式，大小不超过5MB，建议尺寸4*3。</span
          >
        </a-form-model-item>
        <a-form-model-item prop="displayContent" label="自定义文章显示内容">
          <a-textarea
            v-model="publishForm.displayContent"
            placeholder="请输入自定义文章显示内容"
            :rows="4"
          ></a-textarea>
        </a-form-model-item>
        <a-form-model-item
          v-show="publishForm.articleType == '1'"
          prop="pictures"
          label="上传图片"
          :required="publishForm.articleType == '1' ? true : false"
          :rules="
            publishForm.articleType == '1'
              ? [{ required: true, message: '请上传图片' }]
              : []
          "
        >
          <uploadPics
            v-model="publishForm.pictures"
            :accept="'.jpg, .gif, .png'"
            :maxCount="8"
            @setNewsImgs="setNewsImgs"
            :fileListTemp="publishForm.pictures"
            :maxSize="10"
          />
          <span class="upload-mark-text"
            >注释：支持jpg、gif、png三种格式，大小不超过10MB。</span
          >
        </a-form-model-item>
        <a-form-model-item
          v-show="publishForm.articleType == '2'"
          prop="documents"
          label="上传文档"
          :required="publishForm.articleType == '2' ? true : false"
          :rules="
            publishForm.articleType == '2'
              ? [{ required: true, message: '请上传文档' }]
              : []
          "
        >
          <uploadFiles
            v-model="publishForm.documents"
            :accept="'.pdf, .ppt, .pptx, .doc, .docx, .png, .jpg, .bmp, .mp4, .zip, .rar'"
            @setAnnexList="setAnnexList"
            :fileListTemp="publishForm.documents"
            :maxSize="100"
          ></uploadFiles>

          <span class="upload-mark-text"
            >注释：支持pdf、ppt、pptx、doc、docx、png、jpg、bmp、mp4、zip、rar格式，大小不超过100MB。</span
          >
        </a-form-model-item>
        <a-form-model-item
          prop="attachments"
          label="上传附件"
          :label-col="{ span: 3 }"
          :wrapper-col="{ span: 20 }"
        >
          <vxe-table
            class="flow-detail-table"
            headerAlign="center"
            align="center"
            showOverflow="tooltip"
            :data="publishForm.attachments"
            :column-config="{ resizable: true }"
          >
            <vxe-column field="name" title="附件名称" width="40%"></vxe-column>
            <vxe-column field="fileSize" title="附件大小"> </vxe-column>
            <vxe-table-column title="操作">
              <template #default="{ row }">
                <a @click="delFile(row)">删除</a>
              </template>
            </vxe-table-column>
          </vxe-table>
          <uploadFiles
            v-model="publishForm.attachments"
            :accept="'.pdf, .ppt, .pptx, .doc, .docx, .png, .jpg, .bmp, .mp4, .zip, .rar'"
            :showDefaultUploadList="false"
            @setAnnexList="setAnnexList"
            :tabChangeFlag="tabChangeFlag"
            @tabChangeFlagToggle="tabChangeFlagToggle"
            :delFileId="delFileId"
            :fileListTemp="publishForm.attachments"
          ></uploadFiles>
        </a-form-model-item>
        <a-form-model-item prop="share" label="分享权限" :required="true">
          <a-radio-group v-model="publishForm.share">
            <a-radio value="0">仅内部可查看，不可分享</a-radio>
            <a-radio value="1">允许分享</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" @click="publishHandel">发布</a-button>
          <a-button @click="previewHandel">预览</a-button>
          <a-button @click="saveHandel">保存草稿</a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
  </page-layout>
</template>

<script>
import uploadPics from '@/components/Uploads/uploadPics.vue';
import uploadFiles from '@/components/Uploads/uploadFiles.vue';
import RichText from '@/components/RichText/RichText.vue';
import { fileUpload } from '@/api/upload/index.js';
import { articleSave } from '@/api/digitalOperation/managementCenter/publishKnowledge/index.js';
import { categoryList } from '@/api/digitalOperation/managementCenter/classManagement/index.js';
import { articleDetail } from '@/api/digitalOperation/policyDynamics/index.js';
import {
  oneImgsRander,
  arrImgsRander,
  imgToArrHandle,
  oneImgsHandle,
} from '@/utils/index';
export default {
  name: 'index',
  components: { RichText, uploadPics, uploadFiles },
  data() {
    return {
      classOptions: [], //类别options
      publishForm: {
        articleType: '0', //文章类别
        share: '0', //是否分享
        // title: '',
        // coverPic: '',
        publishClass: [],
        // firstClassName: '',
        // secondClassName: '',
        // firstClassId: '',
        // secondClassId: '',
        // richText: '',
        // displayContent: '',
        attachments: [], //附件列表
        pictures: [], //图片列表
        documents: [], //文档列表
      },
      defaultValue: [],
      attachments: [], //附件列表 --入参时使用
      pictures: [], //图片列表 --入参时使用
      documents: [], //文档列表  --入参时使用
      coverPic: [],
      delFileId: '', //上传附件删除文件
      tabChangeFlag: false, //tab切换用
      publishClass: [],
    };
  },
  mounted() {
    this.categoryList();
    if (this.$route.query.id) {
      //编辑回显
      this.articleDetail();
    } else if (this.$route.query.type == 'viewBack') {
      let viewInfo = JSON.parse(localStorage.getItem('publishForm'));
      this.articleDetailHandle(viewInfo);
    }
  },

  methods: {
    //发布知识
    async articleSave(p, text) {
      const [, err] = await articleSave(p);
      // console.log(res, err, '发布知识');
      if (err) return this.$message.error(`${text}失败`);
      this.$message.success(`${text}成功`);
      this.publishSuccessJump();
    },
    //编辑时文章详情
    async articleDetail() {
      const [res, err] = await articleDetail({ id: this.$route.query.id });
      if (err) return;
      console.log(res, err, '文章详情返回结果');
      this.articleDetailHandle(res.data);
    },
    //编辑数据回显处理
    articleDetailHandle(data) {
      console.log('articleDetailHandle', data);
      this.publishForm = data;
      const { pictures, documents, attachments, coverPic } = this.publishForm;
      this.publishForm.pictures = arrImgsRander(pictures);
      this.publishForm.documents = arrImgsRander(documents);
      this.publishForm.attachments = arrImgsRander(attachments);
      this.publishForm.coverPic = oneImgsRander(coverPic);
      this.publishClass = [data.firstClassId, data.secondClassId];
      this.publishForm.publishClass = [data.firstClassId, data.secondClassId];
    },
    publishSuccessJump() {
      this.$router.push({
        path: '/policyResource/policyDynamics/articleList',
      });
    },
    //类别查询
    async categoryList() {
      const [res, err] = await categoryList();
      if (err) return;
      let data = res?.data;
      data = JSON.parse(
        JSON.stringify(data)
          .replace(/tkbCategoryRes/g, 'children')
          .replace(/categoryName/g, 'label')
          .replace(/id/g, 'value')
      );
      this.classOptions = data;
    },
    //文章类型切换
    articleTypeChange(val) {
      this.publishClass = [];
      this.tabChangeFlag = true;
      this.publishForm = {
        articleType: val.target.value,
        share: '0',
        attachments: [], //附件列表
        pictures: [], //图片列表
        documents: [], //文档列表
        publishClass: [],
      };
    },
    //富文本文件上传
    handleImageUpload(blobInfo, succFun, failFun) {
      let { baseImgUrl } = this;
      this.customRequestFile(blobInfo)
        .then((resp) => {
          const [res, err] = resp;
          if (err) return failFun('上传失败');
          succFun(baseImgUrl + res.data.fileName);
        })
        .catch(() => {
          failFun('上传出错');
        });
    },
    customRequestFile(blobInfo) {
      var formData;
      var file = blobInfo.blob();
      formData = new FormData();
      formData.append('file', file, file.name); //此处与源文档不一样
      return fileUpload({ file: file });
    },
    //删除文件
    delFile(row) {
      this.delFileId = row.uid;
    },
    //tab切换后 ,更改tabChangeFlag值
    tabChangeFlagToggle() {
      this.tabChangeFlag = false;
    },
    //单个图片
    setNewsImg(fileList) {
      console.log('单个图片', fileList);
      this.publishForm.coverPic = fileList;
      // console.log(e, this.publishForm.coverPic);
    },
    //多个图片
    setNewsImgs(fileList) {
      console.log('fileList', fileList);
      this.publishForm.pictures = fileList;
    },
    //图片回传
    setAnnexList(fileList, type) {
      type
        ? (this.publishForm.documents = fileList)
        : (this.publishForm.attachments = fileList);
    },
    //类别change
    publishClassChange(val1, val2) {
      console.log(this.publishForm.publishClass);
      this.publishForm.firstClassName = val2[0].label;
      this.publishForm.secondClassName = val2[1].label;
      this.publishForm.firstClassId = val2[0].value;
      this.publishForm.secondClassId = val2[1].value;
      this.publishForm.publishClass = [
        this.publishForm.firstClassId,
        this.publishForm.secondClassId,
      ];
    },
    //预览
    previewHandel() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let p = this.paramsHandle();
          localStorage.setItem('publishForm', JSON.stringify(p));
          this.$router.push({
            name: 'articleDetail',
            query: { type: 'publishKnowledgeView' },
          });
        }
      });
    },
    //发布
    publishHandel() {
      this.$refs.formRef.validate((valid) => {
        console.log('valid', valid);
        if (valid) {
          this.publishForm.state = '1'; //当前状态（0保存，1上架，，2下架）
          let p = this.paramsHandle();
          console.log(p, 'ppppp---');
          this.articleSave(p, '发布');
        }
      });
    },
    //保存为草稿
    saveHandel() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.publishForm.state = '0';
          let p = this.paramsHandle();
          this.articleSave(p, '保存草稿');
        }
      });
    },
    //入参处理
    paramsHandle() {
      delete this.publishForm.publishClass;
      if (this.$route.query.id) {
        //编辑时
        this.publishForm.articleId = this.$route.query.id;
      }
      this.paramsImgs();
      let p = {
        ...this.publishForm,
        coverPic: this.coverPic,
        attachments: this.attachments,
        pictures: this.pictures, //图片列表
        documents: this.documents, //文档列表
      };
      console.log('接口最终入参p', p);
      return p;
    },
    //入参图片统一处理
    paramsImgs() {
      const { pictures, documents, attachments, coverPic } = this.publishForm;
      console.log(pictures, documents, attachments, coverPic, 1111);
      this.pictures = imgToArrHandle(pictures);
      console.log(this.pictures, 222);
      this.documents = imgToArrHandle(documents);
      console.log(this.documents, 333);
      this.attachments = imgToArrHandle(attachments);
      console.log(this.attachments, 44);
      this.coverPic = oneImgsHandle(coverPic);
      console.log(this.coverPic, 555);
    },
  },
};
</script>

<style lang="less" scoped>
.main {
  background-color: #fff;
  padding: 16px 0;
  .remark {
    color: #999;
  }
  button:nth-child(1) {
    margin-left: 48px;
  }
  button:nth-child(2) {
    margin: 0 16px;
  }
  .del-btn {
    color: #165dff;
  }
  .upload-mark-text {
    color: #999;
    font-size: 12px;
  }
}
</style>
