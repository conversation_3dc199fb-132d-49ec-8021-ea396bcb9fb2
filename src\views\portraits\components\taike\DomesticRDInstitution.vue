<template>
  <PortraitCard
    :span="24"
    title="境内研发机构"
    :canExpand="true"
    v-bind="$attrs"
  >
    <a-row>
      <a-col :span="12" class="detail">
        <span class="title" style="width: 200px">
          历年境内研发机构数量合计：
        </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.domesticRAndDInstitutionSum"
            >暂无数据</span
          >
          <template v-else
            ><span class="num">{{
              pictureInfo.domesticRAndDInstitutionSum || '-'
            }}</span
            ><span class="unit">个</span></template
          >
        </span>
      </a-col>
    </a-row>
    <a-row :gutter="12" style="margin-top: 12px">
      <a-col :span="8">
        <ChartsCard
          chartTitle="机构级别统计"
          :options="charts1Options"
          height="174px"
        />
      </a-col>
      <a-col :span="8">
        <ChartsCard
          chartTitle="建设情况统计"
          :options="charts2Options"
          height="174px"
        />
      </a-col>
      <a-col :span="8">
        <ChartsCard
          chartTitle="近4年研发机构数量统计"
          :options="charts3Options"
          height="174px"
        />
      </a-col>
    </a-row>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
import ChartsCard from '../ChartsCard.vue';
import { usePieCharts, useLineCharts } from '../chartHooks';
// import ScrollTable from '../ScrollTable.vue';
export default {
  components: {
    PortraitCard,
    ChartsCard,
    // ScrollTable,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    charts1: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts2: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts3: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
  },
  data() {
    return {
      charts1Options: {},
      charts2Options: {},
      charts3Options: {},
    };
  },

  watch: {
    charts1: {
      deep: true,
      handler() {
        this.handleInitCharts1();
      },
    },
    charts2: {
      deep: true,
      handler() {
        this.handleInitCharts2();
      },
    },
    charts3: {
      deep: true,
      handler() {
        this.handleInitCharts3();
      },
    },
  },
  methods: {
    handleInitCharts1() {
      if (!this.charts1?.data) return;
      const { data = [] } = this.charts1;
      this.charts1Options = usePieCharts({ data });
    },
    handleInitCharts2() {
      if (!this.charts2?.data) return;
      const { data = [] } = this.charts2;
      this.charts2Options = usePieCharts({ data });
    },
    handleInitCharts3() {
      const unit = '个';
      if (!this.charts3?.data) return;
      const { xAxis = [], data = [] } = this.charts3;
      this.charts3Options = useLineCharts({
        xAxis,
        unit,
        series: [
          { name: '近4年研发机构数量统计', data: data || [], color: '#ff6437' },
        ],
      });
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .title {
    text-align: right;
    color: #999999;
    width: 148px;
    line-height: 26px;
  }

  .info {
    text-align: left;
    .num {
      font-family: D-DIN;
      font-size: 24px;
      font-weight: bold;
      line-height: 24px;
      color: #333333;
      z-index: 0;
    }

    .unit {
      margin-left: 4px;
      font-family: PingFang SC;
      font-size: 12px;
      color: #333333;
    }
  }
}
</style>
