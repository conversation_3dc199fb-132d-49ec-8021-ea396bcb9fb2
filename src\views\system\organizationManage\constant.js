export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'organizeName',
      title: '组织名称',
      props: {
        placeholder: '请输入组织名称',
      },
    },
  ],
  formCol: { labelCol: 8, wrapperCol: 16 },
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { organizeName: '' },
};

export const tableColumns = [
  {
    title: '组织名称',
    dataIndex: 'organizeName',
    field: 'organizeName',
  },
  {
    title: '上级组织',
    dataIndex: 'parentName',
    field: 'parentName',
  },
  {
    title: '岗位数',
    dataIndex: 'positionNum',
    field: 'positionNum',
  },
  {
    title: '用户数',
    dataIndex: 'userNum',
    field: 'userNum',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    field: 'operation',
    fixed: 'right',
    width: 170,
    slots: {
      default: 'operation',
    },
  },
];
export const replaceFields = {
  children: 'children',
  title: 'label',
  key: 'id',
};
export const initFormData = (data = {}) => {
  return {
    leader: '', //负责人
    organizeId: '', //组织ID
    merchantId: '', //租户ID
    organizeName: '', //机构名称
    phone: '', //联系电话
    organizeCode: '', ////机构编码
    parentId: '', //父组织ID
    email: '', //邮箱
    ...data,
  };
};
export const formLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 16,
  },
};

export const rules = {
  organizeName: [
    {
      required: true,
      message: '名称不能为空',
      trigger: 'blur',
      whitespace: true,
    },
  ],
  parentId: [
    {
      required: true,
      message: '上级组织不能为空',
      trigger: 'blur',
    },
  ],
  phone: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          try {
            let reg = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
            if (reg.test(value)) {
              callback();
            } else {
              callback(new Error('数据格式错误'));
            }
          } catch (err) {
            callback(new Error('数据格式错误'));
          }
        } else {
          callback();
        }
      },
    },
  ],
  email: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback();
        }
        try {
          // eslint-disable-next-line no-useless-escape
          let reg =
            /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
          if (reg.test(value)) {
            callback();
          } else {
            callback(new Error('数据格式错误'));
          }
        } catch (err) {
          callback(new Error('数据格式错误'));
        }
      },
    },
  ],
};
