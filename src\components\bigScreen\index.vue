<template>
  <div style="width: 100%; height: 100%; overflow: scroll">
    <iframe frameborder="0" style="border: 0px" :src="url"></iframe>
  </div>
</template>

<script>
export default {
  name: 'screen',
  props: {
    url: {
      type: String,
      default: function () {
        return '';
      },
    },
  },
};
</script>

<style scoped lang="scss">
iframe {
  // padding: 10px;
  width: 100%;
  height: 100%;
  // width: 1920px;
  // height: 1080px;
  /*top: 0;*/
  /*left: 0;*/
  transform-origin: top left;
  transform: scale(1, 1);
}
</style>
