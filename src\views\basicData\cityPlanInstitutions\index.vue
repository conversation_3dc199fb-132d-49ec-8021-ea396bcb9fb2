<template>
  <page-layout>
    <table-component
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :parentData="params"
      :pageName="pageName"
    ></table-component>
  </page-layout>
</template>

<script>
import { institutionsMixin } from '../mixins/institutionsMixin';
import tableComponent from '@/views/basicData/components/tableComponent';
import moment from 'moment';

export default {
  name: 'cityPlanInstitutions',
  components: { tableComponent },
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'cityPlanInstitutions',
      planTypeList: [],
      params: {
        planName: undefined,
        planType: undefined,
        rateTime: undefined,
      },
      tableColumn: [
        {
          field: '',
          title: '',
          type: 'checkbox',
          fixed: 'left',
          width: 70,
        },
        {
          field: '',
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: 70,
        },
        {
          field: 'rateTime',
          title: '日期',
          width: 200,
          formatter: ({ cellValue }) => {
            return cellValue ? moment(cellValue).format('YYYY') : '';
          },
        },
        {
          field: 'planName',
          title: '规划名称',
          width: 200,
        },
        {
          field: 'makeTime',
          title: '制定时间',
          width: 200,
        },
        {
          field: 'planType',
          title: '规划类别',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.planTypeList);
          },
        },
        {
          field: 'relevantPolicy',
          title: '相关政策',
          width: 200,
        },
        {
          field: 'remark',
          title: '备注',
          width: 200,
        },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        //筛选控件配置
        config: [
          {
            field: 'rateTime',
            title: '日期',
            element: 'slot',
            slotName: 'dateYear',
            rules: [{ required: true, message: '请选择年份' }],
          },
          {
            field: 'planName',
            title: '规划名称',
          },
          {
            field: 'planType',
            title: '规划类别',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.planTypeList,
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        submitBtn: true,
        okBtn: false,
        addBtn: false,
        viewBtn: true,
        editBtn: true,
        delBtn: false,
        menu: true,
        menuWidth: 200,
        menuFixed: 'right',
        formConfig: [
          {
            field: 'rateTime',
            title: '年份',
            element: 'slot',
            slotName: 'dateYearForPop',
            rules: [{ required: true, message: '请选择年份' }],
          },
          {
            field: 'makeTime',
            title: '制定时间',
            element: 'a-date-picker',
            echoFormatter: (value) => {
              return moment(value);
            },
            rules: [{ required: true, message: '请输入制定时间' }],
          },
          {
            field: 'planName',
            title: '规划名称',
            rules: [{ required: true, message: '请输入规划名称' }],
          },
          {
            field: 'planType',
            title: '规划类别',
            element: 'a-select',
            rules: [{ required: true, message: '请选择规划类别' }],
            props: {
              options: this.planTypeList,
              showSearch: true,
              optionFilterProp: 'children',
            },
            previewFormatter: (value) => {
              return this.translateValue(value, this.planTypeList);
            },
          },
          {
            field: 'relevantPolicy',
            title: '相关政策',
            rules: [{ required: true, message: '请输入相关政策' }],
          },
          {
            field: 'mainContent',
            title: '主要内容',
            element: 'a-textarea',
          },
          {
            field: 'remark',
            title: '备注',
          },
        ],
      };
    },
  },
  created() {
    this.getCodeByType('PLAN_TYPE').then((res) => {
      this.planTypeList = res;
    });
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.mr-10 {
  margin-right: 10px;
}
</style>
