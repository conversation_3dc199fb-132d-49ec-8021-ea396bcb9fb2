import { request } from '@/utils/request/requestTkb';
/**
 * 建筑工地项目统计查询
 */
export function statisticAll(data) {
  return request({
    url: '/projectManage/statistic/all',
    method: 'post',
    data,
  });
}
/**
 * 建筑工地项目统计保存
 */
export function projectManageAdd(data) {
  return request({
    url: '/projectManage/add',
    method: 'post',
    data,
  });
}
/**
 * 建筑工地项目完结数量
 */
export function queryCount(data) {
  return request({
    url: '/projectManage/query/count',
    method: 'post',
    data,
  });
}
