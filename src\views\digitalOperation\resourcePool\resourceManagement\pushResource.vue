<template>
  <page-layout>
    <div class="main">
      <a-form-model
        ref="formRef"
        :model="publishForm"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 10 }"
      >
        <a-form-model-item
          prop="resTypeId"
          label="资源类型"
          :required="true"
          :rules="[{ required: true, message: '请选择资源类型' }]"
        >
          <a-select
            v-model="publishForm.resTypeId"
            placeholder="请选择资源类型"
            @change="resTypeChange"
            :disabled="disabled"
          >
            <a-select-option
              v-for="item in resourceTypes"
              :value="item.value"
              :key="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          prop="resName"
          label="资源名称"
          :required="true"
          :rules="[{ required: true, message: '请输入资源名称' }]"
        >
          <a-input
            :disabled="disabled"
            v-model="publishForm.resName"
            placeholder="请输入资源名称"
            allow-clear
          />
        </a-form-model-item>
        <a-form-model-item
          prop="contractPerson"
          label="联系人"
          :required="true"
          :rules="[{ required: true, message: '请输入联系人' }]"
        >
          <a-input
            :disabled="disabled"
            v-model="publishForm.contractPerson"
            placeholder="请输入联系人"
            allow-clear
          />
        </a-form-model-item>
        <a-form-model-item
          prop="contractPhone"
          label="联系人电话"
          :required="true"
          :rules="[
            {
              required: true,
              validator: checkPhone,
            },
          ]"
        >
          <a-input
            :disabled="disabled"
            v-model="publishForm.contractPhone"
            placeholder="请输入联系人电话"
            allow-clear
          />
        </a-form-model-item>
        <a-form-model-item
          prop="mainContext"
          label="正文编辑"
          :required="true"
          :rules="[{ required: true, message: '请输入正文内容' }]"
        >
          <RichText
            :disabled="disabled"
            v-model="publishForm.mainContext"
            :handleImageUpload="handleImageUpload"
          />
        </a-form-model-item>

        <a-form-model-item prop="picture" label="上传图片">
          <uploadPics
            :disabled="disabled"
            v-model="publishForm.picture"
            :accept="'.png, .jpg'"
            :maxCount="1"
            @setNewsImgs="setNewsImg"
            :fileListTemp="publishForm.picture"
            :maxSize="10"
          />
          <span class="upload-mark-text"
            >注释：只能上传1张，JPG\PNG格式，大小不超过10MB，建议尺寸4:3。</span
          >
        </a-form-model-item>
        <a-form-model-item
          v-if="publishForm.articleType == '2'"
          prop="documents"
          label="上传文档"
          :required="true"
          :rules="[{ required: true, message: '请上传文档' }]"
        >
          <uploadFiles
            :accept="'.pdf, .ppt, .pptx, .doc, .docx, .png, .jpg, .bmp, .mp4, .zip, .rar'"
            :disabled="disabled"
            v-model="publishForm.documents"
            @setAnnexList="setAnnexList"
            :showUploadList="true"
          ></uploadFiles>
        </a-form-model-item>
        <a-form-model-item
          prop="attachment"
          label="上传附件"
          :label-col="{ span: 3 }"
          :wrapper-col="{ span: 20 }"
        >
          <vxe-table
            class="flow-detail-table"
            headerAlign="center"
            align="center"
            showOverflow="tooltip"
            :data="publishForm.attachment"
            :column-config="{ resizable: true }"
          >
            <vxe-column field="name" title="附件名称" width="40%"></vxe-column>
            <vxe-column field="fileSize" title="附件大小"> </vxe-column>
            <vxe-table-column title="操作">
              <template #default="{ row }">
                <a v-if="!disabled" @click="delFile(row)">删除</a>
              </template>
            </vxe-table-column>
          </vxe-table>
          <uploadFiles
            :accept="'.pdf, .ppt, .pptx, .doc, .docx, .png, .jpg, .bmp, .mp4, .zip, .rar'"
            :disabled="disabled"
            v-model="publishForm.attachment"
            :showDefaultUploadList="false"
            @setAnnexList="setAnnexList"
            :delFileId="delFileId"
            :fileListTemp="publishForm.attachment"
          ></uploadFiles>
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" @click="publishHandel" :disabled="disabled"
            >发布资源</a-button
          >
          <a-button
            :disabled="disabled"
            @click="unPublishHandel"
            v-if="$route.query.id"
            >下架资源</a-button
          >
        </a-form-model-item>
      </a-form-model>
    </div>
  </page-layout>
</template>

<script>
import uploadPics from '@/components/Uploads/uploadPics.vue';
import uploadFiles from '@/components/Uploads/uploadFiles.vue';
import RichText from '@/components/RichText/RichText.vue';
import { fileUpload } from '@/api/upload/index.js';
import {
  getAllResType,
  articleSave,
  offShelf,
  getResDetail,
} from '@/api/digitalOperation/resourcePool/resourceManagement/index.js';
import {
  oneImgsRander,
  arrImgsRander,
  imgToArrHandle,
  oneImgsHandle,
} from '@/utils/index';
import { getUser } from '@/api/system/user';
const checkPhone = (rule, value, callback) => {
  const phoneReg = /^1[3456789]\d{9}$/;
  if (phoneReg.test(value)) {
    callback();
  } else {
    callback('请输入正确的手机号');
  }
};
export default {
  name: 'pushResource',
  components: { RichText, uploadPics, uploadFiles },
  data() {
    return {
      checkPhone,
      uploadFileList: [],
      publishForm: {
        contractPerson: '', //联系人
        resName: '', //资源名称
        resTypeId: undefined, //资源类型id
        resType: '', //资源类型名称
        attachment: [], //附件
        contractPhone: '', //联系人手机号
        mainContext: '', //富文本
        picture: [], //图片
      },
      delFileId: '', //上传附件删除文件
      attachment: [], //入参时使用
      picture: [],
      resourceTypes: [],
      disabled: false,
      organizeName: null,
    };
  },
  mounted() {
    this.getUserInfo();
    this.getResourceType();
    if (this.$route.query.id) {
      this.getResDetail();
    }
    if (this.$route.query.type === 'view') {
      this.disabled = true;
    }
  },

  methods: {
    async articleSave() {
      this.picture = oneImgsHandle(this.publishForm.picture);
      this.attachment = imgToArrHandle(this.publishForm.attachment);
      let p = {
        ...this.publishForm,
        createUnit: this.organizeName,
        picture: this.picture,
        attachment: this.attachment,
      };
      const [, err] = await articleSave(p);
      if (err) return;
      this.$message.success('发布成功');
      this.publishSuccessJump();
    },
    async getUserInfo() {
      if (!this.userInfo) {
        const userId = this.$store.state.base.user.userId;
        const [res, err] = await getUser(userId);
        if (err) {
          this.$message.error('获取用户信息异常');
          return;
        }
        this.organizeName = res.data.user.organizeName;
      }
    },
    //查询所有资源类型
    async getResourceType() {
      const [res, err] = await getAllResType();
      if (err) return;
      this.resourceTypes = res.data.map((item) => {
        return {
          value: item.id,
          label: item.resType,
        };
      });
    },
    //查询详情
    async getResDetail() {
      const [res, err] = await getResDetail({ id: this.$route.query.id });
      if (err) return;
      console.log('资源详情', res.data);
      this.publishForm = res?.data;
      const { attachment, picture } = this.publishForm;
      this.publishForm.attachment = arrImgsRander(attachment);
      this.publishForm.picture = oneImgsRander(picture);
    },
    resTypeChange(val) {
      const currentBiz = this.resourceTypes.filter((item) => {
        return item.value == val;
      });
      this.publishForm.resType = currentBiz[0] ? currentBiz[0].label : val;
    },
    publishSuccessJump() {
      this.$router.push({
        path: '/policyResource/resourcePool/resourcePoolManagement',
      });
    }, //policyResource/resourcePool/resourcePoolManagement

    //富文本文件上传
    handleImageUpload(blobInfo, succFun, failFun) {
      let { baseImgUrl } = this;
      this.customRequestFile(blobInfo)
        .then((resp) => {
          const [res, err] = resp;
          if (err) return failFun('上传失败');
          succFun(baseImgUrl + res.data.fileName);
        })
        .catch(() => {
          failFun('上传出错');
        });
    },
    customRequestFile(blobInfo) {
      var formData;
      var file = blobInfo.blob();
      formData = new FormData();
      formData.append('file', file, file.name); //此处与源文档不一样
      //TODO：文件上传接口
      return fileUpload({ file: file });
    },
    delFile(row) {
      this.delFileId = row.uid;
    },

    //单个图片
    setNewsImg(fileList) {
      console.log('单个图片', fileList);
      this.publishForm.picture = fileList;
      // console.log(e, this.publishForm.coverPic);
    },

    //图片回传
    setAnnexList(fileList) {
      this.publishForm.attachment = fileList;
    },

    publishHandel() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.articleSave();
        }
      });
    },
    //下架
    async offShelf(id) {
      const [, err] = await offShelf({ id });
      if (err) return;
      this.$message.success('下架成功');
      this.loadData();
    },
    unPublishHandel() {
      this.$confirm({
        title: '提醒',
        content: '确认下架资源？',
        onOk: () => {
          this.offShelf(this.$route.query.id);
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.main {
  background-color: #fff;
  padding: 16px 0;
  .remark {
    color: #999;
  }
  button:nth-child(1) {
    margin-left: 48px;
  }
  button:nth-child(2) {
    margin: 0 16px;
  }
  .del-btn {
    color: #165dff;
  }
}
</style>
