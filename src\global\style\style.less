// 自定义全局样式

.beauty-scroll {
  // scrollbar-color: @scroll-thumb-bg @scroll-track;
  // scrollbar-width: thin;
  -ms-overflow-style: none;
  position: relative;

  &::-webkit-scrollbar {
    width: 4px !important;
    height: 4px !important;
  }
  //滚动条的滑块
  &::-webkit-scrollbar-thumb {
    background-color: @scroll-thumb-bg !important;
    border-radius: 4px !important;
  }
}

.beauty-container-scroll {
  // scrollbar-color: @scroll-thumb-bg @scroll-track;
  // scrollbar-width: thin;
  -ms-overflow-style: none;
  position: relative;
  display: none;

  &::-webkit-scrollbar {
    width: 4px !important;
    height: 4px !important;
  }
  //滚动条的滑块
  &::-webkit-scrollbar-thumb {
    background-color: @scroll-thumb-bg;
    border-radius: 4px !important;
  }
}

.split-right {
  &:not(:last-child) {
    border-right: 1px solid rgba(98, 98, 98, 0.2);
  }
}

.disabled {
  cursor: not-allowed;
  color: @g-gray-disabled;
  pointer-events: none;
}

.beauty-title {
  color: #333;
  font-weight: bold;
  font-size: 14px;
  line-height: 14px;
  &::before {
    background: #1677ff;
    height: 11px;
    width: 3px;
    content: '';
    display: inline-block;
    margin-right: 4px;
  }
}

// table操作按钮
.operate-button {
  cursor: pointer;
  color: #1677ff;
  padding: 8px 0;
  display: inline-block;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  margin-right: 16px;
}

// antd样式修改
/deep/.ant-tabs-bar {
  background: #ffffff;
  border-radius: 2px;
  padding: 0 24px;
}
/deep/.ant-tabs-nav {
  .ant-tabs-tab:active {
    // color:#1890FF;
  }
}

.ant-btn-primary-disabled,
.ant-btn-primary.disabled,
.ant-btn-primary[disabled],
.ant-btn-primary-disabled:hover,
.ant-btn-primary.disabled:hover,
.ant-btn-primary[disabled]:hover,
.ant-btn-primary-disabled:focus,
.ant-btn-primary.disabled:focus,
.ant-btn-primary[disabled]:focus,
.ant-btn-primary-disabled:active,
.ant-btn-primary.disabled:active,
.ant-btn-primary[disabled]:active,
.ant-btn-primary-disabled.active,
.ant-btn-primary.disabled.active,
.ant-btn-primary[disabled].active {
  color: #d2d2d2;
}

.is--disabled.vxe-checkbox .vxe-checkbox--icon,
.is--disabled.vxe-custom--option .vxe-checkbox--icon,
.is--disabled.vxe-export--panel-column-option .vxe-checkbox--icon,
.is--disabled.vxe-table--filter-option .vxe-checkbox--icon,
.vxe-table--render-default
  .is--disabled.vxe-cell--checkbox
  .vxe-checkbox--icon {
  color: #f6f6f6;
}
.vxe-select--panel {
  max-width: 160px !important;
}


.signal-line {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-body {
    padding: 0;
    max-height: 100vh;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }

  &.ant-modal-wrap {
    overflow-x: hidden;
    scrollbar-color: auto;
    &::-webkit-scrollbar {
      width: 6px; /* 滚动条宽度 */
      background: #223946;
      border-radius: 1px;
    }
    /* 滚动条滑块 */
    &::-webkit-scrollbar-thumb {
      background-color: #86bded; /* 滚动条滑块颜色 */
      border-radius: 1px;
    }
  }
}


@font-face {
  font-family: 'D-DIN';
  src: url('../../assets/fonts/D-DIN-PRO-400-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'D-DIN';
  src: url('../../assets/fonts/D-DIN-PRO-500-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url(../../assets/fonts/优设标题黑.ttf) format('truetype') ;
  font-weight: normal;
  font-style: normal;
}
