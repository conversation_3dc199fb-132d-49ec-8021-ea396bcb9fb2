<template>
  <BuseCrud
    ref="crud"
    title="任务列表"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalConfirm="modalConfirmHandler"
    @loadData="loadData"
    @handleCreate="rowAdd"
    @rowView="rowView"
  >
    <template slot="defaultHeader">
      <a-button class="export" @click="exportHandle">导出</a-button>
    </template>
    <template slot="file-upload">
      <Upload
        v-model="attachment"
        :accept="'.doc'"
        :showUploadList="true"
        :maxCount="1"
        @setNewsImg="setNewsImg"
        :maxSize="4"
      />
      <span class="upload-mark-text">支持doc格式附件，附件大小不能超过4M</span>
    </template>
  </BuseCrud>
</template>

<script>
import { getDicts } from '@/api/system/dict/data';
import { getTableColumn } from './taskList.js';
import { getUser } from '@/api/system/user';
import moment from 'moment';
import {
  taskList,
  taskAdd,
  downloadInformation,
} from '@/api/digitalOperation/taskManagement/taskList.js';
import { resolveBlob } from '@/utils/common/fileDownload';
import Upload from '@/components/Uploads/index.vue';
import { getOrganizeAll } from '@/views/digitalOperation/taskManagement/utils/index.js';
export default {
  name: 'ManagementTkIndex',
  components: { Upload },
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      taskStatus: [],
      params: {
        taskName: undefined,
        content: undefined,
        transactorName: undefined,
        taskStatus: undefined,
        transactorTime: undefined,
        tag: '1', //列表标签 1:任务列表     2:我的批示   3:我的待办
      },
      taskTypes: [],
      userInfo: null,
      isActivityRequest: false,
      attachment: '', //附件
      organizeId: 'ZZ1725053315114491904', //太科办组织id
      organizeAllList: [], //太科办A端用户所有数据
      instruction: '', //批示人id--入参时使用
      instructionName: '', //办理人--入参时使用
      transactor: '', //办理人id--入参时使用
      transactorName: '', //办理人--入参时使用
    };
  },
  created() {
    this.tableColumn = getTableColumn.call(this);
    this.getTaskStatus();
    this.getTaskType();
  },
  computed: {
    modalConfig() {
      return {
        addBtnText: '发布任务',
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        okText: '提交',
        formConfig: [
          {
            title: '创建人',
            field: 'creatorName',
            props: {
              disabled: true,
            },
          },
          {
            title: '所属部门',
            field: 'departmentName',
            props: {
              disabled: true,
            },
          },
          {
            title: '创建时间',
            field: 'createTime',
            defaultValue: moment().format('YYYY-MM-DD'),
            props: {
              disabled: true,
            },
          },
          {
            title: '任务类型',
            field: 'taskType',
            element: 'a-select',
            rules: [{ required: true, message: '请选择活动类型' }],
            props: {
              options: this.taskTypes,
            },
            on: {
              change: (value) => {
                this.isActivityRequest = value === '1' ? true : false;
              },
            },
          },
          {
            title: '任务名称',
            field: 'taskName',
            rules: [
              { required: true, message: '请输入任务名称' },
              {
                min: 1,
                max: 64,
                message: '任务名称不能超过64个字',
                trigger: 'blur',
              },
            ],
            colProps: {
              span: 24,
            },
            itemProps: {
              labelCol: {
                span: 3,
              },
              wrapperCol: {
                span: 20,
              },
            },
          },
          {
            title: '任务内容',
            field: 'content',
            element: 'a-textarea',
            rules: [{ required: true, message: '请输入任务内容' }],
            colProps: {
              span: 24,
            },
            itemProps: {
              labelCol: {
                span: 3,
              },
              wrapperCol: {
                span: 20,
              },
            },
          },
          {
            title: '工作要求',
            field: 'jobRequirement',
            element: 'a-textarea',
            colProps: {
              span: 24,
            },
            itemProps: {
              labelCol: {
                span: 3,
              },
              wrapperCol: {
                span: 20,
              },
            },
          },
          {
            title: '批示人',
            field: 'instructionName',
            element: 'a-cascader',
            rules: this.isActivityRequest
              ? [{ required: true, message: '请输入批示人' }]
              : [],
            show: this.isActivityRequest,
            props: {
              options: this.organizeAllList,
            },
            on: {
              change: this.instructionChange,
            },
          },
          {
            title: '抄送',
            field: 'ccUserIdList',
            element: 'a-tree-select',
            props: {
              treeData: this.organizeAllList,
              treeCheckable: true,
            },
          },
          {
            title: '办理人',
            field: 'transactorName',
            element: 'a-cascader',
            rules: [{ required: true, message: '请输入办理人' }],
            props: {
              options: this.organizeAllList,
            },
            on: {
              change: this.transactorNameChange,
            },
          },
          {
            title: '办理时限',
            field: 'transactorTime',
            element: 'a-date-picker',
            rules: [{ required: true, message: '请选择办理时限' }],
            props: {
              disabledDate: this.getDisabledDate,
            },
          },
          {
            title: '附件',
            field: 'attachment',
            element: 'slot',
            slotName: 'file-upload',
            colProps: {
              span: 24,
            },
            itemProps: {
              labelCol: {
                span: 3,
              },
              wrapperCol: {
                span: 20,
              },
            },
          },
        ],
        customOperationTypes: [
          {
            typeName: 'addTask',
            title: '发布任务',
            condition: () => {
              return false;
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 12,
        },
      };
    },
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            title: '任务名称',
            field: 'taskName',
          },
          {
            title: '任务内容',
            field: 'content',
          },
          {
            title: '办理人',
            field: 'transactorName',
          },
          {
            title: '任务状态',
            field: 'taskStatus',
            element: 'a-select',
            props: {
              options: this.taskStatus,
            },
          },
          {
            title: '办理时限',
            field: 'transactorTime',
            element: 'a-range-picker',
          },
        ],
      };
    },
  },
  mounted() {
    getOrganizeAll().then((res) => {
      this.organizeAllList = res;
    });
    this.loadData();
  },

  methods: {
    async getTaskType() {
      const [res, err] = await getDicts(['task_type']);
      if (err) return;
      this.taskTypes = res.data.map((item) => {
        return {
          value: item.dictValue,
          label: item.dictLabel,
        };
      });
      console.log(this.taskTypes, '2222');
    },
    async getTaskStatus() {
      const [res, err] = await getDicts(['task_state']);
      if (err) return;
      this.taskStatus = res.data.map((item) => {
        return {
          value: item.dictValue,
          label: item.dictLabel,
        };
      });
    },

    getDisabledDate(current) {
      return current && current <= moment().endOf('day');
    },
    // 导出
    async exportHandle() {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
      let p = this.paramsHandle();
      const [res] = await downloadInformation(p);
      resolveBlob(res, mimeMap.xlsx, '任务列表', '.xls');
    },
    //列表查询
    async loadData() {
      this.loading = true;
      console.log('this.params', this.params);
      let p = this.paramsHandle();
      const [res, err] = await taskList(p);
      if (err) return;
      this.loading = false;
      // 设置数据
      this.tablePage.total = res.total;
      this.tableData = res.data;
      console.log(res, err, '任务列表返回结果');
    },
    paramsHandle() {
      let p = {
        ...this.params,
        startTime: this.params.transactorTime?.[0]?.format('YYYY-MM-DD'),
        endTime: this.params.transactorTime?.[1]?.format('YYYY-MM-DD'),
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      };
      delete p.transactorTime;
      return p;
    },
    rowView(row) {
      this.$router.push({
        path: '/taskTarget/taskManagement/instructionDetail',
        query: {
          id: row.id,
          detailPageType: 'taskList', //任务列表
        },
      });
    },
    setNewsImg(val) {
      console.log('val', val);
      this.attachment = val.fileName;
    },
    //弹窗确认按钮事件
    modalConfirmHandler(value) {
      const { crudOperationType } = value;
      console.log(crudOperationType, 4444);
      if (crudOperationType === 'addTask') {
        console.log('value', value);
        //下面写死的数据等待接口提供
        let p = {
          ...value,
          attachment: this.attachment, //附件
          transactor: this.transactor, //办理人id
          instruction: this.instruction, //批示人id
          department: this.userInfo.organizeId, //部门id
          transactorName: this.transactorName, //办理人
          instructionName: this.instructionName, //批示人
          transactorTime: `${value.transactorTime.format(
            'YYYY-MM-DD'
          )} 23:59:59`,
        };
        console.log('发布任务入参', p);
        this.taskAdd(p);
      }
    },
    //批示人change
    instructionChange(val1, val2, val3) {
      console.log('批示人选择', val3);
      this.instruction = val3[1].value; //批示人id
      this.instructionName = val3[1].label; //批示人
      console.log('批示', this.instruction, this.instructionName);
    },
    //办理人change
    transactorNameChange(val1, val2, val3) {
      console.log('办理人选择', val3);
      this.transactor = val3[1].value; //办理人id
      this.transactorName = val3[1].label; //办理人
    },
    async taskAdd(params) {
      const [, err] = await taskAdd(params);
      if (err) return;
      this.$message.success('新增成功');
      this.loadData();
    },
    async rowAdd() {
      if (!this.userInfo) {
        const userId = this.$store.state.base.user.userId;
        const [res, err] = await getUser(userId);
        if (err) {
          this.$message.error('获取用户信息异常');
          return;
        }
        this.userInfo = res.data.user;
      }
      this.$refs.crud.switchModalView(true, 'addTask', {
        createTime: moment().format('YYYY-MM-DD'),
        creatorName: this.userInfo.nickName,
        departmentName: this.userInfo.organizeName,
        taskType: undefined,
        taskName: undefined,
        transactor: undefined,
        transactorName: undefined,
        content: undefined,
        jobRequirement: undefined,
        instruction: undefined,
        instructionName: undefined,
        transactorTime: undefined,
        ccUserIdList: [],
        attachment: undefined,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.export {
  margin-right: 8px;
}
.upload-mark-text {
  color: #999;
  font-size: 12px;
}
</style>
