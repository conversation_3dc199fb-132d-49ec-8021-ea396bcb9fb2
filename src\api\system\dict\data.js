import request from '@/utils/request';

// 查询字典数据列表
export function listData(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/data/list',
    method: 'get',
    params: query,
  });
}

// 查询字典数据详细
export function getData(dictCode) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/dict/data/' +
      dictCode,
    method: 'get',
  });
}

// 根据字典类型查询字典数据信息
export function getDicts(dictCode, query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/dict/data/dictCode/' +
      dictCode,
    method: 'get',
    params: query,
  });
}

// 新增字典数据
export function addData(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/data/add',
    method: 'post',
    data: data,
  });
}

// 删除字典数据
export function delData(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/data/' + query,
    method: 'get',
  });
}

// 导出字典数据
export function exportData(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/data/export',
    method: 'get',
    params: query,
  });
}
