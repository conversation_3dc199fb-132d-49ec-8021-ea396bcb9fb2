<template>
  <page-layout>
    <table-component
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :params="params"
      :pageName="pageName"
    >
    </table-component>
  </page-layout>
</template>

<script>
import { portraitsMixin } from '../mixins/index.js';
import tableComponent from '../components/tableComponent.vue';

export default {
  name: 'index',
  components: { tableComponent },
  mixins: [portraitsMixin],
  data() {
    return {
      pageName: 'businessPortraits',
      enterprisePropertyList: [],
      params: {
        enterpriseName: undefined,
        enterpriseProperty: undefined,
      },
      tableColumn: [
        {
          field: '',
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: 70,
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
        },
        {
          field: 'enterpriseAddress',
          title: '企业地址',
        },
        {
          field: 'affiliatedPark',
          title: '所属园区',
        },
        {
          field: 'enterpriseProperty',
          title: '所属行业',
        },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        //筛选控件配置
        config: [
          {
            field: 'enterpriseName',
            title: '企业名称',
          },
          {
            field: 'enterpriseProperty',
            title: '所属行业',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.enterprisePropertyList,
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        okBtn: false,
        menu: true,
        menuWidth: 200,
        menuFixed: 'top',
        customOperationTypes: [
          {
            //定义操作类型名称
            typeName: 'detail',
            title: '画像详情',
            //该操作下弹窗对应内容插槽
            slotName: 'detail',
          },
        ],
      };
    },
  },
  created() {
    this.getCodeByType('property').then((res) => {
      this.enterprisePropertyList = res || [];
    });
  },
  methods: {},
};
</script>

<style scoped></style>
