<template>
  <a-spin
    :spinning="loading"
    class="portrait"
    :style="
      '--parent-width:' + parentWidth + 'px;--parent-scale:' + parentScale
    "
  >
    <div class="portrait-cnt">
      <div class="portrait-cnt-header">
        <h2>太湖湾科创城数字孪生智慧园区</h2>
        <div class="time-area">
          {{ nowDate }}<br />
          {{ nowTime }}
        </div>
        <div
          class="back-btn"
          @click="closeModal"
          v-if="pageName !== 'taikePortraits'"
        >
          <a-icon type="rollback" />
          返回
        </div>
        <!-- <a-button
          v-if="pageName !== 'taikePortraits'"
          size="small"
          type="primary"
          @click="closeModal"
          style="position: absolute; left: 80px; top: 97px"
          >返回</a-button
        > -->
      </div>
      <div class="portrait-cnt-bottom">
        <LeftCard
          class="portrait-cnt-bottom-left"
          :pictureInfo="pictureInfo"
          :pageName="pageName"
        />
        <div class="portrait-cnt-bottom-right">
          <a-row :gutter="12" class="right-cnt">
            <components
              v-for="(item, index) in cardList"
              :key="index"
              :is="item.components"
              :pictureId="pictureId"
              :pictureInfo="pictureInfo"
              v-bind="item.props || {}"
              :pageName="pageName"
              @openModal="openModal"
              @resetBusinessData="resetBusinessData"
            />
          </a-row>
        </div>
      </div>
    </div>

    <a-modal
      :visible="modal.visible"
      :title="null"
      :closable="false"
      :destroyOnClose="true"
      width="100%"
      wrapClassName="full-modal"
      :footer="null"
      @cancel="modal.visible = false"
    >
      <PortraitsDetail
        :pageName="modal.pictureType"
        :pictureId="modal.pictureId"
        @close="handleModalClose"
      ></PortraitsDetail>
    </a-modal>
  </a-spin>
</template>
<script>
import moment from 'moment';
import { isNullShowText } from '@/utils/common';
import {
  businessTableList,
  parkTableList,
  riskInvestmentColumns,
  enterpriseSecurityColumns_park,
  smallEngineeringColumns_park,
  hiddenDangerColumns_park,
} from './config.js';
import * as api from '@/api/portraits';
import { BusinessAPI, ParkAPI, TaikeAPI } from '@/api/portraits-new';
import { throttle } from 'xe-utils';
import CardTable from './CardTable.vue';
import LeftCard from './LeftCard.vue';
import ChartsCard from './ChartsCard.vue';
import PortraitCard from './PortraitCard.vue';
import CardWrapper from './CardWrapper.vue';
// #region S 企业画像
import BusinessBaseInfo from './business/BaseInfo.vue';
import BusinessData from './business/BusinessData.vue';
import EnergyInfo from './business/EnergyInfo.vue';
import EnterpriseTitle from './business/EnterpriseTitle.vue';
import IntellectualPropertyRight from './business/IntellectualPropertyRight.vue';
// #endregion E 企业画像

//#region S 园区画像
import ParkBaseInfo from './park/BaseInfo.vue';
import ParkBusinessData from './park/BusinessData.vue';
import SettledEnterprise from './park/SettledEnterprise.vue';
import VCCharts from './park/VCCharts.vue';

//#endregion E 园区画像

//#region S 太科城画像
import TaikeBaseInfo from './taike/BaseInfo.vue';
import ParkInfo from './taike/ParkInfo.vue';
import TaikeBusinessData from './taike/BusinessData.vue';
import BigProject from './taike/BigProject.vue';
import IncubationInstitutions from './taike/IncubationInstitutions.vue';
import InnovationPlatform from './taike/InnovationPlatform.vue';
import RDInstitution from './taike/RDInstitution.vue';
import DomesticRDInstitution from './taike/DomesticRDInstitution.vue';
import TaikeSettledEnterprise from './taike/SettledEnterprise.vue';
import ParkSafety from './taike/ParkSafety.vue';
//#endregion E 太科城画像
import { useLineCharts, usePieCharts } from './chartHooks.js';

export default {
  name: 'PortraitsDetail',
  components: {
    CardWrapper,
    CardTable,
    LeftCard,
    BusinessBaseInfo,
    BusinessData,
    EnergyInfo,
    EnterpriseTitle,
    IntellectualPropertyRight,
    ParkBaseInfo,
    ParkBusinessData,
    SettledEnterprise,
    ParkInfo,
    TaikeBusinessData,
    BigProject,
    IncubationInstitutions,
    InnovationPlatform,
    RDInstitution,
    DomesticRDInstitution,
    VCCharts,
    TaikeSettledEnterprise,
    PortraitCard,
    ParkSafety,
  },
  dicts: [
    'enterprise_regulate_status',
    'enterprise_nature_status',
    'enterprise_register_status',
    'qcc_field_mapping',
    'level_type',
    'project_man_type',
    'enterprise_industry_type',
  ],
  props: {
    /**
     * 页面类别
     * businessPortraits  企业画像
     * parkPortraits  园区画像
     * taikePortraits  太科城画像
     */
    pageName: {
      type: String,
      default: function () {
        return this.$route.query?.pageName || '';
      },
    },
    pictureId: {
      type: String,
      default: function () {
        return this.$route.query?.pictureId || '';
      },
    },
    backUrl: {
      type: String,
      default: function () {
        return this.$route.query?.backUrl || '';
      },
    },
    unifiedCreditCode: {
      type: String,
      default: function () {
        return this.$route.query?.unifiedCreditCode || '';
      },
    },
  },
  data() {
    return {
      pictureInfo: {},
      loading: true,
      parentWidth: 1920,
      parentScale: 1,
      nowDate: moment().format('YYYY-MM-DD'),
      nowTime: moment().format('dddd HH:mm:ss'),
      modal: {
        visible: false,
        pictureId: '',
        pictureType: 'businessPortraits',
      },
      introductionClass: 'close-height',
      openFlag: false,
      BusinessDataCharts: {},
      EnergyInfoCharts: {},
      /** 风险投资 饼图*/
      pieCharts1: {},
      /** 能耗信息 柱状图*/
      lineCharts1: {},
      /** 境外投资机构 柱状图*/
      lineCharts2: {},
      /** 风险投资 柱状图*/
      lineCharts3: {},
      bigProjectCharts: {},
      /** 孵化机构 图表相关数据 */
      IICharts: {},
      /** 创新平台 图表相关数据 */
      IPCharts: {},
      /** 研发机构 图表相关数据 */
      RDICharts: {},
      /** 境内研发机构 图表相关数据 */
      DRDICharts: {},
    };
  },
  computed: {
    cardList() {
      const _this = this;
      const tableList =
        this.pageName === 'businessPortraits'
          ? businessTableList.bind(this)()
          : this.pageName === 'parkPortraits'
          ? parkTableList.bind(this)()
          : [];
      const listMap = {
        businessPortraits: [
          // 基础信息
          {
            components: BusinessBaseInfo,
            props: {
              dataDict: this.dict.type,
            },
          },
          // 经营数据
          {
            components: BusinessData,
            props: this.BusinessDataCharts,
          },
          // 企业称号
          {
            components: EnterpriseTitle,
          },
          {
            components: CardWrapper,
            props: {
              cardList: [
                // 知识产权
                {
                  components: IntellectualPropertyRight,
                },
                // 能耗信息
                {
                  components: EnergyInfo,
                  props: this.EnergyInfoCharts,
                },
              ],
            },
          },
        ],
        parkPortraits: [
          // 基础信息
          {
            components: ParkBaseInfo,
            props: {
              dataDict: this.dict.type,
            },
          },
          // 入驻企业
          {
            components: SettledEnterprise,
          },
          // 经营数据
          {
            components: ParkBusinessData,
            props: this.BusinessDataCharts,
          },
          {
            components: CardWrapper,
            props: {
              cardList: [
                // 知识产权
                {
                  components: CardTable,
                  props: {
                    title: '知识产权',
                    span: 12,
                    detailUrl:
                      '/basicData/enterpriseManagement/dataSource/patentForInvention',
                    tableTitle: '专利数量排名TOP10',
                    height: '450px',
                    leftWidth: '140px',
                    columns: [
                      {
                        title: '排名',
                        field: 'rank',
                        width: 90,
                        type: 'html',
                        formatter: ({ cellValue }) =>
                          `<div class='top-title top-title-${
                            +cellValue || 0
                          }' ">TOP${+cellValue || 0}</div>`,
                      },
                      {
                        title: '公司名称',
                        field: 'orgName',
                        minWidth: 100,
                      },
                      {
                        title: '专利数量',
                        field: 'patentSum',
                        width: 82,
                        type: 'html',
                        align: 'right',
                        headerAlign: 'right',
                        formatter: ({ cellValue }) =>
                          `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span>`,
                      },
                    ],
                    loadDataCallback: ParkAPI.getKnowlgPty,
                    cardList: [
                      {
                        img: require('@/assets/images/portraits/1-2001.png'),
                        num: '',
                        label: '发明专利数量',
                        unit: '个',
                        key: 'inventionPatentNum',
                      },
                      {
                        img: require('@/assets/images/portraits/1-2002.png'),
                        num: '',
                        label: 'PCT专利数量',
                        unit: '个',
                        key: 'pctPatentNum',
                      },
                    ],
                  },
                },
                // 人才信息
                {
                  components: CardTable,
                  props: {
                    title: '人才信息(本年)',
                    span: 12,
                    detailUrl:
                      '/basicData/enterpriseManagement/talentManagement',
                    tableTitle: '人才排名TOP10',
                    height: '450px',
                    leftWidth: '140px',
                    columns: [
                      {
                        title: '排名',
                        field: 'rank',
                        width: 90,
                        type: 'html',
                        formatter: ({ cellValue }) =>
                          `<div class='top-title top-title-${
                            +cellValue || 0
                          }' ">TOP${+cellValue || 0}</div>`,
                      },
                      {
                        title: '公司名称',
                        field: 'orgName',
                        minWidth: 80,
                      },
                      {
                        title: '人才数量',
                        field: 'talentCount',
                        minWidth: 80,
                        type: 'html',
                        align: 'right',
                        headerAlign: 'right',
                        formatter: ({ cellValue }) =>
                          `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
                      },
                      {
                        title: '项目数量',
                        field: 'projectCount',
                        minWidth: 80,
                        type: 'html',
                        align: 'right',
                        headerAlign: 'right',
                        formatter: ({ cellValue }) =>
                          `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
                      },
                    ],
                    loadDataCallback: ParkAPI.getTalentInformation,
                    cardList: [
                      {
                        img: require('@/assets/images/portraits/1-2003.png'),
                        num: '',
                        label: '人才数量',
                        unit: '个',
                        key: 'talentCount',
                      },
                      {
                        img: require('@/assets/images/portraits/1-2004.png'),
                        num: '',
                        label: '项目数量',
                        unit: '个',
                        key: 'projectCount',
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            components: CardWrapper,
            props: {
              cardList: [
                // 能耗信息
                {
                  components: CardTable,
                  props: {
                    title: '能耗信息',
                    span: 12,
                    detailUrl:
                      '/basicData/enterpriseManagement/dataSource/powerConsumption',
                    chartTitle: '近6个月电量',
                    leftWidth: '160px',
                    height: '186px',
                    rightComponent: ChartsCard,
                    options: this.lineCharts1,
                    cardList: [
                      {
                        img: require('@/assets/images/portraits/1-2005.png'),
                        num: '',
                        label: '上月耗电量',
                        unit: 'KWh',
                        key: 'lastMonthSElectricityConsumption',
                      },
                    ],
                  },
                },
                // 境外投资机构
                {
                  components: CardTable,
                  props: {
                    title: '境外投资机构',
                    span: 12,
                    detailUrl: '/basicData/enterpriseManagement/overseasManage',
                    ifNotCarryQuery: true,
                    chartTitle: '境外投资记录(近4年)',
                    leftWidth: '176px',
                    height: '186px',
                    rightComponent: ChartsCard,
                    options: this.lineCharts2,
                    cardList: [
                      {
                        img: require('@/assets/images/portraits/1-2006.png'),
                        num: '',
                        label: '境外投资机构数量',
                        unit: '家',
                        key: 'vcInVestEnterpriseNum',
                      },
                    ],
                  },
                },
              ],
            },
          },

          // 风险投资 done
          {
            components: CardTable,
            props: {
              title: '风险投资',
              span: 24,
              detailUrl: '/basicData/enterpriseManagement/riskInvestmentManage',
              canExpand: true,
              leftWidth: '205px',
              height: '186px',
              rightComponent: VCCharts,
              charts1: this.pieCharts1,
              charts2: this.lineCharts3,
              cardList: [
                {
                  img: require('@/assets/images/portraits/1-2007.png'),
                  num: '',
                  label: '被投资企业数量(本年)',
                  unit: '家',
                  key: 'enterpriseNum',
                },
                {
                  img: require('@/assets/images/portraits/1-2008.png'),
                  num: '',
                  label: '投资金额(本年)',
                  unit: '万元',
                  key: 'investAmount',
                },
              ],
            },
          },
        ],
        taikePortraits: [
          // 基础信息
          {
            components: TaikeBaseInfo,
          },
          // 园区信息
          {
            components: ParkInfo,
          },
          // 入驻企业
          {
            components: TaikeSettledEnterprise,
          },
          // 经营数据
          {
            components: TaikeBusinessData,
            props: this.BusinessDataCharts,
          },
          // 重大项目
          {
            components: BigProject,
            props: this.bigProjectCharts,
          },
          // 孵化机构
          {
            components: IncubationInstitutions,
            props: this.IICharts,
          },
          // 创新平台
          {
            components: InnovationPlatform,
            props: this.IPCharts,
          },
          // 研发机构
          {
            components: RDInstitution,
            props: this.RDICharts,
          },
          // 境内研发机构
          {
            components: DomesticRDInstitution,
            props: this.DRDICharts,
          },
          {
            components: CardWrapper,
            props: {
              cardList: [
                // 知识产权
                {
                  components: CardTable,
                  props: {
                    title: '知识产权',
                    span: 12,
                    detailUrl:
                      '/basicData/enterpriseManagement/dataSource/patentForInvention',
                    tableTitle: '专利数量排名TOP10',
                    height: '405px',
                    leftWidth: '140px',
                    columns: [
                      {
                        title: '排名',
                        field: 'rank',
                        width: 90,
                        type: 'html',
                        formatter: ({ cellValue }) =>
                          `<div class='top-title top-title-${
                            +cellValue || 0
                          }' ">TOP${+cellValue || 0}</div>`,
                      },
                      {
                        title: '园区名称',
                        field: 'orgName',
                        minWidth: 100,
                      },
                      {
                        title: '专利数量',
                        field: 'patentSum',
                        width: 82,
                        type: 'html',
                        align: 'right',
                        headerAlign: 'right',
                        formatter: ({ cellValue }) =>
                          `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span>`,
                      },
                    ],
                    loadDataCallback: TaikeAPI.getKnowlgPty,
                    cardList: [
                      {
                        img: require('@/assets/images/portraits/1-2001.png'),
                        num: '',
                        label: '发明专利数量',
                        unit: '个',
                        key: 'inventionPatentNum',
                      },
                      {
                        img: require('@/assets/images/portraits/1-2002.png'),
                        num: '',
                        label: 'PCT专利数量',
                        unit: '个',
                        key: 'pctPatentNum',
                      },
                    ],
                  },
                },
                // 人才信息
                {
                  components: CardTable,
                  props: {
                    title: '人才信息(本年)',
                    span: 12,
                    detailUrl:
                      '/basicData/enterpriseManagement/talentManagement',
                    tableTitle: '人才排名TOP10',
                    height: '405px',
                    leftWidth: '140px',
                    columns: [
                      {
                        title: '排名',
                        field: 'rank',
                        width: 90,
                        type: 'html',
                        formatter: ({ cellValue }) =>
                          `<div class='top-title top-title-${
                            +cellValue || 0
                          }' ">TOP${+cellValue || 0}</div>`,
                      },
                      {
                        title: '园区名称',
                        field: 'orgName',
                        minWidth: 80,
                      },
                      {
                        title: '人才数量',
                        field: 'talentCount',
                        minWidth: 80,
                        type: 'html',
                        align: 'right',
                        headerAlign: 'right',
                        formatter: ({ cellValue }) =>
                          `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
                      },
                      {
                        title: '项目数量',
                        field: 'projectCount',
                        minWidth: 80,
                        type: 'html',
                        align: 'right',
                        headerAlign: 'right',
                        formatter: ({ cellValue }) =>
                          `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
                      },
                    ],
                    loadDataCallback: TaikeAPI.getTalentInformation,
                    cardList: [
                      {
                        img: require('@/assets/images/portraits/1-2003.png'),
                        num: '',
                        label: '人才数量',
                        unit: '个',
                        key: 'talentCount',
                      },
                      {
                        img: require('@/assets/images/portraits/1-2004.png'),
                        num: '',
                        label: '项目数量',
                        unit: '个',
                        key: 'projectCount',
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            components: CardWrapper,
            props: {
              cardList: [
                // 能耗信息
                {
                  components: CardTable,
                  props: {
                    title: '能耗信息',
                    span: 12,
                    detailUrl:
                      '/basicData/enterpriseManagement/dataSource/powerConsumption',
                    chartTitle: '近6个月电量',
                    leftWidth: '180px',
                    height: '214px',
                    rightComponent: ChartsCard,
                    options: this.lineCharts1,
                    cardList: [
                      {
                        img: require('@/assets/images/portraits/1-2005.png'),
                        num: '',
                        label: '上月耗电量',
                        unit: 'KWh',
                        key: 'lastMonthSElectricityConsumption',
                      },
                    ],
                  },
                },
                // 境外投资机构
                {
                  components: CardTable,
                  props: {
                    title: '境外投资机构',
                    span: 12,
                    detailUrl: '/basicData/enterpriseManagement/overseasManage',
                    chartTitle: '境外投资记录(近4年)',
                    leftWidth: '176px',
                    height: '214px',
                    rightComponent: ChartsCard,
                    options: this.lineCharts2,
                    cardList: [
                      {
                        img: require('@/assets/images/portraits/1-2006.png'),
                        num: '',
                        label: '境外投资机构数量',
                        unit: '家',
                        key: 'vcInVestEnterpriseNum',
                      },
                    ],
                  },
                },
              ],
            },
          },

          // 风险投资
          {
            components: CardTable,
            props: {
              title: '风险投资',
              span: 24,
              detailUrl: '/basicData/enterpriseManagement/riskInvestmentManage',
              canExpand: true,
              leftWidth: '205px',
              height: '186px',
              rightComponent: VCCharts,
              charts1: this.pieCharts1,
              charts2: this.lineCharts3,
              cardList: [
                {
                  img: require('@/assets/images/portraits/1-2007.png'),
                  num: '',
                  label: '被投资企业数量(本年)',
                  unit: '家',
                  key: 'enterpriseNum',
                },
                {
                  img: require('@/assets/images/portraits/1-2008.png'),
                  num: '',
                  label: '投资金额(本年)',
                  unit: '万元',
                  key: 'investAmount',
                },
              ],
            },
          },
          // 园区安全
          {
            components: ParkSafety,
            props: {
              title: '园区安全',
              span: 24,
              detailUrl: '/greenSafety/securityManagement/parkSecurity',
              canExpand: true,
            },
          },
          // 小微工程申报
          {
            components: CardTable,
            props: {
              title: '小微工程申报',
              span: 24,
              detailUrl: '/greenSafety/securityManagement/buildingSafety',
              canExpand: true,
              leftWidth: '205px',
              height: '186px',
              columns: smallEngineeringColumns_park.call(this),
              loadDataCallback: TaikeAPI.getEicroProjectsDeclaration,
              cardList: [
                {
                  img: require('@/assets/images/portraits/1-icon-charging-station-time.png'),
                  num: '',
                  label: '待处理数量',
                  unit: '家',
                  key: 'pendingNum',
                },
              ],
            },
          },
          // 隐患整改
          {
            components: CardTable,
            props: {
              title: '隐患整改',
              span: 24,
              detailUrl: '/greenSafety/securityManagement/rectificationInfo',
              canExpand: true,
              leftWidth: '205px',
              height: '186px',
              columns: hiddenDangerColumns_park.call(this),
              loadDataCallback: TaikeAPI.getRectificationRecords,
              cardList: [
                {
                  img: require('@/assets/images/portraits/1-icon-charging-station-bs.png'),
                  num: '',
                  label: '整改中数量',
                  unit: '个',
                  key: 'rectifiedNum',
                },
                {
                  img: require('@/assets/images/portraits/1-icon-photovoltaic-power.png'),
                  num: '',
                  label: '未整改数量',
                  unit: '个',
                  key: 'notRectifiedNum',
                },
              ],
            },
          },
        ],
      };
      return [
        ...listMap[this.pageName],
        ...tableList.map((item) => ({
          components: CardTable,
          props: item,
        })),
      ];
    },
  },
  async mounted() {
    this.getData();
    setTimeout(this.resize, 100);
    window.onresize = throttle(this.resize, 300);
  },
  methods: {
    isNullShowText,
    handleModalClose() {
      this.modal.visible = false;
    },
    closeModal() {
      if (this.$route.query?.pageName) {
        this.$router.push({
          path: `/portraits/${
            this.$route.query?.pageName || 'businessPortraits'
          }`,
        });
      } else {
        this.$emit('close');
      }
    },
    open() {
      this.openFlag = !this.openFlag;
      this.introductionClass = 'open-height';
    },
    // 收起
    close() {
      this.openFlag = !this.openFlag;
      this.introductionClass = 'close-height';
    },
    resize() {
      this.parentWidth =
        [...document.querySelectorAll('.portrait')].at(-1)?.parentNode
          ?.offsetWidth || 1920;

      this.parentScale = this.parentWidth / 1920;
    },
    moment,
    // 查询页面数据信息
    async getData() {
      const _this = this;
      let params = {
        parkId: this.pictureId,
      };
      // 企业画像
      if (this.pageName === 'businessPortraits') {
        this.loading = true;
        const [
          {
            value: [res1],
          },
          {
            value: [res2],
          },
          {
            value: [res3],
          },
          {
            value: [res4],
          },
          {
            value: [res5],
          },
          {
            value: [res6],
          },
        ] = await Promise.allSettled([
          BusinessAPI.getBaseInformation(this.pictureId),
          BusinessAPI.getIntellectualProperty(this.pictureId),
          BusinessAPI.getQtyUsageInformation(this.pictureId),
          BusinessAPI.getEnterpriseTitle(this.pictureId),
          BusinessAPI.getBusinessPictureOperate({
            companyId: this.pictureId,
            rateTime: moment().format('YYYY-MM'),
          }),
          BusinessAPI.getRankingByScc(this.pictureId),
          // BusinessAPI.get(this.pictureId),
        ]);
        this.loading = false;
        const res1Data = res1?.data || {};
        this.pictureInfo = {
          ...res1Data,
          pictureId: this.pictureId,
          label: res1Data?.labelNames?.split(',') || [],
          titles: res4?.data || [],
          taxRanking: res6?.data?.taxRanking,
          growRateRanking: res6?.data?.growRateRanking,
          incomeCurrPer: res5?.data?.tkbTaxableMarketRes?.incomeCurrPer,
          incomeCorrPer: res5?.data?.tkbTaxableMarketRes?.incomeCorrPer,
          growRate: res5?.data?.tkbTaxableMarketRes?.growRate,
          inventionPatentNum: res2?.data?.inventionPatentNum,
          pctPatentNum: res2?.data?.pctPatentNum,
          coordinates:
            (res1Data?.latitude || '-') + ' , ' + (res1Data?.longitude || '-'),
          statusLabel: res1Data?.status
            ? this.dict.type.enterprise_register_status.find(
                (x) => x.value === res1Data?.status
              )?.label || '-'
            : '-',
          lastMonthUsage: undefined,
        };
        this.BusinessDataCharts = {
          charts1: {
            xAxis:
              res5?.data?.taxableMarketResList?.map((x) => x.rateTime) || [],
            data: [
              res5?.data?.taxableMarketResList?.map((x) => x.incomeCurrPer) ||
                [],
              res5?.data?.taxableMarketResList?.map((x) => x.incomeCorrPer) ||
                [],
            ],
          },
          charts2: {
            xAxis:
              res5?.data?.taxableMarketResList?.map((x) => x.rateTime) || [],
            data:
              res5?.data?.taxableMarketResList?.map((x) => x.growRate) || [],
          },
          charts3: {
            xAxis:
              res5?.data?.taxableMarketResList?.map((x) => x.rateTime) || [],
            data: res5?.data?.taxableMarketResList?.map((x) => x.tax) || [],
          },
        };

        const charts4Data = res3?.data || [];
        if (charts4Data.length) {
          charts4Data.reverse();
          this.pictureInfo.lastMonthUsage =
            charts4Data[charts4Data.length - 1].eleQty;
          this.EnergyInfoCharts = {
            charts1: {
              xAxis: charts4Data.map((x) => x.reportYm),
              data: charts4Data.map((x) => x.eleQty),
            },
          };
        }
      } else if (this.pageName === 'parkPortraits') {
        this.loading = true;
        const [
          {
            value: [res1],
          },
          {
            value: [res2],
          },
          {
            value: [res3],
          },
          {
            value: [res4],
          },
          {
            value: [res5],
          },
          {
            value: [res6],
          },
        ] = await Promise.allSettled([
          ParkAPI.getBaseInformation(this.pictureId),
          ParkAPI.getSettBusinessData({
            parkId: this.pictureId,
            rateTime: moment().format('YYYY-MM'),
          }),
          ParkAPI.getSettledInfo({ parkId: this.pictureId }),
          ParkAPI.getUsageInformation({ parkId: this.pictureId }),
          ParkAPI.getOverseasResearchInformation({ parkId: this.pictureId }),
          ParkAPI.getVcInformation({ parkId: this.pictureId }),
        ]);
        this.loading = false;
        const res1Data = res1?.data || {};
        this.pictureInfo = {
          ...res1Data,
          pictureId: this.pictureId,
          label: res1Data?.labelNames?.split(',') || [],
          tax: res2?.data?.tax,
          taxRank: res2?.data?.taxRank,
          muAvgTax: res2?.data?.muAvgTax,
          muAvgTaxRank: res2?.data?.muAvgTaxRank,
          incomeCorrPer: res2?.data?.incomeCorrPer,
          incomeCurrPer: res2?.data?.incomeCurrPer,
          enterpriseCount: res3?.data?.count,
          // 登记状态统计
          regulateList: (res3?.data?.regulateList || []).map((x) => ({
            name: x.regulate,
            value: x.count,
          })),
          // 是否规上统计
          statusList: (res3?.data?.statusList || []).map((x) => ({
            name: x.status,
            value: x.count,
          })),
          // 被投资企业数量(本年)
          enterpriseNum: res6?.data?.enterpriseNum,
          // 投资金额(本年)
          investAmount: res6?.data?.investAmount,
          lastMonthSElectricityConsumption: res4?.data?.[0]?.eleQty,
          vcInVestEnterpriseNum: res5?.data?.vcInVestEnterpriseNum,
        };

        this.BusinessDataCharts = {
          charts1: {
            xAxis: res2?.data?.infoList?.map((x) => x.rateTime) || [],
            data: res2?.data?.infoList?.map((x) => x.tax) || [],
          },
          charts2: {
            xAxis: res2?.data?.infoList?.map((x) => x.rateTime) || [],
            data: [
              res2?.data?.infoList?.map((x) => x.incomeCurrPer) || [],
              res2?.data?.infoList?.map((x) => x.incomeCorrPer) || [],
            ],
          },
        };

        this.lineCharts1 = useLineCharts({
          xAxis: res4?.data?.map((x) => x.reportYm),
          unit: 'KWh',
          series: [
            {
              name: '用电量',
              data: res4?.data?.map((x) => x.eleQty) || [],
              color: '#6c62ff',
            },
          ],
        });

        this.lineCharts2 = useLineCharts({
          xAxis: res5?.data?.vcInVestRecords?.map((x) => x.year),
          unit: '家',
          series: [
            {
              name: '境外投资记录(近4年)',
              data:
                res5?.data?.vcInVestRecords?.map((x) => x.enterpriseNum) || [],
              color: '#38d3b6',
            },
          ],
        });

        this.pieCharts1 = {
          data: res6?.data?.industryTypeNum,
        };
        this.lineCharts3 = {
          xAxis: res6?.data?.parkVcInformation?.map((x) => x.year),
          data: res6?.data?.parkVcInformation?.map((x) => x.amount) || [],
        };
      } else {
        // 太科城画像
        this.loading = true;
        const [
          {
            value: [res1],
          },
          {
            value: [res2],
          },
          {
            value: [res3],
          },
          {
            value: [res4],
          },
          {
            value: [res5],
          },
          {
            value: [res6],
          },
          {
            value: [res7],
          },
          {
            value: [res8],
          },
          {
            value: [res9],
          },
          {
            value: [res10],
          },
          {
            value: [res11],
          },
          {
            value: [res12],
          },
          {
            value: [res13],
          },
        ] = await Promise.allSettled([
          // 1-5
          TaikeAPI.getParkSafetyInfo(this.pictureId),
          TaikeAPI.getSettBusinessData({
            parkId: this.pictureId,
            month: moment().format('YYYY-MM'),
          }),
          TaikeAPI.getSettledInfo({ parkId: this.pictureId }),
          TaikeAPI.getUsageInformation({ parkId: this.pictureId }),
          TaikeAPI.getOverseasResearchInformation({ parkId: this.pictureId }),

          // 6-10
          TaikeAPI.getVcInformation({ parkId: this.pictureId }),
          TaikeAPI.getParkAysInfo(),
          TaikeAPI.getInnovationPlatform(),
          TaikeAPI.getIncubationOrg(),
          TaikeAPI.getInnovatePlatform(),

          // 11-15
          TaikeAPI.getDevOrg(),
          TaikeAPI.getWithinDevOrg(),
          TaikeAPI.getTKBInfo(),
        ]);
        this.loading = false;
        const res1Data = res1?.data || {};
        this.pictureInfo = {
          ...res1Data,
          pictureId: this.pictureId,
          tax: res2?.data?.tax,
          taxRank: res2?.data?.taxRank,
          muAvgTax: res2?.data?.muAvgTax,
          muAvgTaxRank: res2?.data?.muAvgTaxRank,
          incomeCorrPer: res2?.data?.incomeCorrPer,
          incomeCurrPer: res2?.data?.incomeCurrPer,
          taxRankList: res2?.data?.taxRankList || [],
          muTaxRankList: res2?.data?.muTaxRankList || [],
          enterpriseCount: res3?.data?.count,
          // 登记状态统计
          regulateList: (res3?.data?.regulateList || []).map((x) => ({
            name: x.regulate,
            value: x.count,
          })),
          // 是否规上统计
          registerStatusList: res3?.data?.statusList || [],
          // 上月耗电量
          lastMonthSElectricityConsumption: res4?.data?.[0]?.eleQty,
          // 境外投资机构数量
          vcInVestEnterpriseNum: res5?.data?.vcInVestEnterpriseNum,
          // 被投资企业数量(本年)
          enterpriseNum: res6?.data?.enterpriseNum,
          // 投资金额(本年)
          investAmount: res6?.data?.investAmount,

          // 园区信息相关
          ...(res7.data || {}),
          // 重大项目相关
          ...(res8.data || {}),
          // 孵化机构
          ...(res9.data || {}),
          // 创新平台
          ...(res10.data || {}),
          // 研发机构
          ...(res11.data || {}),
          // 境内研发机构
          ...(res12.data || {}),
          // 园区安全
          ...(res1.data || {}),
          // 基础信息
          ...(res13.data || {}),
          label: res13?.data?.mainIndustry?.split('；') || [],
          fiveIndustry: res13?.data?.fiveIndustry?.split('；') || [],
          fiveTenX: res13?.data?.fiveTenX?.split('；') || [],
          tenIndustry: res13?.data?.tenIndustry?.split('；') || [],
          xindustry: res13?.data?.xindustry?.split('；') || [],
        };
        console.log(this.pictureInfo, 'info');
        // 孵化机构
        this.IICharts = {
          charts1: {
            xAxis:
              res9?.data?.incubationInstitutionsSumOfLastFourYears?.map(
                (x) => x.year
              ) || [],
            data:
              res9?.data?.incubationInstitutionsSumOfLastFourYears?.map(
                (x) => x.amount
              ) || [],
          },
          charts2: {
            data: res9?.data?.incubationCarrierTypeList || [],
          },
          charts3: {
            data: res9?.data?.incubationCarrierLevelList || [],
          },
          charts4: {
            data: res9?.data?.creationCompleteList || [],
          },
        };

        // 创新平台
        this.IPCharts = {
          charts1: {
            data: res10?.data?.domainList || [],
          },
          charts2: {
            data: res10?.data?.projectStatusList || [],
          },
          charts3: {
            xAxis: res10?.data?.infoList?.map((x) => x.rateTime) || [],
            data: res10?.data?.infoList?.map((x) => x.platformCount) || [],
          },
          charts4: {
            xAxis: res10?.data?.infoList?.map((x) => x.rateTime) || [],
            data: [
              res10?.data?.infoList?.map((x) => x.totalInvestment) || [],
              res10?.data?.infoList?.map((x) => x.investmentVolume) || [],
            ],
          },
        };

        // 研发机构
        this.RDICharts = {
          charts1: {
            data: res11?.data?.platformTypeList || [],
          },
          charts2: {
            data: res11?.data?.platformLevelList || [],
          },
          charts3: {
            data: res11?.data?.creationCompleteList || [],
          },
          charts4: {
            xAxis:
              res11?.data?.randDInstitutionSumOfLastFourYears?.map(
                (x) => x.year
              ) || [],
            data:
              res11?.data?.randDInstitutionSumOfLastFourYears?.map(
                (x) => x.count
              ) || [],
          },
        };
        // 境内研发机构
        this.DRDICharts = {
          charts1: {
            data: res12?.data?.institutionLevelList || [],
          },
          charts2: {
            data: res12?.data?.creationCompleteList || [],
          },
          charts3: {
            xAxis:
              res12?.data?.randDInstitutionSumOfLastFourYears?.map(
                (x) => x.year
              ) || [],
            data:
              res12?.data?.randDInstitutionSumOfLastFourYears?.map(
                (x) => x.count
              ) || [],
          },
        };

        // 经营数据
        this.BusinessDataCharts = {
          charts1: {
            xAxis: res2?.data?.infoList?.map((x) => x.rateTime) || [],
            data: res2?.data?.infoList?.map((x) => x.tax) || [],
          },
          charts2: {
            xAxis: res2?.data?.infoList?.map((x) => x.rateTime) || [],
            data: [
              res2?.data?.infoList?.map((x) => x.incomeCurrPer) || [],
              res2?.data?.infoList?.map((x) => x.incomeCorrPer) || [],
            ],
          },
          charts3: {
            data: res2?.data?.muTaxRankList || [],
          },
          charts4: {
            data: res2?.data?.taxRankList || [],
          },
          rateTime: res2?.data?.rateTime || '',
        };

        // 重大项目
        this.bigProjectCharts = {
          charts1: {
            data: res8?.data?.levelInfoList || [],
          },
          charts2: {
            xAxis:
              res8?.data?.majorProjectResList?.map((x) => x.rateTime) || [],
            data:
              res8?.data?.majorProjectResList?.map((x) => x.projectCount) || [],
          },
          charts3: {
            xAxis:
              res8?.data?.majorProjectResList?.map((x) => x.rateTime) || [],
            data:
              res8?.data?.majorProjectResList?.map((x) => x.floorSpace) || [],
          },
          charts4: {
            xAxis:
              res8?.data?.majorProjectResList?.map((x) => x.rateTime) || [],
            data: [
              res8?.data?.majorProjectResList?.map((x) => x.investment) || [],
              res8?.data?.majorProjectResList?.map(
                (x) => x.completeInvestment
              ) || [],
            ],
          },
        };

        this.lineCharts1 = useLineCharts({
          xAxis: res4?.data?.map((x) => x.reportYm),
          unit: 'KWh',
          series: [
            {
              name: '用电量',
              data: res4?.data?.map((x) => x.eleQty) || [],
              color: '#6c62ff',
            },
          ],
        });

        this.lineCharts2 = useLineCharts({
          xAxis: res5?.data?.vcInVestRecords?.map((x) => x.year),
          unit: '家',
          series: [
            {
              name: '企业个数',
              data:
                res5?.data?.vcInVestRecords?.map((x) => x.enterpriseNum) || [],
              color: '#38d3b6',
            },
          ],
        });

        this.pieCharts1 = {
          data: res6?.data?.industryTypeNum,
        };
        this.lineCharts3 = {
          xAxis: res6?.data?.parkVcInformation?.map((x) => x.year),
          data: res6?.data?.parkVcInformation?.map((x) => x.amount) || [],
        };
      }

      setInterval(function () {
        _this.nowDate = moment().format('YYYY-MM-DD');
        _this.nowTime = moment().format('dddd HH:mm:ss');
      }, 1000);
    },

    async resetBusinessData(month, type) {
      if (type == 'Taike') {
        const [res] = await TaikeAPI.getSettBusinessData({
          parkId: this.pictureId,
          rateTime: month,
        });
        if (res) {
          this.BusinessDataCharts = {
            charts1: {
              xAxis: res?.data?.infoList?.map((x) => x.rateTime) || [],
              data: res?.data?.infoList?.map((x) => x.tax) || [],
            },
            charts2: {
              xAxis: res?.data?.infoList?.map((x) => x.rateTime) || [],
              data: [
                res?.data?.infoList?.map((x) => x.incomeCurrPer) || [],
                res?.data?.infoList?.map((x) => x.incomeCorrPer) || [],
              ],
            },
            charts3: {
              data: res?.data?.muTaxRankList || [],
            },
            charts4: {
              data: res?.data?.taxRankList || [],
            },
            rateTime: res?.data?.rateTime || '',
          };
          this.pictureInfo = {
            ...this.pictureInfo,
            pictureId: this.pictureId,
            tax: res?.data?.tax,
            taxRank: res?.data?.taxRank,
            muAvgTax: res?.data?.muAvgTax,
            muAvgTaxRank: res?.data?.muAvgTaxRank,
            incomeCorrPer: res?.data?.incomeCorrPer,
            incomeCurrPer: res?.data?.incomeCurrPer,
            taxRankList: res?.data?.taxRankList || [],
            muTaxRankList: res?.data?.muTaxRankList || [],
          };
        }
        // 企业
      } else if (type == 'businessPortraits') {
        const [res] = await BusinessAPI.getBusinessPictureOperate({
          companyId: this.pictureId,
          rateTime: month,
        });
        if (res) {
          this.BusinessDataCharts = {
            charts1: {
              xAxis:
                res?.data?.taxableMarketResList?.map((x) => x.rateTime) || [],
              data: [
                res?.data?.taxableMarketResList?.map((x) => x.incomeCurrPer) ||
                  [],
                res?.data?.taxableMarketResList?.map((x) => x.incomeCorrPer) ||
                  [],
              ],
            },
            charts2: {
              xAxis:
                res?.data?.taxableMarketResList?.map((x) => x.rateTime) || [],
              data:
                res?.data?.taxableMarketResList?.map((x) => x.growRate) || [],
            },
            charts3: {
              xAxis:
                res?.data?.taxableMarketResList?.map((x) => x.rateTime) || [],
              data: res?.data?.taxableMarketResList?.map((x) => x.tax) || [],
            },
          };
          this.pictureInfo = {
            ...this.pictureInfo,
            incomeCurrPer: res?.data?.tkbTaxableMarketRes?.incomeCurrPer,
            incomeCorrPer: res?.data?.tkbTaxableMarketRes?.incomeCorrPer,
            growRate: res?.data?.tkbTaxableMarketRes?.growRate,
          };
        }
      } else if (type == 'parkPortraits') {
        const [res] = await ParkAPI.getSettBusinessData({
          parkId: this.pictureId,
          rateTime: month,
        });
        if (res) {
          this.BusinessDataCharts = {
            charts1: {
              xAxis:
                res?.data?.taxableMarketResList?.map((x) => x.rateTime) || [],
              data: [
                res?.data?.taxableMarketResList?.map((x) => x.incomeCurrPer) ||
                  [],
                res?.data?.taxableMarketResList?.map((x) => x.incomeCorrPer) ||
                  [],
              ],
            },
            charts2: {
              xAxis:
                res?.data?.taxableMarketResList?.map((x) => x.rateTime) || [],
              data:
                res?.data?.taxableMarketResList?.map((x) => x.growRate) || [],
            },
            charts3: {
              xAxis:
                res?.data?.taxableMarketResList?.map((x) => x.rateTime) || [],
              data: res?.data?.taxableMarketResList?.map((x) => x.tax) || [],
            },
          };
          this.pictureInfo = {
            ...this.pictureInfo,
            tax: res?.data?.tax,
            taxRank: res?.data?.taxRank,
            muAvgTax: res?.data?.muAvgTax,
            muAvgTaxRank: res?.data?.muAvgTaxRank,
            incomeCorrPer: res?.data?.incomeCorrPer,
            incomeCurrPer: res?.data?.incomeCurrPer,
          };
        }
      }
    },

    handleModalOpen(pictureType, pictureId) {
      this.modal.pictureId = '';
      this.modal.pictureType = '';
      if (pictureId) {
        this.modal.pictureId = pictureId;
        this.modal.pictureType = pictureType;
        this.modal.visible = true;
      } else {
        this.$message.warn('暂无对应画像');
      }
    },
    openModal(type, pictureId) {
      this.$router.push({
        path: '/portraits/portraitsDetail',
        query: {
          pageName: type,
          pictureId,
        },
      });
    },
  },
};
</script>

<style scoped lang="less">
.portrait {
  --parent-width: 1920px;
  --parent-scale: 1;
  width: 1920px;
  // aspect-ratio: 16/9;
  min-height: 1080px;
  overflow: hidden;
  background: #f5f5f5;
  background: url('@/assets/images/portraits/1-0.png');
  background-repeat: no-repeat;
  background-size: 1920px 1080px;
  transform: translate(-50%, -50%) scale(var(--parent-scale))
    translate(50%, 50%);

  ::v-deep .top-title {
    font-family: YouSheBiaoTiHei;
    font-size: 14px;
    color: #666666;
    border-radius: 0px 16px 16px 0px;
    width: 48px;
    height: 18px;
    text-align: center;
    line-height: 18px;
    opacity: 1;
    &-1 {
      color: #ff6f3f;
      background: linear-gradient(
        270deg,
        rgba(255, 123, 0, 0.2) 0%,
        rgba(255, 123, 0, 0) 100%
      );
    }
    &-2 {
      color: #ffba00;
      background: linear-gradient(
        270deg,
        rgba(255, 178, 0, 0.2) 0%,
        rgba(255, 161, 0, 0) 100%
      );
    }
    &-3 {
      color: #00cd52;
      background: linear-gradient(
        270deg,
        rgba(63, 211, 122, 0.2) 0%,
        rgba(81, 231, 153, 0) 100%
      );
    }
  }
  &-cnt {
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    & > div {
      margin-bottom: 30px;
    }
    &-header {
      width: 100%;
      height: 133px;
      background: url('@/assets/images/portraits/1-5.png');
      background-size: 100% 100%;
      position: relative;
      h2 {
        focont-family: Alimama ShuHeiTi;
        font-size: 36px;
        font-weight: bold;
        line-height: 66px;
        text-align: center;
        color: #333333;
      }
      .time-area {
        position: absolute;
        right: 20px;
        top: 35px;
        height: 43px;
        width: 110px;

        font-family: AlibabaPuHuiTi;
        font-size: 13.47px;
        font-weight: 500;
        line-height: 18.64px;
        color: rgba(51, 51, 51, 0.8);
      }
      .back-btn {
        position: absolute;
        left: 80px;
        top: 97px;
        padding: 5px 15px;
        border-radius: 5px;
        background-color: #4ec57d;
        color: #fff;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
      }
    }

    color: #333333;
    &-bottom {
      width: 1610px;
      height: 731px;
      margin: 0 auto;
      display: flex;
      &-left {
        width: 397px;
        height: 100%;
        margin-right: 13px;
        background-color: #fff;
        background-image: url('@/assets/images/portraits/1-6.png');
        background-size: 333px 206px;
        background-position: 32px 32px;
        background-repeat: no-repeat;
        font-family: HarmonyOS Sans SC;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        letter-spacing: 0em;
        color: rgba(51, 51, 51, 0.8);
        border-radius: 8px;
      }
      &-right {
        width: 1231px;
        padding-right: 21px;
        height: 100%;
        overflow: hidden auto;
      }
    }
  }
  .right-cnt {
    & > .ant-col + .ant-col {
      margin-top: 16px;
    }
  }

  .ant-card {
    background-color: #fff;
    border: none;
    border-radius: 8px;
    color: #333333;
    padding: 0;
    overflow: hidden;
  }

  ::v-deep .ant-card-body {
    padding: 8px 24px 24px 24px;
  }

  scrollbar-color: auto;
  ::-webkit-scrollbar {
    width: 8px; /* 滚动条宽度 */
    background: #dceee3;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background-color: #53c477; /* 滚动条滑块颜色 */
    border-radius: 4px;
  }
}

.open-height,
.close-height {
  margin-bottom: 0.25em;
}
.close-height {
  height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.park-name {
  display: flex;
  flex-wrap: wrap;
  height: 300px;
  overflow: auto;

  button {
    display: inline-block;
    width: 25%;
    text-align: left;
    margin-bottom: 15px;
    ::v-deep span {
      display: inline-block;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

::v-deep .ant-spin-blur,
::v-deep .ant-spin-container {
  height: 100%;
}

::v-deep .ant-btn-sm {
  height: 28px;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #5ecb82;
  font-size: 14px;
  font-weight: 500;
  line-height: 19px;
  letter-spacing: 0em;
  color: #5ecb82;
  &.ant-btn-primary {
    color: #ffffff;
    background: #5ecb82;
  }
}

::v-deep .vxe-header--row {
  font-size: 14px;
}

::v-deep .detail-picture {
  width: 100%;
  height: 218px;
  box-sizing: border-box;
  // border: 2px solid rgba(146, 103, 255, 0.09);
  border-radius: 8px;
  padding: 16px;
  box-sizing: border-box;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .info {
    margin-top: 16px;
  }
  .num {
    font-family: D-DIN;
    font-size: 40px;
    font-weight: bold;
    line-height: 34px;
    letter-spacing: 0px;
    margin-right: 4px;
    color: rgba(4, 15, 36, 0.85);
  }
}
.risk-investment-area {
  .ant-col {
    height: 117px;
    border-radius: 8px;
    padding: 16px;
    &:nth-of-type(1) {
      background-image: url('@/assets/images/portraits/1-2020.png');
      background-size: 564px 117px;
      background-repeat: no-repeat;
    }
    &:nth-of-type(2) {
      background-image: url('@/assets/images/portraits/1-2021.png');
      background-size: 564px 117px;
      background-repeat: no-repeat;
      background-position: 5px 0;
    }

    .tip {
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 500;
      line-height: normal;
      color: #333333;
      padding-left: 16px;
    }
    .num {
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: 500;
      line-height: normal;
      color: #333333;
      padding-left: 16px;
      margin-top: 16px;
      span {
        font-family: D-DIN;
        font-size: 40px;
        font-weight: bold;
        line-height: 34px;
        color: rgba(4, 15, 36, 0.85);
      }
    }
  }
}
</style>
