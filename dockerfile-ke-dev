#FROM node:16.18.0 as build
FROM harbor.ncditieyunying.com:6443/base/node:14.16.0 as build
WORKDIR /tmp
COPY . ./

RUN npm config set registry http://nexus3-inner.ncditieyunying.com/repository/npm-public/ \
    && npm install \
    && npm run build
FROM harbor.ncditieyunying.com:6443/middlewares/nginx:1.25.2
WORKDIR /usr/share/nginx/html/
RUN rm -f *
COPY --from=build /tmp/dist .
COPY --from=build /tmp/nginx.conf /etc/nginx/conf.d/default.conf
