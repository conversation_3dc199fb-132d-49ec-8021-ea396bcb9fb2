<template>
  <page-layout>
    <table-component
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :params="params"
      :pageName="pageName"
    >
    </table-component>
  </page-layout>
</template>

<script>
import { portraitsMixin } from '../mixins/index.js';
import tableComponent from '../components/tableComponent.vue';

export default {
  name: 'index',
  components: { tableComponent },
  mixins: [portraitsMixin],
  dicts: ['park_type'],
  data() {
    return {
      pageName: 'parkPortraits',
      isCompletedList: [],
      parkList: [],
      params: {
        parkName: undefined,
      },
      tableColumn: [
        {
          field: '',
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: 70,
        },
        {
          field: 'parkName',
          title: '园区名称',
        },
        {
          field: 'address',
          title: '地址',
        },
        {
          field: 'type',
          title: '园区类别',
          formatter: ({ cellValue }) => {
            return this.dict?.type?.park_type?.find((q) => q.value == cellValue)
              ?.label;
          },
        },
        {
          field: 'isCompleted',
          title: '建成状况',
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.isCompletedList);
          },
        },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        //筛选控件配置
        config: [
          {
            field: 'parkName',
            title: '园区名称',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        okBtn: false,
        cancelText: '关闭',
        menu: true,
        menuWidth: 200,
        menuFixed: 'top',
        modalFullScreen: true,
        customOperationTypes: [
          {
            //定义操作类型名称
            typeName: 'detail',
            title: '画像详情',
            //该操作下弹窗对应内容插槽
            slotName: 'detail',
          },
        ],
      };
    },
  },
  created() {
    this.getCodeByType('type').then((res) => {
      this.parkList = res;
    });
    this.getCodeByType('completed').then((res) => {
      this.isCompletedList = res;
    });
  },
  methods: {},
};
</script>

<style scoped></style>
