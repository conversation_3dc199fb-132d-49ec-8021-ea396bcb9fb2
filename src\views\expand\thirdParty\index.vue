<!--
 * @Author: @17550589859 <EMAIL>
 * @Date: 2025-04-08 16:53:34
 * @LastEditors: @17550589859 <EMAIL>
 * @LastEditTime: 2025-04-08 17:17:40
 * @FilePath: \management-tk\src\views\expand\thirdParty\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="wrap">
    <a-card
      class="card-item"
      v-for="(item, index) in list"
      :key="index"
      :title="item.label"
    >
      <div class="text" @click="toLink(item)">{{ item.value }}</div>
      <!-- <a :href="item.value">{{ item.value }}</a> -->
    </a-card>
  </div>
</template>

<script>
export default {
  dicts: ['tkb_oa_url'],
  data() {
    return {};
  },
  computed: {
    list() {
      return this.dict?.type?.tkb_oa_url || [];
    },
  },
  methods: {
    toLink(item) {
      if (item.value) {
        window.open(item.value, '_blank');
      }
    },
  },
  mounted() {
    console.log(this.dict.type.tkb_oa_url, 'tkb_oa_url');
  },
};
</script>

<style lang="less" scoped>
.wrap {
  padding: 20px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 16px;
  .card-item {
    border-radius: 8px;
    overflow: hidden;
    .text {
      width: 300px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #70b587;
      cursor: pointer;
    }
  }
}
</style>
