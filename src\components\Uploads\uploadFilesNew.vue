<template>
  <div :class="[showDefaultUploadList ? 'show-list' : 'no-list']">
    <a-form :form="form">
      <a-upload
        list-type="picture"
        :accept="accept"
        :show-upload-list="showDefaultUploadList"
        :default-file-list="fileList"
        :fileList="fileList"
        :customRequest="customRequestPic"
        :before-upload="beforeUploadPic"
        @change="handleChangePic"
      >
        <a-button :disabled="disabled || fileList.length >= maxCount">
          <a-icon type="upload" /> 上传
        </a-button>
      </a-upload>
    </a-form>
  </div>
</template>

<script>
import { fileUpload } from '@/api/upload/index.js';
export default {
  props: {
    accept: {
      type: String,
      default: '',
    },
    showDefaultUploadList: {
      type: Boolean,
      default() {
        return true;
      },
    },
    //弹框类型
    disabled: {
      type: Boolean,
      default: false,
    },
    //
    listType: {
      type: String,
      default: 'picture-card',
    },
    maxSize: {
      type: Number,
      default() {
        return 4; //4M
      },
    },
    maxCount: {
      type: Number,
      default() {
        return 15;
      },
    },
    delFileId: {
      type: String,
      default() {
        return '';
      },
    },
    fileListTemp: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      fileList: [],
    };
  },

  mounted() {},
  watch: {
    fileListTemp(val) {
      console.log('文件改变', val);
      if (val) {
        val.forEach((file) => {
          setTimeout(() => {
            if (!file.handelFlag) {
              // this.appendNode(file);
              file.handelFlag = true;
            }
          }, 0);
        });
        this.fileList = val;
      }
    },
    tabChangeFlag(val) {
      if (val) {
        this.fileList = [];
        this.$emit('tabChangeFlagToggle');
      }
    },
    delFileId(val) {
      if (val) {
        let i = null;
        this.fileList.forEach((element, index) => {
          if (element.uid === val) {
            console.log(222, element, index);
            i = index;
          }
        });
        this.fileList.splice(i, 1);
        this.$emit('setAnnexList', this.fileList, this.showDefaultUploadList);
      }
    },
  },
  methods: {
    //文件回显和数据处理
    beforeUploadPic(file) {
      this.isLt1M = file.size / 1024 / 1024 < this.maxSize; //默认50kb
      if (!this.isLt1M) {
        console.log(file, 'file------');
        this.uploadFlag = false;
        return this.$message.error(`文件大小不能超过 ${this.maxSize}M `);
      }
    },
    handleChangePic(info) {
      this.fileList = info.fileList;
      if (!this.isLt1M) {
        this.fileList = this.fileList.pop();
        this.$emit('setAnnexList', info.fileList, this.showDefaultUploadList);
        return;
      }
      if (info.file.status === 'removed') {
        this.$emit('setAnnexList', info.fileList, this.showDefaultUploadList);
        return;
      }
      if (info.file.status === 'uploading') {
        this.loading = true;
        return;
      }
      if (info.file.status === 'done') {
        info.file.fileSize = this.sizeHandle(info.file.size);
        info.fileList[info.fileList.length - 1].fileSize = this.sizeHandle(
          info.file.size
        );
        // this.appendNode(info.file);
        const fileReader = new FileReader();
        fileReader.readAsDataURL(info.file.originFileObj);
        this.getBase64(info.file.originFileObj, (imageUrl) => {
          this.imageUrl = imageUrl;
          this.loading = false;
        });
      }
    },
    appendNode(file) {
      if (!this.showDefaultUploadList) return;
      let suc = document.getElementsByClassName('ant-upload-list-item-name');
      if (suc && suc.length > 0) {
        suc[suc.length - 1].style.width = '60%';
        let parent = suc[suc.length - 1].parentNode;
        parent.style.display = 'flex';
        let sp = document.createElement('span');
        sp.innerText = file.fileSize;
        sp.style.color = '#999';
        sp.style.marginTop = '16px';
        parent.insertBefore(sp, suc[suc.length - 1].nextSibling);
      }
    },
    sizeHandle(fileSize) {
      let units = ['B', 'KB', 'MB', 'GB', 'TB'];
      let unitIndex = 0;
      while (fileSize >= 1024 && unitIndex < units.length - 1) {
        fileSize = fileSize / 1024;
        unitIndex++;
      }
      let result = `${fileSize.toFixed(2)} ${units[unitIndex]}`;
      return result;
    },
    getBase64(img, callback) {
      const reader = new FileReader();
      reader.addEventListener('load', () => callback(reader.result));
      reader.readAsDataURL(img);
    },
    customRequestPic(data) {
      // 上传提交
      if (!this.isLt1M) {
        return false;
      }
      const formData = new FormData();
      formData.append('file', data.file);
      this.uploadfilePic(formData, data);
    },
    // 文件上传
    async uploadfilePic(formData, data) {
      const [res] = await fileUpload({ file: data.file });

      console.log(res, 'obj----');
      if (res.code === '10000') {
        const responseInfo = {
          // filePath: this.baseImgUrl + res.data.fileName,
          fileName: res.data.fileName,
        };
        data.onSuccess(responseInfo, data.file);
        this.$emit('setAnnexList', this.fileList, this.showDefaultUploadList);
        this.$message.success('文件保存成功');
      } else {
        this.$emit('setAnnexList', null);
        this.$message.error('文件保存失败');
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-upload-list-item-name {
  color: #999;
}
</style>
