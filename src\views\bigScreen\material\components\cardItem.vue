<template>
  <div class="card" :style="{ width: width }">
    <div class="title">
      {{ title }}
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
  },
};
</script>

<style scoped lang="less">
.card {
  overflow: hidden;
  padding: 5px;
  box-sizing: border-box;
}
.title {
  background: url('@/assets/images/materialScreen/titleBg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 40px;
  font-family: <PERSON><PERSON><PERSON>;
  font-size: 16px;
  font-weight: bold;
  line-height: 32px;
  letter-spacing: 0.03em;
  color: #ffffff;
  padding-left: 50px;
  font-family: <PERSON><PERSON><PERSON>;
  width: 500px;
}
.content {
  padding: 5px;
}
</style>
