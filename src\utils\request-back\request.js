import axios from 'axios';
import requestWrapper from './requestWrapper';
import { getToken } from '@/utils/common/auth';
import { getBaseUrl } from '@/utils/common/util.js';
const BASE_URL = process.env.VUE_APP_USE_BUILD_TYPE
  ? getBaseUrl()
  : process.env.VUE_APP_BASE_API;
// 创建axios实例
const request = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: BASE_URL,
  // 超时
  timeout: 30000,
});
request.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
request.defaults.headers['C-RANDOM'] = 'operation';

// request拦截器
request.interceptors.request.use(
  (config) => {
    if (getToken()) {
      config.headers.Authorization = getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

export default requestWrapper.bind(this, request);
