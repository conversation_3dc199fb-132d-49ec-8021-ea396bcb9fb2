<template>
  <a-upload-dragger
    :class="['uploader']"
    :multiple="true"
    :fileList="uploadFileList"
    :action="actionBaseUrl + action"
    :headers="headers"
    :data="data"
    :before-upload="beforeFileUpload"
    @change="handleFileChange"
  >
    <template>
      <div class="upload-btn">
        <div class="ant-upload-text">+添加附件</div>
      </div>
    </template>
  </a-upload-dragger>
</template>
<script>
import { getToken } from '@/utils/common/auth';
import { imageLimitSize } from './editorImage';
import { getBaseUrl } from '@/utils/common/util.js';

export default {
  props: {
    // 上传业务接口baseurl
    actionBaseUrl: {
      type: String,
      default: process.env.VUE_APP_USE_BUILD_TYPE
        ? getBaseUrl()
        : process.env.VUE_APP_BASE_API,
    },
    visible: {
      type: Boolean,
    },
    // 上传业务接口url
    action: {
      type: String,
      default: `/api/authority/admin/upload/single`,
    },
    // 上传业务接口所需的额外入参
    data: {
      type: Object,
    },
    actionResponseHandle: {
      type: Function,
      default: (response) => {
        return {
          url: response.data || '',
        };
      },
    },
    limitSize: {
      type: Number,
      default: imageLimitSize,
    },
    // 页面上传前校验方法，不使用默认校验，可传此方法替换原校验规则
    beforeUpload: {
      type: Function,
    },
    fileList: [String, Array],
  },
  data() {
    return {
      // 上传鉴权字段
      headers: {
        Authorization: getToken(),
      },
      loading: false,
      uploadFileList: [],
    };
  },
  watch: {
    visible: {
      deep: true,
      handler() {
        this.uploadFileList = [];
      },
    },
  },
  methods: {
    handleFileChange(info) {
      const { file } = info;
      if (file.status === 'uploading') {
        // 上传中
        this.loading = true;
        return;
      }
      if (file.status === 'done') {
        // 上传完成，解析接口返回数据，更新fileList值
        this.loading = false;
        const {
          data: { code = '' },
        } = file.response;
        if (code.basicCode === '60000') {
          this.uploadFileList = [];
          this.$message.warn('上传失败,稍后再试');
          return;
        }
        if (file.response) {
          const { url } = this.actionResponseHandle(file.response);
          if (url === '') {
            this.uploadFileList = [];
            return this.$message.warn('上传失败,稍后再试');
          }
          this.success();
          this.$message.success('上传成功');
        }
      } else if (file.status === 'error') {
        this.loading = false;
        this.uploadFileList = [];
        this.$message.error('上传失败！请重新上传');
      }
    },
    beforeFileUpload(file) {
      this.uploadFileList.push(file);
      return new Promise((resolve, reject) => {
        const fileZiped = file;
        let flag = false; // 是否可以上传标识
        if (this.beforeUpload && typeof this.beforeUpload === 'function') {
          // 自定义文件上传前的校验
          flag = this.beforeUpload(fileZiped);
        } else {
          const isLt2M = fileZiped.size / 1024 / 1024 < this.limitSize;
          if (!isLt2M) {
            this.uploadFileList = [];
            this.$message.error(`上传资源大小不能超过 ${this.limitSize}MB!`);
          }
          flag = isLt2M;
        }
        if (flag) {
          // 校验成功，上传文件
          resolve(fileZiped);
        } else {
          // 校验失败，取消上传
          reject();
        }
      });
    },
    success() {
      this.$emit('success', this.uploadFileList);
    },
    handleError() {
      this.$emit('error');
    },
  },
};
</script>

<style lang="less" scoped>
.uploader {
  overflow: hidden;
  width: 100%;
  &.picture-card {
    text-align: center;
  }
  /deep/.ant-upload.ant-upload-drag {
    background-color: #fff;
    border: 1px solid #d9dddf;
    border-top: none;
    border-radius: 0;
  }
  &.limit-one {
    /deep/ .ant-upload-list-picture-card .ant-upload-list-item,
    /deep/ .ant-upload-list-picture-card-container {
      margin: 0;
    }
  }
  /deep/.ant-upload-list-item-card-actions {
    display: none;
  }
  .upload-btn {
    cursor: pointer;
    color: #2a5aeb;
  }

  .uploader-icon {
    font-size: 28px;
    color: #8c939d;
  }
  .image {
    width: 100%;
    height: 148px;
    display: block;
  }
}
</style>
