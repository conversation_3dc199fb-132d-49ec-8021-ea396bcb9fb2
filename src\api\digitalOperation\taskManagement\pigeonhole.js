import { request } from '@/utils/request/requestTkb';
/**
 * 档案目录
 */
export function archiveList(data) {
  return request({
    url: '/task/archive/page/list',
    method: 'post',
    data,
  });
}

/**
 * 归档数据列表
 */

export function catalogue() {
  return request({
    url: '/task/archive/catalogue',
    method: 'get',
  });
}

/**
 * 撤档
 */
export function revocation(id) {
  return request({
    url: `/task/archive/revocation/${id}`,
    method: 'post',
  });
}
