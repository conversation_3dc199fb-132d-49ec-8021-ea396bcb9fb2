<template>
  <a-row class="push-box-container">
    <a-col :span="8">
      <div>
        <a-input-search style="margin-bottom: 8px" @search="onSearch" />
        <a-tree
          :expanded-keys="expandedKeys"
          :auto-expand-parent="autoExpandParent"
          :tree-data="treeData"
          @expand="onExpand"
          @select="onSelect"
        >
        </a-tree>
      </div>
    </a-col>
    <a-col :span="16">
      <a-transfer
        :data-source="transferData"
        :target-keys="targetKeys"
        :selected-keys="selectedKeys"
        :disabled="disabled"
        :show-search="true"
        :filter-option="
          (inputValue, item) => item.title.indexOf(inputValue) !== -1
        "
        :show-select-all="false"
        @change="onChange"
        @selectChange="handleSelectChange"
      >
        <template
          slot="children"
          slot-scope="{
            props: {
              direction,
              filteredItems,
              selectedKeys,
              disabled: listDisabled,
            },
            on: { itemSelectAll, itemSelect },
          }"
        >
          <a-table
            :row-selection="
              getRowSelection({
                disabled: listDisabled,
                selectedKeys,
                itemSelectAll,
                itemSelect,
              })
            "
            :columns="direction === 'left' ? leftColumns : rightColumns"
            :data-source="filteredItems"
            size="small"
            :style="{ pointerEvents: listDisabled ? 'none' : null }"
            :custom-row="
              ({ key, disabled: itemDisabled }) => ({
                on: {
                  click: () => {
                    if (itemDisabled || listDisabled) return;
                    itemSelect(key, !selectedKeys.includes(key));
                  },
                },
              })
            "
          />
        </template>
      </a-transfer>
      <div class="push-btn">
        <a-button type="primary" @click="onOk">确认</a-button>
      </div>
      <a-table
        :row-selection="rowSelection"
        :dataSource="dataSource"
        :columns="selectedTableColumns"
      />
    </a-col>
  </a-row>
</template>
<script>
import difference from 'lodash/difference';
import { organizeAll } from '@/api/digitalOperation/taskManagement/taskList.js';
const leftTableColumns = [
  {
    dataIndex: 'title',
    title: '姓名',
  },
  {
    dataIndex: 'roleName',
    title: '岗位',
  },
];
const rightTableColumns = [
  {
    dataIndex: 'title',
    title: '姓名',
  },
];
const selectedTableColumns = [
  {
    dataIndex: 'title',
    title: '已选择推送人员姓名',
  },
  {
    dataIndex: 'roleName',
    title: '岗位',
  },
];

export default {
  props: {
    rowInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      value: '',
      treeData: [],
      transferData: [],
      organizeId: '',
      organizeName: '',
      targetKeys: [],
      disabled: false,
      showSearch: false,
      leftColumns: leftTableColumns,
      rightColumns: rightTableColumns,
      selectedTableColumns,
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      searchValue: '',
      expandedKeys: [],
      autoExpandParent: true,
      checkedKeys: [],
      selectedKeys: [],
      rightSelectedKeys: [],
      dataSource: [],
      paramsSelectedRowKeys: [],
    };
  },
  components: {},
  mounted() {
    this.organizeAll();
  },
  computed: {
    rowSelection() {
      return {
        onChange: (selectedRowKeys, selectedRows) => {
          console.log(
            `selectedRowKeys: ${selectedRowKeys}`,
            'selectedRows: ',
            selectedRows
          );
          this.paramsSelectedRowKeys = selectedRowKeys;
          console.log(this.paramsSelectedRowKeys, 'this.paramsSelectedRowKeys');
        },
        getCheckboxProps: (record) => ({
          props: {
            disabled: record.name === 'Disabled User', // Column configuration not to be checked
            name: record.name,
          },
        }),
      };
    },
  },
  methods: {
    //确定选择
    onOk() {
      let arr = [];
      this.transferData.forEach((item) => {
        this.rightSelectedKeys.forEach((item1) => {
          if (item1 == item.key) {
            arr.push(item);
          }
        });
      });
      //去重
      this.dataSource = [...new Set([...this.dataSource, ...arr])];
      this.dataSource = [
        ...new Set(this.dataSource.map((item) => JSON.stringify(item))),
      ].map((item) => JSON.parse(item));
    },
    onChange(nextTargetKeys) {
      this.targetKeys = nextTargetKeys;
    },
    handleSelectChange(sourceSelectedKeys, targetSelectedKeys) {
      this.selectedKeys = [...sourceSelectedKeys, ...targetSelectedKeys];
      this.rightSelectedKeys = targetSelectedKeys;
      console.log('rightSelectedKeys', this.rightSelectedKeys);
      console.log('sourceSelectedKeys: ', sourceSelectedKeys);
      console.log('targetSelectedKeys: ', targetSelectedKeys);
    },
    getRowSelection({ disabled, selectedKeys, itemSelectAll, itemSelect }) {
      return {
        getCheckboxProps: (item) => ({
          props: { disabled: disabled || item.disabled },
        }),
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows
            .filter((item) => !item.disabled)
            .map(({ key }) => key);
          const diffKeys = selected
            ? difference(treeSelectedKeys, selectedKeys)
            : difference(selectedKeys, treeSelectedKeys);
          itemSelectAll(diffKeys, selected);
        },
        onSelect({ key }, selected) {
          itemSelect(key, selected);
          console.log(key, selected, 99999);
        },
        selectedRowKeys: selectedKeys,
      };
    },

    onExpand(expandedKeys) {
      console.log('onExpand', expandedKeys);
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    onCheck(checkedKeys) {
      console.log('onCheck', checkedKeys);
      this.checkedKeys = checkedKeys;
    },
    onSelect(selectedKeys, info) {
      console.log('onSelect', selectedKeys, info);
      this.selectedKeys = selectedKeys;
      const currentBiz = this.treeData.filter((item) => {
        return item.key == selectedKeys;
      });
      if (currentBiz.length) {
        const { userInfoList } = currentBiz[0];
        this.transferData = JSON.parse(
          JSON.stringify(userInfoList)
            .replace(/nickName/g, 'title')
            .replace(/userId/g, 'key')
        );
      }
    },
    onSearch(val) {
      console.log(val, 'val');
      this.organizeAll(val);
    },
    //园区查询
    async organizeAll(organizeName) {
      const [res, err] = await organizeAll({
        // id: 'ZZ1724674633342541824',
        organizeName: organizeName || '',
      }); //查园区id
      if (err) return;
      let data = res?.data;
      data = JSON.parse(
        JSON.stringify(data)
          .replace(/organizeName/g, 'title')
          .replace(/organizeId/g, 'key')
      );
      this.treeData = data;
      // this.treeData.map((item) => {
      //   return item.userInfoList.length ? (item.isLeaf = false) : '';
      // });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-modal {
  width: 1200px !important;
}
.push-btn {
  margin: 12px 0;

  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
