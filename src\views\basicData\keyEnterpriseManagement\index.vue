<template>
  <page-layout>
    <PageWrapper
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      :tableOn="{
        'checkbox-change': selectChangeEvent,
        'checkbox-all': selectChangeEvent,
      }"
    >
      <!-- 创建按钮区域插槽 -->
      <template #defaultHeader>
        <a-button
          type="primary"
          style="margin-right: 8px"
          @click="handleCreate"
        >
          新增
        </a-button>
        <a-button style="margin-right: 8px" @click="onClickImport">
          导入
        </a-button>
        <ExportButton :checkItems="checkItems" :pageName="pageName" />
        <a-button
          type="danger"
          style="margin-right: 8px"
          :disabled="!checkItems.length"
          @click="handelDelete"
        >
          删除
        </a-button>
      </template>
      <!-- 企业 -->
      <template #nameSlot>
        <a-select
          :allowClear="true"
          :filterOption="filterOption"
          :showSearch="true"
          placeholder="请选择"
        >
          <a-select-option
            v-for="item in stateSelect"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </template>
      <!-- 园区 -->
      <template #enterSlot="{ item }">
        <a-select
          :allowClear="true"
          :filterOption="filterOption"
          :showSearch="true"
          placeholder="请选择"
          v-model="filterOptions.params[item.field]"
        >
          <a-select-option
            v-for="item in affiliatedPark"
            :key="item.id"
            :value="item.id"
          >
            {{ item.parkName }}
          </a-select-option>
        </a-select>
      </template>
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
      </template>
      <!-- 编辑弹窗 -->
      <ListModal
        :visible="visible"
        :detail="modalData"
        :modelTitle="modelTitle"
        :affiliatedPark="affiliatedPark"
        @loadData="loadData"
        @handleCancel="handleCancel"
      />
    </PageWrapper>
  </page-layout>
</template>

<script>
// import moment from 'moment';
import { institutionsMixin } from '../mixins/institutionsMixin';
import { defaultTableColumn, defaultFilterConfig } from './constant';
import ListModal from './components/ListModal.vue';
import ExportButton from '@/views/basicData/components/ExportButton.vue';
import { pageKeyEmphasis, parkList, deleteEmphasis } from '@/api/basicData';
export default {
  components: { ListModal, ExportButton },
  dicts: ['my_notify_rule'],
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'keyEenterManage',
      loading: false,
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
        params: {
          enterprise: '',
          park: '',
        }, // 筛选器结果数据
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: defaultTableColumn(),
      tableData: [],
      visible: false,
      modalData: null,
      checkItems: [],
      modelTitle: 'add',
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
      affiliatedPark: [],
    };
  },
  created() {
    this.loadData();
    this.parkList();
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      // this.filterOptions.config[1].props.options = this.stateSelect;
      // this.filterOptions.config[0].props.options = this.stateSelect;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [res, err] = await pageKeyEmphasis({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...params,
      });
      this.loading = false;

      if (err) return;
      // 设置数据
      this.tablePage.total = res.data.total;
      this.tableData = res.data.records;
    },
    // 创建按钮点击事件
    handleCreate() {
      this.modelTitle = 'add';
      this.visible = true;
    },
    // 编辑按钮点击事件
    onClickEdit(row) {
      this.modelTitle = 'edit';
      this.visible = true;
      this.modalData = row;
    },
    // 关闭弹窗
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
      this.modalData = null;
      this.manageVisible = false;
    },
    // 管理按钮点击事件
    onClickManage() {
      this.manageVisible = true;
    },
    // 查看详情
    onClickDetail() {
      // this.$router.push('./second/detail');
    },
    // 删除
    handelDelete() {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [, err] = await deleteEmphasis({
            list: that.checkItems,
          });
          if (!err) {
            that.$message.success('发布成功!');
            // 刷新数据
            that.loadData();
            that.checkItems = [];
            return;
          }
        },
      });
    },
    // 园区类别
    stateChange(value) {
      console.log('选中值', value);
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: this.pageName,
        },
      });
    },
    // 所属园区
    async parkList() {
      const [res, err] = await parkList({});
      if (err) return;
      this.affiliatedPark = res.data;
    },
  },
};
</script>
<style scoped>
/deep/.page-wrapper-container {
  margin: 0 !important;
}
</style>
