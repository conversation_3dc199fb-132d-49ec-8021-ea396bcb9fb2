<template>
  <a-layout-sider
    collapsible
    theme="light"
    class="side-menu"
    v-model="collapsed"
    :collapsedWidth="collapsed ? 60 : 208"
    :trigger="null"
    width="240px"
  >
    <i-menu
      :class="['menu', 'beauty-scroll', collapsed ? 'menu-collaped' : '']"
      theme="light"
      :collapsed="collapsed"
      :options="menuData"
      @select="onSelect"
    />
    <div class="menu-bottom" @click="toggleCollapse">
      <a-icon class="icon" :type="collapsed ? 'menu-unfold' : 'menu-fold'" />
    </div>
  </a-layout-sider>
</template>

<script>
import { mapGetters } from 'vuex';
import IMenu from './menu';
export default {
  name: 'SideMenu',
  components: {
    IMenu,
  },
  props: {
    collapsed: {
      type: Boolean,
      required: false,
      default: false,
    },
    menuData: {
      type: Array,
      required: true,
    },
    layout: {
      type: String,
      required: false,
      default: 'head',
    },
  },
  data() {
    return {
      loading: true,
    };
  },
  computed: {
    ...mapGetters('setting', ['flatMenuData']),
  },
  methods: {
    onSelect(obj) {
      const arr = obj.key.split('/');
      const menuObj = this.flatMenuData.find(
        (q) => q.path == arr[arr.length - 1]
      );
      try {
        if (menuObj && menuObj.extra) {
          if (JSON.parse(menuObj && menuObj.extra).fullScreen) {
            const url = window.location.href.split('#')[0] + '#' + obj.key;
            window.open(url);
            return;
          }
        } else {
          this.$emit('menuSelect', obj);
        }
      } catch (e) {
        console.log(e, 'e');
      }
    },
    toggleCollapse() {
      this.$emit('toggleCollapse');
    },
  },
};
</script>

<style lang="less" scoped>
@import 'index';
</style>
