import request from '@/utils/request';

//  新建流程配置
export function editWorkflow(query) {
  if (query.configId) {
    return updateWorkflow(query);
  } else {
    return addWorkflow(query);
  }
}
//  新建流程配置
export function addWorkflow(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/workflow/config/add',
    method: 'POST',
    data: query,
  });
}
// 更新流程配置
export function updateWorkflow(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/workflow/config/update',
    method: 'POST',
    data: query,
  });
}
//删除流程配置
export function deleteWorkflow(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/workflow/config/delete',
    method: 'GET',
    params: query,
  });
}
//4. 分页查询流程配置
export function getWorkflowList(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/workflow/config/page',
    method: 'GET',
    params: query,
  });
}
// 5. 流程配置详情查询
export function getWorkflowDetail(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/workflow/config/detail',
    method: 'GET',
    params: query,
  });
}
// 6. 部署流程
export function workflowDeploy(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/workflow/deploy',
    method: 'GET',
    params: query,
  });
}
// 6. 修改流程配置状态
export function changeWorkflowStatus(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/workflow/config/changeStatus',
    method: 'GET',
    params: query,
  });
}
// 7. 开启一个流程
export function workflowStart(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/workflow/start',
    method: 'POST',
    data: query,
  });
}
//8. 我的流程
export function workProcess(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/workflow/process',
    method: 'POST',
    data: query,
  });
}
//9. 流程节点信息
export function workflowNode(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/workflow/node',
    method: 'GET',
    params: query,
  });
}
//10. 我的任务
export function workflowTask(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/workflow/task',
    method: 'POST',
    data: query,
  });
}
//11. 审批
export function workflowApprove(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/workflow/approve',
    method: 'POST',
    data: query,
  });
}
//12. 表单列表
export function getFormPage(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/form/page',
    method: 'GET',
    params: query,
  });
}
export function saveForm(query) {
  if (query.formId) {
    return updateFormPage(query);
  } else {
    return addFormPage(query);
  }
}
//13. 表单列表新增
export function addFormPage(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/form/add',
    method: 'POST',
    data: query,
  });
}
//14. 表单列表详情
export function getFormPageDetail(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/form/detail',
    method: 'GET',
    params: query,
  });
}
//15. 表单列表删除
export function deleteFormPage(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/form/delete',
    method: 'GET',
    params: query,
  });
}
//16. 表单列表更新
export function updateFormPage(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/form/update',
    method: 'POST',
    data: query,
  });
}
//17. 复制流程配置
export function copyWorkflow(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/workflow/config/copy',
    method: 'GET',
    params: query,
  });
}
// 18. 流程配置下拉列表
export function getWorkflowConfigSelect(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/workflow/config/select',
    method: 'GET',
    params: query,
  });
}
// 19.撤回审批
export function recallWorkflow(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/workflow/recall',
    method: 'GET',
    params: query,
  });
}
// 20.关闭审批
export function closeWorkflow(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/workflow/close',
    method: 'GET',
    params: query,
  });
}
// 21.重新审批
export function restartWorkflow(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/workflow/restart',
    method: 'GET',
    params: query,
  });
}
