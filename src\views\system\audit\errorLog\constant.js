import moment from 'moment';

export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'userId',
      title: '用户',
      props: {
        placeholder: '请输入用户',
      },
    },
    {
      field: 'ipAddress',
      title: 'IP地址',
      props: {
        placeholder: '请输入IP地址',
      },
    },
    {
      field: 'time',
      title: '登录时间',
      element: 'a-date-picker',
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { userId: '', ipAddress: '', time: '' },
};

// 表头
export const tableColumn = [
  { field: 'processName', title: '用户名' },
  { field: 'processKey', title: '所属组织' },
  { field: 'processKey', title: '浏览器' },
  { field: 'processKey', title: '操作系统' },
  { field: 'processKey', title: 'IP地址' },
  { field: 'processKey', title: '异常说明' },
  {
    field: 'updateTime',
    title: '操作时间',
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '详情',
    slots: { default: 'operate' },
  },
];
