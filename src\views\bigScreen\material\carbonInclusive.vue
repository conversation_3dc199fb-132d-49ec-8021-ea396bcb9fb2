<template>
  <cardItem title="碳普惠" width="50%">
    <div class="card-wrap">
      <div class="card-item l-card">
        <div class="text">累计发放碳中和电子证书</div>
        <div class="number">
          <span class="number-value">{{ info.certificate || '-' }}</span>
          张
        </div>
      </div>
      <div class="card-item l-card">
        <div class="text">累计中和访客碳排放量</div>
        <div class="number">
          <span class="number-value">{{ info.Visitor || '-' }}</span>
          tco2
        </div>
      </div>
      <div class="card-item r-card">
        <div class="qr-code">
          <img src="@/assets/images/materialScreen/qr-code.png" alt="" />
        </div>
      </div>
    </div>
  </cardItem>
</template>

<script>
import cardItem from './components/cardItem.vue';
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: { cardItem },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="less">
.card-wrap {
  display: flex;
  flex-direction: row;
  height: 280px;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 10px;
  .card-item {
    height: 120px;
  }
  .r-card {
    width: 90%;
    height: 189px;
    background: linear-gradient(
      132deg,
      rgba(67, 165, 255, 0.3) 0%,
      rgba(67, 165, 255, 0) 84%
    );
    box-sizing: border-box;
    border: 3px solid;
    border-image: linear-gradient(
        28deg,
        #75e584 -7%,
        rgba(117, 229, 132, 0) 12%
      )
      3;
    display: grid;
    place-content: center;
    border: 10px;
    .qr-code {
      padding: 10px;
      border-radius: 5px;
      overflow: hidden;
      background: linear-gradient(
        0deg,
        rgba(32, 49, 83, 0),
        rgba(32, 49, 83, 0)
      );
      box-shadow: inset -2px 0px 0px 0px #69b7ff, inset 2px 0px 0px 0px #00ffaa;
      img {
        width: 140px;
        height: 140px;
      }
    }
  }
  .l-card {
    width: 120px;
    height: 80px;
    box-sizing: border-box;
    background-image: url('@/assets/images/materialScreen/datav-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .text {
      font-family: AlibabaPuHuiTi;
      font-size: 10px;
      font-weight: normal;
      line-height: 18px;
      /* 二级文字-80% */
      color: rgba(224, 240, 255, 0.8);
    }
    .number {
      font-family: Alibaba Sans;
      font-weight: bold;
      line-height: 32px;
      /* 主色-绿 */
      color: #00ffaa;
      width: 100%;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      font-size: 10px;
      .number-value {
        font-size: 16px;
      }
    }
  }
}
</style>
