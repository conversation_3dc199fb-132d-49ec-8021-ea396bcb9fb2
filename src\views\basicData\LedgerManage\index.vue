<template>
  <page-layout>
    <table-component
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :parentData="params"
      ref="tableComponent"
      :pageName="pageName"
      @getEnterprise="getEnterprise"
      :notExportAll="true"
    >
      <!-- 企业 -->
      <template slot="enterpriseName" slot-scope="{ params }">
        <FuzzySelect
          v-model="params.enterpriseName"
          @changeSelect="handlerChange"
          :disabled="modelTitle == 'see'"
        ></FuzzySelect>
      </template>
    </table-component>
  </page-layout>
</template>

<script>
import { institutionsMixin } from '../mixins/institutionsMixin';
import tableComponent from '@/views/basicData/components/tableComponent';
import moment from 'moment';
import FuzzySelect from '@/components/FuzzySelect/index.vue';

export default {
  name: 'ledgerManage',
  components: { tableComponent },
  mixins: [institutionsMixin],
  dicts: ['determine_status'],
  data() {
    return {
      pageName: 'ledgerManage',
      params: {
        name: undefined,
        unifiedCreditCode: undefined,
        year: { startValue: undefined, endValue: undefined },
        parkId: undefined,
        status: undefined,
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        //筛选控件配置
        config: [
          {
            field: 'year',
            title: '认定年份',
            element: 'slot',
            slotName: 'year',
          },
          {
            field: 'name',
            title: '企业名称',
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            itemProps: {
              labelCol: { span: 12 },
              wrapperCol: { span: 12 },
            },
          },
          {
            field: 'parkId',
            title: '所属园区',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.parkList,
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
          {
            field: 'status',
            title: '认定状态',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.dict?.type?.determine_status || [],
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        submitBtn: true,
        okBtn: false,
        addBtn: false,
        viewBtn: false,
        editBtn: true,
        delBtn: false,
        menu: true,
        menuWidth: 100,
        menuFixed: 'right',
        formConfig: [
          {
            field: 'enterpriseName',
            element: (h, item, params) => {
              return (
                <FuzzySelect
                  value={params?.enterpriseName}
                  onChangeSelect={(val) => {
                    if (val) {
                      this.$refs.tableComponent.setForm({
                        enterpriseName: val.name,
                        unifiedCreditCode: val.unifiedCreditCode,
                        address: val.address,
                        parkName: val.parkName,
                        parkId: val.parkId,
                        registerDate: val.registerDate,
                      });
                    } else {
                      this.$refs.tableComponent.setForm({
                        enterpriseName: '',
                        unifiedCreditCode: '',
                        address: '',
                        parkName: '',
                        parkId: '',
                        registerDate: '',
                      });
                    }
                  }}
                  disabled={this.modelTitle == 'see'}
                />
              );
            },
            title: '企业名称',
            rules: [{ required: true, message: '请选择企业' }],
          },
          {
            field: 'unifiedCreditCode',
            title: '统一社会信用代码',
            props: {
              placeholder: '请输入统一社会信用代码',
              disabled: true,
            },
          },
          {
            field: 'address',
            title: '企业地址',
            props: {
              placeholder: '请输入企业地址',
              disabled: true,
              maxLength: 50,
            },
          },
          {
            field: 'parkName',
            title: '所属园区',
            props: {
              placeholder: '请输入所属园区',
              disabled: true,
            },
          },
          {
            field: 'year',
            title: '认定年份',
            element: (h, item, params) => {
              return (
                <BuseRangePicker
                  type="year"
                  needShowSecondPicker={() => false}
                  value={params.year}
                  onChange={(val) => {
                    params.year = val;
                  }}
                  format="YYYY"
                  placeholder="选择认定年份"
                  disableDateFunc={(val) => val.isAfter(moment())}
                />
              );
            },
            rules: [
              { required: true, message: '请选择认定年份' },
              {
                validator: (rule, value, callback) => {
                  console.log(value, 'value');
                  if (value && value.startValue) {
                    callback();
                  } else {
                    callback('请选择认定年份');
                  }
                },
                trigger: 'blur',
              },
            ],
          },
          {
            field: 'status',
            title: '认定状态',
            element: 'a-select',
            props: {
              options: this.dict?.type?.determine_status || [],
              showSearch: true,
              optionFilterProp: 'children',
            },
            rules: [{ required: true, message: '请选择认定状态' }],
            previewFormatter: (value) => {
              return this.dict?.type?.determine_status?.find(
                (q) => q.value == value
              )?.label;
            },
          },
          {
            field: 'remark',
            title: '备注',
            props: {
              placeholder: '请输入备注',
              type: 'textarea',
              maxLength: 30,
            },
          },
        ],
      };
    },
    tableColumn() {
      return [
        {
          field: '',
          title: '',
          type: 'checkbox',
          fixed: 'left',
          width: 70,
        },
        {
          field: '',
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: 70,
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          width: 200,
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          width: 200,
        },
        {
          field: 'address',
          title: '企业地址',
          minWidth: 120,
        },
        {
          field: 'parkName',
          title: '所属园区',
          minWidth: 120,
        },
        {
          field: 'year',
          title: '认定年份',
          width: 200,
          formatter: ({ cellValue }) => {
            return cellValue ? moment(cellValue).format('YYYY') : '';
          },
        },
        {
          field: 'status',
          title: '认定状态',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(
              cellValue,
              this.dict?.type?.determine_status
            );
          },
        },
        {
          field: 'remark',
          title: '备注',
          minWidth: 120,
        },
        {
          field: 'updateBy',
          title: '更新人',
          width: 200,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          width: 200,
          formatter: ({ cellValue }) => {
            return cellValue
              ? moment(cellValue).format('yyyy-MM-DD HH:mm:ss')
              : '';
          },
        },
      ];
    },
  },
  created() {},
  methods: {},
};
</script>

<style lang="scss" scoped></style>
