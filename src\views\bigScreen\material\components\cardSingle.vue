<template>
  <div class="card-item">
    <div class="l-icon">
      <img :src="item.icon" alt="" />
    </div>
    <div class="r-text">
      <div class="label ellipsis">{{ item.label }}</div>
      <div class="num">
        <div class="num-value ellipsis">{{ formatNumber(item.num) }}</div>
        <div class="num-unit" v-if="item.unit">{{ item.unit }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatNumber } from '@/utils';
export default {
  props: {
    item: {
      type: Object,
      default: () => {
        return {
          label: '',
          num: '',
          unit: '',
          icon: '',
        };
      },
    },
  },
  methods: {
    formatNumber,
  },
};
</script>

<style scoped lang="less">
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.card-item {
  width: 146px;
  height: 80px;
  display: flex;
  align-items: center;
  color: #fff;
  overflow: hidden;
  box-sizing: border-box;
  .l-icon {
    width: 60px;
    img {
      width: 100%;
      object-fit: contain;
    }
  }
  .r-text {
    width: calc(100% - 50px);
    box-sizing: border-box;
    padding-left: 4px;
    .label {
      /* 正文 */
      font-family: AlibabaPuHuiTi;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      /* 二级文字-80% */
      color: rgba(224, 240, 255, 0.8);
    }
    .num {
      color: #00ffaa;
      display: flex;
      align-items: baseline;
      justify-content: flex-start;
      .num-value {
        max-width: calc(100% - 10px);
        font-family: Alibaba Sans;
        font-size: 14px;
        font-weight: bold;
        text-align: left;
      }
      .num-unit {
        width: 10px;
        margin-left: 3px;
        font-family: AlibabaPuHuiTi;
        font-size: 12px;
        /* 主色-绿 */
        color: #00ffaa;
      }
    }
  }
}
</style>
