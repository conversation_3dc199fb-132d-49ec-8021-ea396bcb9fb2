<template>
  <a-modal
    width="1000px"
    title=" 创建Bucket"
    :visible="visible"
    cancelText="取消"
    @ok="handleCreate"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <a-form-model
        ref="myRef"
        :model="ruleForm"
        :rules="rules"
        v-bind="layout"
      >
        <a-form-model-item has-feedback label="Bucket名称" prop="bucketName">
          <a-input
            v-model="ruleForm.bucketName"
            placeholder="请输入Bucket名称"
            autocomplete="off"
          />
        </a-form-model-item>
        <a-form-model-item has-feedback label="storageType" prop="serverType">
          <a-select
            v-model="ruleForm.serverType"
            style="width: 555px"
            @change="handleChange"
          >
            <a-select-option value="oss"> oss </a-select-option>
            <a-select-option value="minio"> minio </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item has-feedback label="endpoint" prop="endpoint">
          <a-input
            v-model="ruleForm.endpoint"
            placeholder="请输入endpoint"
            autocomplete="off"
          />
        </a-form-model-item>
        <a-form-model-item has-feedback label="accessKeyId" prop="accessId">
          <a-input
            v-model="ruleForm.accessId"
            placeholder="请输入accessKeyId"
            autocomplete="off"
          />
        </a-form-model-item>
        <a-form-model-item
          has-feedback
          label="accessKeySecret"
          prop="accessKey"
        >
          <a-input
            v-model="ruleForm.accessKey"
            placeholder="请输入accessKeySecret"
            autocomplete="off"
          />
        </a-form-model-item>
        <a-form-model-item
          has-feedback
          label="publicEndpoint"
          prop="publicEndpoint"
        >
          <a-input
            v-model="ruleForm.publicEndpoint"
            placeholder="请输入publicEndpoint"
            autocomplete="off"
          />
        </a-form-model-item>

        <a-form-model-item
          has-feedback
          label="是否使用代理"
          prop="proxyUsername"
        >
          <a-switch :checked="ruleForm.isUsedProxy" @change="onChange" />
        </a-form-model-item>
        <div v-if="ruleForm.isUsedProxy">
          <a-form-model-item
            has-feedback
            label="代理服务器验证的用户"
            prop="proxyUsername"
          >
            <a-input
              v-model="ruleForm.proxyUsername"
              placeholder="请输入代理服务器验证的用户名"
              autocomplete="off"
            />
          </a-form-model-item>

          <a-form-model-item
            has-feedback
            label="代理服务器主机地址"
            prop="proxyHost"
          >
            <a-input
              v-model="ruleForm.proxyHost"
              placeholder="请输入代理服务器主机地址"
              autocomplete="off"
            />
          </a-form-model-item>
          <a-form-model-item
            has-feedback
            label="代理服务器验证的密码"
            prop="proxyPassword"
          >
            <a-input
              v-model="ruleForm.proxyPassword"
              placeholder="请输入代理服务器验证的密码"
              autocomplete="off"
            />
          </a-form-model-item>

          <a-form-model-item
            has-feedback
            label="代理服务器端口"
            prop="proxyPort"
          >
            <a-input
              v-model="ruleForm.proxyPort"
              placeholder="请输入代理服务器端口"
              autocomplete="off"
            />
          </a-form-model-item>

          <a-form-model-item
            has-feedback
            label="代理服务器工作站"
            prop="proxyWorkstation"
          >
            <a-input
              v-model="ruleForm.proxyWorkstation"
              placeholder="请输入代理服务器工作站"
              autocomplete="off"
            />
          </a-form-model-item>
          <a-form-model-item
            has-feedback
            label="代理服务器域"
            prop="proxyDomain"
          >
            <a-input
              v-model="ruleForm.proxyDomain"
              placeholder="请输入代理服务器域"
              autocomplete="off"
            />
          </a-form-model-item>
        </div>
        <a-form-model-item has-feedback label="备注" prop="remark">
          <a-textarea
            v-model="ruleForm.remark"
            placeholder="请输入备注"
            :auto-size="{ minRows: 3, maxRows: 5 }"
          />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
    },
    fromObejct: {
      type: Object,
    },
  },
  data() {
    return {
      loading: false,
      ruleForm: {
        bucketName: '',
        proxyUsername: '',
        isUsedProxy: false,
        serverType: 'oss',
        proxyHost: '',
        proxyPassword: '',
        accessId: '',
        proxyPort: '',
        endpoint: '',
        publicEndpoint: '',
        proxyWorkstation: '',
        proxyDomain: '',
        accessKey: '',
        remark: '',
      },
      rules: {
        bucketName: [
          {
            required: true,
            message: '请输入bucket',
            trigger: 'cange',
          },
        ],
        accessId: [
          {
            required: true,
            message: '请输入accessId',
            trigger: 'cange',
          },
        ],
        endpoint: [
          {
            required: true,
            message: '请输入endpoint',
            trigger: 'cange',
          },
        ],
        accessKey: [
          {
            required: true,
            message: '请输入accessKey',
            trigger: 'cange',
          },
        ],
        serverType: [
          {
            required: true,
            message: '请输入serverType',
            trigger: 'cange',
          },
        ],
      },
      layout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
      },
    };
  },
  watch: {
    visible: {
      deep: true,
      handler(val) {
        if (val) {
          this.init();
        } else {
          this.ruleForm.bucketName = '';
          (this.ruleForm.proxyUsername = ''),
            (this.ruleForm.isUsedProxy = false),
            (this.ruleForm.serverType = 'oss'),
            (this.ruleForm.proxyHost = ''),
            (this.ruleForm.proxyPassword = ''),
            (this.ruleForm.accessId = ''),
            (this.ruleForm.proxyPort = ''),
            (this.ruleForm.endpoint = ''),
            (this.ruleForm.proxyWorkstation = ''),
            (this.ruleForm.proxyDomain = ''),
            (this.ruleForm.accessKey = ''),
            (this.ruleForm.remark = '');
          this.$refs.myRef.resetFields();
        }
      },
    },
    fromObejct: {
      deep: true,
      handler(val) {
        this.ruleForm = val;
      },
    },
  },
  methods: {
    async init() {
      //
    },
    /**
     * 新增、编辑
     */
    async handleCreate() {
      this.$refs.myRef.validate(async (valid) => {
        if (valid) {
          this.$emit('hlandSumbit', this.ruleForm);
        }
      });
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    handleChange(value) {
      this.ruleForm.serverType = value;
    },
    onChange(checked) {
      this.ruleForm.isUsedProxy = checked;
      this.ruleForm.proxyUsername = '';
      this.ruleForm.proxyHost = '';
      this.ruleForm.proxyPort = '';
      this.ruleForm.proxyPassword = '';
      this.ruleForm.proxyWorkstation = '';
      this.ruleForm.proxyDomain = '';
    },
  },
};
</script>

<style lang="less" scoped></style>
