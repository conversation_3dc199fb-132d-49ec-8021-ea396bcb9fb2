<template>
  <a-modal
    :title="`${title}`"
    okText="确定"
    cancelText="取消"
    width="800px"
    :maskClosable="false"
    :visible="visible"
    @ok="handleAddOk"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <a-form-model
        ref="form"
        :model="formInfo"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="用户名" prop="userName">
          <a-input
            placeholder="请输入用户名"
            :maxLength="25"
            v-model="formInfo.userName"
          />
        </a-form-model-item>
        <a-form-model-item label="所属租户" prop="merchantId">
          {{ merchantName }}
        </a-form-model-item>
        <a-form-model-item label="所属组织" prop="organizeId">
          <treeselect
            :loading="treeLoading"
            v-model="formInfo.organizeId"
            :options="organizeTreeList"
            placeholder="请选择所属组织"
            :clearable="false"
            :default-expand-level="10"
            @select="(e) => getPositionOptions(e.id, true)"
          />
        </a-form-model-item>
        <a-form-model-item label="选择岗位" prop="positionIds">
          <a-select
            :loading="positionLoading"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model="formInfo.positionIds"
            @change="changePosition"
            mode="multiple"
            placeholder="请选择岗位"
          >
            <a-select-option
              v-for="item in positionList"
              :key="item.positionId"
              :value="item.positionId"
              >{{ item.positionName }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="角色" prop="role">
          <a-tag v-for="item in disabledRoleList" :key="item">{{ item }}</a-tag>
          <a-select
            v-if="roleOptions"
            mode="multiple"
            v-model="formInfo.roleIds"
            placeholder="请选择角色"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            :loading="roleLoading"
            :filter-option="filterOption"
          >
            <a-select-option
              v-for="item in roleOptions"
              :key="item.roleId"
              :value="item.roleId"
              :disabled="
                !!cantDeleteRoleList.find((role) => item.roleId === role)
              "
            >
              {{ item.roleName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="姓名" prop="nickName">
          <a-input
            placeholder="请输入姓名"
            :max-length="50"
            v-model="formInfo.nickName"
          />
        </a-form-model-item>

        <a-form-model-item label="工号" prop="jobNumber">
          <a-input
            placeholder="请输入工号"
            :max-length="20"
            v-model="formInfo.jobNumber"
          />
        </a-form-model-item>
        <a-form-model-item label="手机号码" prop="phonenumber">
          <a-input
            placeholder="请输入手机号码"
            v-model="formInfo.phonenumber"
          />
        </a-form-model-item>
        <a-form-model-item label="邮箱" prop="email">
          <a-input
            placeholder="请输入邮箱"
            :max-length="50"
            v-model="formInfo.email"
          />
        </a-form-model-item>

        <a-form-model-item label="性别" prop="sex">
          <a-radio-group v-model="formInfo.sex" :options="sexOptions" />
        </a-form-model-item>
        <a-form-model-item label="状态" prop="status">
          <a-radio-group
            name="radioGroup"
            v-model="formInfo.status"
            :options="statusOptionsSecond"
          >
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { addUser, updateUser, getUser } from '@/api/system/user';
import { getPoitionList } from '@/api/system/position';
import { aseDecrypt, rsaCode } from '@/utils/common/auth';
import { listRole } from '@/api/system/role';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { initFormData, formRules, statusOptionsSecond } from '../constant';

export default {
  components: { Treeselect },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: String,
      default: '',
    },
    organizeId: {
      type: String,
      default: '',
    },
    organizeTreeList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    merchantName() {
      return this.$store.state?.base?.merchant?.merchantName;
    },
    merchantId() {
      return this.$store.state?.base?.merchant?.merchantId;
    },
    title() {
      return `${
        this.formInfo.userId || (this.loading && this.userId) ? '编辑' : '新增'
      }用户`;
    },
    disabledRoleList() {
      return this.cantDeleteRoleList?.map((item) => {
        return this.roleOptions.find((role) => role.roleId === item)?.roleName;
      });
    },
  },
  data() {
    return {
      statusOptionsSecond,
      sexOptions: [], //性别
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      formInfo: initFormData(), //初始化表单
      rules: formRules, //规则

      roleLoading: false, //角色加载标识
      roleOptions: [], //角色列表
      loading: false, //页面加载标识
      treeLoading: false, //树加载标识
      positionLoading: false, //岗位加载
      positionList: [], //岗位列表
      cantDeleteRoleList: [], //不能删除的角色列表
    };
  },
  watch: {
    async visible(val) {
      if (val) {
        //初始化晴空数据
        this.positionList = [];
        this.cantDeleteRoleList = [];

        if (this.userId) {
          // 获取详情
          await this.getDetail();
        } else {
          // 新增用户--初始化表单
          this.formInfo = initFormData({
            organizeId: this.organizeId || undefined,
            sex: this.sexOptions?.length ? this.sexOptions[0]?.value || '' : '',
          });
          //获取下拉选择列表
          this.getPositionOptions(this.organizeId);
        }
      } else {
        //晴空表单校验
        this.$refs.form && this.$refs.form.resetFields();
      }
    },
  },
  async created() {
    // 获取角色列表
    this.getRoles();
    // 获取系统字典：性别
    const sexOptions = await this.$store.dispatch(
      'dict/getDict',
      'sys_user_sex'
    );
    this.sexOptions = sexOptions;
    this.formInfo.sex = sexOptions?.length ? sexOptions[0]?.value || '' : '';
  },
  methods: {
    /**
     * 查询岗位下拉下拉树结构
     * clearPosition 是否情况岗位
     * organizeId 组织ID
     */
    async getPositionOptions(organizeId, clearPosition = false) {
      this.positionLoading = true;
      const [result, error] = await getPoitionList({
        organizeId: organizeId,
        page: 1,
        limit: 9999,
      });
      if (!error) {
        this.positionList = [];
      }
      // 如果需要清除岗位选择
      if (clearPosition) {
        this.cantDeleteRoleList = [];
        this.formInfo.positionIds = [];
      }
      // 重置岗位下拉选择
      this.positionList = result.data || [];
      this.positionLoading = false;
    },
    // 获取用户详情
    async getDetail() {
      this.loading = true;
      const [result, error] = await getUser(this.userId);
      this.loading = false;
      if (error) return;
      const { organizeId, email, phonenumber, positionInfos, ...req } =
        result.data.user;
      delete req.merchant;
      delete req.roleInfos;

      this.formInfo = {
        ...req,
        positionIds: result?.data?.positionIds || [],
        merchantId: this.merchantId,
        organizeId: organizeId || this.organizeTreeList[0].organizeId || '',
        email: email ? aseDecrypt(email) : undefined,
        phonenumber: phonenumber ? aseDecrypt(phonenumber) : undefined,
        roleIds: result.data?.roleIds || [],
      };
      // 获取下拉选择列表（根据用户的数据筛选）
      this.getPositionOptions(this.formInfo.organizeId, false);
      // 处理不能删除的数据
      this.initDisabledRoleList(positionInfos);
    },
    // 初始化获取不能删除的角色
    initDisabledRoleList(positionInfos) {
      let roleIdTotal = [];
      positionInfos.forEach((item) => {
        // 如果岗位存在角色 信息添加所有的角色eID进去
        if (item?.roleInfos?.length) {
          roleIdTotal = roleIdTotal.concat(
            item.roleInfos?.map((role) => role.roleId) || []
          );
        }
      });
      // 获取到不重复的id
      const noRepeatData = Array.from(new Set(roleIdTotal)).filter(
        (item) => !!item
      );
      this.cantDeleteRoleList = noRepeatData;
    },
    // 修改岗位--> 重新获取数据
    changePosition(e) {
      if (e.length) {
        // 角色ID统一名称
        let roleIdTotal = [];
        this.positionList
          .filter((item) => e.indexOf(item.positionId) > -1)
          ?.map((item) => {
            roleIdTotal = roleIdTotal.concat(item?.roleIds || []);
          });
        // 不重复的角色ID
        const noRepeatData = Array.from(new Set(roleIdTotal)).filter(
          (item) => !!item
        );
        // 设置不能删除的角色列表
        this.cantDeleteRoleList = noRepeatData;
      } else {
        this.cantDeleteRoleList = [];
      }
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    /** 查询角色列表 */
    async getRoles() {
      this.roleLoading = true;
      const [result, error] = await listRole({
        limit: 999,
        page: 1,
      });
      if (!error) {
        this.roleOptions = result.data;
      }
      this.roleLoading = false;
    },
    // 新增确认
    async handleAddOk() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.loading) return;

          this.loading = true;
          const queryParams = {
            ...this.formInfo,
            merchantId: this.merchantId,
            email: rsaCode(this.formInfo.email),
            phonenumber: this.formInfo.phonenumber
              ? rsaCode(this.formInfo.phonenumber)
              : undefined,
          };

          const [, error] = this.userId
            ? await updateUser(queryParams)
            : await addUser(queryParams);
          this.loading = false;
          if (error) return;
          this.$message.success(`${this.title}成功`);
          this.$emit('ok');
          this.closeModal();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.vue-treeselect__control {
  line-height: 30px;
}
/deep/.vue-treeselect__input-container {
  line-height: 30px;
}
/deep/.vue-treeselect__placeholder,
.vue-treeselect__single-value {
  line-height: 30px;
  // font-size: 12px;
}
</style>
