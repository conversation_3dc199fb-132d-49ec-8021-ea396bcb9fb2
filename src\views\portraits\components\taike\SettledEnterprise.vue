<template>
  <PortraitCard :span="24" title="入驻企业" :canExpand="true">
    <a-row :gutter="12">
      <a-col :span="24" class="icon-title">
        <img
          :src="require('@/assets/images/portraits/1-2009.png')"
          alt=""
        />企业数量

        <span
          v-if="!pictureInfo.enterpriseCount"
          class="detail-num detail-num-null"
          >暂无数据</span
        >
        <template v-else>
          <span class="detail-num">{{ pictureInfo.enterpriseCount }}</span>
          <span class="unit">家</span>
        </template>
      </a-col>
      <a-col :span="12">
        <ChartsCard chartTitle="登记状态统计" :options="charts1Options" />
      </a-col>
      <a-col :span="12">
        <ChartsCard chartTitle="规模统计" :options="charts2Options" />
      </a-col>
      <a-col :span="10">
        <EnterpriseList :pictureId="pictureId" @openModal="openModal" />
      </a-col>
      <a-col :span="7">
        <ScrollTable
          tableTitle="企业标签统计"
          height="374px"
          :loadDataCallback="loadData1"
          :columns="table1.columns"
          :dataCount="table1.total"
          :tableData="table1.data"
        />
      </a-col>
      <a-col :span="7">
        <ScrollTable
          tableTitle="企业称号统计"
          height="374px"
          :loadDataCallback="loadData2"
          :columns="table2.columns"
          :dataCount="table2.total"
          :tableData="table2.data"
        />
      </a-col>
    </a-row>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
import ChartsCard from '../ChartsCard.vue';
import ScrollTable from '../ScrollTable.vue';
import EnterpriseList from '../EnterpriseList.vue';

import { TaikeAPI } from '@/api/portraits-new/index.js';
import { usePieCharts } from '../chartHooks';

export default {
  components: {
    PortraitCard,
    ChartsCard,
    ScrollTable,
    EnterpriseList,
  },
  props: {
    pictureId: {
      type: String,
      default: '',
    },
    pictureInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      charts1Options: {},
      charts2Options: {},
      table1: {
        columns: [
          {
            title: '企业标签',
            field: 'labelName',
            minWidth: 80,
          },
          {
            title: '企业数量',
            field: 'count',
            headerAlign: 'right',
            align: 'right',
            width: 80,
          },
          {
            title: '企业占比',
            field: 'account',
            width: 80,
            type: 'html',
            headerAlign: 'right',
            align: 'right',
            formatter: ({ cellValue }) =>
              `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
          },
        ],
        data: [],
        total: 0,
      },
      table2: {
        columns: [
          {
            title: '属性名称',
            field: 'titleName',
            overflow: 'tooltip',
            minWidth: 100,
          },
          {
            title: '企业数量',
            field: 'count',
            width: 80,
            type: 'html',
            headerAlign: 'right',
            align: 'right',
            formatter: ({ cellValue }) =>
              `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
          },
          {
            title: '企业占比',
            field: 'account',
            width: 90,
            type: 'html',
            headerAlign: 'right',
            align: 'right',
            formatter: ({ cellValue }) =>
              `<span style="color: #009B67;font-family: D-DIN;font-size: 18px;">${cellValue}</span`,
          },
        ],
        data: [],
        total: 0,
      },
    };
  },
  watch: {
    pictureInfo: {
      deep: true,
      handler() {
        this.handleInitCharts1();
        this.handleInitCharts2();
      },
    },
  },
  mounted() {
    this.loadData1();
    this.loadData2();
  },
  methods: {
    openModal(...args) {
      this.$emit('openModal', ...args);
    },
    async loadData1(args) {
      this.loading = true;
      const [res, err] = await TaikeAPI.getSettledLabelInfo({
        ...this.tablePage,
        ...args,
        parkId: this.pictureId,
      });
      this.loading = false;
      if (err || typeof res.data !== 'object') return;
      let list = [];
      list = res.data;
      list.forEach((item) => {
        this.table1.data.push({
          ...item,
          index: this.table1.data.length,
        });
      });
      this.table1.total = res.total;
    },
    async loadData2(args) {
      this.loading = true;
      const [res, err] = await TaikeAPI.getSettledTitleInfo({
        ...this.tablePage,
        ...args,
        parkId: this.pictureId,
      });
      this.loading = false;
      if (err || typeof res.data !== 'object') return;
      let list = [];
      list = res.data;
      list.forEach((item) => {
        this.table2.data.push({
          ...item,
          index: this.table2.data.length,
        });
      });
      this.table2.total = res.total;
    },
    handleInitCharts1() {
      const { registerStatusList = [] } = this.pictureInfo;
      this.charts1Options = usePieCharts({
        legendType: 'plain',
        data: registerStatusList,
      });
    },
    handleInitCharts2() {
      const { regulateList = [] } = this.pictureInfo;
      console.log(regulateList, 'regulateList');
      this.charts2Options = usePieCharts({
        data: regulateList,
      });
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;
  .title {
    width: 70px;
    text-align: right;
    color: #999999;
  }
  .info {
    width: calc(100% - 70px);
    .fold-button {
      color: #86bded;
      float: right;
    }
    .num {
      font-family: D-DIN;
      font-size: 20px;
      font-weight: bold;
      line-height: 20px;
      letter-spacing: 0px;
      color: #333333;
      z-index: 0;
    }
    .unit {
      margin-left: 4px;
    }
  }
  &-inline {
    display: inline-block;
  }
  &-picture {
    &-3 {
      background-image: url('@/assets/images/portraits/1-003.png');
    }
  }
}
.ant-col {
  margin-top: 24px;
}
.icon-title {
  margin-top: 8px;
  font-weight: normal;
  font-size: 14px;
  img {
    width: 48px;
    height: 48px;
    margin-right: 16px;
  }
  .detail-num {
    font-family: D-DIN;
    font-size: 26px;
    font-weight: bold;
    display: inline-block;
    color: #00173a;
    margin-left: 15px;
    position: relative;
    top: 3px;
    &-null {
      font-size: 16px;
    }
  }
  .unit {
    font-weight: normal;
    margin-left: 4px;
    font-size: 12px;
  }
}
</style>
