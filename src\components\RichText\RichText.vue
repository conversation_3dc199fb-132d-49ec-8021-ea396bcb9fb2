<template>
  <div>
    <editor
      ref="tinymce"
      v-model="content"
      :init="init"
      :disabled="disabled"
    ></editor>
  </div>
</template>

<script>
import tinymce from 'tinymce/tinymce';
import Editor from '@tinymce/tinymce-vue';
import 'tinymce/themes/silver';
import 'tinymce/plugins/image';
import 'tinymce/plugins/media';
import 'tinymce/plugins/table';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/contextmenu';
import 'tinymce/plugins/wordcount';
import 'tinymce/plugins/colorpicker';
import 'tinymce/plugins/textcolor';
import 'tinymce/plugins/preview';
import 'tinymce/plugins/code';
import 'tinymce/plugins/link';
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/codesample';
import 'tinymce/plugins/hr';
import 'tinymce/plugins/fullscreen';
import 'tinymce/plugins/textpattern';
import 'tinymce/plugins/searchreplace';
import 'tinymce/plugins/autolink';
import 'tinymce/plugins/directionality';
import 'tinymce/plugins/visualblocks';
import 'tinymce/plugins/visualchars';
import 'tinymce/plugins/template';
import 'tinymce/plugins/charmap';
import 'tinymce/plugins/nonbreaking';
import 'tinymce/plugins/insertdatetime';
import 'tinymce/plugins/imagetools';
import 'tinymce/plugins/autosave';
import 'tinymce/plugins/autoresize';
//import 'tinymce/plugins/emoticons';
import 'tinymce/plugins/wordcount';
import 'tinymce/plugins/paste';
import './plugins/ax_wordlimit';
import RichMixin from './RichTextMixin';
export default {
  mixins: [RichMixin],
  model: {
    prop: 'value',
    event: 'change',
  },
  components: {
    Editor,
  },
  props: {
    handleImageUpload: {
      type: Function,
      default: null,
    },
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    plugins: {
      type: [String, Array],
      default:
        'preview searchreplace   autolink  directionality visualblocks visualchars fullscreen image  media template code codesample table charmap hr nonbreaking insertdatetime advlist lists wordcount imagetools textpattern autosave autoresize wordcount wordlimit paste',
    },
    toolbar: {
      type: [String, Array],
      default:
        'undo redo fontselect   bold italic underline  strikethrough fontsizeselect  forecolor backcolor bullist numlist alignleft aligncenter alignright alignjustify table  image    media  fullscreen',
    },
  },
  data() {
    const _that = this;
    const baseUrl =
      process.env.NODE_ENV === 'production'
        ? process.env.VUE_APP_ROUTER_BASE
        : '';
    return {
      init: {
        language_url: `${baseUrl}/richEditor/langs/zh-Hans.js`,
        language: 'zh-Hans',
        paste_data_images: false,
        wordlimit: {
          max: _that.limitWords,
          spaces: false,
          isInput: false,
        },
        menubar: false,
        skin_url: `${baseUrl}/richEditor/skins/ui/oxide`,
        //emoticons_database_url: `${baseUrl}/richEditor/emojis.js`,
        content_css: `${baseUrl}/richEditor/content.css`,
        height: 770,
        min_height: 770,
        max_height: 770,
        toolbar_mode: 'wrap',
        plugins: this.plugins,
        toolbar: this.toolbar,
        content_style: 'p {margin: 5px 0;}',
        fontsize_formats: '12px 14px 16px 18px 24px 36px 48px 56px 72px',
        font_formats:
          '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif',
        branding: false,
        images_upload_handler: this.handleImageUpload,
        init_instance_callback: function (editor) {
          editor.on('wordlimit', _that.wordLimitHandler);
        },
      },
      content: this.value,
    };
  },
  mounted() {
    if (!this.handleImageUpload) {
      console.error('the function handleImageUpload undefined');
    }
    tinymce.init({});
  },
};
</script>
<style lang="less"></style>
