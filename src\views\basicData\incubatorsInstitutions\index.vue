<template>
  <page-layout>
    <table-component
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :parentData="params"
      :pageName="pageName"
    ></table-component>
  </page-layout>
</template>

<script>
import { institutionsMixin } from '../mixins/institutionsMixin';
import tableComponent from '@/views/basicData/components/tableComponent';
import moment from 'moment';

export default {
  name: 'incubatorInstitutions',
  components: { tableComponent },
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'incubatorsInstitutions',
      carrierTypeList: [],
      creationCompleteList: [],
      levelList: [],
      params: {
        rateTime: undefined,
        subjectOperation: undefined,
        carrierType: undefined,
        creationComplete: undefined,
        level: undefined,
        carrierName: undefined,
      },
      tableColumn: [
        {
          field: '',
          title: '',
          type: 'checkbox',
          fixed: 'left',
          width: 70,
        },
        {
          field: '',
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: 70,
        },
        {
          field: 'rateTime',
          title: '日期',
          width: 200,
          formatter: ({ cellValue }) => {
            return cellValue ? moment(cellValue).format('YYYY') : '';
          },
        },
        {
          field: 'carrierName',
          title: '载体名称',
          width: 200,
        },
        {
          field: 'subjectOperation',
          title: '运营主体',
          width: 200,
        },
        {
          field: 'serviceTime',
          title: '运营时间',
          width: 200,
        },
        {
          field: 'incubationArea',
          title: '总孵化面积（平方米）',
          width: 200,
        },
        {
          field: 'carrierType',
          title: '载体类别',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.carrierTypeList);
          },
        },
        {
          field: 'level',
          title: '载体级别',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.levelList);
          },
        },
        {
          field: 'creationComplete',
          title: '建设情况',
          width: 200,
          formatter: ({ cellValue }) => {
            return this.translateValue(cellValue, this.creationCompleteList);
          },
        },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        //筛选控件配置
        config: [
          {
            field: 'carrierName',
            title: '载体名称',
          },
          {
            field: 'subjectOperation',
            title: '运营主体',
          },
          {
            field: 'carrierType',
            title: '载体类别',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.carrierTypeList,
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
          {
            field: 'level',
            title: '载体级别',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.levelList,
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
          {
            field: 'creationComplete',
            title: '建设情况',
            element: 'a-select',
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.creationCompleteList,
              showSearch: true,
              optionFilterProp: 'children',
            },
          },
          {
            field: 'rateTime',
            title: '日期',
            element: 'slot',
            slotName: 'dateYear',
            rules: [{ required: true, message: '请选择年份' }],
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        submitBtn: true,
        okBtn: false,
        addBtn: false,
        viewBtn: true,
        editBtn: true,
        delBtn: false,
        menu: true,
        menuWidth: 200,
        menuFixed: 'right',
        formConfig: [
          {
            field: 'rateTime',
            title: '年份',
            element: 'slot',
            slotName: 'dateYearForPop',
            rules: [{ required: true, message: '请选择年份' }],
          },
          {
            field: 'serviceTime',
            title: '运营时间',
            element: 'a-date-picker',
            echoFormatter: (value) => {
              return moment(value);
            },
            rules: [{ required: true, message: '请输入运营时间' }],
          },
          {
            field: 'subjectOperation',
            title: '运营主体',
            rules: [{ required: true, message: '请输入运营主体' }],
          },
          {
            field: 'carrierName',
            title: '载体名称',
            rules: [{ required: true, message: '请输入载体名称' }],
          },
          {
            field: 'incubationArea',
            title: '总孵化面积',
            props: {
              suffix: '平方米',
            },
            rules: [
              { required: true, validator: this.checkNum, trigger: 'change' },
            ],
          },
          {
            field: 'carrierType',
            title: '载体类别',
            element: 'a-select',
            props: {
              options: this.carrierTypeList,
              showSearch: true,
              optionFilterProp: 'children',
            },
            previewFormatter: (value) => {
              return this.translateValue(value, this.carrierTypeList);
            },
            rules: [{ required: true, message: '请选择载体类别' }],
          },
          {
            field: 'level',
            title: '载体级别',
            element: 'a-select',
            props: {
              options: this.levelList,
              showSearch: true,
              optionFilterProp: 'children',
            },
            previewFormatter: (value) => {
              return this.translateValue(value, this.levelList);
            },
            rules: [{ required: true, message: '请选择载体级别' }],
          },
          {
            field: 'creationComplete',
            title: '建设情况',
            element: 'a-select',
            props: {
              options: this.creationCompleteList,
              showSearch: true,
              optionFilterProp: 'children',
            },
            previewFormatter: (value) => {
              return this.translateValue(value, this.creationCompleteList);
            },
            rules: [{ required: true, message: '请输入建设情况' }],
          },
        ],
      };
    },
  },
  created() {
    this.getCodeByType('CARRIER_TYPE').then((res) => {
      this.carrierTypeList = res;
    });
    this.getCodeByType('CARRIER_LEVEL').then((res) => {
      this.levelList = res;
    });
    this.getCodeByType('CREATION_COMPLETE').then((res) => {
      this.creationCompleteList = res;
    });
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
