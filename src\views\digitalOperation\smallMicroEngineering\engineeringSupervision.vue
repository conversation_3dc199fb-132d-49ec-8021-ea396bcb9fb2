<template>
  <BuseCrud
    ref="crud"
    title="小微工程列表"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @rowView="rowViewHandler"
    @rowEdit="rowEditHandler"
    @loadData="loadData"
  >
    <template slot="defaultTitle">
      <TabRadio
        v-model="params.nodeStatusAll"
        :radioList="statusRadioList"
        @change="onStatusChange"
        class="mb10"
      >
      </TabRadio>
    </template>
  </BuseCrud>
</template>

<script>
import { approvalList } from '@/api/digitalOperation/securityManagement/buildingSafety/index.js';
import TabRadio from '@/components/tabRadio/TabRadio.vue';
export default {
  name: 'EngineeringSupervision',
  components: {
    TabRadio,
  },
  data() {
    return {
      menuShow: true,
      tableData: [],

      params: {
        receiveTime: undefined,
        projectState: '',
        nodeStatusAll: 'pending',
        nodeStatus: undefined,
        unifiedCreditCode: this.$route.query.pictureId,
      },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      statusRadioList: [
        //当前状态（1待处理，2已处理）
        {
          label: '待处理',
          value: 'pending',
        },
        {
          label: '已处理',
          value: 'pass,reject',
        },
      ],
    };
  },
  computed: {
    tableColumn() {
      return [
        {
          title: '接收时间',
          field: 'receiveTime',
          minWidth: 120,
        },
        {
          title: '统一社会信用代码  ',
          field: 'unifiedCreditCode',
          minWidth: 140,
        },
        {
          title: '申报企业',
          field: 'enterpriseName',
          minWidth: 120,
        },
        {
          title: '项目名称',
          field: 'projectName',
          minWidth: 120,
        },
        {
          title: '经办人/负责人',
          field: 'nodeName',
          minWidth: 120,
        },
        {
          title: '建设单位',
          field: 'constructionUnit',
          minWidth: 120,
        },
        {
          title: '审批角色',
          field: 'approvalRole',
          visible: this.params.state === '2',
          minWidth: 120,
        },
        {
          title: '审批意见',
          field: 'nodeStatus',
          minWidth: 120,
          formatter({ cellValue }) {
            return cellValue === 'pass' ? '通过' : '不通过';
          },
          visible: this.params.nodeStatusAll !== 'pending',
        },
        {
          title: '流程进度',
          field: 'projectState',
          minWidth: 120,
          formatter({ cellValue }) {
            return cellValue === '1' ? '未完结' : '已完结';
          },
          visible: this.params.nodeStatusAll !== 'pending',
        },
      ];
    },
    filterOptions() {
      return {
        config: [
          {
            title: '接收时间',
            field: 'receiveTime',
            element: 'a-range-picker',
          },
          {
            title: '统一社会信用代码',
            field: 'unifiedCreditCode',
          },
          {
            title: '审批意见',
            field: 'nodeStatus',
            element: 'a-select',
            placeholder: '请选择审批意见',
            show: this.params.nodeStatusAll !== 'pending',
            props: {
              options: [
                {
                  label: '通过',
                  value: 'pass',
                },
                {
                  label: '不通过',
                  value: 'reject',
                },
              ],
            },
          },
          {
            title: '流程进度',
            field: 'projectState',
            element: 'a-select',
            show: this.params.nodeStatusAll !== 'pending',
            props: {
              options: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '已完结',
                  value: '0',
                },
                {
                  label: '未完结',
                  value: '1',
                },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: true,
        editBtn: true,
        editBtnText: '项目审批',
        viewBtnText: '查看详情',
        delBtn: false,
        crudPermission: [
          {
            type: 'UPDATE',
            condition: () => {
              return this.params.nodeStatusAll === 'pending';
            },
          },
          {
            type: 'VIEW',
            condition: () => {
              return this.params.nodeStatusAll !== 'pending';
            },
          },
        ],
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData(isTabflag) {
      let p = this.paramsHandle(isTabflag);
      this.loading = true;
      const [res, err] = await approvalList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    paramsHandle() {
      const {
        receiveTime,
        nodeStatusAll,
        nodeStatus,
        projectState,
        unifiedCreditCode,
      } = this.params;

      let p = {
        startTime: receiveTime?.[0]?.format('YYYY-MM-DD'),
        endTime: receiveTime?.[1]?.format('YYYY-MM-DD'),
        nodeStatus: nodeStatus
          ? nodeStatus.split(',')
          : nodeStatusAll.split(','),
        projectState: projectState,
        unifiedCreditCode: unifiedCreditCode,
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      };
      console.log('入参', p);
      return p;
    },
    rowViewHandler(row) {
      this.$router.push({
        name: 'projectApplication',
        query: {
          id: row.projectId,
          type: 'view',
        },
      });
    },
    rowEditHandler(row) {
      this.$router.push({
        name: 'projectApplication',
        query: {
          id: row.projectId,
          type: 'edit',
        },
      });
    },
    onStatusChange(value) {
      this.params.nodeStatusAll = value;
      this.loadData();
    },
  },
};
</script>

<style lang="less" scoped></style>
