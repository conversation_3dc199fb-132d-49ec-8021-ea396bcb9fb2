<template>
  <page-layout>
    <div class="tag-manage">
      <!--        目录-->
      <tag-tree @getNodeInfo="getNodeInfo" :tagList="cardList"></tag-tree>
      <!--        标签内容区-->
      <div class="tag-manage-right">
        <div class="seach-condition">
          <a-form :model="seachForm" layout="inline">
            <a-form-item label="标签名称">
              <a-input
                v-model="seachForm.labelName"
                placeholder="请填写标签名称"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="标签编号">
              <a-input
                v-model="seachForm.labelCode"
                placeholder="请填写标签编号"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="标签类型">
              <a-select
                v-model="seachForm.labelType"
                style="width: 180px"
                :options="typeList"
              ></a-select>
            </a-form-item>
            <a-form-item label="企业/园区">
              <a-select
                v-model="seachForm.labelClass"
                style="width: 180px"
                :options="classList"
              ></a-select>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="searchData">搜索</a-button>
              <a-button style="margin-left: 10px" @click="reset">重置</a-button>
            </a-form-item>
          </a-form>
        </div>
        <div class="catalog-content" v-show="catalogInfo.id">
          <p class="catalog-name">
            {{ catalogInfo.name }}
          </p>
          <div class="tag-card" v-for="item in cardList" :key="item.id">
            <div class="icon">
              <img
                v-if="item.labelType === '0'"
                src="../img/initialization.png"
              />
              <img v-else src="../img/personalization.png" />
              <p v-if="item.labelType === '0'">初始化标签</p>
              <p v-if="item.labelType === '1'">个性化标签</p>
            </div>
            <a-divider class="line" type="vertical" dashed />
            <div class="details">
              <div class="details-left">
                <div class="upper">
                  <span class="code">{{ item.labelCode }}</span>
                  <p class="tag-name">
                    <span class="title">标签名：</span>
                    <span class="info">{{ item.labelName }}</span>
                  </p>
                </div>
                <div class="lower">
                  <p>
                    <span class="title">标签规则：</span>
                    <span class="info">{{ item.labelRule }}</span>
                  </p>
                </div>
              </div>
              <div class="details-right">
                <div class="upper">
                  <p>
                    <span class="title">创建人：</span>
                    <span class="info">{{ item.creator }}</span>
                  </p>
                </div>
                <div class="lower">
                  <p>
                    <span class="title">创建时间：</span>
                    <span class="info">{{ item.createTime }}</span>
                  </p>
                </div>
              </div>
            </div>
            <a-divider class="line" type="vertical" dashed />
            <div class="online">
              <div
                class="picture"
                v-if="item.status === '0'"
                @click="changeStatus(item)"
              >
                <img src="../img/online.png" />
                <p style="color: #1396db">上线</p>
              </div>
              <div class="picture" v-else @click="changeStatus(item)">
                <img src="../img/offline.png" />
                <p style="color: #d81e06">下线</p>
              </div>
            </div>
          </div>
          <p
            class="more-button"
            v-show="!loading && pages > this.pageNum"
            @click="getMoreData"
          >
            &lt; 更多 &gt;
          </p>
          <div class="more-loading" v-show="loading">
            <a-spin tip="加载中..." />
          </div>
        </div>
      </div>
    </div>
  </page-layout>
</template>

<script>
import TagTree from '@/views/tagLibrary/tagManage/components/tagTree';
import * as api from '@/api/tagLibrary';
import moment from 'moment';

// 只要窗口大小发生像素变化就会触发
window.addEventListener('resize', function () {
  // 当前窗口宽度
  const doc = document.querySelector('.catalog-content');
  const tagDoc = document.querySelector('.tag-manage');
  if (tagDoc && doc) {
    const winHeight = tagDoc.clientHeight - 150;
    const contentHeight = doc.scrollHeight;
    // 判断内容高度是否超出屏幕高度
    if (contentHeight > winHeight) {
      doc.style.height = `${winHeight}px`;
    }
  }
});
export default {
  name: 'index',
  components: { TagTree },
  data() {
    return {
      limit: 10,
      pageNum: 1,
      currentPage: 1,
      total: 0,
      pages: 0,
      catalogInfo: {},
      loading: false,
      cardList: [],
      labelCol: {
        span: 2,
      },
      wrapperCol: {
        span: 14,
      },
      seachForm: {
        labelName: '',
        labelCode: '',
        labelType: '',
        labelClass: '',
      },
      typeList: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '0',
          label: '初始化标签',
        },
        {
          value: '1',
          label: '自定义标签',
        },
      ],
      classList: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '1',
          label: '园区',
        },
        {
          value: '2',
          label: '企业',
        },
      ],
    };
  },
  created() {
    this.getData();
  },

  mounted() {
    // 当前窗口宽度
    const doc = document.querySelector('.catalog-content');
    const tagDoc = document.querySelector('.tag-manage');
    if (tagDoc && doc) {
      const winHeight = tagDoc.clientHeight - 150;
      doc.style.height = `${winHeight}px`;
    }
  },
  methods: {
    // 加载更多数据
    getMoreData() {
      this.pageNum++;
      this.getData();
    },
    // 查询数据
    async getData() {
      if (!this.catalogInfo.id) {
        return;
      }
      this.loading = true;
      const params = {
        dirId: this.catalogInfo.id,
        limit: this.limit,
        pageNum: this.pageNum,
        ...this.seachForm,
      };
      // 初始化时为根目录添加操作按钮
      const [res, err] = await api.getListLabelWareHouse(params);
      if (err) return;
      this.loading = false;
      res.data.forEach((item) => {
        item.createTime = moment(item.createTime).format('YYYY-MM-DD hh:mm:ss');
      });
      if (this.currentPage !== this.pageNum) {
        res.data.forEach((item) => {
          this.cardList.push(item);
        });
      } else {
        this.cardList = [...res.data];
      }
      this.total = res.total;
      this.pages = Math.ceil(this.total / this.limit);
      this.currentPage = this.pageNum;
    },
    // 搜索
    searchData() {
      this.currentPage = 1;
      this.pageNum = 1;
      this.getData();
    },
    // 点击目录更新页面标签数据
    getNodeInfo(data) {
      this.catalogInfo = data;
      this.searchData();
    },
    // 上线/下线
    async changeStatus(item) {
      if (item.status === '0') {
        item.status = '1';
      } else {
        item.status = '0';
      }
      item.createTime = new Date(item.createTime);
      const [, err] = await api.updateLabelWareHouse(item);
      this.getData();
      if (err) return;
    },
    // 重置
    reset() {
      this.seachForm = {
        labelName: '',
        labelCode: '',
        labelType: '',
        labelClass: '',
      };
    },
  },
};
</script>

<style scoped lang="scss">
.tag-manage {
  display: flex;
  flex-direction: row;
  height: 80vh;
  background: white;
  padding: 10px;

  .tag-manage-left {
    width: 20%;
    height: 100%;
    border-right: #d9d9d9 1px solid;
    padding: 10px;
  }

  .tag-manage-right {
    width: 80%;
    padding: 20px;

    .catalog-content {
      margin-top: 30px;
      /*max-height: 60vh;*/
      overflow: auto;

      .catalog-name {
        padding: 5px;
        background: #1677ffdb;
        font-size: 15px;
        color: white;
        border-radius: 5px;
      }

      .tag-card {
        margin-bottom: 10px;
        height: 100px;
        border: #d9d9d9 1px solid;
        border-radius: 5px;
        display: flex;
        flex-direction: row;
        align-items: center;
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);

        p {
          margin-bottom: unset;
          color: #898989;
        }

        .line {
          border-color: #7cb305;
          height: 80px;
        }

        .icon {
          width: 10%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          p {
            /*color: blue;*/
          }
        }

        .details {
          width: 70%;
          display: flex;
          flex-direction: row;

          .details-left,
          .details-right {
            .upper {
              padding: 3px;

              .code {
                font-weight: 700;
                color: #0000ff;
              }

              .title {
                color: #545353;
                font-weight: 700;
              }
            }

            .lower {
              padding: 3px;

              .title {
                color: #545353;
                font-weight: 700;
              }
            }
          }

          .details-left {
            width: 60%;

            .upper {
              display: flex;
              flex-direction: row;

              .code {
                width: 30%;
                font-weight: 700;
                color: #0000ff;
              }

              .tag-name {
                display: flex;
                width: 70%;

                .title {
                  width: 30%;
                  text-align: right;
                }

                .info {
                  width: 70%;
                  color: #ff9900;
                  font-weight: 700;
                }
              }
            }
          }

          .details-right {
            width: 40%;
          }
        }

        .online {
          width: 20%;
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          cursor: pointer;

          p {
            text-align: center;
            font-weight: 700;
            margin-top: 5px;
          }

          img {
            width: 40px;
            height: 40px;
            max-width: 100%;
            height: auto;
          }

          .picture:hover {
            transition-duration: 0.5s;
            transform: scale(1.2);
            transition-duration: 0.5s;
          }
        }
      }

      .more-button {
        margin: 20px 0;
        text-align: center;
        cursor: pointer;
      }

      .more-button:hover {
        color: #1994ff;
      }

      .more-loading {
        text-align: center;
        border-radius: 4px;
        margin-bottom: 20px;
        padding: 30px 50px;
        margin: 20px 0;
      }
    }
  }
}
</style>
