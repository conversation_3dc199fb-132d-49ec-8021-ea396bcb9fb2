import { request } from '@/utils/request/requestTkb';

/**
 *  新版企业画像相关接口
 */
export const BusinessAPI = {
  /**
   * 知识产权
   */
  getIntellectualProperty(unifiedCreditCode) {
    return request({
      url: `/enterprise/portrait/intellectualProperty`,
      method: 'POST',
      data: { unifiedCreditCode },
    });
  },
  /**
   * 基本信息
   */
  getBaseInformation(unifiedCreditCode) {
    return request({
      url: `/enterprise/portrait/baseInformation`,
      method: 'POST',
      data: { unifiedCreditCode },
    });
  },
  /**
   * 企业称号
   */
  getEnterpriseTitle(unifiedCreditCode) {
    return request({
      url: `/enterprise/portrait/enterpriseTitle`,
      method: 'POST',
      data: { unifiedCreditCode },
    });
  },
  /**
   * 人才信息
   */
  getTalentInformation(data) {
    console.log(data, 'data');
    return request({
      url: `/enterprise/portrait/talentInformation`,
      method: 'POST',
      data,
    });
  },
  /**
   * 风险投资
   */
  getVcInformation(data) {
    return request({
      url: `/enterprise/portrait/vcInformation`,
      method: 'POST',
      data,
    });
  },
  /**
   * 境外投资机构
   */
  getOverseasResearchInformation(data) {
    return request({
      url: `/enterprise/portrait/overseasResearchInformation`,
      method: 'POST',
      data,
    });
  },
  /**
   * 能耗信息
   */
  getQtyUsageInformation(unifiedCreditCode) {
    return request({
      url: `/enterprise/portrait/qtyUsageInformation`,
      method: 'POST',
      data: { unifiedCreditCode },
    });
  },
  /**
   * 企业经营数据
   */
  getBusinessPictureOperate(data) {
    return request({
      // url: `/business/picture/queryBusinessPictureOperate?companyId=${companyId}`,
      url: `/business/picture/queryBusinessByCompanyId`,
      method: 'POST',
      data,
    });
  },
  /**
   * 根据社会信用代码获取排名
   */
  getRankingByScc(companyId) {
    return request({
      url: `/business/picture/getRankingByScc?companyId=${companyId}`,
      method: 'POST',
    });
  },
  /**
   * 企业安全
   */
  getEnterpriseSafetyInfo(data) {
    return request({
      url: `/enterprise/portrait/getEnterpriseSafetyInfo`,
      method: 'POST',
      data,
    });
  },
  /**
   * 小微工程
   */
  getEicroProjectsDeclaration(data) {
    return request({
      url: `/enterprise/portrait/getEicroProjectsDeclaration`,
      method: 'POST',
      data,
    });
  },
  /**
   * 隐患整改
   */
  getRectificationRecords(data) {
    return request({
      url: `/enterprise/portrait/getRectificationRecords`,
      method: 'POST',
      data,
    });
  },
};

/**
 *  新版园区画像相关接口
 */
export const ParkAPI = {
  /**
   * 隐患整改
   */
  getRectificationRecordsByParkId({ unifiedCreditCode, ...data }) {
    return request({
      url: `/park/portrait/getRectificationRecordsByParkId`,
      method: 'POST',
      data: { parkId: unifiedCreditCode, ...data },
    });
  },
  /**
   * 知识产权
   */
  getKnowlgPty({ unifiedCreditCode, ...data }) {
    return request({
      url: `/park/portrait/knowlgPty`,
      method: 'POST',
      data: { parkId: unifiedCreditCode, ...data },
    });
  },
  /**
   * 人才信息
   */
  getTalentInformation({ unifiedCreditCode, ...data }) {
    return request({
      url: `/park/portrait/talentInformation`,
      method: 'POST',
      data: { parkId: unifiedCreditCode, ...data },
    });
  },
  /**
   * 能耗信息
   */
  getUsageInformation({ unifiedCreditCode, ...data }) {
    return request({
      url: `/park/portrait/qtyUsageInformation`,
      method: 'POST',
      data: { parkId: unifiedCreditCode, ...data },
    });
  },
  /**
   * 风险投资
   */
  getVcInformation(data) {
    return request({
      url: `/park/portrait/vcInformationByParkId`,
      method: 'POST',
      data,
    });
  },
  /**
   * 境外投资机构
   */
  getOverseasResearchInformation(data) {
    return request({
      url: `/park/portrait/overseasResearchInformationByParkId`,
      method: 'POST',
      data,
    });
  },
  /**
   * 园区安全
   */
  getParkSafetyInfo({ unifiedCreditCode, ...data }) {
    return request({
      url: `/park/portrait/getParkSafetyInfoByParkId`,
      method: 'POST',
      data: { parkId: unifiedCreditCode, ...data },
    });
  },
  /**
   * 小微工程申报
   */
  getEicroProjectsDeclaration({ unifiedCreditCode, ...data }) {
    return request({
      url: `/park/portrait/getEicroProjectsDeclarationByParkId`,
      method: 'POST',
      data: { parkId: unifiedCreditCode, ...data },
    });
  },
  /**
   * 园区基本信息
   */
  getBaseInformation(parkId) {
    return request({
      url: `/park/portrait/baseInformation`,
      method: 'POST',
      data: { parkId },
    });
  },

  /**
   * 入驻企业
   */
  getSettledInfo(data) {
    return request({
      url: `/park/portrait/settledInfo`,
      method: 'POST',
      data,
    });
  },
  /**
   * 入驻企业 - 企业标签统计
   */
  getSettledLabelInfo(data) {
    return request({
      url: `/park/portrait/settledLabelInfo`,
      method: 'POST',
      data,
    });
  },
  /**
   * 入驻企业 - 企业称号统计
   */
  getSettledTitleInfo(data) {
    return request({
      url: `/park/portrait/settledTitleInfo`,
      method: 'POST',
      data,
    });
  },
  /**
   * 经营数据
   */
  getSettBusinessData(data) {
    return request({
      url: `/park/portrait/businessData`,
      method: 'POST',
      data,
    });
  },
};

/**
 *  新版太科城画像相关接口
 */
export const TaikeAPI = {
  /**
   * 太科城基础信息
   */
  getTKBInfo(data) {
    return request({
      url: `/tkb/portrait/tkbInfo`,
      method: 'POST',
      data,
    });
  },
  /**
   * 隐患整改
   */
  getRectificationRecords(data) {
    return request({
      url: `/tkb/portrait/getRectificationRecords`,
      method: 'POST',
      data,
    });
  },
  /**
   * 知识产权
   */
  getKnowlgPty(data) {
    return request({
      url: `/tkb/portrait/knowlgPty`,
      method: 'POST',
      data,
    });
  },
  /**
   * 人才信息
   */
  getTalentInformation(data) {
    return request({
      url: `/tkb/portrait/talentInformation`,
      method: 'POST',
      data,
    });
  },
  /**
   * 能耗信息
   */
  getUsageInformation(data) {
    return request({
      url: `/tkb/portrait/qtyUsageInformation`,
      method: 'POST',
      data,
    });
  },
  /**
   * 风险投资
   */
  getVcInformation(data) {
    return request({
      url: `/tkb/portrait/vcInformation`,
      method: 'POST',
      data,
    });
  },
  /**
   * 境外投资机构
   */
  getOverseasResearchInformation(data) {
    return request({
      url: `/tkb/portrait/overseasResearchInformation`,
      method: 'POST',
      data,
    });
  },
  /**
   * 园区安全
   */
  getParkSafetyInfo(data) {
    return request({
      url: `/tkb/portrait/getParkSafetyInformation`,
      method: 'POST',
      data,
    });
  },
  /**
   * 小微工程申报
   */
  getEicroProjectsDeclaration(data) {
    return request({
      url: `/tkb/portrait/getEicroProjectsDeclaration`,
      method: 'POST',
      data,
    });
  },
  /**
   * 园区基本信息
   */
  getBaseInformation(data) {
    return request({
      url: `/tkb/portrait/baseInformation`,
      method: 'POST',
      data,
    });
  },

  /**
   * 入驻企业
   */
  getSettledInfo(data) {
    return request({
      url: `/tkb/portrait/settledInfo`,
      method: 'POST',
      data,
    });
  },
  /**
   * 入驻企业 - 企业标签统计
   */
  getSettledLabelInfo(data) {
    return request({
      url: `/tkb/portrait/settledLabelInfo`,
      method: 'POST',
      data,
    });
  },
  /**
   * 入驻企业 - 企业称号统计
   */
  getSettledTitleInfo(data) {
    return request({
      url: `/tkb/portrait/settledTitleInfo`,
      method: 'POST',
      data,
    });
  },
  /**
   * 经营数据
   */
  getSettBusinessData(data) {
    return request({
      url: `/tkb/portrait/businessData`,
      method: 'POST',
      data,
    });
  },
  /**
   * 园区信息
   */
  getParkAysInfo(data) {
    return request({
      url: `/tkb/portrait/parkAysInfo`,
      method: 'POST',
      data,
    });
  },
  /**
   * 园区信息-标签统计
   */
  getParkLabelAysInfo(data) {
    return request({
      url: `/tkb/portrait/parkLabelAysInfo`,
      method: 'POST',
      data,
    });
  },
  /**
   * 创新平台
   */
  getInnovatePlatform(data) {
    return request({
      url: `/tkb/portrait/innovatePlatform`,
      method: 'POST',
      data,
    });
  },
  /**
   * 重大项目
   */
  getInnovationPlatform(data) {
    return request({
      url: `/tkb/portrait/innovationPlatform`,
      method: 'POST',
      data,
    });
  },
  /**
   * 孵化机构
   */
  getIncubationOrg(data) {
    return request({
      url: `/tkb/portrait/incubationOrg`,
      method: 'POST',
      data,
    });
  },
  /**
   * 研发机构
   */
  getDevOrg(data) {
    return request({
      url: `/tkb/portrait/devOrg`,
      method: 'POST',
      data,
    });
  },
  /**
   * 境内研发机构
   */
  getWithinDevOrg(data) {
    return request({
      url: `/tkb/portrait/withinDevOrg`,
      method: 'POST',
      data,
    });
  },
};

/** 企业简要信息-分页查询 */
export const getEnterpriseList = (data) => {
  return request({
    url: `/base/enterpriseInfo/enterpriseMainInfo`,
    method: 'POST',
    data,
  });
};
