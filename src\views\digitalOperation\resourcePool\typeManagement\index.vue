<template>
  <BuseCrud
    ref="crud"
    title="类型列表"
    :loading="loading"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    @modalConfirm="modalConfirmHandler"
    :modalConfig="modalConfig"
    @loadData="loadData"
    @handleCreate="handleCreate"
    @rowDel="deleteResource"
  >
    <template slot="defaultHeader">
      <a-button type="primary mr-10" @click="deleteResource(false)"
        >删除</a-button
      >
    </template>
  </BuseCrud>
</template>

<script>
import {
  pageList,
  saveResourceType,
  editResourceType,
  deleteResourceType,
  deleteBatchResourceType,
} from '@/api/digitalOperation/resourcePool/typeManagement/index.js';
export default {
  name: 'typeManagement',
  data() {
    return {
      tableData: [],
      params: { name: '', sex: '', age: '' },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
    };
  },
  computed: {
    modalConfig() {
      return {
        viewBtn: false,
        addBtn: true,
        editBtn: true,
        formConfig: [
          {
            title: '资源类型',
            field: 'resType',
            rules: [{ required: true, message: '请输入资源类型' }],
          },
        ],
      };
    },
  },

  created() {
    this.tableColumn = [
      {
        type: 'checkbox',
      },
      {
        title: '资源类型',
        field: 'resType',
      },
    ];
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      let p = {
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      };
      const [res, err] = await pageList(p);
      if (err) return;
      this.loading = false;
      this.tableData = res.data.records;
      //设置总条数
      this.tablePage.total = res.data.total;
    },
    async saveResourceType(p) {
      const [res, err] = await saveResourceType(p);
      console.log(res, err);
      if (err?.code === '31000') {
        return this.$message.error('该资源类型重复！');
      } else if (err) {
        return;
      }
      this.$message.success('新增成功');
      this.loadData();
    },
    async editResourceType(p) {
      const [, err] = await editResourceType(p);
      if (err) return;
      this.$message.success('编辑成功');
      this.loadData();
    },

    async deleteResourceType(id) {
      const [, err] = await deleteResourceType(id);
      if (err) return;
      this.$message.success('删除成功');
      this.loadData();
    },
    async deleteBatchResourceType(ids) {
      const [, err] = await deleteBatchResourceType(ids);
      if (err) return;
      this.$message.success('批量删除成功');
      this.loadData();
    },
    handleCreate() {
      this.$refs.crud.switchModalView(true);
    },
    deleteResource(row) {
      let that = this;
      this.$confirm({
        title: '删除操作',
        content: '此操作将删除资源信息, 是否继续？',
        onOk: () => {
          if (row) {
            that.deleteResourceType({ id: row.id });
          } else {
            const records = this.$refs.crud
              .getVxeTableRef()
              .getCheckboxRecords();
            let arr = [];
            records.forEach((item) => {
              arr.push(item.id);
            });
            that.deleteBatchResourceType({ deleteList: arr });
          }
        },
        onCancel() {
          console.log('Cancel');
        },
      });
    },
    modalConfirmHandler(params) {
      let p = { resType: params.resType };
      params.crudOperationType === 'update'
        ? ((p.id = params.id), this.editResourceType(p))
        : this.saveResourceType(p);
    },
  },
};
</script>

<style lang="less" scoped>
.mr-10 {
  margin-right: 10px;
}
</style>
