// 工具方法
import enquireJs from 'enquire.js';
import { Modal } from 'ant-design-vue';

import moment from 'moment';
import { getBaseUrl } from './common/util';
import { getDictsNew } from '@/api/system/dict.js';
const { confirm } = Modal;
const baseURL = process.env.VUE_APP_USE_BUILD_TYPE
  ? getBaseUrl()
  : process.env.VUE_APP_BASE_API;

export async function getDictByApi({
  api = getDictsNew,
  target,
  params = {},
  value,
  label = 'label',
  handler,
  labelHandler,
  fn,
  fieldsMap = [
    {
      key: 'source',
      field: 'source',
      handler: (item) => item,
    },
  ],
}) {
  let result;
  if (fn) {
    result = await fn(params);
  } else if (!api || !target || (!value && !handler && !fieldsMap)) {
    return false;
  } else {
    const [res] = await api(params);
    if (res?.code === '10000') {
      result = res?.rows || res?.data || [];
    }
  }
  target.splice(
    0,
    target.length,
    ...(result || []).map((x) => {
      const otherParams = fieldsMap?.reduce((acc, item) => {
        acc[item.key] = item?.handler ? item.handler(x) : x?.[item.field];
        return acc;
      }, {});
      return {
        label: labelHandler ? labelHandler(x) : x[label],
        value: handler ? handler(x) : x?.[value],
        ...otherParams,
      };
    })
  );
  return target;
}
export function confirmFunc(
  title = '确定执行该操作吗？',
  okText = '确定',
  cancelText = '取消'
) {
  return new Promise((resolve) => {
    confirm({
      title: title,
      okText,
      cancelText,
      onOk: async () => {
        resolve(true);
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}

export function initParams(list = [], initParams = {}, multiConfig) {
  let result = {};
  if (multiConfig) {
    const multiMap = list
      .filter((x) => x.mapping)
      .reduce((acc, x) => {
        if (!acc[x.mapping.field]) {
          acc[x.mapping.field] = {};
        }
        acc[x.mapping.field][x.mapping.lang] =
          (initParams && initParams?.[x.mapping.field]?.[x.mapping.lang]) ??
          x?.props?.defaultValue ??
          x?.defaultValue;
        return acc;
      }, {});
    const otherMap = list.filter((x) => !x.mapping && !x?.onlyShow);
    result = {
      ...otherMap.reduce((acc, item) => {
        acc[item['field']] =
          (initParams && initParams[item['field']]) ??
          item?.props?.defaultValue ??
          item?.defaultValue;
        return acc;
      }, {}),
      ...multiMap,
    };
  } else {
    result = list
      .filter((x) => !x?.onlyShow)
      .reduce((acc, item) => {
        acc[item['field']] =
          (initParams && initParams[item['field']]) ??
          item?.props?.defaultValue ??
          item?.defaultValue;
        return acc;
      }, {});
  }
  return result;
}
export function isDef(v) {
  return v !== undefined && v !== null;
}

/**
 * Remove an item from an array.
 */
export function remove(arr, item) {
  if (arr.length) {
    const index = arr.indexOf(item);
    if (index > -1) {
      return arr.splice(index, 1);
    }
  }
}

export function isRegExp(v) {
  return _toString.call(v) === '[object RegExp]';
}

export function enquireScreen(call) {
  const handler = {
    match: function () {
      call && call(true);
    },
    unmatch: function () {
      call && call(false);
    },
  };
  enquireJs.register('only screen and (max-width: 767.99px)', handler);
}

const _toString = Object.prototype.toString;

export function extend(target) {
  for (let i = 1, l = arguments.length; i < l; i++) {
    let arg = arguments[i];
    for (var key in arg) {
      Object.prototype.hasOwnProperty.call(arg, key) &&
        (target[key] = arg[key]);
    }
  }
  return target;
}

export function recursionData(data) {
  if (!data) return [];
  data.forEach((item) => {
    item.title = item.label;
    if (item.children && item.children.length) {
      recursionData(item.children);
    }
  });
  return data;
}

// 添加日期范围
export function addDateRange(params, dateRange) {
  var search = params;
  search.beginTime = '';
  search.endTime = '';
  if (dateRange != null && '' != dateRange) {
    search.beginTime = this.dateRange[0].format('YYYY-MM-DD');
    search.endTime = this.dateRange[1].format('YYYY-MM-DD');
  }
  !search.beginTime && delete search.beginTime;
  !search.endTime && delete search.endTime;
  return search;
}

// 通用下载方法
export function download(fileName) {
  window.location.href =
    baseURL +
    '/common/download?fileName=' +
    encodeURI(fileName) +
    '&delete=' +
    true;
}

/**
 * 时间戳转换工具
 * @param {*} timestampNum 时间戳
 * @param {*} digitNum 小数位数
 * @param {*} sourceUnit 源时间戳单位，默认 ms
 * @param {*} targetUnit 转换时间戳单位，默认 h
 * @returns
 */
export function timestampTransform(
  timestampNum,
  digitNum = 0,
  sourceUnit = 'ms',
  targetUnit = 'h'
) {
  const hourNum = moment
    .duration(timestampNum, sourceUnit)
    .as(targetUnit)
    .toFixed(digitNum);
  return hourNum;
}

export function getUUID() {
  var data = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
  var nums = '';
  for (var i = 0; i < 9; i++) {
    var r = parseInt(Math.random() * 9 + 1);
    nums += data[r];
  }
  return nums + new Date().getTime();
}

export function recursionDataSlide(data, childFlag) {
  if (!data) return [];
  data.forEach((item) => {
    item.title = item.label;

    if (childFlag) {
      item.slots = { icon: 'team' };
    } else {
      item.slots = { icon: 'bank' };
    }
    //目录
    if (item.type && item.type === 'M') {
      item.slots = { icon: 'directory' };
      //菜单
    } else if (item.type && item.type === 'C') {
      if (item.children && item.children.length) {
        item.slots = { icon: 'menu' };
      } else {
        item.slots = { icon: 'file' };
      }

      //按钮
    } else if (item.type && item.type === 'F') {
      item.slots = { icon: 'button' };
    }

    if (item.children && item.children.length) {
      recursionDataSlide(item.children, true);
    } else {
      delete item.children;
    }
  });
  return data;
}

export function getGraphJson(data, hasChild) {
  if (!data) return [];
  data.forEach((item) => {
    // item.id = item.label;
    item.text = item.label;
    if (hasChild) {
      item.color = '#fee6d5';
      item.fontColor = '#000';
      item.borderColor = '#000';
    }
    if (item.children && item.children.length) {
      getGraphJson(item.children, true);
    }
  });
  return data;
}

// b kb mb
export const changeByte = (byte) => {
  let size = '';
  if (byte < 0.1 * 1024) {
    // 小于0.1KB，则转化成B
    size = `${byte.toFixed(2)}B`;
  } else if (byte < 0.1 * 1024 * 1024) {
    // 小于0.1MB，则转化成KB
    size = `${(byte / 1024).toFixed(2)}KB`;
  } else if (byte < 0.1 * 1024 * 1024 * 1024) {
    // 小于0.1GB，则转化成MB
    size = `${(byte / (1024 * 1024)).toFixed(2)}MB`;
  } else {
    // 其他转化成GB
    size = `${(byte / (1024 * 1024 * 1024)).toFixed(2)}GB`;
  }

  const sizeStr = `${size}`; // 转成字符串
  const index = sizeStr.indexOf('.'); // 获取小数点处的索引
  const dou = sizeStr.substr(index + 1, 2); // 获取小数点后两位的值
  // eslint-disable-next-line eqeqeq
  if (dou == '00') {
    // 判断后两位是否为00，如果是则删除00
    return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2);
  }
  return size;
};

//文件路径截取文件名
export const getFileName = (url) => {
  if (!url) return null;
  let index = url.lastIndexOf('$');
  let fileName = index > -1 ? url.slice(index + 1, url.length) : '';
  return fileName;
};
//文件路径截取文件类型
export const getFileType = (url) => {
  if (!url) return null;
  let index = url.lastIndexOf('$');
  let fileName = index > -1 ? url.slice(index + 1, url.length) : '';
  let i = fileName.lastIndexOf('.');
  let fileType = i > -1 ? fileName.slice(i + 1, fileName.length) : '';
  return fileType;
};
const baseImgUrl = process.env.VUE_APP_BASE_API_IMG;
//附件返回格式为list,并且item为半路径时回显
export const randerImgs = (imgs) => {
  if (!imgs) return [];
  return imgs.map((item, index) => {
    return {
      name: getFileName(item),
      type: getFileType(item),
      url: baseImgUrl + item,
      fileUrl: item,
      uid: `${getFileName(item)}${index}`,
    };
  });
};
//附件处理成入参形式--字符串，以，拼接
export const imgToStringHandle = (attachment) => {
  let arr = [];
  attachment.forEach((item) => {
    arr.push(item.response ? item.response.fileName : item.fileUrl);
  });
  attachment = arr.join(',');
  return attachment;
};

//附件返回格式为list,并且item为对象半路径时回显
export const arrImgsRander = (imgs) => {
  if (!imgs) return [];
  return imgs.map((item, index) => {
    return {
      name: item.fileName,
      size: item.fileSize,
      fileSize: item.fileSize,
      url: baseImgUrl + item.fileUrl,
      fileUrl: item.fileUrl,
      uid: `${item.fileName}${index}`,
    };
  });
};
//附件处理成入参形式--[]数组形式
export const imgToArrHandle = (fileList) => {
  console.log('处理前', fileList);
  if (!fileList || fileList.length == 0) return [];
  return fileList.map((item) => {
    return {
      fileName: item.name,
      fileSize: item.fileSize || item.size,
      fileUrl: item.fileUrl || item.response.fileName,
    };
  });
};

//附件返回格式为字符串,并且只有一条时
export const oneImgsRander = (imgs) => {
  if (!imgs) return [];
  return [{ uid: '1', url: baseImgUrl + imgs, fileUrl: imgs }];
};

//附件处理成入参形式--只有一条数据，并以字符串形式入参
export const oneImgsHandle = (fileList) => {
  if (!fileList || !fileList.length) return '';
  return fileList[0].response
    ? fileList[0].response.fileName
    : fileList[0].fileUrl;
};

export const checkNumber = async (_rule, value) => {
  value = Number(value);
  if (!Number.isInteger(value)) {
    return Promise.reject('需为正整数');
  } else {
    // if (value > 100) {
    // return Promise.reject('最多输入100');
    // } else
    if (value < 1) {
      return Promise.reject('最少输入1');
    } else {
      return Promise.resolve();
    }
  }
};
// 下拉框模糊搜索
export function filterOption(inputValue, option) {
  return (
    option?.componentOptions?.children[0].text
      .toLowerCase()
      ?.indexOf(inputValue?.toLowerCase()) >= 0
  );
}
// 禁用时间段
export function disabledEndDate(val, startDate) {
  if (startDate) {
    const startNum = new Date(moment(startDate).format('YYYY-MM-DD')).getTime();
    const now = new Date(moment(val).format('YYYY-MM-DD')).getTime();
    return now < startNum;
  }
  return false;
}
export function disabledStartDate(val, endDate) {
  if (endDate) {
    const endNum = new Date(moment(endDate).format('YYYY-MM-DD')).getTime();
    const now = new Date(moment(val).format('YYYY-MM-DD')).getTime();
    return now > endNum;
  }
  return false;
}

export function getLastLevelNodes(tree) {
  let result = [];

  tree.forEach((node) => {
    // 如果当前节点有子节点
    if (node.children && node.children.length > 0) {
      // 递归调用，处理子节点
      result = result.concat(getLastLevelNodes(node.children));
    } else {
      // 如果没有子节点，说明是最后一级，添加到结果中
      result.push(node);
    }
  });

  return result;
}
export function formatNumber(num) {
  // 处理 null、undefined 或者非数字的情况
  if (num === null || num === undefined || isNaN(num)) {
    return '0'; // 或者您希望返回的其他默认值，例如 'N/A'
  }

  // 将输入转换为数字
  const numberValue = Number(num);

  // 使用 toLocaleString 格式化数字
  return numberValue.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
}
// utils/time.js
export function startClock(updateCallback, interval = 1000) {
  const updateTime = () => {
    const now = new Date();
    updateCallback(now);

    // 递归调用 setTimeout
    setTimeout(updateTime, interval);
  };

  updateTime(); // 立即调用一次
}
export function isIntervalGreaterThanOneYear(startDate, endDate) {
  console.log(startDate, endDate);
  // 将日期字符串转换为 Moment 对象
  const start = moment(startDate);
  const end = moment(endDate);

  // 检查开始日期是否早于结束日期
  if (start.isAfter(end)) {
    // throw new Error('开始时间不能晚于结束时间');
    return false;
  }

  // 计算两个日期之间的间隔
  const duration = moment.duration(end.diff(start));
  console.log(duration.asMonths(), 'duration');
  // 判断是否大于一年
  return duration.asMonths() >= 12;
}
