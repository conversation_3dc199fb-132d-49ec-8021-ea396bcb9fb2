<template>
  <div class="instruction-detail">
    <a-tabs type="card" class="tabs" @change="tabCallback">
      <a-tab-pane key="1" tab="任务信息">
        <taskDetail :taskDetail="taskDetail" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="办理情况">
        <HandlingSituation
          @refresh="refresh"
          :taskTransactionInfoResList="taskDetail.taskTransactionInfoResList"
        />
      </a-tab-pane>
      <a-tab-pane key="3" tab="操作记录">
        <operatingRecord
          :taskOperatingRecordResList="taskDetail.taskOperatingRecordResList"
        />
      </a-tab-pane>
    </a-tabs>
    <div class="task-detail-buttons">
      <div v-if="detailPageType == 'toDoList'">
        <!-- operateStatus  操作状态  1:发起任务  2:提交结果  3:确认办结  4:归档  5:已逾期  6:已到期-->
        <a-button
          type="primary"
          @click="submitResult"
          :disabled="
            taskDetail.operateStatus !== '1' && taskDetail.operateStatus !== '2'
          "
          >提交结果</a-button
        >
        <a-button
          type="primary"
          :disabled="taskDetail.operateStatus !== '2'"
          @click="confirmCompletion"
          :loading="confirmCompletionLoading"
          >确认办结</a-button
        >
        <!--  -->
        <a-button
          type="primary"
          :disabled="taskDetail.operateStatus !== '3'"
          @click="pigeonholeHandler"
          :loading="pigeonholeLoading"
          >归档</a-button
        >
        <!-- -->
      </div>
      <div>
        <a-button
          type="primary"
          :disabled="!taskDetail.operateStatus"
          @click="terminationHandler"
          v-if="
            detailPageType == 'toDoList' ||
            (detailPageType == 'taskList' &&
              taskDetail.creator === $store.state.base.user.userId)
          "
          >终止</a-button
        >
      </div>
    </div>

    <submitResultModal
      :visible="submitModalVisible"
      :taskDetail="taskDetail"
      @refresh="refresh"
      @close="() => (this.submitModalVisible = false)"
    />
    <terminationModal
      @close="() => (this.terminationModalVisible = false)"
      :taskDetail="taskDetail"
      @refresh="refresh"
      :visible.sync="terminationModalVisible"
    />
  </div>
</template>

<script>
import taskDetail from './components/taskDetail.vue';
import HandlingSituation from './components/handlingSituation.vue';
import operatingRecord from './components/operatingRecord.vue';
import submitResultModal from './components/modal/submitResult.vue';
import terminationModal from './components/modal/termination.vue';
import { taskInfo } from '@/api/digitalOperation/taskManagement/taskList.js';
import { operation } from '@/api/digitalOperation/taskManagement/todoList.js';
export default {
  name: 'ManagementTkDetail',
  components: {
    taskDetail,
    HandlingSituation,
    operatingRecord,
    submitResultModal,
    terminationModal,
  },

  data() {
    return {
      pigeonholeLoading: false,
      signForLoading: false,
      confirmCompletionLoading: false,
      submitModalVisible: false,
      terminationModalVisible: false,
      taskDetail: {},
      //详情页面类型
      detailPageType: '', //taskList-任务列表,myInstruction-我的批示 pigeonhole-归档 sendToMe-抄送我的，toDoList-我的待办
    };
  },

  mounted() {
    this.taskInfo();
    this.detailPageType = this.$route.query.detailPageType;
  },

  methods: {
    async taskInfo() {
      const [res, err] = await taskInfo({ id: this.$route.query.id });
      console.log(9999);
      if (err) return;
      // 设置数据
      this.taskDetail = res.data;
      console.log(res, err, '任务详情返回结果');
    },
    async operation(tag) {
      let p = {
        taskId: this.taskDetail.id,
        departmentName: this.taskDetail.departmentName,
        tag, //操作按钮标识: 1:发起任务  2:提交结果  3:确认办结  4:归档  5:终止
      };
      console.log('p', p);
      const [, err] = await operation(p);
      if (err) return;
      this.$message.success(`${tag === '3' ? '确认办结' : '归档'}成功`);
      this.taskInfo();
    },
    refresh() {
      console.log('刷新', 333);
      this.taskInfo();
    },
    /**
     * 提交结果
     */
    submitResult() {
      this.submitModalVisible = true;
    },
    /**
     * 确认办结
     */
    confirmCompletion() {
      let that = this;
      this.$confirm({
        content: `确认要'确认办结'吗？`,
        onOk() {
          that.confirmCompletionLoading = true;
          that.operation('3');
          that.confirmCompletionLoading = false;
        },
        cancelText: '取消',
      });
    },
    /**
     * 归档
     */
    pigeonholeHandler() {
      let that = this;
      this.$confirm({
        content: `确认要'归档'吗？`,
        onOk() {
          that.pigeonholeLoading = true;
          that.operation('4');
          that.pigeonholeLoading = false;
        },
        cancelText: '取消',
      });
    },
    /**
     * 终止
     */
    terminationHandler() {
      this.terminationModalVisible = true;
    },
    tabCallback() {
      console.log('切换啊切换');
      this.taskInfo();
    },
    handleImageUpload() {},
  },
};
</script>

<style lang="less" scoped>
.instruction-detail {
  margin: 24px;
  /deep/.ant-tabs-bar {
    margin-bottom: 0;
    background: transparent;
  }
  /deep/.ant-tabs-content {
    background: #ffffff;
  }
  /deep/.bd3001-page-wrapper-container {
    margin-bottom: 0;
  }
}
.task-detail-buttons {
  background: #fff;
  padding: 24px;
  display: flex;
  button {
    margin-right: 10px;
  }
}
</style>
