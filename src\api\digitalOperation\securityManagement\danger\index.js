import { request } from '@/utils/request/requestTkb';

/**
 *
 * @param {*} data
 * @returns
 */
export function getSafeDangerList(data = {}) {
  return request({
    url: '/danger/safe/getSafeDangerList',
    method: 'post',
    data,
  });
}

/**
 *
 * @param {*} data
 * @returns
 */
export function addSafeDanger(data = {}) {
  return request({
    url: '/danger/safe/addSafeDanger',
    method: 'post',
    data,
  });
}
/**
 *
 * @param {*} data
 * @returns
 */
export function updateSafeDanger(data = {}) {
  return request({
    url: '/danger/safe/updateSafeDanger',
    method: 'post',
    data,
  });
}
/**
 *
 * @param {*} data
 * @returns
 */
export function deleteSafeDanger(data = {}) {
  return request({
    url: '/danger/safe/deleteSafeDanger',
    method: 'post',
    data,
  });
}

/**
 * 填写整改情况接口
 * @param {*} data
 * @returns
 */
export function fillRectification(data = {}) {
  return request({
    url: '/danger/safe/fillRectification',
    method: 'post',
    data,
  });
}

/**
 * 查看整改情况详情
 * @param {*} dangerId
 * @returns
 */
export function getDangerDetail(data = {}) {
  return request({
    url: '/danger/safe/getDetail',
    method: 'post',
    data,
  });
}

/**
 * 获取安全隐患排查统计表
 * @param {*} startTime
 * @param {*} endTime
 * @returns
 */
export function getCheckStatistics(data = {}) {
  return request({
    url: '/danger/safe/getCheckStatistics',
    method: 'post',
    data,
  });
}

/**
 * 获取整改情况统计表
 * @param {*} startTime
 * @param {*} endTime
 * @returns
 */
export function getRectificationStatistical(data = {}) {
  return request({
    url: '/danger/safe/getRectificationStatistical',
    method: 'post',
    data,
  });
}

/**
 * 导出整改情况统计表
 * @param {*} startTime
 * @param {*} endTime
 * @returns
 */
export function downloadRectificationStatistical(data = {}) {
  return request({
    url: '/danger/safe/downloadRectificationStatistical',
    method: 'post',
    data,
    responseType: 'blob',
  });
}

/**
 * 导出整改信息维护列表
 * @param {*} startTime
 * @param {*} endTime
 * @returns
 */
export function downloadInformation(data = {}) {
  return request({
    url: '/danger/safe/downloadInformation',
    method: 'post',
    data,
    responseType: 'blob',
  });
}

/**
 * 导出隐患排查统计表
 * @param {*} startTime
 * @param {*} endTime
 * @returns
 */
export function downloadSafeDangerStatistics(data = {}) {
  return request({
    url: '/danger/safe/downloadSafeDangerStatistics',
    method: 'post',
    data,
    responseType: 'blob',
  });
}

/**
 * 根据园区id 模糊搜索企业
 * @param {*} pageNum
 * @param {*} limit
 * @param {*} parkId
 * @param {*} enterpriseName
 *
 */

export function getEnterpriseByName(data = {}) {
  return request({
    url: '/danger/safe/getEnterpriseByName',
    method: 'post',
    data,
  });
}
