<template>
  <div class="ps-container">
    <p class="content">
      目前，太湖湾科创城在建项目<b>{{ form.oneValue || 0 }}</b
      >个，正在实施的超过一定规模的危大工程<b>{{ form.twoValue || 0 }}</b
      >个，备案登使用中大型设备<b>{{ form.threeValue || 0 }}</b
      >台，其中塔吊<b>{{ form.fourValue || 0 }}</b
      >台、施工升降机<b>{{ form.fiveValue || 0 }}</b
      >台，吊篮<b>{{ form.sixValue || 0 }}</b
      >台。
    </p>
    <p class="content">
      2023年度太湖湾科创城小微工程备案项目<b>{{ icpProjects || 0 }}</b
      >个，其中国有园区<b>{{ minForm.twoValue || 0 }}</b
      >个，民营园区<b>{{ minForm.threeValue || 0 }}</b
      >个，已备案未完工项目<b>{{ minForm.oneValue || 0 }}</b
      >个。
    </p>
    <a-form-model
      :model="form"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      @submit="handleSubmit"
    >
      <h3>报监项目</h3>
      <a-row type="flex">
        <a-col :span="8">
          <a-form-model-item label="在建项目">
            <a-input
              type="number"
              v-model="form.oneValue"
              suffix="个"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="危大工程">
            <a-input
              type="number"
              v-model="form.twoValue"
              suffix="个"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="大型设备">
            <a-input
              type="number"
              v-model="form.threeValue"
              suffix="个"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="塔吊">
            <a-input
              type="number"
              v-model="form.fourValue"
              suffix="个"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="升降机">
            <a-input
              type="number"
              v-model="form.fiveValue"
              suffix="台"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="吊篮">
            <a-input
              type="number"
              v-model="form.sixValue"
              suffix="台"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <a-form-model
      :model="minForm"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      @submit="handleSubmit"
    >
      <h3>小微工程</h3>
      <a-row type="flex">
        <a-col :span="8">
          <a-form-model-item label="在建项目">
            <a-input
              type="number"
              v-model="minForm.oneValue"
              suffix="个"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="国有园区">
            <a-input
              type="number"
              v-model="minForm.twoValue"
              suffix="个"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="社会园区">
            <a-input
              type="number"
              v-model="minForm.threeValue"
              suffix="个"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="建筑装修">
            <a-input
              type="number"
              v-model="minForm.fourValue"
              suffix="个"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="高危作业">
            <a-input
              type="number"
              v-model="minForm.fiveValue"
              suffix="个"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="绿化提升">
            <a-input
              type="number"
              v-model="minForm.sixValue"
              suffix="个"
              placeholder="请输入数量"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-button type="primary" html-type="submit">保存</a-button>
    </a-form-model>
  </div>
</template>

<script>
import {
  statisticAll,
  projectManageAdd,
  queryCount,
} from '@/api/digitalOperation/securityManagement/buildingSafety/projectStatistics.js';

export default {
  name: 'ManagementTkProjectStatistics',

  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      //备案项目，需接口获取
      icpProjects: 0,
      form: {
        oneValue: undefined,
        twoValue: undefined,
        threeValue: undefined,
        fourValue: undefined,
        fiveValue: undefined,
        sixValue: undefined,
        type: '1', //1:报监项目  2:小微工程
      },
      minForm: {
        oneValue: undefined,
        twoValue: undefined,
        threeValue: undefined,
        fourValue: undefined,
        fiveValue: undefined,
        sixValue: undefined,
        type: '2', //1:报监项目  2:小微工程
      },
    };
  },

  mounted() {
    this.statisticAll();
    this.queryCount();
  },

  methods: {
    //查询
    async statisticAll() {
      const [res, err] = await statisticAll();
      if (err) return;
      if (res.data.length) {
        this.form = res.data[0];
        this.minForm = res.data[1];
      }
    },
    //完结数量
    async queryCount() {
      const [res, err] = await queryCount();
      if (err) return;
      this.icpProjects = res.data;
    },
    //保存
    async projectManageAdd(p) {
      const [, err] = await projectManageAdd(p);
      if (err) return this.$message.error('保存失败');
      this.$message.success('保存成功');
      this.statisticAll();
      this.queryCount();
    },

    handleSubmit() {
      let params = [{ ...this.form }, { ...this.minForm }];
      this.projectManageAdd(params);
    },
  },
};
</script>

<style lang="less" scoped>
.ps-container {
  background: #fff;
  padding: 24px;
  .content {
    font-size: 18px;
    b {
      font-size: 20px;
      color: red;
    }
  }
}
</style>
