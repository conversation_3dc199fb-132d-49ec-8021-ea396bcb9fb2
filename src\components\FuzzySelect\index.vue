<template>
  <a-select
    ref="select"
    :allowClear="false"
    :filterOption="false"
    :showSearch="true"
    placeholder="请选择"
    v-model="modelValue"
    :notFoundContent="null"
    @search="handleSearch"
    style="width: 100%"
    v-bind="$attrs"
    v-on="$listeners"
    @change="handlerChange"
  >
    <!-- :showArrow="false"  -->
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData || {}"></slot>
    </template>
    <a-select-option
      v-for="item in companyList"
      :key="item.value"
      :value="item.value"
    >
      {{ item.label }}
    </a-select-option>
  </a-select>
</template>

<script>
import { debounce } from 'lodash';
import { getEnterpriseList } from '@/api/basicData/index.js';
export default {
  props: {
    value: {
      type: String,
      default: '',
    },
    searchCallback: {
      type: Function,
    },
  },
  components: {},
  data() {
    return {
      companyList: [],
      ifFirst: false,
    };
  },
  computed: {
    modelValue: {
      get() {
        return this.value || undefined;
      },
      set(val) {
        this.$emit('input', val);
      },
    },
  },
  watch: {
    value: {
      handler(n) {
        if (n && !this.ifFirst) {
          this.ifFirst = true;
          this.handleSearch(n);
        }
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleSearch: debounce(async function (val) {
      if (this.searchCallback && typeof this.searchCallback === 'function') {
        const res = await this.searchCallback(val);
        this.companyList = res || [];
      } else {
        const [res] = await getEnterpriseList({
          limit: 100,
          pageNum: 1,
          name: val,
        });
        if (res && res.data) {
          this.companyList = res.data.map((q) => {
            return {
              ...q,
              label: q.name,
              value: q.unifiedCreditCode,
            };
          });
        } else {
          this.companyList = [];
        }
      }
    }, 500),
    handlerChange(val) {
      if (val) {
        this.$emit(
          'changeSelect',
          this.companyList.find((q) => q.value == val)
        );
      } else {
        return null;
      }
    },
  },
};
</script>

<style scoped lang="less"></style>
