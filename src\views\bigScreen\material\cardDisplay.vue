<template>
  <cardItem width="100%" :title="title">
    <div class="card-wrap">
      <cardSingle
        v-for="(item, index) in list"
        :key="index"
        :item="item"
      ></cardSingle>
    </div>
  </cardItem>
</template>

<script>
import cardSingle from './components/cardSingle.vue';
import cardItem from './components/cardItem.vue';
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
    },
  },
  components: { cardItem, cardSingle },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="less">
.card-wrap {
  display: flex;
  justify-content: space-around;
  align-items: center;
  overflow: auto;
}
</style>
