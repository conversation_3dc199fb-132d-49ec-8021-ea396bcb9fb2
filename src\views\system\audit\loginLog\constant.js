import moment from 'moment';

export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'userName',
      title: '用户',
      props: {
        placeholder: '请输入用户',
      },
    },
    {
      field: 'ip',
      title: 'IP地址',
      props: {
        placeholder: '请输入IP地址',
      },
    },
    {
      field: 'time',
      title: '登录时间',
      element: 'a-range-picker',
      props: {
        showTime: {
          defaultValue: [
            moment('00:00:00', 'HH:mm:ss'),
            moment('23:59:59', 'HH:mm:ss'),
          ],
        },
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { userName: '', ip: '', time: ['', ''] },
};

// 表头
export const tableColumn = [
  { field: 'userName', title: '用户名' },
  { field: 'browser', title: '浏览器' },
  { field: 'os', title: '操作系统' },
  { field: 'ip', title: 'IP地址' },
  { field: 'remark', title: '登录状态' },
  { field: 'menuName', title: '操作信息', showOverflow: true },
  {
    field: 'createTime',
    title: '登录时间',
    width: 190,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '详情',
    width: 90,
    slots: { default: 'operate' },
  },
];
