<template>
  <BuseCrud
    ref="crud"
    title="查看详情"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @loadData="loadData"
  >
    <template #defaultHeader>
      <a-button type="primary" :loading="exportLoading" @click="handleExport"
        >导出</a-button
      >
    </template>
    <template slot="pageCenter">
      <a-row class="page-center-overview">
        <a-col :span="8">
          <dl>
            <dt class="title">接收总量</dt>
            <dd class="desc">{{ data.totalRecive ?? '/' }}</dd>
          </dl>
        </a-col>
        <a-col :span="8">
          <dl>
            <dt class="title">查看总量</dt>
            <dd class="desc">{{ data.totalRead ?? '/' }}</dd>
          </dl></a-col
        >
        <a-col :span="8">
          <dl>
            <dt class="title">查看率</dt>
            <dd class="desc">
              {{
                data.readRate || data.readRate === 0 ? data.readRate + '%' : '/'
              }}
            </dd>
          </dl>
        </a-col>
      </a-row>
    </template>
  </BuseCrud>
</template>

<script>
import { getDicts } from '@/api/system/dict/data';
import {
  pushDetail,
  pushDetailStatistics,
  exportArticlePushInfo,
} from '@/api/digitalOperation/managementCenter/articleManagement/index.js';
import { saveAs } from 'file-saver';
export default {
  name: 'ManagementTkViewDetail',

  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          title: '接收时间',
          field: 'receiveTime',
        },
        {
          title: '企业名称',
          field: 'name',
        },
        {
          title: '姓名',
          field: 'userName',
        },
        {
          title: '手机号码',
          field: 'account',
        },
        {
          title: '状态',
          field: 'alreadyRead',
          slots: {
            default: ({ row }) => {
              return row.alreadyRead == 1 ? (
                <span style="color: #70B603;">已查看</span>
              ) : (
                <span style="color: #F59A23;">未查看</span>
              );
            },
          },
        },
      ],
      tableData: [],
      viewStates: [],
      params: {
        alreadyRead: undefined,
        pageNum: 1,
        limit: 10,
      },
      data: [], //推送数据统计
      exportLoading: false,
    };
  },
  computed: {
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            title: '状态',
            field: 'alreadyRead',
            element: 'a-select',
            props: {
              options: this.viewStates,
            },
          },
        ],
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        menu: false,
      };
    },
  },
  created() {
    this.getViewState();
  },

  mounted() {
    this.loadData();
  },

  methods: {
    async handleExport() {
      this.exportLoading = true;
      const [res, err] = await exportArticlePushInfo({
        articleId: this.$route.query.id,
      });
      this.exportLoading = false;
      if (err) return;
      const blob = new Blob([res], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, '推送详情.xlsx');
    },
    //列表查询
    async loadData() {
      this.loading = true;
      this.pushDetailStatistics();
      const [res, err] = await pushDetail({
        articleId: this.$route.query.id,
        ...this.params,
        pageNum: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      });
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    //推送统计
    async pushDetailStatistics() {
      const [res, err] = await pushDetailStatistics({
        id: this.$route.query.id,
      });
      if (err) return;
      this.data = res.data;
    },
    async getViewState() {
      const [res, err] = await getDicts(['view_status']);
      if (err) return;
      this.viewStates = res.data.map((item) => {
        return {
          value: item.dictValue,
          label: item.dictLabel,
        };
      });
    },
    handleSubmit(e) {
      e.preventDefault();
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('Received values of form: ', values);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page-center-overview {
  background: #fff;
  height: 112px;
  margin: 16px 0;
  border-radius: 2px;
  dl {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: left;
    height: 112px;
    padding: 0 24px;
    text-align: left;
    .title {
      opacity: 0.65;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      line-height: 22px;
      margin-bottom: 6px;
    }
    .desc {
      opacity: 0.85;
      font-size: 30px;
      color: #000000;
      line-height: 38px;
      margin-bottom: 0;
    }
  }
}
</style>
