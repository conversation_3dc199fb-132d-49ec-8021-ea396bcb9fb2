<template>
  <a-modal
    width="600px"
    :title="modelTitle === 'add' ? '新增' : '编辑'"
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm ref="ruleForm" :config="formConfig" :params="formValue">
        <!-- 企业 -->
        <template #nameSlot>
          <a-select
            :allowClear="true"
            :filterOption="false"
            :showSearch="true"
            placeholder="请选择"
            v-model="formValue.enterprise"
            :notFoundContent="null"
            @change="handleSearch"
            @search="handleSearch"
            :labelInValue="true"
          >
            <a-select-option
              v-for="item in companyList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import { initFormValue } from '../constant';
import { saveIndepEnterprise, editIndepEnterprise } from '@/api/basicData';
import { institutionsMixin } from '../../mixins/institutionsMixin';

export default {
  props: ['visible', 'detail', 'isLook', 'modelTitle'],
  mixins: [institutionsMixin],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.companyList = [];
          if (!this.detail) return;
          if (this.modelTitle !== 'see') {
            this.formValue.enterprise = {
              label: this.detail.enterpriseName,
              key: this.detail.enterpriseId,
            };
          } else {
            this.formValue.enterprise = this.detail.enterpriseName;
          }
          this.formValue.enterpriseName = this.detail.enterpriseName;
          this.formValue.enterpriseId = this.detail.enterpriseId;
          this.companyList = [
            {
              label: this.formValue.enterpriseName,
              value: this.formValue.enterpriseId,
            },
          ];
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
            ...{ enterprise: this.formValue.enterprise },
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
      formConfig: [
        {
          field: 'enterprise',
          title: '企业名称',
          element: 'slot',
          slotName: 'nameSlot',
          rules: [{ required: true, message: '请输入企业名称' }],
        },
        {
          field: 'address',
          title: '企业地址',
          element: 'a-input',
          rules: [{ required: true, message: '请输入企业地址' }],
        },
        {
          field: 'contactPerson',
          title: '联系人',
          element: 'a-input',
        },
        {
          field: 'phone',
          title: '电话',
          element: 'a-input',
        },
        {
          field: 'enterpriseNum',
          title: '人数',
          element: 'a-input',
        },
      ],
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      console.log(2222);
      this.formConfig[0].props.options = this.stateSelect;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.formValue.enterpriseId = this.formValue.enterprise.key;
          this.formValue.enterpriseName = this.formValue.enterprise.label;
          if (this.modelTitle === 'add') {
            this.saveIndepEnterprise();
          } else {
            this.editIndepEnterprise();
          }
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 新增
    async saveIndepEnterprise() {
      const [, err] = await saveIndepEnterprise(this.formValue);
      if (err) return;
      this.$message.success('新增成功!');
      this.$emit('loadData');
    },
    // 编辑
    async editIndepEnterprise() {
      const [, err] = await editIndepEnterprise(this.formValue);
      if (err) return;
      this.$message.success('编辑成功!');
      this.$emit('loadData');
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
