<template>
  <div class="full-screen"><screen :url="url"></screen></div>
</template>

<script>
import screen from '@/components/bigScreen';
import getBigScreenUrl from '@/global/bigScreen/bigScreenUrl';

export default {
  name: 'parkSecurityScreen',
  components: { screen },
  data() {
    return {
      url: getBigScreenUrl.call(this),
    };
  },
  mounted() {
    document.title = '本质安全一张图';
  },
};
</script>

<style scoped></style>
