import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import moment from 'moment';
import 'moment/locale/zh-cn'; //  moment 中文语言包
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/antd.less';
import VxeUI from 'vxe-pc-ui';
import 'vxe-pc-ui/lib/style.css';
import VxeUITable from 'vxe-table';
import 'vxe-table/lib/style.css';
import directive from '@/global/directive';
import { getDicts } from '@/api/system/dict';
import CustomComponent from '@/global/component';
import bootstrap from '@/bootstrap';
import './global/style/index.less';
import ProComponents from '@bangdao/pro-components';
import VueClipBoard from 'vue-clipboard2';
import VueContextMenu from 'vue-contextmenu';
import 'font-awesome/css/font-awesome.min.css';
import zh_CN from 'ant-design-vue/lib/locale-provider/zh_CN';
import * as echarts from 'echarts';
import 'core-js/modules/es.array.at.js';

Vue.prototype.$echarts = echarts;
Vue.use(ProComponents);
Vue.use(VueContextMenu);

import {
  QueryListBasePage,
  BaseForm,
  BuseCrud,
  BuseRangePicker,
  DynamicForm,
} from '@bangdao/buse-components';
Vue.use(DynamicForm);
Vue.use(QueryListBasePage);
Vue.use(BaseForm);
Vue.use(BuseCrud);
Vue.use(BuseRangePicker);
// 字典数据组件
import DictData from '@/components/DictData';
// 全局方法挂载
Vue.prototype.getDicts = getDicts;
//图片基本前缀路径
Vue.prototype.baseImgUrl = process.env.VUE_APP_BASE_API_IMG;
// 字典数据组件
import useDict from '@/utils/hooks/dict';
// 全局方法挂载
Vue.prototype.useDict = useDict;

// 使用moment中文语言包
moment.locale('zh-cn');
Vue.prototype.$zhCN = zh_CN;

// 注册antd-vue组件库
Vue.use(Antd);

// 注册vxe-table组件库
Vue.use(VxeUI);
Vue.use(VxeUITable);

// 注册自定义指令
Vue.use(directive);

// 注册自定义组件
Vue.use(CustomComponent);

// 使用复制功能
Vue.use(VueClipBoard);

bootstrap({ router, store, message: Vue.prototype.$message });

DictData.install();

Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount('#app');
