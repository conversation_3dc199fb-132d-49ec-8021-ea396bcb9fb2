<template>
  <div class="ranking-chart-box">
    <h2>最近检查合格率排名</h2>
    <p class="pass-tip">
      总符合率：<b>{{ coincidenceRate }}%</b>
    </p>
    <BaseChart
      class="chart-box"
      v-if="rankingDataForEchart.yAxis.length > 0"
      :yAxis="rankingDataForEchart.yAxis"
      :seriesData="rankingDataForEchart.seriesData"
      :tooltipFormatter="tooltipFormatter"
    />
    <echartEmptyBox v-else />
  </div>
</template>

<script>
import echartEmptyBox from '@/components/echartEmptyBox/index.vue';
import BaseChart from '@/components/chart/lineChartForFSRanking.vue';
import { getColors, getTemplateBase } from '../echart.js';
// import { mockDataForRanking } from '../mockData';
import { fireSafetyCoincidenceRate } from '@/api/digitalOperation/securityManagement/parkSafety/echart/FS.js';
export default {
  name: 'ManagementTkRanking',
  components: {
    BaseChart,
    echartEmptyBox,
  },

  data() {
    return {
      mockDataForRanking: [],
      coincidenceRate: '',
    };
  },
  computed: {
    //根据接口数据获取图表所需数据
    rankingDataForEchart() {
      const resolvedData = {
        data: this.mockDataForRanking
          .map((item) => {
            return item.checkRate;
          })
          .reverse(),
        yAxis: this.mockDataForRanking
          .map((item) => {
            return item.name;
          })
          .reverse(),
      };
      const barColors = getColors(resolvedData.data.length);
      const seriesData = [
        {
          name: '符合率',
          type: 'bar',
          barMaxWidth: '80%',
          itemStyle: {
            color: ({ dataIndex }) => {
              return barColors[resolvedData.data.length - dataIndex - 1];
            },
          },
          data: resolvedData.data,
        },
      ];
      const yAxis = resolvedData.yAxis;
      return {
        seriesData,
        yAxis,
      };
    },
  },
  create() {},

  mounted() {
    this.getRankingData();
  },

  methods: {
    tooltipFormatter(info) {
      let str = `<div style="text-align: left; color:#1D2129;" >${info[0].name}</div>`;
      const itemInfo =
        this.mockDataForRanking.filter(
          (item) => item.name === info[0].name
        )[0] || {};
      str += getTemplateBase('', '检查单位数量', itemInfo.unitNumber);
      // str += getTemplateBase('', '总检查项', itemInfo.checkTotal);
      str += getTemplateBase('', '合格数量', itemInfo.checkPass);
      info.forEach((item) => {
        str += getTemplateBase(item.marker, item.seriesName, item.value + '%');
      });
      return str;
    },
    //获取排名数据
    async getRankingData() {
      const [res, err] = await fireSafetyCoincidenceRate();
      if (err) return;
      let arr = [];
      res.data.list.forEach((item) => {
        arr.push({
          name: item.parkName,
          unitNumber: item.unitNums,
          // checkTotal: item.inspectionItems,
          checkPass: item.compliantUnitNums,
          checkRate: item.coincidenceRate,
        });
      });
      this.mockDataForRanking = arr;
      this.coincidenceRate = res.data.coincidenceRate;
    },
  },
};
</script>

<style lang="less" scoped>
.ranking-chart-box {
  margin: 16px;
  height: auto;
  height: 800px;
  .pass-tip {
    font-size: 16px;
  }
}
</style>
