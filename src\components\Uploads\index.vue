<template>
  <div>
    <a-form :form="form">
      <a-upload
        list-type="picture"
        :accept="accept"
        :show-upload-list="showUploadList"
        :default-file-list="fileList"
        :fileList="fileList"
        :customRequest="customRequestPic"
        :before-upload="beforeUploadPic"
        @change="handleChangePic"
      >
        <a-button :disabled="fileList.length == 1">
          <a-icon type="upload" /> 上传
        </a-button>
      </a-upload>
    </a-form>
  </div>
</template>

<script>
import { fileUpload } from '@/api/upload/index.js';
export default {
  props: {
    accept: {
      type: String,
      default: '',
    },
    showUploadList: {
      type: <PERSON><PERSON><PERSON>,
      default() {
        return true;
      },
    },
    //弹框类型
    disabled: {
      type: Boolean,
      default: false,
    },

    maxSize: {
      type: Number,
      default() {
        return 10485760; //10M
      },
    },
    fileListTemp: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      fileList: [],
    };
  },

  mounted() {},
  watch: {
    fileListTemp(val) {
      console.log('文件改变', val);
      if (val) {
        this.fileList = val;
      }
    },
  },
  methods: {
    //文件回显和数据处理
    beforeUploadPic(file) {
      this.isLt1M = file.size / 1024 / 1024 < this.maxSize; //默认10M
      if (!this.isLt1M) {
        console.log(file, 'file------');
        this.uploadFlag = false;
        this.$message.error(`文件大小不能超过 ${this.maxSize}M `);
      }
    },
    handleChangePic(info) {
      console.log(info, '0000000');
      this.fileList = info.fileList;
      if (!this.isLt1M) {
        this.fileList = this.fileList.pop();
        return;
      }

      if (info.file.status === 'uploading') {
        this.loading = true;
        return;
      }
      if (info.file.status === 'done') {
        const fileReader = new FileReader();
        fileReader.readAsDataURL(info.file.originFileObj);
        this.getBase64(info.file.originFileObj, (imageUrl) => {
          this.imageUrl = imageUrl;
          this.loading = false;
        });
      }
    },
    getBase64(img, callback) {
      const reader = new FileReader();
      reader.addEventListener('load', () => callback(reader.result));
      reader.readAsDataURL(img);
    },
    customRequestPic(data) {
      // 上传提交
      if (!this.isLt1M) {
        return false;
      }
      const formData = new FormData();
      formData.append('file', data.file);
      this.uploadfilePic(formData, data);
    },
    // 文件上传
    async uploadfilePic(formData, data) {
      const [res] = await fileUpload({ file: data.file });
      console.log(res, 'obj----');
      if (res.code === '10000') {
        const responseInfo = {
          fileName: res.data.fileName,
        };
        data.onSuccess(responseInfo, data.file);
        console.log('33333390', responseInfo);
        this.$emit('setNewsImg', responseInfo);
        this.$message.success('文件保存成功');
      } else {
        this.$emit('setNewsImg', null);
        this.$message.error('文件保存失败');
      }
    },
  },
};
</script>
<style scoped lang="less">
/deep/.ant-upload-list-picture .ant-upload-list-item,
/deep/.ant-upload-list-picture-card .ant-upload-list-item {
  // position: relative;
  // height: 66px;
  // padding: 8px;
  border: 0;
}
</style>
