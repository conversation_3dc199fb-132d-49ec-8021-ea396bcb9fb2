<template>
  <div class="action-echart-box">
    <h2>各类行动检查情况</h2>
    <p class="pass-tip">
      被检查单位<b>{{ total }}</b
      >个
    </p>
    <BaseChart
      class="chart-box"
      v-if="actionsResultData.axisData.length > 0"
      :axisData="actionsResultData.axisData"
      :seriesData="actionsResultData.seriesData"
      :yAxis="yAxis"
      :tooltipFormatter="tooltipFormatter"
      unit="符合率"
    />
    <echartEmptyBox v-else />
  </div>
</template>

<script>
import echartEmptyBox from '@/components/echartEmptyBox/index.vue';
import BaseChart from '@/components/chart/lineChart.vue';
import { getTemplateBase } from '../echart';
import { industryVariousAct } from '@/api/digitalOperation/securityManagement/parkSafety/echart/IE.js';
export default {
  name: 'ManagementTkActions',
  components: {
    echartEmptyBox,
    BaseChart,
  },

  data() {
    return {
      total: 0,
      actionsResultData: {
        axisData: [],
        seriesData: [
          {
            name: '近期符合率',
            type: 'bar',
            data: [],
          },
          {
            name: '历史符合率',
            type: 'line',
            data: [],
          },
        ],
      },
    };
  },
  created() {
    this.yAxis = {
      type: 'value',
      nameGap: 40,
      nameTextStyle: {
        // 字体样式
        padding: [0, -60, 0, 0],
        color: 'rgba(0,0,0,0.45)',
        fontSize: 14, // 字体大小
      },
      axisLabel: {
        formatter: '{value}%',
      },
    };
  },

  mounted() {
    this.getActionsData();
  },

  methods: {
    async getActionsData() {
      const [res, err] = await industryVariousAct();
      if (err) return;
      console.log(res.data, '9999909.0');
      this.total = res.total;
      let axisData = [];
      let seriesData0 = [];
      let seriesData1 = [];
      res.data.forEach((item) => {
        axisData.push(item.name);
        seriesData0.push(item.coincidenceRate);
        seriesData1.push(item.historyCoincidenceRate);
      });

      setTimeout(() => {
        this.actionsResultData.axisData = axisData;
        this.actionsResultData.seriesData[0].data = seriesData0;
        this.actionsResultData.seriesData[1].data = seriesData1;
      }, 500);
      //TODO:获取近期和历史符合率数据
    },
    tooltipFormatter(info) {
      let str = `<div style="text-align: left; color:#1D2129;" >${info[0].name}</div>`;
      info.forEach((item) => {
        str += getTemplateBase(
          item.marker,
          item.seriesName,
          item.value || 0 + '%'
        );
      });
      return str;
    },
  },
};
</script>

<style lang="less" scoped>
.action-echart-box {
  margin: 16px;
}
</style>
