<template>
  <div>
    <a target="_blank" :href="baseImgUrl + attachment" v-if="attachment">
      <img
        class="attachment-img"
        :src="FileImgs[getFileType(attachment)]"
        alt=""
      />
      <span>{{ getFileName(attachment) }}</span>
    </a>
  </div>
</template>

<script>
import FileImgs from '@/assets/config/art_detail.js';
import { getFileName, getFileType } from '@/utils/index';

export default {
  name: 'downLoadTemplate',
  props: {
    attachment: {
      type: String,
      default() {
        return '';
      },
    },
  },
  data() {
    return {
      FileImgs,
      getFileName,
      getFileType,
    };
  },
  created() {},

  mounted() {},

  methods: {},
};
</script>
<style scoped lang="less">
.attachment-img {
  width: 30px;
  height: 30px;
}
</style>
