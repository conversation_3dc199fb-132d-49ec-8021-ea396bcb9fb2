<template>
  <a-modal
    width="600px"
    :title="
      modelTitle === 'add' ? '新增' : modelTitle === 'see' ? '详情' : '编辑'
    "
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="ruleForm"
        :config="formConfig"
        :params="formValue"
        :preview="preview"
      >
        <!-- 年份插槽 -->
        <template #year>
          <BuseRangePicker
            type="year"
            :needShowSecondPicker="() => false"
            v-model="formValue.rateTime"
            format="YYYY"
            :disableDateFunc="disableDateFunc"
          />
        </template>
        <!-- 单选按钮组 -->
        <template #radioSlot>
          <a-radio-group
            name="radioGroup"
            :default-value="1"
            v-model="formValue.level"
          >
            <a-radio
              v-for="item in levelList"
              :key="item.value"
              :value="item.value"
              >{{ item.label }}</a-radio
            >
          </a-radio-group>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment';
import { initFormValue } from '../constant';
import { addResearch, editResearch } from '@/api/basicData';
export default {
  props: ['visible', 'detail', 'preview', 'isLook', 'modelTitle', 'buildState'],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          let rateTime = undefined;
          if (this.modelTitle === 'see') {
            rateTime = moment(this.detail.rateTime).format('YYYY-MM');
          } else {
            rateTime = {
              endOpen: false,
              endValue: null,
              startOpen: true,
              startValue: moment(this.detail.rateTime),
            };
          }
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
            rateTime: rateTime,
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
      levelList: [
        { label: '市级、省级', value: '1' },
        { label: '市级', value: '2' },
        { label: '省级', value: '3' },
        { label: '其它', value: '4' },
      ],
    };
  },
  computed: {
    formConfig() {
      const formConfig = [
        {
          field: 'rateTime',
          title: '日期',
          element: 'slot',
          slotName: 'year',
          rules: [
            { required: true, message: '请输入' },
            {
              validator: (rule, value, callback) => {
                console.log(value, 'value');
                if (value && value.startValue) {
                  callback();
                } else {
                  callback('请选择年份');
                }
              },
              trigger: 'blur',
            },
          ],
        },
        {
          field: 'organizationName',
          title: '机构名称',
          element: 'a-input',
          props: {
            placeholder: '请输入',
          },
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'level',
          title: '机构级别',
          element: 'slot',
          slotName: 'radioSlot',
          // element: 'a-radio-group',
          // props: {
          // options: [
          //   { label: '市级、省级', value: '1' },
          //   { label: '市级', value: '2' },
          //   { label: '省级', value: '3' },
          //   { label: '其它', value: '4' },
          // ],
          // },
          on: {
            change(form, e) {
              form.level = e.target?.value;
            },
          },
          preview: {
            formatter: (row) => {
              return this.getStatus(row.level);
            },
          },
          rules: [{ required: true, message: '请选择' }],
        },
        {
          field: 'creationComplete',
          title: '建设情况',
          element: 'a-select',
          props: {
            options: this.buildState,
            showSearch: true,
            optionFilterProp: 'children',
          },
          preview: {
            formatter: (row) => {
              return row.creationComplete === '1'
                ? '规划建设'
                : row.creationComplete === '2'
                ? '建成启用'
                : row.creationComplete === '3'
                ? '其它'
                : '';
            },
          },
          rules: [{ required: true, message: '请输入' }],
        },
      ];
      return formConfig;
    },
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      console.log(2222);
      this.formConfig[0].props.options = this.stateSelect;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      if (this.modelTitle === 'see') {
        this.handleCancel();
        return;
      }
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.formValue.rateTime = moment(
            this.formValue.rateTime?.startValue
          ).format('YYYY');
          if (this.modelTitle === 'add') {
            this.addResearch();
          } else {
            this.editResearch();
          }
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 调用父组件方法
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    // 新增
    async addResearch() {
      const [, err] = await addResearch(this.formValue);
      if (err) return;
      this.$message.success('新增成功!');
      this.$emit('loadData');
    },
    // 编辑
    async editResearch() {
      const [, err] = await editResearch(this.formValue);
      if (err) return;
      this.$message.success('编辑成功!');
      this.$emit('loadData');
    },
    getStatus(code) {
      switch (code) {
        case '1':
          return '市级、省级';
        case '2':
          return '市级';
        case '3':
          return '省级';
        case '4':
          return '其它';
      }
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
