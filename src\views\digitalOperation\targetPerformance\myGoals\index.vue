<!-- 给B端使用 -->
<template>
  <BuseCrud
    ref="crud"
    title="目标列表"
    :loading="loading"
    :filterOptions="filterOptions"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalCancel="modalCancelHandler"
    @loadData="loadData"
  >
    <template slot="defaultHeader">
      <a-button type="default" class="mr10" @click="exportData">导出</a-button>
    </template>
    <template slot="year">
      <BuseRangePicker
        type="year"
        v-model="filterParams.year"
        :needShowSecondPicker="() => false"
        format="YYYY"
      />
    </template>
    <template slot="yearSlot" slot-scope="{ params }">
      <BuseRangePicker
        type="year"
        v-model="params.year"
        :needShowSecondPicker="() => false"
        format="YYYY"
      />
    </template>
    <template slot="stageDate" slot-scope="{ params }">
      <p v-if="typeof params.stageDate === 'string'">{{ params.stageDate }}</p>
      <p v-else>
        <span
          class="mr10"
          v-for="(dateItem, index) in params.stageDate"
          :key="index"
          >{{ dateItem }}</span
        >
      </p>
    </template>
  </BuseCrud>
</template>

<script>
import { enumList } from '@/api/digitalOperation/targetPerformance/indexEnumeration/index.js';
import {
  list,
  downloadInformation,
} from '@/api/digitalOperation/targetPerformance/myGoals/index.js';
import { getTableColumns } from './myGoals';
import { resolveBlob } from '@/utils/common/fileDownload';
export default {
  name: 'TargetManagement',
  data() {
    return {
      menuShow: true,
      tableData: [],
      filterParams: {
        year: undefined,
        oneTargetId: undefined,
        twoTargetId: undefined,
      },
      loading: false,
      oneTargetList: [], //一级指标
      twoTargetList: [], //二级指标
      okBtnShow: false, //是否展示ok按钮
      userInfo: null,
      viewModel: false, //是否是查看
      // tablePage: { total: 0, currentPage: 1, pageSize: 10 },
    };
  },
  created() {
    this.tableColumn = getTableColumns.call(this);
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            title: '目标年份',
            field: 'year',
            element: 'slot',
            slotName: 'year',
          },
          {
            title: '一级指标',
            field: 'oneTargetId',
            element: 'a-select',
            props: {
              options: this.oneTargetList,
            },
            on: {
              change: this.oneTargetChange,
            },
          },
          {
            title: '二级指标',
            field: 'twoTargetId',
            element: 'a-select',
            props: {
              options: this.twoTargetList,
            },
          },
          {
            title: '需本月填报',
            field: 'needFill',
            element: 'a-select',
            props: {
              options: [
                { label: '全部', value: 2 },
                { label: '是', value: 1 },
                { label: '否', value: 0 },
              ],
            },
          },
        ],
        params: this.filterParams,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        editBtn: false,
        viewBtnText: '查看目标详情',
        submitBtn: true,
        delBtn: false,
        formConfig: [
          {
            field: 'year',
            title: '年份',
            element: 'slot',
            slotName: 'yearSlot',
            props: {
              disabled: this.viewModel,
            },
            rules: [
              {
                required: true,
                message: '请选择年份',
                validator: (rule, value, callback) => {
                  if (value.startValue) {
                    callback();
                  } else {
                    callback('请选择年份');
                  }
                },
              },
            ],
          },
          {
            title: '一级指标',
            field: 'oneTargetId',
            element: 'a-select',
            rules: [{ required: true, message: '请选择一级指标' }],
            props: {
              disabled: this.viewModel,
              options: this.oneTargetList,
            },
            on: {
              change: this.oneTargetChangeModel,
            },
          },
          {
            title: '二级指标',
            field: 'twoTargetId',
            element: 'a-select',
            rules: [{ required: true, message: '请选择二级指标' }],
            props: {
              disabled: this.viewModel,
              options: this.twoTargetList,
            },
          },
          {
            title: '我的目标',
            field: 'myGoal',
          },
          {
            title: '阶段性统计日期',
            field: 'stageDate',
            previewSlot: 'stageDate',
            colProps: {
              span: 24,
            },
            itemProps: {
              labelCol: { span: 4 },
              wrap: { span: 20 },
            },
          },
          {
            title: '补充说明',
            field: 'supplementary',
            colProps: {
              span: 24,
            },
            itemProps: {
              labelCol: { span: 3 },
              wrap: { span: 20 },
            },
          },
        ],
        customOperationTypes: [
          {
            title: '进度填报',
            typeName: 'targetAssignment',
            event: (row) => {
              this.$router.push({
                name: 'progressFilling',
                params: {
                  targetId: row.id,
                },
              });
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 12,
        },
      };
    },
  },
  mounted() {
    this.getEnumList();
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      const result = await list(this.filterParams);
      this.loading = false;
      this.tableData = result.data;
    },
    async getEnumList() {
      const [res, err] = await enumList();
      if (err) return;
      let data = res?.data;
      data = JSON.parse(
        JSON.stringify(data)
          .replace(/bpmTargetEnumTwoList/g, 'children')
          .replace(/targetName/g, 'label')
          .replace(/id/g, 'value')
      );
      this.oneTargetList = data;
      console.log(this.oneTargetList, 'this.oneTargetList');
    },
    //列表页一级指标change
    oneTargetChange(val) {
      this.filterParams.twoTargetId = undefined;
      this.filterTwoTarget(val);
    },
    //新增时一级指标change
    oneTargetChangeModel(val) {
      this.$refs.crud.$refs.modalView.formParams.twoTargetId = undefined;
      this.filterTwoTarget(val);
    },
    filterTwoTarget(val) {
      const currentBiz = this.oneTargetList.filter((item) => {
        return item.value === val;
      });
      this.twoTargetList = currentBiz[0] ? currentBiz[0].children : [];
    },
    modalCancelHandler() {},
    // 导出
    async exportData() {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
      let p = this.filterParamsHandle();
      const [res] = await downloadInformation(p);
      resolveBlob(res, mimeMap.xlsx, '目标列表', '.xls');
    },
  },
};
</script>

<style lang="less" scoped></style>
