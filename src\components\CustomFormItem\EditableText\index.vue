<template>
  <span
    >{{ value }}
    <a-button
      class="editable-link"
      v-if="canEdit && !disabled"
      type="link"
      icon="edit"
      @click="editEvent"
    ></a-button>
  </span>
</template>
<script>
export default {
  name: 'EditableText',
  props: {
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    canEdit: {
      type: Boolean,
      default: true,
    },
    editEvent: {
      type: Function,
      default() {
        return () => {};
      },
    },
  },
};
</script>
<style lang="less" scoped>
.editable-link {
  &:hover {
    border: none;
  }
}
</style>
