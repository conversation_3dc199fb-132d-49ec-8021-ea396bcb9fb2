import request from '@/utils/request';

// 查询租户列表
export function getMerchantList(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/merchant/list',
    method: 'get',
    params: query,
  });
}

// 根据租户编号获取详细信息
export function getMerchant(merchantId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/merchant/' +
      merchantId,
    method: 'get',
  });
}

// 查询所有租户下拉树结构
export function getAllMerchantTree(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/merchant/treeselect',
    method: 'get',
    params: query,
  });
}

// 新增租户
export function addMerchant(data) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/merchant/addMerchant',
    method: 'post',
    data,
  });
}

// 修改租户
export function updateMerchant(data) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/merchant/updateMerchant',
    method: 'post',
    data,
  });
}

// 删除租户
export function delMerchant(merchantId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/merchant/remove/' +
      merchantId,
    method: 'get',
  });
}

// 租户应用树
export function getMerchantAppTree(params = {}) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/merchant/appTree',
    method: 'get',
    params,
  });
}
// 租户的展示应用列表
export function getMerchantShowAppTree(params = {}) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/merchant/merchantAppTree',
    method: 'get',
    params,
  });
}
