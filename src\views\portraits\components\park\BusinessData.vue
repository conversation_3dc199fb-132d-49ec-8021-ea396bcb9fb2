<template>
  <PortraitCard :span="24" title="经营数据" :can-expand="true">
    <template slot="leftTitle">
      <span
        >经营数据
        <a-month-picker
          v-model="month"
          size="default"
          style="width: 140px; margin-left: 12px"
          placeholder="选择月份"
          valueFormat="YYYY-MM"
          :allowClear="false"
          :disabledDate="disabledDate"
          @change="handleChangeMonth"
        />
      </span>
    </template>
    <a-row>
      <a-col :span="6" class="detail">
        <span class="title"> 上月税收(本年累计)： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.tax">暂无数据</span>
          <template v-else
            ><span class="num">{{ pictureInfo.tax || '-' }}</span
            ><span class="unit">万元</span></template
          >
        </span>
      </a-col>
      <a-col :span="6" class="detail">
        <span class="title"> 税收排名： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.taxRank">暂无数据</span>
          <span class="num" v-else>No.{{ pictureInfo.taxRank || '-' }}</span>
        </span>
      </a-col>
      <a-col :span="12" class="detail">
        <span class="title"> 上月营收(本年累计)： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.incomeCurrPer">暂无数据</span>
          <template v-else>
            <span class="num">{{ pictureInfo.incomeCurrPer || '-' }}</span
            ><span class="unit">万元</span>
          </template>
        </span>
      </a-col>
      <a-col :span="6" class="detail">
        <span class="title"> 亩均税收(本年)： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.muAvgTax">暂无数据</span>
          <template v-else>
            <span class="num">{{ pictureInfo.muAvgTax || '-' }}</span
            ><span class="unit">万元/亩</span>
          </template>
        </span>
      </a-col>
      <a-col :span="6" class="detail">
        <span class="title"> 亩均税收排名： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.muAvgTaxRank">暂无数据</span>
          <span class="num" v-else
            >No.{{ pictureInfo.muAvgTaxRank || '-' }}</span
          >
        </span>
      </a-col>
      <a-col :span="6" class="detail">
        <span class="title"> 同期营收(上年累计)： </span>
        <span class="info">
          <span class="unit" v-if="!pictureInfo.incomeCorrPer">暂无数据</span>
          <template v-else
            ><span class="num">{{ pictureInfo.incomeCorrPer || '-' }}</span
            ><span class="unit">万元</span></template
          >
        </span>
      </a-col>
    </a-row>
    <a-row :gutter="12" style="margin-top: 12px">
      <a-col :span="12" class="detail">
        <ChartsCard chartTitle="近6个月税收(累计)" :options="charts1Options" />
      </a-col>

      <a-col :span="12" class="detail">
        <ChartsCard chartTitle="近6个月营收(累计)" :options="charts2Options" />
      </a-col>
    </a-row>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
import ChartsCard from '../ChartsCard.vue';
import { useLineCharts } from '../chartHooks';
import moment from 'moment';
export default {
  components: {
    PortraitCard,
    ChartsCard,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    charts1: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [],
      }),
    },
    charts2: {
      type: Object,
      default: () => ({
        xAxis: [],
        data: [[], []],
      }),
    },
  },
  data() {
    return {
      charts1Options: {},
      charts2Options: {},
      month: moment().format('YYYY-MM'),
    };
  },
  watch: {
    charts1: {
      deep: true,
      handler() {
        this.handleInitCharts1();
      },
    },
    charts2: {
      deep: true,
      handler() {
        this.handleInitCharts2();
      },
    },
  },
  methods: {
    disabledDate(current) {
      return current && current > moment().endOf('month');
    },
    handleChangeMonth() {
      this.$emit('resetBusinessData', this.month, 'businessPortraits');
    },
    handleInitCharts1() {
      const unit = '万元';
      if (!this.charts1?.data) return;
      const { xAxis = [], data = [] } = this.charts1;

      this.charts1Options = useLineCharts({
        xAxis,
        unit,
        series: [
          { name: '近6个月税收(累计)', data: data || [], color: '#61DDAA' },
        ],
        grid: {
          left: 60,
          right: 10,
          top: 40,
          bottom: 20,
        },
      });
    },
    handleInitCharts2() {
      const unit = '万元';
      if (!this.charts2?.data) return;
      const { xAxis = [], data = [] } = this.charts2;
      this.charts2Options = useLineCharts({
        xAxis,
        unit,
        series: [
          { name: '本年累计', data: data[0] || [], color: '#5184f8' },
          { name: '上年同期累计', data: data[1] || [], color: '#56d8a0' },
        ],
        grid: {
          left: 65,
          right: 10,
          top: 40,
          bottom: 20,
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;
  margin-bottom: 16px;
  .title {
    text-align: right;
    color: #999999;
    width: 148px;
    line-height: 26px;
  }

  .info {
    text-align: left;
    .num {
      font-family: D-DIN;
      font-size: 24px;
      font-weight: bold;
      line-height: 24px;
      color: #333333;
      z-index: 0;
    }

    .unit {
      margin-left: 4px;
      font-family: PingFang SC;
      font-size: 12px;
      color: #333333;
    }
  }
}
</style>
