<template>
  <div
    :class="listType === 'picture-card' ? 'diy-upload-picture' : 'diy-upload'"
  >
    <draggable
      :disabled="dragDisabled"
      v-model="fileList"
      target=".ant-upload-list"
      @update="handleChange"
    >
      <a-upload
        v-show="visible"
        name="file"
        :accept="accept"
        :file-list="fileList"
        :remove="handleRemove"
        :beforeUpload="beforeUpload"
        :customRequest="customRequestFile"
        :showUploadList="{
          showRemoveIcon: !disabled,
        }"
        :disabled="disabled"
        :listType="listType"
        v-bind="$attrs"
        v-on="events"
      >
        <template v-if="listType === 'picture-card'">
          <a-spin :spinning="loading" v-if="fileList.length < maxNum">
            <div>
              <a-icon type="plus" />
              <div class="ant-upload-text">{{ '上传' }}</div>
            </div>
          </a-spin>
        </template>
        <template v-else>
          <a-button
            :disabled="disabled"
            class="diy-upload-button"
            :loading="loading"
          >
            <a-icon type="upload" />
            {{ btnText }}
          </a-button>
        </template>
      </a-upload>
      <template v-if="showInput">
        <div class="name-input">
          <div v-for="(item, index) in fileList" :key="index">
            <a-input
              placeholder="展示名称"
              v-model="item.inputName"
              style="width: 104px; margin-right: 8px"
              @change="emitChange(fileList)"
              :maxLength="10"
              type="textarea"
            ></a-input>
          </div>
        </div>
      </template>
    </draggable>
    <template v-if="finalTips.length && !disabled">
      <div
        v-for="(finalTipText, index) in finalTips"
        :key="index"
        class="diy-upload-tip"
      >
        {{ disabled ? '' : finalTipText }}
      </div>
    </template>
  </div>
</template>
<script>
import { upload } from '@/api/common';
import { VueDraggable as draggable } from 'vue-draggable-plus';

export default {
  components: {
    draggable,
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: {
      type: [String, Array],
      default: '',
    },
    btnText: {
      type: String,
      default: 'Browse File',
    },
    accept: {
      type: String,
      default: '.jpg,.png,.jpeg,',
    },
    tip: {
      type: [String, Array],
      default: '',
    },
    maxSize: {
      type: Number,
      default() {
        return 5;
      },
    },
    minSize: {
      type: Number,
      default() {
        return 0;
      },
    },
    imgRatio: {
      type: String,
      // default: '1:1'
      default: '120*120',
    },
    maxNum: {
      type: Number,
      default() {
        return 1;
      },
    },
    visible: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    defaultList: {
      type: Array,
      default() {
        return [];
      },
    },
    listType: {
      type: String,
      default: 'picture-card',
    },
    dragDisabled: {
      type: Boolean,
      default: true,
    },
    showInput: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fileList: [],
      file: null,
      loading: false,
    };
  },
  computed: {
    cpTip() {
      return `支持 ${this.accept} 格式，图片建议尺寸： ${this.imgRatio}，图片大小至多 ${this.maxSize}M。`;
    },
    finalTips() {
      if (this.tip) {
        if (Array.isArray(this.tip)) {
          return this.tip;
        } else if (typeof this.tip === 'string') {
          return [this.tip];
        }
      }
      return [];
    },
    events() {
      let { change, ...other } = this.$listeners;
      return other;
    },
  },
  methods: {
    handleRemove(file) {
      if (this.disabled) return false;
      const index = this.fileList.indexOf(file);
      this.fileList.splice(index, 1);
      this.emitChange(this.fileList);
    },
    handleChange() {
      this.emitChange(this.fileList);
    },
    beforeUpload(file) {
      const fileArr = file.name.split('.');
      const fileAccept = this.accept.split(',');
      const suffix = fileArr[fileArr.length - 1];
      const result = fileAccept.some(function (item) {
        return item.slice(1)?.toLowerCase() === suffix?.toLowerCase();
      });
      if (this.maxNum === 1) {
        this.fileList.splice(0, 1);
      }
      if (this.fileList.length >= Number(this.maxNum)) {
        this.$message.warning(`${this.maxNum}`);
        return false;
      }
      if (!result) {
        this.$message.warning(this.cpTip);
        return false;
      }
      if (file.size < this.minSize * 1024) {
        let errorMsg;
        if (this.minSize > 1024) {
          errorMsg = `最小上传限制为 ${this.minSize / 1024}MB`;
        } else {
          errorMsg = `最小上传限制为 ${this.minSize}KB`;
        }
        this.$message.warning(errorMsg);
        return false;
      }
      if (file.size > this.maxSize * 1024 * 1024) {
        const errorMsg = `最大上传限制为 ${this.maxSize}M`;
        this.$message.warning(errorMsg);
        return false;
      }
      return true;
    },
    async customRequestFile(data) {
      this.loading = true;
      const formData = new FormData();
      formData.append('file', data.file);
      // console.log(formData.get('file'))
      // formData.append('fileType', undefined);
      this.file = data.file;
      const [res, err] = await upload(formData);
      this.loading = false;
      if (err) {
        this.$message.error(err.msg || '上传失败');
        return;
      }
      this.file.url = res.data;
      this.file.thumbnailUrl = res.data;
      this.file.inputName = '';
      this.fileList.push(this.file);
      this.emitChange(this.fileList);
    },
    emitChange(val) {
      let url;
      if (!val.length) {
        url = undefined;
      } else if (this.maxNum === 1) {
        url = val[0]?.url || '';
      } else {
        url = val?.map((x) => ({
          url: x?.url,
          name: x?.inputName,
        }));
      }
      this.$nextTick(() => {
        this.$emit('change', url);
        this.$emit('update', val);
      });
    },
  },
  watch: {
    value: {
      handler: function (val) {
        console.log('val', val);
        if (!this.value) {
          this.fileList = [];
          return;
        }
        if (this.value?.length) {
          if (!val) {
            console.log('测试1');
            this.fileList = [];
            return;
          }
          if (!Array.isArray(this.value)) {
            console.log('测试2', this.value);
            this.fileList = [
              {
                uid: -1,
                status: 'done',
                name: this.value.split('/').at(-1),
                url: this.value,
              },
            ];
            return;
          }
          this.fileList = this.value.map(({ url, name }, index) => ({
            uid: 0 - (index + 1),
            status: 'done',
            name: url.split('/').at(-1),
            url: url,
            inputName: name,
          }));
        }
      },
      immediate: true,
    },
    defaultList(val) {
      console.log('默认', val);
      this.fileList = val;
    },
  },
};
</script>

<style lang="less">
.has-error {
  .diy-upload-picture .ant-upload.ant-upload-select-picture-card {
    border-color: #f5222d;
  }
}
</style>

<style lang="less" scoped>
.diy-upload {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding: 20px;
  border: 1px dashed rgba(4, 15, 36, 25%);
  border-radius: 4px;

  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  &-button {
    margin: 20px 0;
  }

  &-tip {
    font-size: 12px;
    font-weight: normal;
    line-height: 1.5em;
    color: rgba(4, 15, 36, 45%);
    text-align: left;
    letter-spacing: 0;
  }
}

.diy-upload-picture {
  ::v-deep .ant-upload.ant-upload-select-picture-card {
    background-color: #fff;

    .anticon {
      padding-bottom: 11px;
      font-size: 1.8em;
      color: #979ca4;
    }
  }
}

.name-input {
  display: flex;
  flex-direction: row;
}
</style>
