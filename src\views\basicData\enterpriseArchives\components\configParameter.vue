<template>
  <a-modal
    width="1000px"
    title="设置新增企业参数"
    :visible="visible"
    :destroyOnClose="true"
    :confirmLoading="confirmLoading"
    cancelText="取消"
    @ok="okHandler"
    @cancel="handleCancel"
    :keyboard="false"
    :maskClosable="false"
    :afterClose="handleCancel"
  >
    <editTable
      v-model="tableData"
      :tableColumn="columns"
      :addBtn="false"
      :delBtn="false"
      :showDel="false"
      :loading="loading"
      :editRules="editRules"
    >
    </editTable>
  </a-modal>
</template>

<script>
import editTable from '@/components/editTable/index.vue';
export default {
  props: {},
  components: { editTable },
  data() {
    return {
      confirmLoading: false,
      visible: false,
      tableData: [],
      loading: false,
      dict: {},
      editRules: {
        parkId: [
          {
            required: true,
            message: '请选择园区',
            trigger: 'blur',
          },
        ],
        regulate: [
          {
            required: true,
            message: '请选择是否规上',
            trigger: 'blur',
          },
        ],
        nature: [
          {
            required: true,
            message: '请选择企业性质',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  computed: {
    columns() {
      const _this = this;
      return [
        {
          type: 'seq',
          title: '序号',
          width: 80,
        },
        {
          field: 'unifiedCreditCode',
          title: '统一社会信用代码',
          minWidth: 150,
        },
        {
          field: 'name',
          title: '企业名称',
          minWidth: 120,
        },
        {
          field: 'address',
          title: '企业地址',
          minWidth: 120,
        },
        {
          title: '所属园区',
          field: 'parkId',
          minWidth: 180,
          editRender: {
            name: 'select',
            placeholder: '请输入',
            immediate: true,
          },
          slots: {
            edit: ({ row, data, rowIndex, $table, ...arg }, h) => {
              return (
                <vxe-select
                  value={row.parkId}
                  onChange={({ value }) => {
                    console.log(value, row);
                    row.parkId = value;
                  }}
                  onBlur={() => {
                    $table && $table.reloadData(data);
                  }}
                  placeholder="请选择"
                  options={this.dict?.parkList || []}
                  transfer
                  filterable={true}
                ></vxe-select>
              );
            },
          },

          formatter: ({ cellValue }) => {
            return this.dict?.parkList?.find((q) => q.value == cellValue)
              ?.label;
          },
        },
        {
          title: '是否规上',
          field: 'regulate',
          minWidth: 180,
          editRender: {
            name: 'select',
            placeholder: '请输入',
            immediate: true,
          },
          slots: {
            edit: ({ row, data, rowIndex, $table, ...arg }, h) => {
              return (
                <vxe-select
                  value={row.regulate}
                  onChange={({ value }) => {
                    row.regulate = value;
                  }}
                  onBlur={() => {
                    $table && $table.reloadData(data);
                  }}
                  placeholder="请选择"
                  options={this.dict?.enterprise_regulate_status || []}
                  transfer
                  filterable={true}
                ></vxe-select>
              );
            },
          },
          formatter: ({ cellValue }) => {
            return this.dict?.enterprise_regulate_status?.find(
              (q) => q.value == cellValue
            )?.label;
          },
        },
        {
          title: '企业性质',
          field: 'nature',
          minWidth: 180,
          editRender: {
            name: 'select',
            placeholder: '请输入',
            immediate: true,
          },
          slots: {
            edit: ({ row, data, rowIndex, $table, ...arg }, h) => {
              return (
                <vxe-select
                  value={row.nature}
                  onChange={({ value }) => {
                    row.nature = value;
                  }}
                  onBlur={() => {
                    $table && $table.reloadData(data);
                  }}
                  placeholder="请选择"
                  options={this.dict?.enterprise_nature_status || []}
                  transfer
                  filterable={true}
                ></vxe-select>
              );
            },
          },
          formatter: ({ cellValue }) => {
            return this.dict?.enterprise_nature_status?.find(
              (q) => q.value == cellValue
            )?.label;
          },
        },
        {
          title: '企业标签',
          field: 'labelId',
          minWidth: 180,
          editRender: {
            name: 'select',
            placeholder: '请输入',
            immediate: true,
          },
          slots: {
            edit: ({ row, data, rowIndex, $table, ...arg }, h) => {
              return (
                <vxe-select
                  value={row.labelId}
                  onChange={({ value }) => {
                    row.labelId = value;
                  }}
                  onBlur={() => {
                    $table && $table.reloadData(data);
                  }}
                  multiple={true}
                  placeholder="请选择"
                  options={this.dict?.labelList || []}
                  transfer
                  filterable={true}
                  popupClassName={'multiplePanel'}
                ></vxe-select>
              );
            },
          },
          formatter: ({ cellValue }) => {
            return cellValue && Array.isArray(cellValue)
              ? cellValue
                  ?.map((item) => {
                    return this.dict?.labelList?.find((q) => q.value == item)
                      ?.label;
                  })
                  .join(',')
              : '';
          },
        },
      ];
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    okHandler() {
      if (this.tableData.length == 0) return;
      const flag = this.tableData.every((q) => {
        return q.parkId && q.regulate && q.nature;
      });
      if (flag) {
        this.$emit('okSuccess', this.tableData);
      } else {
        this.$message.info('请完成企业参数填写');
      }
    },
    openDialog(dict, tableData) {
      this.dict = dict;
      this.visible = true;
      this.tableData = tableData.map((q) => {
        return {
          ...q,
          parkId: undefined,
          regulate: undefined,
          nature: undefined,
          labelId: [],
        };
      });
    },
    handleCancel() {
      this.tableData = [];
      this.visible = false;
    },
  },
};
</script>

<style lang="less">
::v-deep .vxe-select--panel {
  max-width: 160px !important;
}
</style>
<style scoped lang="less"></style>
