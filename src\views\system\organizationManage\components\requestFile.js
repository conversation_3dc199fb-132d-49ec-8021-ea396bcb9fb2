import axios from 'axios';
import moment from 'moment';
import { getToken } from '@/utils/common/auth';
import { notification } from 'ant-design-vue';

import { getBaseUrl } from '@/utils/common/util.js';
const FILE_TYPE_ENUM = {
  xlsx: {
    fileExt: '.xlsx',
    mimeType:
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    responseType: 'application/vnd.ms-excel',
  },
  zip: {
    fileExt: '.zip',
    mimeType: 'application/zip',
    responseType: 'application/zip',
  },
};

/**
 * 文件下载
 * @param {string} opt.url 下载文件接口路径
 * @param {'get' | 'post'} opt.method ['get']
 * @param {*} opt.params get接口参数
 * @param {*} opt.data  post接口参数
 * @param {string} opt.fileName 文件名称
 * @param {'xlsx' | 'zip'} opt.fileType 文件类型
 */
export function downLoadFile(opt) {
  const { method = 'post', url, params, data, fileName, fileType } = opt || {};
  axios({
    headers: { Authorization: getToken() },
    method,
    baseURL: process.env.VUE_APP_USE_BUILD_TYPE
      ? getBaseUrl()
      : process.env.VUE_APP_BASE_API,
    url,
    params,
    data,
    responseType: 'blob',
  })
    .then((res) => {
      resolveBlob(res, fileType, fileName);
    })
    .catch((err) => {
      console.lof(err);
      notification.error('暂无可下载数据！');
    });
}

/**
 * 解析blob响应内容并下载
 * @param {*} res blob响应内容
 * @param {'xlsx' | 'zip'} fileType 文件类型
 * @param {String} fileName 文件名称
 */
export function resolveBlob(res, fileType, fileName = '') {
  // 校验是否有指定文件类型的相关配置数据
  const fileTypeInfo = FILE_TYPE_ENUM[fileType];
  if (!fileTypeInfo) {
    notification.error({
      message: '暂不支持当前类型的下载',
    });
    return;
  }
  // 校验接口返回流类型是否符合文件类型
  const { mimeType, fileExt } = fileTypeInfo;
  // console.log(fileTypeInfo);
  // console.log(res.data);
  if (mimeType && res.data.type !== mimeType) {
    notification.error({
      message: '暂无可下载数据！',
    });
    return;
  }
  // 创建流对象进行下载
  const blob = new Blob([res.data], { type: mimeType });
  if (window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob);
  } else {
    const aLink = document.createElement('a');
    aLink.href = URL.createObjectURL(blob);
    const date = moment();
    // 设置下载文件名称
    const fileNameInner = fileName
      ? `${fileName}${fileExt}`
      : `下载文件[${date.format('YYYY-MM-DD')}]${fileExt}`;
    aLink.setAttribute('download', fileNameInner);
    document.body.appendChild(aLink);
    aLink.click();
    document.body.removeChild(aLink);
  }
}
