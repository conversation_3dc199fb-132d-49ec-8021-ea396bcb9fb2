import { request } from '@/utils/request/requestTkb';

//消防安全分页接口
export function firePageList(data) {
  return request({
    url: '/fire/inspection/page/list',
    method: 'post',
    data,
  });
}
//消防安全查看历史接口
export function fireHistory(id) {
  return request({
    url: `/fire/inspection/list/${id}`,
    method: 'post',
  });
}

//消防安全查看详情接口
export function fireInfo(id) {
  return request({
    url: `/fire/inspection/info/${id}`,
    method: 'GET',
  });
}

//消防安全新增接口
export function fireAdd(data) {
  return request({
    url: '/fire/inspection/add',
    method: 'post',
    data,
  });
}
//消防安全查看详情中保存接口
export function addOrUpdate(data) {
  return request({
    url: '/fire/inspection/addOrUpdate',
    method: 'post',
    data,
  });
}
//消防安全删除接口

export function fireDelete(id) {
  return request({
    url: `/fire/inspection/delete/${id}`,
    method: 'post',
  });
}
