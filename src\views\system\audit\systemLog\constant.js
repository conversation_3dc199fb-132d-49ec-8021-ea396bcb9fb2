import moment from 'moment';

export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'userName',
      title: '用户',
      props: {
        placeholder: '请输入用户',
      },
    },
    {
      field: 'menuName',
      title: '操作信息',
      props: {
        placeholder: '请输入操作信息',
      },
    },
    {
      field: 'time',
      title: '操作时间',
      element: 'a-range-picker',
      props: {
        showTime: {
          defaultValue: [
            moment('00:00:00', 'HH:mm:ss'),
            moment('23:59:59', 'HH:mm:ss'),
          ],
        },
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { userName: '', menuName: '', time: ['', ''] },
};

// 表头
export const tableColumn = [
  { field: 'userName', title: '用户名' },
  { field: 'appName', title: '操作模块' },
  { field: 'menuName', title: '操作信息', showOverflow: true },
  { field: 'remark', title: '操作结果' },
  { field: 'ip', title: 'IP地址' },
  {
    field: 'createTime',
    title: '操作时间',
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '详情',
    width: 90,
    slots: { default: 'operate' },
  },
];
