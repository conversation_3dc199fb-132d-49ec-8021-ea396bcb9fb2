export const workflowStateOptions = [
  {
    value: 'PASS',
    label: '通过',
  },
  {
    value: 'REJECT',
    label: '驳回',
  },
  {
    value: 'PROCESSING',
    label: '进行中',
  },
];
export const workflowStateDoneOptions = [
  {
    value: 'PASS',
    label: '通过',
  },
  {
    value: 'REJECT',
    label: '驳回',
  },
];
export const getWorkflowStateLabel = (val) => {
  return workflowStateOptions.find((item) => item.value === val)?.label || val;
};
// 审计类型
export const auditTypeOptions = [
  { value: 'ACTIVITY_ONLINE', label: '活动上线' },
];
export const getAuditTypeLabel = (val) => {
  return auditTypeOptions.find((item) => item.value === val)?.label;
};
