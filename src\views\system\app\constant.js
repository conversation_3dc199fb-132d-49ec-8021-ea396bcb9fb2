import moment from 'moment';
import { visibleEnum } from '@/views/system/constant/system';
export const filterOption = {
  // 筛选器配置
  config: [
    {
      field: 'appName',
      title: '应用名称',
      props: {
        placeholder: '请输入应用名称',
      },
    },
    {
      field: 'appCategory',
      title: '应用类型',
      element: 'a-select',
      props: {
        options: [{ value: '', label: '全部' }],
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { appName: '', appCategory: '' },
};
export const getTableColumns = ({ appCategoryOptions } = {}) => [
  {
    title: 'logo',
    field: 'appLogo',
    key: 'appLogo',
    width: 60,
    slots: { default: 'appLogo' },
  },
  {
    title: '应用名称',
    field: 'appName',
    key: 'appName',
    width: 150,
  },
  {
    title: '应用类型',
    field: 'appCategory',
    key: 'appCategory',
    width: 150,
    scopedSlots: {
      customRender: 'appCategory',
    },
    formatter: ({ cellValue }) => {
      return cellValue
        ? (appCategoryOptions || []).find((item) => item.value === cellValue)
            ?.label || ''
        : '--';
    },
  },
  {
    title: '应用路径',
    field: 'appPath',
    key: 'appPath',
    width: 250,
  },
  {
    title: '应用描述',
    field: 'appDesc',
    key: 'appDesc',
    minWidth: 200,
  },
  {
    title: '是否可见',
    field: 'visible',
    key: 'visible',
    width: 80,
    slots: { default: 'visible' },
  },
  {
    title: '创建时间',
    field: 'createTime',
    key: 'createTime',
    width: 200,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '操作',
    field: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 250,
    slots: { default: 'operate' },
  },
];

export const appCategoryTagColor = {
  MARKETING_OPERATIONS: 'blue', //营销运营
  UNIFIED_PAYMENT: 'orange', //支付服务
  SYSTEM_MANAGEMENT: 'purple', //系统管理
  DATA_SERVICES: 'green', //数据服务
};

export const formRules = {
  appName: [
    {
      required: true,
      message: '应用名称不能为空',
      trigger: 'blur',
      whitespace: true,
    },
  ],
  appCode: [
    {
      required: true,
      message: 'appCode不能为空',
      trigger: 'blur',
      whitespace: true,
    },
  ],
  appDesc: [
    {
      required: true,
      message: 'APP描述不能为空',
      trigger: 'blur',
      whitespace: true,
    },
  ],
  appPath: [
    {
      required: true,
      message: '跳转路径不能为空',
      trigger: 'blur',
      whitespace: true,
    },
  ],
  visible: [
    {
      required: true,
      message: '是否可见不能为空',
      trigger: 'blur',
    },
  ],
  appOrder: [
    {
      required: true,
      message: '排序不能为空',
      trigger: 'blur',
    },
  ],
};

export const formLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

export const goToAppOption = [
  {
    value: 'NEW_TAB',
    label: '新窗口',
  },
  {
    value: 'CURRENT_TAB',
    label: '本窗口',
  },
];

// 是否可见
export const openTypeEnum = {
  CURRENT_TAB: 'CURRENT_TAB',
  NEW_TAB: 'NEW_TAB',
};
export const initForm = () => {
  return {
    appName: '', // APP名称 必填
    appLogo: '', // APP图标 必填
    appCode: '', // appCode 必填
    appPath: '', // 跳转路径 必填
    appDesc: '', // APP描述 非必填
    appCategory: undefined, // 分类 必填 MARKETING_OPERATIONS
    appOrder: undefined, // 排序 必填
    appDocUrl: '', // 文档地址 非必填
    openType: openTypeEnum.NEW_TAB, //跳转类型
    visible: visibleEnum.VISIBLE, // 是否可见 必填 0 可见 1 不可见
    appFlag: '', // 标记 非必填
    pathRegx: undefined, // 路由匹配规则
    serverPath: '', // 后端服务
  };
};
