export default {
  props: {
    limitWords: {
      type: Number,
      default: null,
    },
    wordLimitHandler: {
      type: Function,
      default: () => {},
    },
  },
  methods: {
    // Select an emoticon
    choose(item) {
      // Gets the current rich text
      let ed = this.$refs.tinymce.editor;
      // Gets the inserted object
      let range = ed.selection.getRng();
      // Create an img
      let imgNode = ed.getDoc().createElement('img');
      imgNode.src = `/qq/old/${item}.gif`;
      // Insert an emoticon
      range.insertNode(imgNode);
      // Close the pop-up box
      this.dialogVisible = false;
      // The cursor focuses and moves behind the inserted emoticon
      ed.execCommand('seleceAll');
      ed.selection.getRng().collapse();
      ed.focus();
    },
    getWordCount() {
      let ed = this.$refs.tinymce.editor;
      return ed.plugins.wordcount.body.getCharacterCountWithoutSpaces();
    },
  },
  watch: {
    value(newValue) {
      this.content = newValue;
    },
    content(newValue) {
      this.$emit('input', newValue);
      this.$emit('change', newValue);
      this.$emit('wordCountChange', this.getWordCount());
    },
  },
};
