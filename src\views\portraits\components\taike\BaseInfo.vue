<template>
  <PortraitCard :span="24" title="基本信息" :canExpand="true">
    <template #default="{ showAll }">
      <a-row>
        <a-col :span="24" class="detail" style="margin-top: 16px">
          <div class="title">
            <p>简介：</p>
          </div>
          <div :class="['info', !showAll ? 'three-lines' : '']">
            <p>{{ pictureInfo.slogan || '-' }}</p>
          </div>
        </a-col>
      </a-row>
    </template>
  </PortraitCard>
</template>
<script>
import PortraitCard from '../PortraitCard.vue';
// import UpdateRecord from '@/views/basicData/enterpriseArchives/components/updateRecord.vue';
export default {
  components: {
    PortraitCard,
    // UpdateRecord,
  },
  props: {
    pictureInfo: {
      type: Object,
      default: () => {},
    },
    dataDict: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      showAll: false,
      updateHistory: { visible: false },
      baseList: [
        { title: '园区名称', field: 'parkName' },
        { title: '园区类别', field: 'typeName' },
        { title: '主要地址', field: 'address' },
        { title: '详细园区名称', field: 'statusLabel' },
        { title: '详细地址', field: 'addressDetail' },
        { title: '总规划面积', field: 'totalArea' },
        { title: '总建成面积', field: 'completedArea' },
        { title: '建成状况', field: 'isCompletedName' },
        { title: '电话', field: 'phone' },
        { title: '荣誉', field: 'honor' },
        { title: '经营范围', field: 'businessRange' },
        { title: '简介', field: 'intro' },
        { title: '出租率', field: 'ratingPre' },
        { title: '绿化率', field: 'greenPre' },
        { title: '占地面积', field: 'floorSpace' },
        { title: '就业人口数量', field: 'population' },
      ],
    };
  },
  methods: {
    toDetail(path) {
      if (path) {
        this.$router.push({
          path,
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.detail {
  display: flex;
  flex-direction: row;

  .title {
    width: 98px;
    text-align: right;
    color: #999999;
  }

  .info {
    width: calc(100% - 98px);
  }

  ::v-deep .three-lines {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
