<template>
  <page-layout>
    <a-row type="flex" justify="space-between" :gutter="[16, 0]">
      <a-col :span="4">
        <OrganizationTree
          ref="orgaizationTree"
          :organizeId.sync="organizeId"
          :organizeName.sync="organizeName"
          @getTreeDone="getTreeDone"
          @afterChooseMerchant="afterChooseMerchant"
        ></OrganizationTree>
      </a-col>
      <a-col :span="20" :style="{ margin: '-16px 0 -60px 0' }">
        <PageWrapper
          ref="vxeRef"
          :title="`岗位列表${organizeName ? '- ' + organizeName : ''}`"
          createText=""
          :loading="loading"
          :tablePage="tablePage"
          :tableColumn="tableColumns"
          :tableData="tableData"
          @loadData="queryList"
          :filterOptions="filterOptions"
          @handleReset="handleReset"
          @handleCreate="handleAdd"
        >
          <template #defaultHeader>
            <a-button
              v-hasPermi="['system:position:delete']"
              style="margin-right: 8px"
              @click="onClickHeaderDelete()"
            >
              批量删除
            </a-button>
            <a-button
              v-hasPermi="['system:position:add']"
              type="primary"
              :loading="loading"
              @click="handleAdd"
            >
              创建岗位
            </a-button>
          </template>
          <!-- 创建按钮区域插槽 -->
          <template #operation="{ row }">
            <a-button
              v-hasPermi="['system:position:edit']"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleUpdate(row)"
            >
              编辑
            </a-button>
            <a-button
              v-hasPermi="['system:position:copy']"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handelCopy(row)"
            >
              复制
            </a-button>
            <a-button
              v-hasPermi="['system:position:editUser']"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleOrganizationChange(row)"
            >
              岗位成员
            </a-button>
            <a-button
              v-hasPermi="['system:position:editRole']"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleRoleChange(row)"
            >
              岗位权限
            </a-button>
            <a-button
              v-hasPermi="['system:position:delete']"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleDelete(row)"
            >
              删除
            </a-button>
          </template>
        </PageWrapper>
      </a-col>
    </a-row>
    <EditModal
      :visible.sync="editModalVisible"
      :positionId="positionId"
      :organizeId="organizeId"
      :treeSelect="organizationTreeList"
      @ok="queryList"
    />
    <EditRole
      :visible.sync="editRoleVisible"
      :positionId="positionId"
      :positionName="positionName"
      :organizeId="organizeId"
      :organizeName="organizeName"
      @ok="queryList"
    />
    <EditUser
      :visible.sync="editUserVisible"
      :positionId="positionId"
      :positionName="positionName"
      :organizeId="organizeId"
      :organizeName="organizeName"
      @ok="queryList"
    />
  </page-layout>
</template>
<script>
import {
  getPoitionList,
  deletePoition,
  removeBatch,
  copyPosition,
} from '@/api/system/position';
import EditModal from './components/EditModal.vue';
import EditUser from './components/EditUser.vue';
import EditRole from './components/EditRole.vue';
import { Modal } from 'ant-design-vue';
import OrganizationTree from '@/components/SystemTreeSelect/OrganizationTree.vue';
import { filterOptions, tableColumns } from './constant';

export default {
  components: { EditModal, EditRole, EditUser, OrganizationTree },
  data() {
    return {
      loading: false,
      // 菜单树
      organizationTreeList: [],
      organizationArrayList: [],
      organizeId: '',
      organizeName: '',
      // 筛选条件
      filterOptions,
      // 表格数据
      tableColumns,
      tableData: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      // 是否显示弹出层
      editModalVisible: false,
      editRoleVisible: false,
      editUserVisible: false,
      positionId: '',
      positionName: '',
    };
  },
  computed: {
    merchantId() {
      return this.$store.state?.base?.merchant?.merchantId;
    },
  },
  methods: {
    /**
     * 获取树完成之后保存数据
     */
    getTreeDone(data) {
      this.organizationTreeList = data?.tree || [];
      this.organizationArrayList = data?.array || [];
      this.queryList();
    },
    /**
     * 选择组织后
     */
    async afterChooseMerchant() {
      this.filterOptions.params = {
        positionName: '',
      };
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      this.queryList();
    },
    // 获取列表数据
    async queryList() {
      if (!this.organizeId || this.loading) return;
      // 请求列表
      this.loading = true;
      const params = this.filterOptions.params;
      const [result, error] = await getPoitionList({
        ...params,
        organizeId: this.organizeId,
        page: this.tablePage.currentPage,
        limit: this.tablePage.pageSize,
      });
      this.loading = false;
      if (error) return;
      this.tableData = result.data || [];
      this.tablePage.total = result.count;
    },
    // 重置
    async handleReset() {
      this.organizeId = this.organizationTreeList?.[0].id;
      this.organizeName = this.organizationTreeList?.[0]?.label;
      this.afterChooseMerchant();
    },
    /**
    /**
     * 菜单新增删除后的刷新操作
     */
    refresh() {
      this.filterOptions.params = {
        positionName: '',
      };
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      this.$refs?.orgaizationTree?.refresh();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.positionId = '';
      this.editModalVisible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.positionId = row.positionId;
      this.editModalVisible = true;
    },
    /** 修改按钮操作 */
    handleRoleChange(row) {
      this.positionId = row.positionId;
      this.editRoleVisible = true;
    },
    /** 修改按钮操作 */
    handleOrganizationChange(row) {
      this.positionId = row.positionId;
      this.editUserVisible = true;
    },
    /** 复制 */
    async handelCopy(row) {
      if (this.loading) return;
      Modal.confirm({
        title: '警告',
        content: '确认复制岗位?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.loading = true;
          const [, error] = await copyPosition(row.positionId);
          this.loading = false;
          if (error) return;
          this.$message.success('复制成功');
          this.queryList();
        },
      });
    },
    // 删除
    async onClickHeaderDelete() {
      const deleteList = this.$refs.vxeRef.getCheckboxRecords();
      if (deleteList.length === 0) {
        this.$message.warn('请选择需要删除岗位');
        return;
      }
      Modal.confirm({
        title: '警告',
        content:
          '删除岗位后，将清除该岗位下的所有用户成员，并且收回该岗位的权限，请谨慎操作！',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          const ids = deleteList.map((item) => item.positionId);
          this.loading = true;
          const [, error] = await removeBatch({ ids });
          this.loading = false;
          if (error) return;
          this.tablePage.currentPage = 1;
          this.$message.success('删除成功');
          this.queryList();
        },
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.loading) return;
      Modal.confirm({
        title: '警告',
        content:
          '删除岗位后，将清除该岗位下的所有用户成员，并且收回该岗位的权限，请谨慎操作！',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.loading = true;
          const [, error] = await deletePoition(row.positionId);
          this.loading = false;
          if (error) return;
          this.$message.success('删除成功');
          this.tablePage.currentPage = 1;
          this.queryList();
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.table-title {
  font-weight: 500;
  font-size: 16px;
}
.tree-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 48px;
}
</style>
