<template>
  <a-modal
    title="修改密码"
    okText="确定"
    cancelText="取消"
    width="400px"
    :maskClosable="false"
    :visible="visible"
    @ok="onResetPassword"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <a-form-model :model="form" ref="form" :rules="rules">
        <a-form-model-item prop="password">
          <a-input-password
            size="large"
            v-model="form.password"
            placeholder="请输入密码"
            autocomplete="autocomplete"
            type="password"
          >
            <a-icon slot="prefix" type="lock" />
          </a-input-password>
        </a-form-model-item>
        <a-form-model-item prop="passwordSecond">
          <a-input-password
            size="large"
            v-model="form.passwordSecond"
            placeholder="请再次输入密码"
            autocomplete="autocomplete"
            type="password"
          >
            <a-icon slot="prefix" type="lock" />
          </a-input-password>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { resetUserPwd } from '@/api/system/user';
import { rsaCode } from '@/utils/common/auth';
import { initForm2ResetPsw } from '../constant';
import {
  PASSWORD_REGEX,
  PASSWORD_REGEX_DESC,
} from '@/views/system/constant/system';

export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: String,
    },
  },
  data() {
    return {
      loading: false,
      form: initForm2ResetPsw(),
      rules: {
        password: [
          {
            required: true,
            message: '请输入密码',
            whitespace: true,
          },
          {
            validator: (rule, value, callback) => {
              if (PASSWORD_REGEX.test(value)) {
                callback();
              } else {
                callback(new Error(PASSWORD_REGEX_DESC));
              }
            },
            trigger: 'blur',
          },
        ],
        passwordSecond: [
          {
            required: true,
            message: '请输入密码',
            whitespace: true,
          },
          {
            validator: (rule, value, callback) => {
              if (value && value !== this.form.password) {
                callback(new Error('两次密码不同!'));
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
      },
    };
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.form = initForm2ResetPsw();
        } else {
          this.$refs.form && this.$refs.form.resetFields();
        }
      },
    },
  },
  methods: {
    async onResetPassword() {
      if (this.loading) return;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const password = rsaCode(this.form.password);
          const [, error] = await resetUserPwd({
            userId: this.userId,
            password,
          });
          this.loading = false;
          if (error) return;
          this.$message.success('修改成功');
          this.$emit('ok');
          this.closeModal();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-form-item {
  margin-bottom: 24px;
}
</style>
