<template>
  <div class="lx-screen">
    <a-spin
      :spinning="loading"
      class="portrait"
      :style="
        '--parent-width:' + parentWidth + 'px;--parent-scale:' + parentScale
      "
    >
      <div class="top">
        <div class="title">朗新CPU空间·低碳园区</div>
        <div class="time" @click="resize">
          <div>{{ currentTime.date }}</div>
          <div>{{ currentTime.week }} {{ currentTime.times }}</div>
        </div>
      </div>
      <transition name="fade" mode="out-in" appear>
        <div class="left flex-wrap">
          <cardDisplay title="减排成效" :list="leftTopList"></cardDisplay>
          <cardItem width="100%" title="碳中和率">
            <LineChart
              :axisData="carbonTendencyObj.time"
              :serieData="carbonTendencyObj.TendencyArr"
              :isSmooth="true"
              v-if="
                carbonTendencyObj &&
                carbonTendencyObj.time &&
                carbonTendencyObj.time.length
              "
            ></LineChart>
            <a-empty v-else></a-empty>
          </cardItem>
          <emissionReducingPotential
            :info="info"
            :pieList="pieList"
          ></emissionReducingPotential>
          <carbonInclusive :info="info"></carbonInclusive>
          <cardItem width="100%" title="碳资产">
            <div class="card-assets-wrap">
              <div class="assets-item">
                <img
                  src="@/assets/images/materialScreen/icon-year-co2.png"
                  alt=""
                />
                <div class="text">当年减排量折算</div>
                <div class="num">
                  <span class="num-value">20000</span>
                  万元
                </div>
              </div>
              <div class="assets-item">
                <img
                  src="@/assets/images/materialScreen/icon-year-co2.png"
                  alt=""
                />
                <div class="text">当年减排量折算</div>
                <div class="num">
                  <span class="num-value">2000000000</span>
                  万元
                </div>
              </div>
            </div>
          </cardItem>
        </div>
      </transition>
      <transition name="fade" mode="out-in" appear>
        <div class="right flex-wrap">
          <cardDisplay title="碳排概括" :list="rightTopList"></cardDisplay>
          <cardItem width="100%" title="碳排趋势">
            <LineBarChart
              :axisData="carbonTendencyBarObj.time"
              :serieData="carbonTendencyBarObj.TendencyArr"
              :isSmooth="true"
              unit="tco2"
              v-if="
                carbonTendencyBarObj &&
                carbonTendencyBarObj.time &&
                carbonTendencyBarObj.time.length
              "
            ></LineBarChart>
            <a-empty v-else></a-empty>
          </cardItem>
          <cardItem width="100%" title="碳排结构">
            <div class="card-structure-wrap">
              <cardSingle
                v-for="(item, index) in structureList"
                :key="index"
                :item="item"
              ></cardSingle>
            </div>
            <div class="pie-chart">
              <div class="pie-item-chart">
                <pieChart
                  title="分区碳排放占比"
                  :list="carbonPieList"
                  v-if="carbonPieList.length > 0"
                ></pieChart>
                <a-empty v-else></a-empty>
              </div>
              <div class="pie-item-chart">
                <pieChart
                  title="分项碳排放"
                  :list="CarbonAssetsPieList"
                  v-if="CarbonAssetsPieList.length > 0"
                ></pieChart>
                <a-empty v-else></a-empty>
              </div>
            </div>
          </cardItem>
        </div>
      </transition>
      <div class="mask-model"></div>
    </a-spin>
  </div>
</template>

<script>
import { throttle } from 'xe-utils';
import cardDisplay from './cardDisplay.vue';
import pieChart from './components/pieChart.vue';
import emissionReducingPotential from './emissionReducingPotential.vue';
import carbonInclusive from './carbonInclusive.vue';
import cardItem from './components/cardItem.vue';
import cardSingle from './components/cardSingle.vue';
import LineChart from './components/lineChart.vue';
import LineBarChart from './components/lineBarChart.vue';
import { startClock } from '@/utils';
import moment from 'moment';
import { getDicts } from '@/api/system/dict/data';
export default {
  props: {},
  components: {
    cardDisplay,
    cardItem,
    LineChart,
    LineBarChart,
    emissionReducingPotential,
    carbonInclusive,
    cardSingle,
    pieChart,
  },
  data() {
    return {
      parentWidth: 1920,
      parentScale: 1,
      loading: false,
      carbonTendencyObj: null,
      carbonTendencyBarObj: null,
      pieList: [],
      carbonPieList: [],
      CarbonAssetsPieList: [],
      currentTime: {
        date: '',
        week: '',
        times: '',
      },
      info: {},
    };
  },
  computed: {
    leftTopList() {
      return [
        {
          label: '年节约标煤',
          num: this.info?.standardCoal || '-',
          unit: 't',
          icon: require('@/assets/images/materialScreen/icon-save-coal.png'),
        },
        {
          label: '年CO2减排',
          num: this.info?.co2 || '-',
          unit: 't',
          icon: require('@/assets/images/materialScreen/icon-year-co2.png'),
        },
        {
          label: '年SO2减排',
          num: this.info?.so2 || '-',
          unit: 't',
          icon: require('@/assets/images/materialScreen/icon-year-so2.png'),
        },
        {
          label: '年NO2减排',
          num: this.info?.no2 || '-',
          unit: 't',
          icon: require('@/assets/images/materialScreen/icon-year-no2.png'),
        },
      ];
    },
    rightTopList() {
      return [
        {
          label: '本周排放量',
          num: this.info?.week || '-',
          unit: 'tCO2',
          icon: require('@/assets/images/materialScreen/icon-week-emissions.png'),
        },
        {
          label: '本月排放量',
          num: this.info?.month || '-',
          unit: 'tCO2',
          icon: require('@/assets/images/materialScreen/icon-month-emissions.png'),
        },
        {
          label: '本年排放量',
          num: this.info?.year || '-',
          unit: 'tCO2',
          icon: require('@/assets/images/materialScreen/icon-year-emissions.png'),
        },
      ];
    },
    structureList() {
      return [
        {
          label: '燃气',
          num: this.info?.gasRight || '-',
          unit: '%',
          icon: require('@/assets/images/materialScreen/icon-week-emissions.png'),
        },
        {
          label: '电',
          num: this.info?.electricityRight || '-',
          unit: '%',
          icon: require('@/assets/images/materialScreen/icon-week-emissions.png'),
        },
        {
          label: '水',
          num: this.info?.waterRight || '-',
          unit: '%',
          icon: require('@/assets/images/materialScreen/icon-week-emissions.png'),
        },
      ];
    },
  },
  watch: {},
  created() {
    this.loadData();
  },
  mounted() {
    startClock((time) => {
      this.currentTime = {
        date: moment(time).format('yyyy:MM:DD'),
        week: moment(time).format('dddd'),
        times: moment(time).format('HH:mm:ss'),
      };
    });
    this.$nextTick(() => {
      setTimeout(this.resize, 100);
    });
    window.onresize = throttle(this.resize, 100);
  },
  methods: {
    resize() {
      console.log('resize');
      this.parentWidth =
        [...document.querySelectorAll('.portrait')].at(-1)?.parentNode
          ?.offsetWidth || 1920;
      console.log(this.parentWidth, ' this.parentWidth');
      this.parentScale = this.parentWidth / 1920;
    },
    async loadData() {
      const [result, err] = await getDicts(['material_screen_data']);
      console.log(result, err, 'data');
      if (result && result.data.length > 0) {
        const data = result.data.find((q) => q.dictValue == 'data');
        console.log(JSON.parse(data.dictLabel));
        const res = JSON.parse(data.dictLabel);
        // 碳中和率
        this.carbonTendencyObj = res.carbonTendencyObj;
        // 碳排趋势
        this.carbonTendencyBarObj = res.carbonTendencyBarObj;
        // 分区潜力
        this.pieList = res.pieList;
        // 分区碳排放占比
        this.carbonPieList = res.carbonPieList;
        // 分项碳排放
        this.CarbonAssetsPieList = res.CarbonAssetsPieList;
        this.info = res.info;
      }
      // 数据模板
      // const res = {
      //   data: {
      //     carbonTendencyObj: {
      //       time: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      //       TendencyArr: [
      //         {
      //           name: '碳中和率',
      //           data: [10, 100, 70, 40, 50, 70, 80, 90, 100, 100, 100, 100],
      //         },
      //       ],
      //     },
      //     carbonTendencyBarObj: {
      //       time: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      //       TendencyArr: [
      //         {
      //           name: '同比',
      //           data: [50, 80, 10, 60, 50, 70, 80, 90, 100, 100, 100, 100],
      //           type: 'line',
      //           color: '#FF944D',
      //         },
      //         {
      //           name: '环比',
      //           data: [10, 100, 70, 40, 50, 70, 80, 90, 100, 100, 100, 100],
      //           type: 'line',
      //           color: '#3B6CFF',
      //         },
      //         {
      //           name: '碳排放量',
      //           data: [30, 50, 60, 40, 70, 80, 80, 90, 100, 100, 100, 100],
      //           type: 'dimensionalBar',
      //           color: '',
      //         },
      //       ],
      //     },
      //     pieList: [
      //       {
      //         name: '机房',
      //         value: 500,
      //       },
      //       {
      //         name: '展厅',
      //         value: 500,
      //       },
      //       {
      //         name: '办公',
      //         value: 800,
      //       },
      //       {
      //         name: '食堂',
      //         value: 800,
      //       },
      //       {
      //         name: '停车场',
      //         value: 800,
      //       },
      //       {
      //         name: '公共空间',
      //         value: 800,
      //       },
      //     ],
      //     carbonPieList: [
      //       {
      //         name: '机房占比',
      //         value: 500,
      //       },
      //       {
      //         name: '展厅占比',
      //         value: 500,
      //       },
      //       {
      //         name: '办公占比',
      //         value: 800,
      //       },
      //       {
      //         name: '食堂占比',
      //         value: 800,
      //       },
      //       {
      //         name: '停车场占比',
      //         value: 800,
      //       },
      //       {
      //         name: '公共空间占比',
      //         value: 800,
      //       },
      //     ],
      //     CarbonAssetsPieList: [
      //       {
      //         name: '空调占比',
      //         value: 500,
      //       },
      //       {
      //         name: '照明占比',
      //         value: 800,
      //       },
      //       {
      //         name: '动力占比',
      //         value: 800,
      //       },
      //       {
      //         name: '电梯占比',
      //         value: 800,
      //       },
      //       {
      //         name: '特殊占比',
      //         value: 800,
      //       },
      //     ],
      //     info: {
      //       // 年节约标煤
      //       standardCoal: 20000,
      //       // 年CO2减排
      //       co2: 20000,
      //       // 年SO2减排
      //       so2: 20000,
      //       // 年NO2减排
      //       no2: 800,
      //       // 本周排放量
      //       week: 20000,
      //       // 本月排放量
      //       month: 20000,
      //       // 本年排放量
      //       year: 20000,
      //       // 燃气
      //       gasRight: 20,
      //       // 电
      //       electricityRight: 50,
      //       // 水
      //       waterRight: 30,
      //       // 燃气
      //       gasProportion: 40,
      //       // 电
      //       electricityProportion: 40,
      //       // 水
      //       waterProportion: 20,
      //       // 累计发放碳中和电子证书
      //       certificate: 10000,
      //       // 累计中和访客碳排放量
      //       Visitor: 1000,
      //     },
      //   },
      // };
    },
  },
};
</script>

<style scoped lang="less">
.lx-screen {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: #fff;
  overflow: hidden;
}
.portrait {
  --parent-width: 1920px;
  --parent-scale: 1;
  width: 1920px;
  // aspect-ratio: 16/9;
  min-height: 1080px;
  overflow: hidden;
  background: url('@/assets/images/materialScreen/materialScreenBg.jpg');
  background-repeat: no-repeat;
  background-size: 1920px 1080px;
  transform: translate(-50%, -50%) scale(var(--parent-scale))
    translate(50%, 50%);
  background-color: #060b1f;
  position: relative;
  /deep/ .ant-spin-container {
    height: 100vh;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .top {
    width: 100%;
    z-index: 2;
    // position: absolute;
    // left: 0;
    // right: 0;
    // top: 0;
    height: 83px;
    background: url('@/assets/images/materialScreen/top-tilte.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    .time {
      position: absolute;
      right: 20px;
      top: 10px;
      width: 120px;
      height: 60px;
      color: #fff;
      font-family: Alimama ShuHeiTi;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 5px;
      user-select: none;
    }
    .title {
      text-align: center;
      width: 100%;
      font-family: Alimama ShuHeiTi;
      font-size: 32px;
      font-weight: bold;
      line-height: 32px;
      text-align: center;
      letter-spacing: 1px;
      font-variation-settings: 'opsz' auto;
      color: #ffffff;
      line-height: 70px;
    }
  }
  .flex-wrap {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: flex-start;
    height: calc(100vh - 10px);
    overflow: hidden;
  }
  .left {
    z-index: 2;
    width: 600px;
    // position: absolute;
    // top: 104px;
    // left: 40px;
    // bottom: 40px;
    .card-assets-wrap {
      width: 100%;
      display: flex;
      gap: 10px;
      height: 50px;
      .assets-item {
        flex: 1;
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        align-items: center;
        background-image: url('@/assets/images/materialScreen/icon-small-card-bg.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        img {
          width: 40px;
          height: 40px;
        }
        .text {
          font-family: AlibabaPuHuiTi;
          font-size: 14px;
          line-height: 18px;
          color: rgba(224, 240, 255, 0.8);
        }
        .num {
          font-family: AlibabaPuHuiTi;
          font-size: 12px;
          font-weight: normal;
          line-height: 20px;
          letter-spacing: 0em;
          /* 主色-绿 */
          color: #00ffaa;
          display: flex;
          align-items: baseline;
          .num-value {
            padding-right: 5px;
            display: inline-block;
            font-size: 16px;
            width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: right;
          }
        }
      }
    }
  }
  .right {
    z-index: 2;
    width: 600px;
    // position: absolute;
    // top: 104px;
    // right: 40px;
    // bottom: 40px;
    .card-structure-wrap {
      background-image: url('@/assets/images/materialScreen/icon-small-card-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 20px;
      padding: 0 20px;
      margin: 10px 0;
      height: 88px;
    }
    .pie-chart {
      display: flex;
      .pie-item-chart {
        flex: 1;
        padding: 10px;
      }
    }
  }
  .mask-model {
    z-index: 1;
    position: fixed;
    inset: 0;
    background: linear-gradient(
      to right,
      rgba(15, 19, 30, 0.6) 0%,
      rgba(25, 36, 70, 0.4) 10%,
      transparent 30%,
      transparent 65%,
      rgba(25, 36, 70, 0.4) 95%,
      rgba(42, 58, 108, 0.6) 100%
    );
  }
}
</style>
