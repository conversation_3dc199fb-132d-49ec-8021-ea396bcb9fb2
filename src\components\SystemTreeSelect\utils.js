export function recursionDataSlide(data, childFlag) {
  if (!data) return [];
  data.forEach((item) => {
    item.title = item.label;

    if (childFlag) {
      item.slots = { icon: 'team' };
    } else {
      item.slots = { icon: 'bank' };
    }
    // 菜单的处理逻辑
    //目录
    if (item.type && item.type === 'M') {
      item.slots = { icon: 'directory' };
      //菜单
    } else if (item.type && item.type === 'C') {
      if (item.children && item.children.length) {
        item.slots = { icon: 'menu' };
      } else {
        item.slots = { icon: 'file' };
      }
      //按钮
    } else if (item.type && item.type === 'F') {
      item.slots = { icon: 'button' };
    } else if (item.type && item.type === 'COMMON') {
      item.slots = { icon: 'user' };
    } else if (item.type && item.type === 'ISV') {
      item.slots = { icon: 'team' };
    }

    if (item.children && item.children.length) {
      recursionDataSlide(item.children, true);
    } else {
      delete item.children;
    }
  });
  return data;
}
