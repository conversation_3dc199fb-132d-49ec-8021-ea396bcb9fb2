function getTableColumns() {
  return [
    {
      title: '目标年份',
      field: 'year',
    },
    {
      title: '一级指标',
      field: 'oneTargetName',
    },
    {
      title: '二级指标',
      field: 'twoTargetName',
    },
    {
      title: '我的目标',
      field: 'assignedAmount',
    },
    {
      title: '目标发布者',
      field: 'departmentName',
    },
    {
      title: '接收时间',
      field: 'createTime',
    },
    {
      title: '当前完成情况',
      field: 'completeSchedule',
    },
    {
      title: '完成进度',
      field: 'finishingRateUnit',
    },
    {
      title: '需本月填报',
      field: 'flag',
    },
    {
      title: '进度预警提醒',
      field: 'progressWarningReminder',
      slots: {
        default({ row }) {
          return row.progressWarningReminder === '1' ? (
            <a-icon type="warning" style={{ color: 'red', fontSize: '20px' }} />
          ) : (
            '/'
          );
        },
      },
    },
  ];
}

export { getTableColumns };
