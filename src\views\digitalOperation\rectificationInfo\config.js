//（0 危化品隐患 1 易燃易爆品隐患 2 易制毒隐患 3电气安全隐患 4 生物安全隐患 5消防安全隐患 6 特种设备隐患 7 其他
const DangerType = [
  {
    label: '危化品隐患',
    value: '0',
  },
  {
    label: '易燃易爆品隐患',
    value: '1',
  },
  {
    label: '易制毒隐患',
    value: '2',
  },
  {
    label: '电气安全隐患',
    value: '3',
  },
  {
    label: '生物安全隐患',
    value: '4',
  },
  {
    label: '消防安全隐患',
    value: '5',
  },
  {
    label: '特种设备隐患',
    value: '6',
  },
  {
    label: '其他',
    value: '7',
  },
];
const RectifyStatus = [
  {
    label: '未整改',
    value: '0',
  },
  {
    label: '已整改',
    value: '2',
  },
  {
    label: '整改中',
    value: '1',
  },
];
//0 A端 1 B端 2 C端
const SourceType = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '经发局录入',
    value: '0',
  },
  {
    label: '园区自查',
    value: '1',
  },
  {
    label: '企业自查',
    value: '2',
  },
];

const TableListColumns = [
  {
    title: '园区',
    field: 'parkName',
  },
  {
    title: '总数',
    field: 'totalCount',
  },
  {
    title: '隐患排查来源',
    children: [
      {
        title: '经发局录入(个)',
        field: 'govImport',
      },
      {
        title: '企业自查(个)',
        field: 'enterpriseImport',
      },
      {
        title: '园区自查(个)',
        field: 'parkImport',
      },
    ],
  },
  {
    title: '隐患整改情况',
    children: [
      {
        title: '未整改(个)',
        field: 'notChangeNum',
      },
      {
        title: '已整改(个)',
        field: 'changeNum',
      },
      {
        title: '整改完成率(%)',
        field: 'changePercent',
      },
    ],
  },
  {
    title: '超期未整改情况',
    children: [
      {
        title: '超期未整改(个)',
        field: 'overdueNum',
      },
      {
        title: '未整改隐患占比(%)',
        field: 'overduePercent',
      },
    ],
  },
];

export default {
  DangerType,
  RectifyStatus,
  SourceType,
  TableListColumns,
};
