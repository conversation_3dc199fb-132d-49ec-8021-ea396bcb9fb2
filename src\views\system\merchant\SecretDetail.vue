<template>
  <a-modal
    title="租户信息"
    :visible="visible"
    :footer="null"
    width="700px"
    @cancel="closeModal"
  >
    <a-form-model ref="form" v-bind="formLayout">
      <a-form-model-item label="租户名称">
        {{ merchantDetail.merchantName }}
      </a-form-model-item>

      <a-form-model-item label="Access ID">
        {{ merchantDetail.accessId || '-' }}
      </a-form-model-item>
      <a-form-model-item label="Access Secret">
        {{ merchantDetail.accessSecret || '-' }}
      </a-form-model-item>
      <a-form-model-item label="PrivateKey">
        <div :class="{ 'key-word': !showAll }">
          {{ (merchantDetail && merchantDetail.privateKey) || '-' }}
        </div>
        <div class="key-bottom">
          <div
            class="key-more"
            @click="copy2clipboard(merchantDetail.privateKey)"
            v-if="merchantDetail?.privateKey?.length > 2"
          >
            <a-icon type="copy" />复制
          </div>
          <div
            class="key-more"
            @click="showAll = !showAll"
            v-if="merchantDetail?.privateKey?.length > 90"
          >
            {{ !showAll ? '展开更多' : '收起' }}
          </div>
        </div>
      </a-form-model-item>
      <a-form-model-item label="PublicKey">
        <div :class="{ 'key-word': !showAllPub }">
          {{ merchantDetail.publicKey || '-' }}
        </div>
        <div class="key-bottom">
          <div
            class="key-more"
            v-clipboard:copy="merchantDetail.publicKey"
            v-clipboard:success="onCopy"
            v-clipboard:error="onError"
            v-if="merchantDetail?.publicKey?.length > 2"
          >
            <a-icon type="copy" />复制
          </div>
          <div
            class="key-more"
            @click="showAllPub = !showAllPub"
            v-if="merchantDetail?.publicKey?.length > 90"
          >
            {{ !showAllPub ? '展开更多' : '收起' }}
          </div>
        </div>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
export default {
  props: {
    visible: Boolean,
    merchantDetail: Object,
  },
  data() {
    return {
      loading: false,
      showAll: false,
      showAllPub: false,
      formLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
      },
    };
  },
  methods: {
    // 复制成功
    onCopy() {
      this.$message.success('复制成功');
    },
    onError() {
      this.$message.error('复制失败');
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>
<style lang="less" scoped>
.key-word {
  // 文本溢出超出两行显示省略号
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.key-more {
  color: #1890ff;
  cursor: pointer;
}
.key-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
