<template>
  <page-layout>
    <PageWrapper
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      :tableOn="{
        'checkbox-change': selectChangeEvent,
        'checkbox-all': selectChangeEvent,
      }"
    >
      <!-- 创建按钮区域插槽 -->
      <template #defaultHeader>
        <a-button
          type="primary"
          style="margin-right: 8px"
          @click="handleCreate"
        >
          新增
        </a-button>
        <a-button style="margin-right: 8px" @click="onClickImport">
          导入
        </a-button>
        <ExportButton :checkItems="checkItems" :pageName="pageName" />
        <a-button
          type="danger"
          style="margin-right: 8px"
          :disabled="!checkItems.length"
          @click="handelDelete"
        >
          删除
        </a-button>
      </template>
      <!-- 月份插槽 -->
      <template #year>
        <BuseRangePicker
          type="year"
          :needShowSecondPicker="() => false"
          v-model="filterOptions.params.rateTime"
          format="YYYY"
          placeholder="请选择年份"
          :disableDateFunc="disableDateFunc"
        />
      </template>
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
        <span class="operate-button" @click="onClickDetail(row)">详情</span>
      </template>
      <!-- 编辑弹窗 -->
      <ListModal
        :visible="visible"
        :detail="modalData"
        :modelTitle="modelTitle"
        :preview="preview"
        :buildState="buildState"
        @loadData="loadData"
        @handleCancel="handleCancel"
      />
    </PageWrapper>
  </page-layout>
</template>

<script>
import moment from 'moment';
import { institutionsMixin } from '../mixins/institutionsMixin';
import { defaultTableColumn, defaultFilterConfig } from './constant';
import ListModal from './components/ListModal.vue';
import ExportButton from '@/views/basicData/components/ExportButton.vue';
import {
  metricResearch,
  deleteResearch,
  downloadResearch,
} from '@/api/basicData';
export default {
  components: { ListModal, ExportButton },
  dicts: ['my_notify_rule'],
  mixins: [institutionsMixin],
  data() {
    return {
      pageName: 'institutionsManagement',
      loading: false,
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
        params: {
          rateTime: undefined,
          name: '',
          applicationNum: '',
        }, // 筛选器结果数据
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: defaultTableColumn(),
      tableData: [],
      visible: false,
      modalData: null,
      checkItems: [],
      modelTitle: 'add',
      // 载体类别
      buildState: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '1',
          label: '规划建设',
        },
        {
          value: '2',
          label: '建成启动',
        },
        {
          value: '3',
          label: '其它',
        },
      ],
      detail: {},
      preview: false,
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    // 下拉字典加载完成
    onDictReady() {
      this.filterOptions.config[2].props.options = this.buildState;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 下拉联动
    onEnterChange() {
      console.log(222);
    },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [res, err] = await metricResearch({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...params,
        rateTime: this.filterOptions.params.rateTime?.startValue.format('YYYY'),
      });
      this.loading = false;

      if (err) return;
      // 设置数据
      this.tablePage.total = res.data.total;
      this.tableData = res.data.records;
    },
    // 创建按钮点击事件
    handleCreate() {
      this.modelTitle = 'add';
      this.visible = true;
    },
    // 编辑按钮点击事件
    onClickEdit(row) {
      this.modelTitle = 'edit';
      this.visible = true;
      this.modalData = row;
    },
    onClickDetail(row) {
      this.modelTitle = 'see';
      this.modalData = row;
      this.preview = true;
      this.visible = true;
    },
    // 关闭弹窗
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
      this.modalData = null;
      this.manageVisible = false;
      this.preview = false;
    },
    // 管理按钮点击事件
    onClickManage() {
      this.manageVisible = true;
    },
    // 删除
    handelDelete() {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [, err] = await deleteResearch({
            list: that.checkItems,
          });
          if (!err) {
            that.$message.success('删除成功!');
            that.checkItems = [];
            // 刷新数据
            that.loadData();
            return;
          }
        },
      });
    },
    // 园区类别
    stateChange(value) {
      console.log('选中值', value);
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: this.pageName,
        },
      });
    },
    // 年份选择
    yearChange(date, dateString) {
      console.log('月份选择回调', date, dateString);
    },
    // 导出
    async onClickExport() {
      const [res] = await downloadResearch({
        list: this.checkItems,
      });
      this.resolveBlob(res, this.mimeMap, '导出', '.xls');
    },
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
  },
};
</script>
<style scoped>
/deep/.page-wrapper-container {
  margin: 0 !important;
}
</style>
