<template>
  <a-modal
    :title="title"
    okText="确定"
    :maskClosable="false"
    :visible="visible"
    cancelText="取消"
    width="800px"
    @ok="submitForm"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <a-form-model
        ref="form"
        :model="form"
        :rules="formRules"
        v-bind="formLayout"
      >
        <a-form-model-item label="应用名称" prop="appName">
          <a-input v-model="form.appName" placeholder="请输入应用名称" />
        </a-form-model-item>
        <a-form-model-item label="应用logo" prop="appLogo">
          <Upload
            class="icon-upload"
            list-type="picture-card"
            :file-list.sync="form.appLogo"
            :data="{
              fileType: 'IMAGE',
            }"
            style="margin-bottom: -20px"
          />
        </a-form-model-item>
        <a-form-model-item label="appCode" prop="appCode">
          <a-input v-model="form.appCode" placeholder="请输入appCode" />
        </a-form-model-item>
        <a-form-model-item label="应用路径" prop="appPath">
          <a-input v-model="form.appPath" placeholder="请输入应用路径" />
        </a-form-model-item>
        <a-form-model-item label="跳转方式" prop="openType">
          <a-radio-group v-model="form.openType" :options="goToAppOption">
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="应用描述">
          <a-input
            type="textarea"
            v-model="form.appDesc"
            placeholder="请输入应用描述"
          />
        </a-form-model-item>
        <a-form-model-item label="应用分类" prop="appCategory">
          <a-select
            v-model="form.appCategory"
            :options="appCategoryOptions"
            placeholder="请选择应用分类"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            style="width: 100%"
            allowClear
          >
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="应用排序" prop="appOrder">
          <a-input-number
            style="width: 100%"
            :precision="0"
            :min="0"
            v-model="form.appOrder"
            placeholder="请输入应用排序"
          />
        </a-form-model-item>
        <a-form-model-item label="文档地址" prop="appDocUrl">
          <a-input v-model="form.appDocUrl" placeholder="请输入文档地址" />
        </a-form-model-item>
        <a-form-model-item label="是否可见" prop="visible">
          <a-radio-group v-model="form.visible" :options="visibleOptions">
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="个性化配置" prop="appFlag">
          <a-input
            type="textarea"
            v-model="form.appFlag"
            placeholder="请输入个性化配置"
          />
        </a-form-model-item>
        <a-form-model-item label="路由匹配规则" prop="pathRegx">
          <a-input
            type="textarea"
            v-model="form.pathRegx"
            placeholder="请输入路由匹配规则"
          />
        </a-form-model-item>
        <a-form-model-item label="后端服务" prop="serverPath">
          <a-input
            type="textarea"
            v-model="form.serverPath"
            placeholder="请输入后端服务"
          />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>
<script>
import { add, update, getSingle } from '@/api/system/apps';
import Upload from '@/components/Upload';
import { formRules, formLayout, initForm, goToAppOption } from './constant';
import { visibleOptions } from '@/views/system/constant/system';

export default {
  components: { Upload },
  props: {
    visible: Boolean,
    appId: String,
    appCategoryOptions: Array,
  },
  data() {
    return {
      visibleOptions,
      loading: false,
      formLayout,
      // 表单参数
      form: initForm(),
      // 表单校验
      formRules,
      goToAppOption,
    };
  },
  computed: {
    title() {
      return `${
        this.form.appId || (this.loading && this.appId) ? '编辑' : '新增'
      }应用`;
    },
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.appId) {
          this.getDetail();
        } else {
          this.form = initForm();
        }
      } else {
        this.$refs.form && this.$refs.form.resetFields();
      }
    },
  },
  methods: {
    async getDetail() {
      this.loading = true;
      const [result, error] = await getSingle({
        appId: this.appId,
      });
      this.loading = false;
      if (error) {
        this.form = initForm();
        return;
      }
      this.form = result.data || {};
    },
    /** 提交按钮 */
    submitForm() {
      if (this.loading) return;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const data = {
            ...this.form,
            appCategory: this.form?.appCategory || '',
          };
          const [, error] = this.form.appId
            ? await update(data)
            : await add(data);
          this.loading = false;
          if (error) return;
          this.$message.success(`${this.title}成功`);
          this.$emit('ok');
          this.closeModal();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .ant-form-item {
  margin-bottom: 24px !important;
}
</style>
