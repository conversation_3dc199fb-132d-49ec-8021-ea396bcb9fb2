import moment from 'moment';
// 租户类型，ISV可以创建子租户而COMMON则不行
export const merchantTypeEnum = {
  ISV: 'ISV',
  COMMON: 'COMMON',
};
export const merchantTypeOptions = [
  {
    label: '服务商',
    value: 'ISV',
  },
  {
    label: '普通租户',
    value: 'COMMON',
  },
];
export const getMerchantTypeLabel = (merchantType) => {
  return merchantTypeOptions.find((item) => item.value === merchantType)?.label;
};

// 状态
export const statusEnum = {
  NORMAL: '0',
  ABNORMAL: '1',
};
export const statusOptions = [
  {
    key: statusEnum.NORMAL,
    value: 'NORMAL',
    label: '启用',
    color: 'success',
  },
  {
    key: statusEnum.ABNORMAL,
    value: 'ABNORMAL',
    label: '停用',
    color: 'default',
  },
];
export const getStatusBadgeColor = (value) => {
  return statusOptions.find(
    (item) => item.value === value || item.key === value
  )?.color;
};

export const getStatusLabel = (value) => {
  return statusOptions.find(
    (item) => item.value === value || item.key === value
  )?.label;
};

export const statusOptionsWithAll = [
  {
    value: '',
    label: '全部',
  },
  ...statusOptions,
];
export const filterOption = {
  // 筛选器配置
  config: [
    {
      field: 'merchantName',
      title: '租户名称',
      props: {
        placeholder: '请输入租户名称',
      },
    },
    {
      field: 'merchantType',
      title: '租户类型',
      element: 'a-select',
      props: {
        options: [{ value: '', label: '全部' }, ...merchantTypeOptions],
        showSearch: true,
        optionFilterProp: 'children',
      },
    },
    {
      field: 'status',
      title: '租户状态',
      element: 'a-select',
      props: {
        options: statusOptionsWithAll,
        showSearch: true,
        optionFilterProp: 'children',
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { merchantName: '', status: '', merchantType: '' },
};
export const tableColumns = [
  {
    title: '租户名称',
    dataIndex: 'merchantName',
    field: 'merchantName',
    minWidth: 120,
    slots: {
      default: 'merchantName',
    },
  },

  {
    title: '租户类型',
    dataIndex: 'merchantType',
    field: 'merchantType',
    minWidth: 120,
    slots: {
      default: ({ row }) => {
        return getMerchantTypeLabel(row.merchantType) || '--';
      },
    },
  },
  {
    title: '上级租户',
    dataIndex: 'parentName',
    field: 'parentName',
    minWidth: 120,
    slots: {
      default: ({ row }) => {
        return row?.parentName || '-';
      },
    },
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    field: 'updateTime',
    minWidth: 180,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    field: 'createBy',
    minWidth: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    field: 'status',
    minWidth: 120,
    slots: {
      default: 'status',
    },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    field: 'operation',
    fixed: 'right',
    width: 200,
    slots: {
      default: 'operation',
    },
  },
];

export const rules = {
  merchantName: [
    { required: true, message: '租户名称不能为空', trigger: 'blur' },
  ],
  merchantType: [
    { required: true, message: '租户类型不能为空', trigger: 'blur' },
  ],
  parentId: [
    { required: true, message: '上级服务商不能为空', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '邮箱不能为空', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        try {
          let reg =
            /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
          if (reg.test(value)) {
            callback();
          } else {
            callback(new Error('邮箱格式错误'));
          }
        } catch (err) {
          callback(new Error('邮箱格式错误'));
        }
      },
    },
  ],
  phone: [
    {
      pattern: /^1[2|3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur',
    },
  ],
};
export const formLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 15 },
};
export const replaceFields = {
  children: 'children',
  title: 'label',
  key: 'id',
};
// 初始化表单
export const initForm = (parentId) => {
  return {
    merchantId: undefined,
    merchantType: merchantTypeEnum.ISV,
    // merchantCode: '',
    parentId: parentId || '', //父租户ID
    leader: undefined, //负责人
    phone: undefined,
    email: undefined,
    shortName: undefined,
    // clientPublicKey: undefined, //调用方公钥
    // appIds: [], //选中的应用ID列表
    icon: '',
    status: 'NORMAL', //状态： 0 / NORMAL  启用 ;; 1 / ABNORMAL  停用
  };
};
