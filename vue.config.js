/** @format */
const webpack = require('webpack');
const path = require('path');
const { defineConfig } = require('@vue/cli-service');
const antdColor = require('./src/global/style/antd.color.js');
const TerserPlugin = require('terser-webpack-plugin');

module.exports = defineConfig({
  transpileDependencies: true,
  outputDir: 'dist',
  assetsDir: 'static',
  publicPath:
    process.env.NODE_ENV === 'production' ? '/tkb-platform-front' : '/',
  productionSourceMap: false,
  lintOnSave: false,
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [path.resolve(__dirname, './src/global/style/index.less')],
    },
  },
  css: {
    loaderOptions: {
      less: {
        lessOptions: { modifyVars: antdColor, javascriptEnabled: true },
      },
    },
  },
  devServer: {
    proxy: {
      [process.env.VUE_APP_BASE_API_TKB]: {
        // target: 'http://**************:19090/tkb-admin/',
        // target: 'http://**********:8084/digital-portal-client/tkb-admin/', // 测试服
        // target: 'http://tkb.ncditieyunying.com/tkb-admin', //b端
        // target: 'http://tkb.ncditieyunying.com/tkb-platform/', //a端
        target:
          'http://tkb.ncditieyunying.com/digital-portal-client/tkb-platform/', //a端
        // 'http://***********:18080/digital-portal-client/tkb-platform/', //a端
        // 'http://**************:19090/tkb-platform/', //a端

        // target: 'http://*************:19095/digital-portal-client/tkb-admin/', // 朱为民本地
        ///
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API_TKB]: '',
        },
        changeOrigin: true,
      },
    },
    client: {
      overlay: false,
    },
  },
  configureWebpack: {
    plugins: [
      // Ignore all locale files of moment.js
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/locale$/,
        contextRegExp: /moment$/,
      }),
    ],
    module: {
      rules: [
        {
          test: /\.(wofflwoff2leotlttflttclotf)$/i,
          type: 'assets/resources',
        },
      ],
    },
  },

  chainWebpack: (config) => {
    /**
     * splitChunks 将ant-design-vue从node_modules中取出，总体积没变
     */
    config.optimization.splitChunks({
      chunks: 'all',
      cacheGroups: {
        libs: {
          name: 'chunk-libs',
          test: /[\\/]node_modules[\\/]/,
          priority: 10,
          chunks: 'initial', // only package third parties that are initially dependent
        },
        antdVue: {
          name: 'chunk-antd-vue',
          priority: 20,
          test: /[\\/]node_modules[\\/]_?ant-design-vue(.*)/,
        },
      },
    });

    if (process.env.NODE_ENV === 'production') {
      config.optimization.minimizer('terser').use(TerserPlugin, [
        {
          terserOptions: {
            compress: {
              drop_console: true,
            },
          },
        },
      ]);
    }
  },
});
