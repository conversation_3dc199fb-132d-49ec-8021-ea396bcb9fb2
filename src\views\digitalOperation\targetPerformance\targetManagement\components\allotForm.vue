<template>
  <a-form :form="form">
    <a-row
      type="flex"
      v-for="(k, index) in form.getFieldValue('keys')"
      :key="k"
    >
      <a-col :span="10">
        <a-form-item label="分配园区" v-bind="formItemLayout">
          <a-select
            v-decorator="[
              `area[${k}]`,
              {
                rules: [
                  {
                    required: true,
                    message: '请选择园区',
                  },
                ],
                initialValue: k,
              },
            ]"
            :disabled="needDisabledAreaSelect"
            :options="areaList"
            placeholder="请选择园区"
          />
        </a-form-item>
      </a-col>
      <a-col :span="10">
        <a-form-item v-bind="formItemLayout" label="目标分配量">
          <a-input
            v-decorator="[
              `number[${k}]`,
              {
                validateTrigger: ['change', 'blur'],
                rules: [
                  {
                    required: true,
                    whitespace: true,
                    message: '请填写目标分配量',
                  },
                  { validator: checkNumber, trigger: '请输入正整数' },
                ],
                initialValue: areaInitData[index]?.number || undefined,
              },
            ]"
            placeholder="请填写目标分配量"
          >
            <!-- <a-select
              slot="addonAfter"
              @change="(e) => unitSelectChange(e, k)"
              :default-value="areaInitData[index]?.unit || '1'"
              style="width: 80px"
            >
              <a-select-option value="1"> % </a-select-option>
              <a-select-option value="2"> 个 </a-select-option>
              <a-select-option value="3"> 亿元 </a-select-option>
            </a-select> -->
            <span slot="addonAfter">{{ targetUnit }}</span>
          </a-input>
        </a-form-item>
      </a-col>
      <a-col :span="20">
        <a-form-item
          label="补充说明"
          :labelCol="{ span: 4 }"
          :wrapperCol="{ span: 20 }"
        >
          <a-textarea
            v-decorator="[
              `detail[${k}]`,
              {
                rules: [
                  {
                    required: false,
                    whitespace: true,
                  },
                ],
                initialValue: areaInitData[index]?.detail || undefined,
              },
            ]"
            placeholder="请输入具体要求描述，不超过200个字"
          />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
import { checkNumber } from '@/utils/index';

export default {
  props: {
    areaKeys: {
      type: Array,
      default: () => [],
    },
    areaList: {
      type: Array,
      default: () => [],
    },
    areaInitData: {
      type: Array,
      default: () => [],
    },
    targetUnit: {
      type: String,
      default: () => {},
    },
    //是否禁用园区下拉框
    //默认是禁用
    needDisabledAreaSelect: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    areaKeys(newVal) {
      this.form.setFieldsValue({
        keys: newVal,
      });
    },
  },
  data() {
    return {
      checkNumber,
      unitMap: new Map(),
      formItemLayout: {
        labelCol: {
          span: 8,
        },
        wrapperCol: {
          span: 16,
        },
      },
    };
  },
  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'dynamic_form_item' });
  },
  created() {
    this.form.getFieldDecorator('keys', {
      initialValue: this.areaKeys,
      preserve: true,
    });
  },
  methods: {
    unitSelectChange(e, k) {
      this.unitMap.set(k, e);
    },
    //数据格式转换
    resolveData(values) {
      return values.keys.map((item) => {
        return {
          area: values.area[item],
          number: values.number[item],
          detail: values.detail[item],
          //如果取不到单位，就默认
          unit: this.unitMap.get(item) || '1',
        };
      });
    },
  },
};
</script>
<style>
.dynamic-delete-button {
  cursor: pointer;
  position: relative;
  top: 4px;
  font-size: 24px;
  color: #999;
  transition: all 0.3s;
}
.dynamic-delete-button:hover {
  color: #777;
}
.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}
</style>
