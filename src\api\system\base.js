import request from '@/utils/request';

// 登录
export function login(username, password, code, requestNo) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/login',
    method: 'post',
    data: {
      username,
      password,
      code,
      requestNo,
    },
  });
}

// 退出方法
export function logout() {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/logout',
    method: 'get',
  });
}
// 首次重置密码
export function changeFirstPassword(data) {
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/user/resetPwdFirst',
    method: 'post',
    data: data,
  });
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/getInfo',
    method: 'get',
  });
}

// 获取动态路由配置
export const getRouters = (params) => {
  const { appId } = params;
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/getRouters',
    method: 'get',
    params: appId !== 'undefined' ? params : {},
  });
};
