<template>
  <!-- 添加或修改菜单对话框 -->
  <a-modal
    :title="title"
    :visible="visible"
    :loading="loading"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
    width="760px"
    @ok="submitForm"
    @cancel="closeModal"
  >
    <a-form-model ref="form" :model="form" :rules="rules" v-bind="formLayout">
      <a-form-model-item
        label="租户类型"
        prop="merchantType"
        @change="changeMerchantType"
      >
        <a-radio-group
          :options="merchantTypeOptions"
          v-model="form.merchantType"
        />
      </a-form-model-item>
      <a-form-model-item
        v-if="form.merchantType === 'ISV'"
        label="上级服务商"
        prop="parentId"
      >
        <treeselect
          style="line-height: 35px"
          v-model="form.parentId"
          :required="true"
          :options="merchantListOptions"
          placeholder="请选择租户类型"
          :default-expand-level="10"
        />
      </a-form-model-item>
      <a-form-model-item
        label="上级服务商"
        v-if="form.merchantType === 'COMMON'"
      >
        {{ merchantName || '' }}
      </a-form-model-item>
      <a-form-model-item label="租户名称" prop="merchantName">
        <a-input
          :max-length="30"
          v-model="form.merchantName"
          placeholder="请输入租户名称"
        />
      </a-form-model-item>
      <a-form-model-item label="租户简称" prop="shortName">
        <a-input
          v-model="form.shortName"
          placeholder="请输入租户简称"
          :max-length="12"
        />
      </a-form-model-item>
      <a-form-model-item label="租户icon" prop="icon">
        <Upload
          class="icon-upload"
          list-type="picture-card"
          :limitSize="2"
          :file-list.sync="form.icon"
          :data="{
            fileType: 'IMAGE',
          }"
        />
      </a-form-model-item>
      <a-form-model-item
        label="管理员用户名"
        v-if="merchantId ? form.adminName : true"
        prop="adminName"
        :rules="[
          {
            required: !merchantId,
            message: '管理员用户名不能为空',
            trigger: 'blur',
          },
        ]"
      >
        <a-input
          v-if="!merchantId"
          v-model="form.adminName"
          placeholder="请输入管理员用户名"
          :max-length="25"
        />
        <div v-else>{{ form.adminName || '-' }}</div>
      </a-form-model-item>
      <a-form-model-item label="联系人" prop="leader">
        <a-input
          v-model="form.leader"
          placeholder="请输入联系人"
          :max-length="20"
        />
      </a-form-model-item>
      <a-form-model-item label="联系电话" prop="phone">
        <a-input
          v-model="form.phone"
          placeholder="请输入联系电话"
          :max-length="11"
        />
      </a-form-model-item>
      <a-form-model-item label="邮箱" prop="email">
        <a-input
          v-model="form.email"
          placeholder="请输入邮箱"
          :max-length="50"
        />
      </a-form-model-item>
      <a-form-model-item label="租户状态" prop="status">
        <a-radio-group v-model="form.status" :options="statusOptionsSecond">
        </a-radio-group>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import {
  getMerchant,
  addMerchant,
  updateMerchant,
} from '@/api/system/merchant';
import Upload from '@/components/Upload';
import { mapState } from 'vuex';
import { aseDecrypt, rsaCode } from '@/utils/common/auth';
import { statusOptionsSecond } from '@/views/system/constant/system';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import {
  merchantTypeOptions,
  initForm,
  rules,
  formLayout,
} from './constant.js';
export default {
  components: {
    Upload,
    Treeselect,
  },
  props: {
    visible: Boolean,
    merchantId: String,
    parentId: String,
    merchantName: String,
    merchantListOptions: Array,
  },
  computed: {
    ...mapState('base', ['merchant']),
    title() {
      return `${
        this.form.merchantId || (this.loading && this.merchantId)
          ? '编辑'
          : '新增'
      }租户`;
    },
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.merchantId) {
          this.getDetail();
        } else {
          this.form = initForm(this.parentId);
        }
      } else {
        this.$refs.form && this.$refs.form?.resetFields();
      }
    },
  },
  data() {
    return {
      loading: false,
      formLayout,
      // 状态数据字典
      statusOptionsSecond,
      merchantTypeOptions,
      // 表单参数
      form: initForm(this.parentId),
      // 表单校验
      rules,
    };
  },
  mounted() {},
  methods: {
    changeMerchantType() {
      this.form.parentId = this.merchantId;
    },
    /**
     * 获取菜单详情
     */
    async getDetail() {
      this.loading = true;
      const [result, error] = await getMerchant(this.merchantId);
      this.loading = false;
      if (error) {
        this.form = initForm();
        return;
      }
      const { phone, email, ...req } = result.data;
      this.form = {
        ...req,
        email: email ? aseDecrypt(email) : email,
        phone: phone ? aseDecrypt(phone) : phone,
      };
    },
    /**
     * 提交按钮
     */
    submitForm() {
      if (this.loading) return;
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const { phone, email, ...req } = this.form;
          const queryParams = {
            ...req,
            email: email ? rsaCode(email) : email,
            phone: phone ? rsaCode(phone) : phone,
          };
          const [, error] = this.form.merchantId
            ? await updateMerchant(queryParams)
            : await addMerchant(queryParams);
          this.loading = false;
          if (error) return;
          this.$message.success(`${this.title}成功`);
          this.$emit('ok');
          this.closeModal();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .ant-form-item {
  margin-bottom: 16px !important;
}

.icon-upload {
  margin-bottom: -24px;
}
</style>
