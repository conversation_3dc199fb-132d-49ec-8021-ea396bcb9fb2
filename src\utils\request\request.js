import axios from 'axios';
import { getToken } from '@/utils/common/auth';
import { paramsEncryption, dataDecrypt, responseHandle } from './handles';
import { requestMock } from './requestMock';
import { mockList } from './mockList';
import { getBaseUrl } from '@/utils/common/util.js';
const SYSTEM_ERROR = {
  // TODO:
  message: '系统异常，请重试',
};
const BASE_URL = process.env.VUE_APP_USE_BUILD_TYPE
  ? getBaseUrl()
  : process.env.VUE_APP_BASE_API_TKB;
// 创建axios实例
export const myAxios = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: BASE_URL,
  // 超时
  timeout: 30000,
});
myAxios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
// if (process.env.VUE_APP_IS_LOCAL) {
//   myAxios.defaults.headers['C-RANDOM'] = 'operation';
// }
/**
 * 请求拦截器
 */
myAxios.interceptors.request.use(
  (config) => {
    if (getToken()) {
      config.headers.Authorization = getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // 非开发环境请求参数加密
    if (process.env.NODE_ENV !== 'development') {
      config.params = paramsEncryption(config.params);
      config.data = paramsEncryption(config.data);
    } else {
      // config.headers['C-RANDOM'] = 'operation';
    }
    return config;
  },
  () => {
    return Promise.reject(SYSTEM_ERROR);
  }
);

/**
 * 响应拦截器
 */
myAxios.interceptors.response.use(
  (response) => {
    if (response.status !== 200) {
      return Promise.reject(SYSTEM_ERROR);
    }
    return response;
  },
  () => {
    return Promise.reject(SYSTEM_ERROR);
  }
);

/**
 * @param {*} config 请求参数
 * @param {*} extend 扩展配置
 * @returns
 */
export const request = async (config, extend) => {
  // 判断非开发环境中是否有mock接口配置
  if (process.env.NODE_ENV !== 'development' && mockList && mockList.length) {
    alert('当前非开发环境，请删除mock接口配置');
  }

  // 判断当前接口是否在mock列表中
  const isMock =
    (mockList &&
      mockList.length &&
      mockList.find((item) => item === config.url)) ||
    false;
  if (isMock) {
    // 去请求mock
    return requestMock(config);
  }

  try {
    let res = await myAxios(config);
    // 非开发环境接口出参解密
    if (process.env.NODE_ENV !== 'development') {
      res = dataDecrypt(res);
    }
    return responseHandle(res, extend);
  } catch (error) {
    // 处理404等code不等于200情况
    return responseHandle(
      {
        status: 400,
        msg: error.message,
      },
      extend
    );
  }
};
