<template>
  <page-layout>
    <div class="container">
      <PageWrapper
        style="margin: 0"
        title="Bucket列表"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        @loadData="documentBucketList"
        @handleReset="handleReset"
      >
        <!-- 创建按钮区域插槽 -->
        <template #defaultHeader>
          <a-button
            icon="plus"
            type="primary"
            style="margin-right: 8px"
            @click="onClickAdd"
          >
            新增
          </a-button>
        </template>
        <!-- filter插槽 -->
        <template #identity="{ item }">
          <a-input
            v-model="filterOptions.params[item.field]"
            placeholder="AutoFilter插槽"
          />
        </template>
        <!-- 状态 -->
        <template #status="{ row }">
          <a-switch
            :checked="row.status === 'NORMAL'"
            @change="onChange(row)"
          />
        </template>
        <!-- table插槽 -->
        <template #operate="{ row }">
          <a-button
            type="link"
            @click="onClickDuplicate(row)"
            :disabled="row.isDefault"
          >
            设为默认
          </a-button>
          <a-button type="link" @click="onClickEdit(row)"> 编辑 </a-button>
          <a-button type="link" @click="onClickDelete(row)"> 删除 </a-button>
        </template>
        <!-- 编辑弹窗 -->
        <ModalPup
          :visible="visible"
          :fromObejct="fromObejct"
          @hlandSumbit="hlandSumbit"
          @handleCancel="handleCancel"
        />
      </PageWrapper>
    </div>
  </page-layout>
</template>

<script>
import ModalPup from './components/Modal.vue';
import { Modal } from 'ant-design-vue';
import { filterOptions, tableColumn } from './constant';
import {
  documentBucketList,
  documentBucketAsDefault,
  documentBucketChangeStatus,
  documentBucketRemove,
  documentBucketAdd,
  documentBucketEdit,
} from '@/api/system/documentCenter/bucketApi';
export default {
  components: { ModalPup },
  data() {
    return {
      disabled: true,
      loading: false,
      filterOptions,
      // 表头
      tableColumn: tableColumn(),
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      visible: false,
      center: 'center',
      fromObejct: {},
      isEditOrAdd: false, // 判断是否是新增还是编辑 开关阀
      configId: '',
    };
  },
  created() {
    this.documentBucketList();
  },
  methods: {
    // handleReset 重置
    handleReset() {
      for (let key in this.filterOptions.params) {
        this.filterOptions.params[key] = undefined;
      }
      this.filterOptions.params.serverType = '';
      this.filterOptions.params.status = '';
      this.tablePage.currentPage = 1;
      this.documentBucketList();
    },
    onClickEdit(row) {
      const {
        bucketName = '',
        proxyUsername = '',
        isUsedProxy = true,
        serverType = 'OSS',
        proxyHost = '',
        proxyPassword = '',
        accessId = '',
        proxyPort = '',
        endpoint = '',
        proxyWorkstation = '',
        proxyDomain = '',
        accessKey = '',
        remark = '',
        configId,
        publicEndpoint = '',
      } = row;
      this.fromObejct = {
        bucketName,
        proxyUsername,
        isUsedProxy,
        serverType,
        proxyHost,
        proxyPassword,
        accessId,
        proxyPort,
        endpoint,
        publicEndpoint,
        proxyWorkstation,
        proxyDomain,
        accessKey,
        remark,
      };
      this.visible = true;
      this.isEditOrAdd = true;
      this.configId = configId;
    },
    // 设为默认
    onClickDuplicate(row) {
      Modal.confirm({
        title: '提示',
        content: '确定设定' + row.bucketName + '默认存储对象',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.documentBucketAsDefault(row.configId);
        },
      });
    },
    // 删除
    onClickDelete(row) {
      Modal.confirm({
        title: '警告',
        content:
          '删除后将无法再对该' +
          row.bucketName +
          '存储内容进行读写操作，请谨慎操作！',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.documentBucketRemove(row.configId);
        },
      });
    },
    handleCancel(update) {
      if (update) {
        this.documentBucketList();
      }
      this.visible = false;
    },
    // 切换状态
    onChange(row) {
      const status = row.status === 'NORMAL' ? 'ABNORMAL' : 'NORMAL';
      this.documentBucketChangeStatus(row.configId, status);
    },
    onClickAdd() {
      this.isEditOrAdd = false;
      this.visible = true;
    },
    // 新增 or 编辑
    hlandSumbit(val) {
      if (this.isEditOrAdd) {
        this.documentBucketEdit(val);
      } else {
        this.documentBucketAdd(val);
      }
    },
    /**
     * 接口处理数据Start
     */
    // 查询bucket配置列表
    async documentBucketList() {
      this.loading = true;
      const params = this.filterOptions.params;
      const getData = {
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
        ...params,
      };
      const [result, error] = await documentBucketList(getData);
      this.loading = false;
      if (error) return;
      const { data, count } = result;
      this.tableData = data;
      this.tablePage.total = count;
    },
    // 设为默认配置
    async documentBucketAsDefault(configId) {
      const [result, error] = await documentBucketAsDefault(configId);
      if (error) return;
      if (result) {
        this.$message.success('设置默认成功');
        this.documentBucketList();
      }
    },
    // 切换状态
    async documentBucketChangeStatus(configId, status) {
      const getData = {
        configId,
        status,
      };
      const [result, error] = await documentBucketChangeStatus(getData);
      if (error) return;
      if (result) {
        this.$message.success('设置成功');
        this.documentBucketList();
      }
    },
    // 移除bucket配置信息
    async documentBucketRemove(configId) {
      const [result, error] = await documentBucketRemove(configId);
      if (error) return;
      if (result) {
        this.$message.success('删除成功');
        this.documentBucketList();
      }
    },
    // 新增
    async documentBucketAdd(val) {
      const getData = {
        ...val,
      };
      const [result, error] = await documentBucketAdd(getData);
      if (error) return;
      if (result) {
        this.visible = false;
        this.$message.success('新增成功');
        this.documentBucketList();
      }
    },
    // 编辑
    async documentBucketEdit(val) {
      const getData = {
        configId: this.configId,
        ...val,
      };
      const [result, error] = await documentBucketEdit(getData);
      if (error) return;
      if (result) {
        this.$message.success('更新成功');
        this.documentBucketList();
        this.visible = false;
      }
    },
    /**
     * 接口处理数据End
     */
  },
};
</script>

<style lang="less" scoped>
.container {
  background-color: #f4f4f4;
}
// table操作按钮
.operate-button {
  display: inline-block;
  margin-right: 16px;
  padding: 8px 0;
  color: #1677ff;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  cursor: pointer;
}
/deep/ .ant-btn {
  padding: 0px 8px 0px 8px;
}
/deep/.ant-select-dropdown-menu-item {
  text-align: left;
}
</style>
