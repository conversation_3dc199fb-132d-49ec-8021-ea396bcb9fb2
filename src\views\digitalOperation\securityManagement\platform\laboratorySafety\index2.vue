<template>
  <BuseCrud
    ref="crud"
    title="检查列表"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :modalConfig="modalConfig"
    @modalConfirm="modalConfirmHandler"
    @rowDel="deleteRowHandler"
    @handleCreate="rowAdd"
    @loadData="loadData"
  >
    <template slot="uploadFile">
      <uploadPics
        :accept="'.jpg, .gif, .png'"
        @setNewsImgs="setNewsImgs"
        :fileListTemp="pictures"
        :maxSize="10000"
      />
    </template>
  </BuseCrud>
</template>

<script>
import uploadPics from '@/components/Uploads/uploadPics.vue';
export default {
  name: 'LaboratorySafety',
  components: {
    uploadPics,
  },
  data() {
    return {
      pictures: [],
      menuShow: true,
      tableData: [],
      tableColumn: [],
      params: { checkTime: undefined, isCorrect: '0' },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'checkTime',
            title: '检查时间',
            element: 'a-range-picker',
          },
          {
            title: '是否有整改项',
            field: 'isCorrect',
            element: 'a-select',
            props: {
              options: [
                {
                  label: '全部',
                  value: '0',
                },
                {
                  label: '是',
                  value: '1',
                },
                {
                  label: '否',
                  value: '2',
                },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: true,
        editBtn: false,
        menu: true,
        viewBtnText: '查看详情',
        addTitle: '实验室安全检查情况录入',
        formConfig: [
          {
            title: '检查对象',
            field: 'checkName',
            rules: [
              {
                required: true,
                message: '请选择检查对象',
              },
            ],
          },
          {
            title: '检查时间',
            field: 'checkTime',
            element: 'a-date-picker',
            rules: [
              {
                required: true,
                message: '请选择检查时间',
              },
            ],
          },
          {
            title: '是否有整改项',
            field: 'isCorrect',
            element: 'a-select',
            props: {
              options: [
                {
                  label: '是',
                  value: '1',
                },
                {
                  label: '否',
                  value: '2',
                },
              ],
            },
            previewFormatter: (value) => {
              if (value === '1') {
                return '是';
              } else if (value === '2') {
                return '否';
              }
            },
            rules: [
              {
                required: true,
                message: '请选择是否有整改项',
              },
            ],
          },
          {
            title: '上传附件',
            field: 'uploadFile',
            element: 'slot',
            slotName: 'uploadFile',
            previewFormatter: (value) => {
              return value.map((item) => {
                return (
                  <a href={item.url} class="mr10">
                    {item.fileName}
                  </a>
                );
              });
            },
          },
        ],
      };
    },
  },
  created() {
    this.tableColumn = [
      {
        title: '检查时间',
        field: 'checkTime',
      },
      {
        title: '检查对象',
        field: 'checkName',
      },
      {
        title: '是否有整改项',
        field: 'checkItem',
        formatter({ cellValue }) {
          return cellValue === '1' ? '是' : '否';
        },
      },
    ];
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      const result = await new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: 200,
            data: [
              {
                checkTime: '2020-01-01',
                checkName: '张三',
                isCorrect: '1',
                uploadFile: [
                  {
                    fileName: '11.jpg',
                    url: '/static/images/11.jpg',
                  },
                  {
                    fileName: '13.jpg',
                    url: '/static/images/13.jpg',
                  },
                ],
              },
            ],
          });
        }, 1000);
      });
      this.loading = false;
      this.tableData = result.data;
      this.tablePage.total = result.total;
    },
    modalConfirmHandler(formValues) {
      console.log('modalConfirmHandler', formValues);
    },
    deleteRowHandler() {
      this.$confirm({
        title: '确认删除',
        content: '是否删除该行数据',
        onOk: () => {
          this.$message.success('删除成功');
        },
        onCancel: () => {
          this.$message.error('取消删除');
        },
      });
    },
    setNewsImgs(fileList) {
      this.pictures = fileList;
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, 'ADD');
    },
  },
};
</script>

<style lang="less" scoped></style>
