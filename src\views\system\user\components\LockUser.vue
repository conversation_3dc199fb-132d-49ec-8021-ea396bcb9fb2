<template>
  <a-modal
    title="锁定用户"
    okText="确定"
    cancelText="取消"
    :maskClosable="false"
    width="400px"
    :visible="visible"
    @ok="handleOk"
    @cancel="closeModal"
  >
    <a-spin :spinning="loading">
      <a-form-model :model="form" ref="form" :rules="rules">
        <a-form-model-item label="锁定时间（分钟）" prop="lockDuration">
          <a-input-number
            :min="0"
            style="width: 99%"
            allow-clear
            v-model="form.lockDuration"
            placeholder="请输入锁定时间（分钟）"
          />
        </a-form-model-item>
        <a-form-model-item label="锁定原因" prop="reason">
          <a-input
            type="textarea"
            size="large"
            allow-clear
            v-model="form.reason"
            :max-length="100"
            :auto-size="{ minRows: 4, maxRows: 10 }"
            placeholder="请再次输入锁定原因"
          >
          </a-input>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { lockUser } from '@/api/system/user';
import { initForm2LockUser, formRule2LockUser } from '../constant';

export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: String,
    },
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.form = initForm2LockUser();
        } else {
          this.$refs.form && this.$refs.form.resetFields();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      form: initForm2LockUser(),
      rules: formRule2LockUser,
    };
  },
  created() {},
  methods: {
    async handleOk() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          if (this.loading) return;
          this.loading = true;
          const { lockDuration, reason } = this.form;
          const [, error] = await lockUser({
            userId: this.userId,
            reason,
            lockDuration: lockDuration * 60000, //转化成毫秒
          });
          this.loading = false;
          if (error) return;
          this.$message.success('锁定成功');
          this.$emit('ok');
          this.closeModal();
        }
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped></style>
