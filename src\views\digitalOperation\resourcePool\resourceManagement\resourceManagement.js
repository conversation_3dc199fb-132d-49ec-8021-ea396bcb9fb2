function getTableColumn(shelfStatus = '1') {
  const _this = this;
  return [
    {
      type: 'checkbox',
      width: '50px',
    },
    {
      title: '资源类型',
      field: 'resType',
      formatter({ cellValue }) {
        const currentBiz = _this.resourceType.filter((item) => {
          return item.value == cellValue;
        });
        return currentBiz[0] ? currentBiz[0].label : cellValue;
      },
    },
    {
      title: '资源名称',
      field: 'resName',
    },
    {
      title: '联系人',
      field: 'contractPerson',
    },
    {
      title: '联系人电话',
      field: 'contractPhone',
    },
    {
      title: '发布时间',
      field: 'releaseTime',
      visible: shelfStatus === '1',
    },
    {
      title: '下架时间',
      field: 'delistTime',
      visible: shelfStatus === '2',
    },
    // {
    //   title: '推送状态',
    //   field: 'pushStatus',
    //   formatter({ cellValue }) {
    //     return cellValue == 0 ? '未推送' : '已推送';
    //   },
    // },

    {
      title: '推送方式',
      field: 'pushType',
      formatter: ({ cellValue }) => {
        return cellValue || cellValue == 0
          ? {
              2: '公开推送',
              1: '按标签推送',
              0: '定向推送',
              3: '按标签推送、定向推送',
            }[+cellValue]
          : '/';
      },
    },
  ];
}
/**
 *
 * @param {string} pushStatus 推送类型：1已推送、2撤销推送
 * @returns
 */
function getPushTableColumn(pushStatus = '1') {
  return [
    // {
    //   type: 'checkbox',
    //   width: '50px',
    // },
    {
      title: '资源类型',
      field: 'resType',
    },
    {
      title: '资源名称',
      field: 'resName',
    },
    {
      title: '资源状态',
      field: 'shelfStatus',
      formatter({ cellValue }) {
        return cellValue === '1' ? '在架' : '已下架';
      },
    },
    {
      title: '已推送人',
      field: 'pushTarget',
    },
    {
      title: '推送人单位',
      field: 'pushTargetUnit',
    },
    {
      title: '推送时间',
      field: 'pushTime',
      visible: pushStatus === '1',
    },
    {
      title: '查看状态',
      field: 'checkState',
      visible: pushStatus === '1',
      formatter({ cellValue }) {
        return cellValue == '1' ? '已查看' : '未查看';
      },
    },
    {
      title: '撤销推送时间',
      field: 'cancelPushTime',
      visible: pushStatus === '2',
    },
  ];
}

export { getTableColumn, getPushTableColumn };
