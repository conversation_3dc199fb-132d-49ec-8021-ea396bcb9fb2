<template>
  <a-modal
    :title="`用户信息`"
    okText="确定"
    cancelText="取消"
    width="500px"
    :visible="visible"
    :footer="null"
    @cancel="closeModal"
    :bodyStyle="{ padding: '24px 48px' }"
  >
    <a-spin :spinning="loading">
      <a-descriptions title="">
        <a-descriptions-item label="用户名称" :span="3">
          {{ userDetail.userName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="用户昵称" :span="3">
          {{ userDetail.jobNumber || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="手机号" :span="3">
          {{
            (userDetail.phonenumber && aseDecrypt(userDetail.phonenumber)) ||
            '-'
          }}
        </a-descriptions-item>
        <a-descriptions-item label="邮箱" :span="3">
          {{ (userDetail.email && aseDecrypt(userDetail.email)) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="所属租户" :span="3">
          {{ (userDetail.merchant && userDetail.merchant.merchantName) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="所属组织" :span="3">
          {{ userDetail.userName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="岗位组" :span="3">
          {{ userDetail.userName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="角色" :span="3">
          {{ userDetail.roleNameInfos || '-' }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="密码" :span="3">
          <a-button type="link" @click="resetPassword">重置密码</a-button>
        </a-descriptions-item> -->
      </a-descriptions>
    </a-spin>
  </a-modal>
</template>

<script>
// updateUser
import { getUser } from '@/api/system/user';
import { aseDecrypt } from '@/utils/common/auth';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: String,
      default: '',
    },
  },

  watch: {
    visible(val) {
      if (val) {
        this.getDetail();
      }
    },
  },
  data() {
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      userDetail: {},
      loading: false,
    };
  },
  methods: {
    aseDecrypt,
    async getDetail() {
      this.loading = true;
      const [result, error] = await getUser(this.userId);

      this.loading = false;
      if (error) return;
      const { user = {} } = result.data;
      this.userDetail = {
        ...user,
        roleNameInfos: user.roleInfos?.map((item) => item.roleName).join(','),
      };
    },
    /**
     * 关闭弹窗
     */
    resetPassword() {
      this.$confirm({
        title: '提示',
        content: () => (
          <div>
            重置密码后当前密码失效，通过邮箱自动获取重置之后的密码；确认要重置密码吗
          </div>
        ),
        async onOk() {
          this.$message.success('重置成功');
        },
      });
    },
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style lang="less" scoped></style>
