<template>
  <a-modal
    width="600px"
    title="标签管理"
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm ref="ruleForm" :config="formConfig" :params="formValue">
        <!-- 园区 -->
        <template #enterSlot>
          <a-select
            :allowClear="true"
            :filterOption="filterOption"
            @change="onProductChange"
            :showSearch="true"
            mode="multiple"
            placeholder="请选择"
            v-model="formValue.labelId"
          >
            <a-select-option
              v-for="item in label"
              :key="item.id"
              :value="item.id"
            >
              {{ item.labelName }}
            </a-select-option>
          </a-select>
        </template>
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import { initFormLabelValue } from '../constant';
import { editLabel } from '@/api/basicData';
export default {
  props: ['visible', 'detail', 'modelTitle', 'label'],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          this.formValue = {
            ...initFormLabelValue(),
            ...this.detail,
            labelId: this.detail.labelId?.split(','),
          };
          console.log('初始数据', this.formValue);
        } else {
          this.formValue = initFormLabelValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormLabelValue(),
      choseLabel: '',
    };
  },
  computed: {
    formConfig() {
      const formConfig = [
        {
          field: 'labelId',
          title: '标签',
          element: 'slot',
          slotName: 'enterSlot',
          // props: {
          //   showSearch: true,
          //   filterOption: this.filterOption,
          //   options: this.label,
          //   mode: 'multiple'
          // },
        },
      ];
      return formConfig;
    },
  },
  methods: {
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        console.log(valid);
        if (valid) {
          this.formValue.labelId = this.choseLabel;
          this.editLabel();
          this.handleCancel();
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 编辑
    async editLabel() {
      const [, err] = await editLabel(this.formValue);
      if (err) return;
      this.$message.success('编辑成功!');
      this.$emit('loadData');
    },
    // 选中标签下拉
    onProductChange(val) {
      this.choseLabel = val.join(',');
    },
  },
};
</script>
<style scoped>
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
