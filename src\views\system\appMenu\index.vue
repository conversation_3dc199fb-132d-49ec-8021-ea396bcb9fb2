<template>
  <page-layout :title="`【${appName}】菜单管理`">
    <a-row
      type="flex"
      justify="space-between"
      class="menu-wrapper"
      :gutter="[16, 0]"
    >
      <a-col :span="4">
        <a-card
          title="菜单树"
          :bordered="false"
          style="height: 100%"
          :bodyStyle="{
            'padding-top': '12px',
            overflow: 'auto',
          }"
        >
          <a
            v-if="menuTree && menuTree.length"
            slot="extra"
            href="javascript:;"
            @click="() => (showAll = !showAll)"
          >
            <a-icon v-if="showAll" type="shrink" />
            <a-icon v-else type="arrows-alt" />
            {{ showAll ? '收起全部' : '展开全部' }}
          </a>
          <a-input-search
            style="width: 100%"
            v-model="menuTreeFilterName"
            size="small"
          />
          <a-spin :spinning="treeLoading">
            <a-tree
              v-if="showTree"
              ref="tree"
              show-icon
              :expandedKeys="expandedKeys"
              :selectedKeys="selectedKeys"
              :default-expand-all="showAll"
              :treeData="menuTree"
              :replaceFields="replaceFields"
              :filterTreeNode="filterTreeNode"
              @select="handleNodeClick"
              @expand="handleNodeExpand"
            >
              <!-- <a-icon slot="directory" type="cluster" /> -->
              <a-icon slot="directory" type="cluster" />
              <a-icon slot="menu" type="bars" />
              <a-icon slot="file" type="file" />
              <a-icon slot="button" type="alert" />
            </a-tree>
          </a-spin>
        </a-card>
      </a-col>
      <a-col :span="20">
        <!-- 表格 -->
        <PageWrapper
          style="margin: 0"
          :title="`${
            currentSelectMenuName ? `【${currentSelectMenuName}】` : ''
          }菜单列表`"
          createText=""
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="tableColumns"
          :tableProps="{ 'pager-config': null }"
          :tableData="menuList"
          @loadData="loadData"
          @handleReset="handleReset"
          @handleCreate="handleAdd"
        >
          <template #defaultHeader>
            <a-button
              v-hasPermi="['system:appMenu:add']"
              style="margin-right: 8px"
              type="primary"
              :loading="loading"
              @click="handleAdd()"
            >
              新增菜单
            </a-button>
          </template>
          <template #icon="{ row }">
            <a-icon v-if="row.icon" :type="row.icon" />
          </template>
          <template #menuType="{ row }">
            <a-tag :color="getMenuTypeInfo(row.menuType).color">
              {{ getMenuTypeInfo(row.menuType).label }}
            </a-tag>
          </template>
          <template #visible="{ row }">
            <a-badge v-bind="getVisibleBadgeInfo(row.visible)" />
          </template>
          <template #operation="{ row }">
            <a-button
              icon="edit"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleUpdate(row)"
            >
              编辑
            </a-button>
            <a-button
              v-hasPermi="['system:appMenu:add']"
              icon="plus"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleAdd(row)"
            >
              新增子菜单
            </a-button>
            <a-button
              v-hasPermi="['system:appMenu:delete']"
              v-if="row.userId !== 1"
              icon="delete"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleDelete(row)"
            >
              删除
            </a-button>
          </template>
        </PageWrapper>
      </a-col>
    </a-row>
    <!-- 菜单编辑 -->
    <EditModal
      :visible.sync="editModalVisible"
      :appId="appId"
      v-bind="editModalData"
      @ok="refresh"
    />
  </page-layout>
</template>
<script>
// 菜单接口
import {
  listMenu,
  treeselect,
  delMenu,
  pointListMenu,
} from '@/api/system/menu';
// 获取app列表接口
import { getSingle } from '@/api/system/apps';
import { recursionDataSlide } from '@/utils';
import { handleTreeToArray } from '@/utils/common/tree';
import EditModal from './EditModal.vue';
import { Modal } from 'ant-design-vue';
import {
  filterOptions,
  tableColumns,
  menuTypeOptions,
  replaceFields,
} from './constant.js';
import {
  visibleOptions,
  getVisibleBadgeInfo,
} from '@/views/system/constant/system';

export default {
  components: { EditModal },
  data() {
    return {
      visibleOptions,
      // 菜单树
      // 菜单树选项
      menuTreeFilterName: '',
      showAll: false,
      showTree: false,
      treeLoading: true,
      menuTree: undefined,
      replaceFields,
      selectedKeys: [],
      expandedKeys: [],
      // 遮罩层
      loading: true,
      // 搜索数据
      filterOptions,
      // 表格表头
      tableColumns,
      // 表格数据
      menuList: [],
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      // 应用名称
      appName: '',
      // 是否显示弹出层
      editModalVisible: false,
      editModalData: {
        parentId: '',
        menuId: '',
      },
      parentId: '',
    };
  },
  computed: {
    appId() {
      return this.$route?.params?.id || '';
    },
    menuTreeArray() {
      return handleTreeToArray(this.menuTree);
    },
    currentSelectMenuName() {
      if (this.parentId) {
        return (
          this.menuTreeArray.find((item) => item.id === this.parentId)?.label ||
          ''
        );
      } else {
        return '';
      }
    },
  },
  watch: {
    // 根据名称筛选部门树
    menuTreeFilterName(val) {
      if (val) {
        this.filterTreeNode(this.$refs.tree);
      }
    },
    // 展开所有树节点
    showAll(val) {
      if (val) {
        this.expandedKeys = this.menuTreeArray.map((item) => item.id);
      } else {
        this.expandedKeys = [];
      }
    },
  },
  async created() {
    // 获取应用菜单详情
    this.getAppDetail();
    // 获取树结构
    this.getMenuTree();
    this.loadData();
  },
  methods: {
    /**
     * 数据转化函数
     */
    getVisibleBadgeInfo,
    getMenuTypeInfo(val) {
      return menuTypeOptions.find((item) => item.value === val) || {};
    },
    /**
     * 查询应用详情
     */
    async getAppDetail() {
      this.loading = true;
      const [result, error] = await getSingle({
        appId: this.appId,
      });
      this.loading = false;
      if (error) return;
      const { appName } = result.data || {};
      this.appName = appName;
    },
    /**
     * 查询菜单下拉树结构
     */
    async getMenuTree() {
      this.treeLoading = true;
      const [result, error] = await treeselect({
        appId: this.appId,
      });
      this.treeLoading = false;
      if (error) return;
      const data = recursionDataSlide(result.data);
      this.showTree = true;
      this.menuTree = data;
    },
    handleReset() {
      this.filterOptions.params = { menuName: '', visible: '' };
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      this.selectedKeys = [];
      this.loadData();
    },
    /**
     * 列表查询
     */
    handleSearch(filterParams) {
      if (filterParams) {
        this.queryParams = filterParams;
      }
      if (this.selectedKeys && this.selectedKeys[0]) {
        this.handleNodeClick(this.selectedKeys);
      } else {
        this.loadData();
      }
    },
    /**
     * 表格数据请求
     */
    async loadData() {
      // 请求列表
      this.loading = true;
      const params = this.filterOptions.params;
      const [result, error] = await listMenu({
        ...params,
        appId: this.appId,
      });
      this.loading = false;
      if (error) return;
      this.menuList = result.data;
    },
    /** 获取菜单的子项 */
    async handleNodeClick(data) {
      const menuId = data && data[0];
      if (!menuId) {
        return;
      } else {
        this.selectedKeys = data;
        this.parentId = menuId;
      }
      this.loading = true;
      const [result, error] = await pointListMenu({
        ...this.queryParams,
        appId: this.appId,
        parentId: menuId,
      });
      this.loading = false;
      if (error) return;
      this.menuList = result.data;
    },
    /**
     * 筛选节点
     */
    filterTreeNode(node) {
      if (this.menuTreeFilterName && node.title) {
        return node.title.indexOf(this.menuTreeFilterName) !== -1;
      } else return false;
    },
    /** 节点点击展开 */
    async handleNodeExpand(data) {
      this.expandedKeys = data;
    },
    /**
     * 菜单新增删除后的刷新操作
     */
    refresh() {
      this.getMenuTree();
      this.handleSearch();
    },
    /**
     * 新增按钮操作
     */
    handleAdd(row) {
      this.editModalData = {
        menuId: '',
        parentId: row?.menuId || undefined,
      };
      this.editModalVisible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.editModalData = {
        menuId: row.menuId,
        parentId: '',
      };
      this.editModalVisible = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.loading) return;
      Modal.confirm({
        title: '警告',
        content: '是否确认删除名称为"' + row.menuTitle + '"的数据项?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.loading = true;
          const [, error] = await delMenu({ menuId: row.menuId });
          this.loading = false;
          if (error) return;
          this.$message.success('删除成功');
          this.refresh();
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.table-title {
  font-weight: 500;
  font-size: 16px;
}
.menu-wrapper {
  display: flex;
  align-items: stretch;
}
/deep/.vxe-pager .vxe-pager--wrapper {
  display: none;
}
</style>
