export const queryList = ({ limit, pageNum }) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const list = [
        {
          id: 10001,
          state: '软件园',
          name: '朗新产业园',
        },
        {
          id: 10002,
          state: '国有园区',
          name: '朗新产业园',
        },
      ];
      resolve([
        {
          total: list.length,
          data: list.slice((pageNum - 1) * limit, pageNum * limit),
        },
        undefined,
      ]);
    }, 100);
  });
};
