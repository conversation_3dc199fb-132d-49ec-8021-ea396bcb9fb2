<template>
  <div :class="[tableTitle ? 'table-container' : '']">
    <h3 v-if="tableTitle">{{ tableTitle }}</h3>
    <div
      class="infinite-scroll-table"
      :style="{ height: height }"
      ref="tableContainer"
      @mouseenter="pauseAutoScroll"
      @mouseleave="resumeAutoScroll"
    >
      <!-- 表头 -->
      <div class="table-header" ref="tableHeader">
        <div class="header-row">
          <div
            v-for="(column, index) in columns"
            :key="index"
            class="header-cell"
            :style="{
              width: column.width ? column.width + 'px' : 'auto',
              minWidth: column.minWidth ? column.minWidth + 'px' : '80px',
              textAlign: column.headerAlign || 'left',
            }"
          >
            {{ column.title }}
          </div>
        </div>
      </div>

      <!-- 表体容器 -->
      <div
        class="table-body-container"
        ref="bodyContainer"
        @scroll="handleScroll"
      >
        <div
          class="table-body"
          ref="tableBody"
          :style="{ transform: `translateY(${translateY}px)` }"
        >
          <div
            v-for="(row, index) in visibleData"
            :key="getRowKey(row, index)"
            class="table-row"
            :class="{ 'even-row': index % 2 === 0, 'odd-row': index % 2 === 1 }"
          >
            <div
              v-for="(column, colIndex) in columns"
              :key="colIndex"
              class="table-cell"
              :style="{
                width: column.width ? column.width + 'px' : 'auto',
                minWidth: column.minWidth ? column.minWidth + 'px' : '80px',
                textAlign: column.align || 'left',
              }"
            >
              <span
                v-if="column.type === 'html'"
                v-html="formatCellValue(row, column)"
              ></span>
              <span v-else>{{ formatCellValue(row, column) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    height: {
      type: String,
      default: '150px',
    },
    tableTitle: {
      type: String,
      default: '',
    },
    columns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    // 滚动配置
    scrollSpeed: {
      type: Number,
      default: 1, // 每次滚动的像素数
    },
    scrollInterval: {
      type: Number,
      default: 50, // 滚动间隔（毫秒）
    },
    visibleRowCount: {
      type: Number,
      default: 10, // 可见行数
    },
    bufferSize: {
      type: Number,
      default: 5, // 缓冲区大小
    },
    rowHeight: {
      type: Number,
      default: 40, // 行高（像素）
    },
    autoScroll: {
      type: Boolean,
      default: true, // 是否自动滚动
    },
  },
  data() {
    return {
      // 滚动相关状态
      scrollTimer: null,
      isScrollPaused: false,
      isUserScrolling: false,
      userScrollTimer: null,

      // 数据管理
      visibleData: [], // 当前可见的数据
      dataBuffer: [], // 数据缓冲区
      currentStartIndex: 0, // 当前显示数据的起始索引

      // 虚拟滚动
      translateY: 0,
      totalHeight: 0,

      // 性能优化
      throttleTimer: null,

      // 组件状态
      isDestroyed: false,
    };
  },
  computed: {
    // 计算总的缓冲区大小
    totalBufferSize() {
      return this.visibleRowCount + this.bufferSize * 2;
    },

    // 计算容器高度
    containerHeight() {
      return parseInt(this.height) || 150;
    },
  },

  watch: {
    tableData: {
      handler(newData) {
        if (newData && newData.length > 0) {
          this.initializeData();
          this.startAutoScroll();
        } else {
          this.stopAutoScroll();
        }
      },
      immediate: true,
    },

    autoScroll(newVal) {
      if (newVal) {
        this.startAutoScroll();
      } else {
        this.stopAutoScroll();
      }
    },
  },

  mounted() {
    this.$nextTick(() => {
      this.initializeData();
      if (this.autoScroll && this.tableData.length > 0) {
        this.startAutoScroll();
      }
    });
  },

  beforeDestroy() {
    this.isDestroyed = true;
    this.stopAutoScroll();
    this.clearAllTimers();
  },
  methods: {
    // 初始化数据缓冲区
    initializeData() {
      if (!this.tableData || this.tableData.length === 0) {
        this.visibleData = [];
        this.dataBuffer = [];
        return;
      }

      // 创建循环数据缓冲区
      this.createDataBuffer();

      // 初始化可见数据
      this.updateVisibleData();

      // 重置滚动位置
      this.translateY = 0;
      this.currentStartIndex = 0;
    },

    // 创建数据缓冲区（循环使用原始数据）
    createDataBuffer() {
      const sourceData = this.tableData;
      const bufferSize = Math.max(this.totalBufferSize, sourceData.length);

      this.dataBuffer = [];
      for (let i = 0; i < bufferSize; i++) {
        const sourceIndex = i % sourceData.length;
        this.dataBuffer.push({
          ...sourceData[sourceIndex],
          _originalIndex: sourceIndex,
          _bufferIndex: i,
        });
      }
    },

    // 更新可见数据
    updateVisibleData() {
      const startIndex = this.currentStartIndex;
      const endIndex = startIndex + this.visibleRowCount;

      this.visibleData = this.dataBuffer.slice(startIndex, endIndex);

      // 如果数据不足，从头开始补充
      if (this.visibleData.length < this.visibleRowCount) {
        const remaining = this.visibleRowCount - this.visibleData.length;
        const additionalData = this.dataBuffer.slice(0, remaining);
        this.visibleData = [...this.visibleData, ...additionalData];
      }
    },
    // 开始自动滚动
    startAutoScroll() {
      if (
        !this.autoScroll ||
        this.isScrollPaused ||
        this.isUserScrolling ||
        this.isDestroyed
      ) {
        return;
      }

      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }

      this.scrollTimer = setInterval(() => {
        if (this.isDestroyed || this.isScrollPaused || this.isUserScrolling) {
          return;
        }

        this.performAutoScroll();
      }, this.scrollInterval);
    },

    // 停止自动滚动
    stopAutoScroll() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
    },

    // 执行自动滚动
    performAutoScroll() {
      if (!this.dataBuffer.length) return;

      this.translateY += this.scrollSpeed;

      // 检查是否需要更新数据
      const scrolledRows = Math.floor(this.translateY / this.rowHeight);

      if (scrolledRows >= 1) {
        // 移动到下一行
        this.currentStartIndex =
          (this.currentStartIndex + scrolledRows) % this.dataBuffer.length;
        this.translateY = this.translateY % this.rowHeight;

        // 更新可见数据
        this.updateVisibleData();

        // 扩展数据缓冲区（如果需要）
        this.expandBufferIfNeeded();
      }
    },

    // 扩展缓冲区（循环添加数据）
    expandBufferIfNeeded() {
      const currentBufferSize = this.dataBuffer.length;
      const requiredSize =
        this.currentStartIndex + this.visibleRowCount + this.bufferSize;

      if (requiredSize > currentBufferSize) {
        const additionalSize = requiredSize - currentBufferSize;
        for (let i = 0; i < additionalSize; i++) {
          const sourceIndex = (currentBufferSize + i) % this.tableData.length;
          this.dataBuffer.push({
            ...this.tableData[sourceIndex],
            _originalIndex: sourceIndex,
            _bufferIndex: currentBufferSize + i,
          });
        }
      }
    },

    // 用户交互方法
    pauseAutoScroll() {
      this.isScrollPaused = true;
    },

    resumeAutoScroll() {
      this.isScrollPaused = false;
      if (this.autoScroll && !this.isUserScrolling) {
        this.startAutoScroll();
      }
    },

    // 处理用户手动滚动
    handleScroll() {
      if (this.throttleTimer) return;

      this.throttleTimer = setTimeout(() => {
        this.throttleTimer = null;
      }, 16); // 60fps

      // 标记用户正在滚动
      this.isUserScrolling = true;

      // 清除用户滚动定时器
      if (this.userScrollTimer) {
        clearTimeout(this.userScrollTimer);
      }

      // 设置延迟恢复自动滚动
      this.userScrollTimer = setTimeout(() => {
        this.isUserScrolling = false;
        if (this.autoScroll && !this.isScrollPaused) {
          this.startAutoScroll();
        }
      }, 2000); // 2秒后恢复自动滚动
    },

    // 格式化单元格值
    formatCellValue(row, column) {
      if (column.formatter && typeof column.formatter === 'function') {
        return column.formatter({ cellValue: row[column.field], row });
      }
      return row[column.field] || '';
    },

    // 获取行的唯一键
    getRowKey(row, index) {
      return row._bufferIndex !== undefined
        ? `row_${row._bufferIndex}`
        : `row_${index}`;
    },

    // 清理所有定时器
    clearAllTimers() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }

      if (this.userScrollTimer) {
        clearTimeout(this.userScrollTimer);
        this.userScrollTimer = null;
      }

      if (this.throttleTimer) {
        clearTimeout(this.throttleTimer);
        this.throttleTimer = null;
      }
    },
  },
};
</script>

<style scoped lang="less">
.table-container {
  width: 100%;
  background: rgba(249, 249, 249, 0.6);
  padding: 16px;
  border-radius: 8px;

  h3 {
    padding-left: 19px;
    font-family: PingFang SC;
    font-size: 12px;
    color: #333333;
    background-image: url('@/assets/images/portraits/1-0041.png');
    background-size: 14px 13px;
    background-repeat: no-repeat;
    background-position: 0 2px;
    margin-bottom: 12px;
  }
}

.infinite-scroll-table {
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
}

.table-header {
  background: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-row {
  display: flex;
  min-height: 40px;
}

.header-cell {
  padding: 8px 12px;
  border-right: 1px solid #e8e8e8;
  font-weight: 600;
  font-size: 12px;
  color: #333;
  display: flex;
  align-items: center;
  background: #f5f5f5;

  &:last-child {
    border-right: none;
  }
}

.table-body-container {
  height: calc(100% - 40px);
  overflow: hidden;
  position: relative;
}

.table-body {
  width: 100%;
  transition: transform 0.1s ease-out;
}

.table-row {
  display: flex;
  min-height: 40px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  &.even-row {
    background-color: #fafafa;
  }

  &.odd-row {
    background-color: #fff;
  }
}

.table-cell {
  padding: 8px 12px;
  border-right: 1px solid #f0f0f0;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  word-break: break-word;
  overflow: hidden;

  &:last-child {
    border-right: none;
  }

  span {
    line-height: 1.4;
  }
}

// 滚动条样式
.table-body-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-body-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-body-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .table-cell,
  .header-cell {
    padding: 6px 8px;
    font-size: 11px;
  }

  .table-row {
    min-height: 36px;
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table-row {
  animation: fadeIn 0.3s ease-out;
}

// 加载状态
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

// 空数据状态
.empty-data {
  padding: 40px 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>
