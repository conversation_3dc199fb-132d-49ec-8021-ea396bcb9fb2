import moment from 'moment';
// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    width: 60,
    fixed: 'left',
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    field: 'rateTime',
    title: '日期',
    width: 150,
    formatter: ({ cellValue }) => {
      return cellValue ? moment(cellValue).format('YYYY') : '';
    },
  },
  {
    field: 'organizationName',
    title: '机构名称',
    minWidth: 150,
  },
  {
    field: 'level',
    title: '机构级别',
    width: 250,
    formatter: ({ cellValue }) => {
      return getStatus(cellValue);
    },
  },
  {
    field: 'creationComplete',
    title: '建设情况',
    width: 250,
    formatter: ({ cellValue }) => {
      return cellValue == 1
        ? '规划建设'
        : cellValue == 2
        ? '建成启用'
        : cellValue == 3
        ? '其它'
        : '';
    },
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 160,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'rateTime',
    title: '日期',
    element: 'slot',
    slotName: 'year',
    rules: [{ required: true, message: '请输入日期' }],
  },
  {
    field: 'name',
    title: '机构名称',
    element: 'a-input',
    props: {
      placeholder: '请输入名称',
    },
  },
  {
    field: 'buildStatus',
    title: '建设情况',
    element: 'a-select',
    props: {
      placeholder: '请输入名称',
      options: [],
    },
  },
];

export const initFormValue = () => {
  return {
    rateTime: undefined,
    organizationName: '',
    creationComplete: '',
  };
};

export const getStatus = (code) => {
  switch (code) {
    case '1':
      return '市级、省级';
    case '2':
      return '市级';
    case '3':
      return '省级';
    case '4':
      return '其它';
  }
};
