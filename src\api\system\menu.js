import request from '@/utils/request';

// 查询菜单列表
export function listMenu(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/menu/list',
    method: 'get',
    params: query,
  });
}

// 查询菜单节点
export function pointListMenu(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/menu/pointList',
    method: 'get',
    params: query,
  });
}

// 查询菜单详细
export function getMenu(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/menu/detail',
    method: 'get',
    params: query,
  });
}

// 查询菜单下拉树结构
export function treeselect(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/menu/treeselect',
    method: 'get',
    params: data,
  });
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/menu/add',
    method: 'post',
    data: data,
  });
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/menu/edit',
    method: 'post',
    data: data,
  });
}

// 删除菜单
export function delMenu(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/menu/delete',
    method: 'get',
    params: data,
  });
}
