// import moment from 'moment';
import { visibleOptions, visibleEnum } from '@/views/system/constant/system';
export const filterOptions = {
  // 筛选器配置
  config: [
    {
      field: 'menuName',
      title: '菜单名称',
      props: {
        placeholder: '请输入菜单名称',
      },
    },
    {
      field: 'visible',
      title: '菜单状态',
      element: 'a-select',
      props: {
        options: [
          {
            label: '全部',
            value: '',
          },
          ...visibleOptions,
        ],
        showSearch: true,
        optionFilterProp: 'children',
      },
    },
  ],
  // 初始展示几个筛选项 非必填
  showCount: undefined,
  // 筛选器参数
  params: { menuName: '', visible: '' },
};

export const tableColumns = [
  {
    title: '菜单标题',
    field: 'menuTitle',
    key: 'menuTitle',
  },
  {
    title: '路由名称',
    field: 'menuName',
    key: 'menuName',
  },
  {
    title: '路由类型',
    field: 'menuType',
    key: 'menuType',
    width: 60,
    align: 'center',
    slots: { default: 'menuType' },
  },
  {
    title: '图标',
    field: 'icon',
    key: 'icon',
    width: 60,
    align: 'center',
    slots: { default: 'icon' },
  },
  {
    title: '权限标识',
    field: 'perms',
    key: 'perms',
  },
  {
    title: '菜单状态',
    field: 'visible',
    key: 'visible',
    width: 80,
    slots: { default: 'visible' },
  },

  {
    title: '排序',
    field: 'orderNum',
    key: 'orderNum',
    width: 60,
    align: 'center',
  },
  {
    title: '操作',
    field: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 260,
    slots: { default: 'operation' },
  },
];
export const replaceFields = {
  children: 'children',
  title: 'label',
  key: 'id',
};
export const menuConfig = {
  C: {
    label: '菜单',
    color: 'blue',
  },
  M: {
    label: '目录',
    color: 'green',
  },
  F: {
    label: '按钮',
    color: 'orange',
  },
};

export const menuTypeEnum = {
  DIR: 'M',
  MENU: 'C',
  BUTTON: 'F',
};
export const menuTypeOptions = [
  {
    label: '目录',
    value: menuTypeEnum.DIR,
    color: 'green',
  },
  {
    label: '菜单',
    value: menuTypeEnum.MENU,
    color: 'blue',
  },
  {
    label: '按钮',
    value: menuTypeEnum.BUTTON,
    color: 'orange',
  },
];
export const getMenuTypeLabel = (value) => {
  return menuTypeOptions.find((item) => item.value === value)?.label;
};

export const initMenuFormData = (data) => {
  const { menuType = menuTypeEnum.DIR } = data || {};
  let formData = {};
  switch (menuType) {
    case menuTypeEnum.DIR:
      formData = {
        menuId: undefined,
        parentId: undefined,
        icon: '',
        menuTitle: '',
        menuName: '',
        path: '',
        component: 'Layout',
        redirect: undefined,
        orderNum: undefined,
        extra: undefined,
        isFrame: '1',
        visible: visibleEnum.VISIBLE,
        serverUrl: undefined,
        serverMethod: undefined,
        ...data,
        menuType: menuTypeEnum.DIR,
        perms: undefined,
      };
      break;
    case menuTypeEnum.MENU:
      formData = {
        menuId: undefined,
        parentId: undefined,
        icon: '',
        menuTitle: '',
        menuName: '',
        path: '',
        component: '',
        orderNum: undefined,
        extra: undefined,
        isFrame: '1',
        visible: visibleEnum.VISIBLE,
        serverUrl: undefined,
        serverMethod: undefined,
        ...data,
        menuType: menuTypeEnum.MENU,
        redirect: undefined,
        perms: undefined,
      };
      break;
    case menuTypeEnum.BUTTON:
      formData = {
        menuId: undefined,
        parentId: undefined,
        perms: '',
        orderNum: undefined,
        extra: undefined,
        serverUrl: undefined,
        serverMethod: undefined,
        ...data,
        menuType: menuTypeEnum.BUTTON,
        icon: '',
        menuTitle: '',
        menuName: '',
        path: '',
        component: '',
        redirect: undefined,
        visible: visibleEnum.VISIBLE,
      };
      break;
  }
  return formData;
};
export const formLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 14,
  },
};

export const rules = {
  menuName: [
    {
      required: true,
      message: '不能为空',
      trigger: 'blur',
    },
  ],
  // path: [
  //   {
  //     required: true,
  //     message: "地址不能空",
  //     trigger: "blur",
  //   },
  // ],
  menuTitle: [
    {
      required: true,
      message: '不能为空',
      trigger: 'blur',
    },
  ],
  // component: [
  //   {
  //     required: true,
  //     message: "组件路径不能为空",
  //     trigger: "blur",
  //   },
  // ],
  orderNum: [
    {
      required: true,
      message: '菜单顺序不能为空',
      trigger: 'blur',
    },
  ],
};
