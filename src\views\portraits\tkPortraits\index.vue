<template>
  <div class="scroll-table-demo">
    <div class="demo-header">
      <h1>无限循环滚动表格演示</h1>
      <div class="controls">
        <button @click="toggleAutoScroll" class="control-btn">
          {{ autoScrollEnabled ? '暂停滚动' : '开始滚动' }}
        </button>
        <button @click="changeSpeed" class="control-btn">
          速度: {{ currentSpeed }}x
        </button>
        <button @click="addRandomData" class="control-btn">
          添加数据
        </button>
        <button @click="resetData" class="control-btn">
          重置数据
        </button>
      </div>
    </div>

    <div class="demo-content">
      <!-- 企业税收排名表格 -->
      <div class="table-section">
        <ScrollTable3
          :height="'450px'"
          :tableTitle="'企业税收排名TOP20'"
          :columns="taxRankingColumns"
          :tableData="taxRankingData"
          :scrollSpeed="scrollSpeed"
          :scrollInterval="60"
          :visibleRowCount="10"
          :autoScroll="autoScrollEnabled"
          :rowHeight="45"
        />
      </div>

      <!-- 园区产业分布表格 -->
      <div class="table-section">
        <ScrollTable3
          :height="'450px'"
          :tableTitle="'园区产业分布统计'"
          :columns="industryColumns"
          :tableData="industryData"
          :scrollSpeed="scrollSpeed"
          :scrollInterval="80"
          :visibleRowCount="8"
          :autoScroll="autoScrollEnabled"
          :rowHeight="50"
        />
      </div>

      <!-- 项目投资情况表格 -->
      <div class="table-section">
        <ScrollTable3
          :height="'450px'"
          :tableTitle="'重点项目投资情况'"
          :columns="projectColumns"
          :tableData="projectData"
          :scrollSpeed="scrollSpeed"
          :scrollInterval="70"
          :visibleRowCount="9"
          :autoScroll="autoScrollEnabled"
          :rowHeight="48"
        />
      </div>
    </div>

    <!-- 状态信息面板 -->
    <div class="status-panel">
      <h3>组件状态</h3>
      <div class="status-grid">
        <div class="status-item">
          <span class="label">自动滚动:</span>
          <span class="value" :class="{ active: autoScrollEnabled }">
            {{ autoScrollEnabled ? '开启' : '关闭' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">滚动速度:</span>
          <span class="value">{{ scrollSpeed }}px/次</span>
        </div>
        <div class="status-item">
          <span class="label">税收数据:</span>
          <span class="value">{{ taxRankingData.length }}条</span>
        </div>
        <div class="status-item">
          <span class="label">产业数据:</span>
          <span class="value">{{ industryData.length }}条</span>
        </div>
        <div class="status-item">
          <span class="label">项目数据:</span>
          <span class="value">{{ projectData.length }}条</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScrollTable3 from './ScrollTable3.vue';

export default {
  name: 'ScrollTableDemo',
  components: {
    ScrollTable3,
  },
  data() {
    return {
      autoScrollEnabled: true,
      scrollSpeed: 1,
      currentSpeed: 1,

      // 企业税收排名列配置
      taxRankingColumns: [
        {
          title: '排名',
          field: 'rank',
          width: 80,
          type: 'html',
          align: 'center',
          formatter: ({ cellValue }) => {
            const rankClass = cellValue <= 3 ? 'top-rank' : cellValue <= 10 ? 'mid-rank' : 'normal-rank';
            return `<div class="rank-badge ${rankClass}">TOP${cellValue}</div>`;
          },
        },
        {
          title: '企业名称',
          field: 'companyName',
          minWidth: 180,
          align: 'left',
        },
        {
          title: '所属行业',
          field: 'industry',
          width: 120,
          align: 'center',
        },
        {
          title: '税收(万元)',
          field: 'taxAmount',
          width: 120,
          type: 'html',
          align: 'right',
          formatter: ({ cellValue }) =>
            `<span class="tax-amount">${Number(cellValue).toLocaleString()}</span>`,
        },
        {
          title: '同比增长',
          field: 'growth',
          width: 100,
          type: 'html',
          align: 'center',
          formatter: ({ cellValue }) => {
            const isPositive = cellValue >= 0;
            const color = isPositive ? '#52c41a' : '#ff4d4f';
            const symbol = isPositive ? '+' : '';
            return `<span style="color: ${color}; font-weight: bold;">${symbol}${cellValue}%</span>`;
          },
        },
      ],

      // 园区产业分布列配置
      industryColumns: [
        {
          title: '产业类型',
          field: 'industryType',
          minWidth: 150,
          align: 'left',
        },
        {
          title: '企业数量',
          field: 'companyCount',
          width: 100,
          type: 'html',
          align: 'center',
          formatter: ({ cellValue }) =>
            `<span class="company-count">${cellValue}家</span>`,
        },
        {
          title: '产值(亿元)',
          field: 'outputValue',
          width: 120,
          type: 'html',
          align: 'right',
          formatter: ({ cellValue }) =>
            `<span class="output-value">${Number(cellValue).toFixed(2)}</span>`,
        },
        {
          title: '占比',
          field: 'percentage',
          width: 100,
          type: 'html',
          align: 'center',
          formatter: ({ cellValue }) =>
            `<span class="percentage">${cellValue}%</span>`,
        },
        {
          title: '发展趋势',
          field: 'trend',
          width: 100,
          type: 'html',
          align: 'center',
          formatter: ({ cellValue }) => {
            const trendMap = {
              '上升': { color: '#52c41a', icon: '↗' },
              '稳定': { color: '#1890ff', icon: '→' },
              '下降': { color: '#ff4d4f', icon: '↘' }
            };
            const trend = trendMap[cellValue] || trendMap['稳定'];
            return `<span style="color: ${trend.color}; font-weight: bold;">${trend.icon} ${cellValue}</span>`;
          },
        },
      ],

      // 项目投资列配置
      projectColumns: [
        {
          title: '项目名称',
          field: 'projectName',
          minWidth: 200,
          align: 'left',
        },
        {
          title: '投资方',
          field: 'investor',
          width: 150,
          align: 'left',
        },
        {
          title: '投资金额(亿元)',
          field: 'investmentAmount',
          width: 130,
          type: 'html',
          align: 'right',
          formatter: ({ cellValue }) =>
            `<span class="investment-amount">${Number(cellValue).toFixed(2)}</span>`,
        },
        {
          title: '项目状态',
          field: 'status',
          width: 100,
          type: 'html',
          align: 'center',
          formatter: ({ cellValue }) => {
            const statusMap = {
              '建设中': { color: '#1890ff', bg: '#e6f7ff' },
              '已完工': { color: '#52c41a', bg: '#f6ffed' },
              '规划中': { color: '#faad14', bg: '#fffbe6' },
              '暂停': { color: '#ff4d4f', bg: '#fff2f0' }
            };
            const status = statusMap[cellValue] || statusMap['规划中'];
            return `<span class="status-badge" style="color: ${status.color}; background: ${status.bg};">${cellValue}</span>`;
          },
        },
        {
          title: '预计完工',
          field: 'expectedCompletion',
          width: 120,
          align: 'center',
        },
      ],
