<template>
  <div class="scroll-table-demo">
    <div class="demo-header">
      <h1>无限循环滚动表格演示</h1>
      <div class="controls">
        <button @click="toggleAutoScroll" class="control-btn">
          {{ autoScrollEnabled ? '暂停滚动' : '开始滚动' }}
        </button>
        <button @click="changeSpeed" class="control-btn">
          速度: {{ currentSpeed }}x
        </button>
        <button @click="addRandomData" class="control-btn">添加数据</button>
        <button @click="resetData" class="control-btn">重置数据</button>
      </div>
    </div>

    <div class="demo-content">
      <!-- 企业税收排名表格 -->
      <div class="table-section">
        <ScrollTable3
          :height="'500px'"
          :tableTitle="'企业税收排名TOP15'"
          :columns="taxRankingColumns"
          :tableData="taxRankingData"
          :scrollSpeed="scrollSpeed"
          :scrollInterval="60"
          :visibleRowCount="10"
          :autoScroll="autoScrollEnabled"
          :rowHeight="45"
        />
      </div>
    </div>

    <!-- 状态信息面板 -->
    <div class="status-panel">
      <h3>组件状态</h3>
      <div class="status-grid">
        <div class="status-item">
          <span class="label">自动滚动:</span>
          <span class="value" :class="{ active: autoScrollEnabled }">
            {{ autoScrollEnabled ? '开启' : '关闭' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">滚动速度:</span>
          <span class="value">{{ scrollSpeed }}px/次</span>
        </div>
        <div class="status-item">
          <span class="label">数据总数:</span>
          <span class="value">{{ taxRankingData.length }}条</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScrollTable3 from './ScrollTable3.vue';

export default {
  name: 'ScrollTableDemo',
  components: {
    ScrollTable3,
  },
  data() {
    return {
      autoScrollEnabled: true,
      scrollSpeed: 1,
      currentSpeed: 1,

      // 企业税收排名列配置
      taxRankingColumns: [
        {
          title: '排名',
          field: 'rank',
          width: 80,
          type: 'html',
          align: 'center',
          formatter: ({ cellValue }) =>
            `<div class='top-title top-title-${+cellValue}' ">TOP${+cellValue}</div>`,
        },
        {
          title: '企业名称',
          field: 'companyName',
          minWidth: 180,
          align: 'left',
        },
        {
          title: '所属行业',
          field: 'industry',
          width: 120,
          align: 'center',
        },
        {
          title: '税收(万元)',
          field: 'taxAmount',
          width: 120,
          type: 'html',
          align: 'right',
          formatter: ({ cellValue }) =>
            `<span class="tax-amount">${Number(
              cellValue
            ).toLocaleString()}</span>`,
        },
        {
          title: '同比增长',
          field: 'growth',
          width: 100,
          type: 'html',
          align: 'center',
          formatter: ({ cellValue }) => {
            const isPositive = cellValue >= 0;
            const color = isPositive ? '#52c41a' : '#ff4d4f';
            const symbol = isPositive ? '+' : '';
            return `<span style="color: ${color}; font-weight: bold;">${symbol}${cellValue}%</span>`;
          },
        },
      ],

      // 模拟税收排名数据
      taxRankingData: [
        {
          rank: 1,
          companyName: '新疆天山水泥股份有限公司',
          industry: '建材制造',
          taxAmount: 2850.5,
          growth: 15.2,
        },
        {
          rank: 2,
          companyName: '乌鲁木齐钢铁集团有限公司',
          industry: '钢铁冶炼',
          taxAmount: 2456.8,
          growth: 8.7,
        },
        {
          rank: 3,
          companyName: '新疆广汇能源股份有限公司',
          industry: '能源化工',
          taxAmount: 2198.3,
          growth: 12.4,
        },
        {
          rank: 4,
          companyName: '新疆特变电工股份有限公司',
          industry: '电力设备',
          taxAmount: 1987.6,
          growth: 6.9,
        },
      ],
    };
  },

  methods: {
    // 切换自动滚动
    toggleAutoScroll() {
      this.autoScrollEnabled = !this.autoScrollEnabled;
    },

    // 切换滚动速度
    changeSpeed() {
      const speeds = [0.5, 1, 2, 3];
      const currentIndex = speeds.indexOf(this.scrollSpeed);
      const nextIndex = (currentIndex + 1) % speeds.length;
      this.scrollSpeed = speeds[nextIndex];
      this.currentSpeed = this.scrollSpeed;
    },

    // 添加随机数据
    addRandomData() {
      const companies = [
        '新疆丝路科技有限公司',
        '天山创新技术股份有限公司',
        '昆仑智能制造有限公司',
        '塔里木新能源科技公司',
        '阿尔泰山生物科技有限公司',
      ];

      const industries = [
        '科技服务',
        '智能制造',
        '新能源',
        '生物科技',
        '信息技术',
      ];

      const newRank = this.taxRankingData.length + 1;
      const randomCompany =
        companies[Math.floor(Math.random() * companies.length)];
      const randomIndustry =
        industries[Math.floor(Math.random() * industries.length)];
      const randomTax = Math.floor(Math.random() * 500) + 100;
      const randomGrowth = (Math.random() * 30 - 10).toFixed(1);

      this.taxRankingData.push({
        rank: newRank,
        companyName: randomCompany,
        industry: randomIndustry,
        taxAmount: randomTax,
        growth: parseFloat(randomGrowth),
      });
    },

    // 重置数据
    resetData() {
      this.taxRankingData = this.taxRankingData.slice(0, 15);
    },
  },
};
</script>

<style scoped lang="less">
.scroll-table-demo {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  margin-bottom: 20px;

  h1 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
    font-size: 28px;
    font-weight: bold;
  }
}

.controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.control-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
}

.demo-content {
  margin-bottom: 30px;
}

.table-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e8e8e8;
  width: 400px; /* 减小宽度以测试横向滚动 */
}

.status-panel {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;

  .label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
  }

  .value {
    color: #333;
    font-size: 14px;
    font-weight: 600;

    &.active {
      color: #52c41a;
    }
  }
}

// 表格内部样式定制
:deep(.rank-badge) {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  color: white;
  text-align: center;
  min-width: 45px;

  &.top-rank {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  }

  &.mid-rank {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    box-shadow: 0 2px 8px rgba(254, 202, 87, 0.3);
  }

  &.normal-rank {
    background: linear-gradient(135deg, #54a0ff, #2e86de);
    box-shadow: 0 2px 8px rgba(84, 160, 255, 0.3);
  }
}

:deep(.tax-amount) {
  color: #52c41a;
  font-family: 'D-DIN', 'Arial', monospace;
  font-size: 13px;
  font-weight: bold;
}

:deep(.status-badge) {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

// 响应式设计
@media (max-width: 768px) {
  .scroll-table-demo {
    padding: 10px;
  }

  .demo-header h1 {
    font-size: 24px;
  }

  .controls {
    gap: 10px;
  }

  .control-btn {
    padding: 8px 16px;
    font-size: 13px;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table-section {
  animation: slideInUp 0.6s ease-out;
}

.status-panel {
  animation: slideInUp 0.8s ease-out;
}
</style>
