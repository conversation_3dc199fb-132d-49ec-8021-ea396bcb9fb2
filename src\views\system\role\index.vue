<template>
  <page-layout>
    <PageWrapper
      style="margin: 0"
      title="角色列表"
      createText=""
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumns"
      :tableData="tableData"
      @loadData="queryList"
      @handleReset="handleReset"
      @handleCreate="handleCreate"
    >
      <template #defaultHeader>
        <a-button
          v-hasPermi="['system:role:add']"
          style="margin-right: 8px"
          type="primary"
          :loading="loading"
          @click="handleCreate"
        >
          添加角色
        </a-button>
      </template>

      <template #status="{ row }">
        <a-switch
          :checked="row.status === 'NORMAL'"
          checked-children="生效"
          un-checked-children="失效"
          @change="handleStatusChange(row)"
        />
      </template>
      <template #operation="{ row }">
        <a-button
          v-hasPermi="['system:role:edit']"
          icon="edit"
          type="link"
          style="padding: 0; margin-right: 8px"
          @click="handleUpdate(row)"
        >
          编辑
        </a-button>
        <a-button
          v-hasPermi="['system:role:delete']"
          icon="delete"
          type="link"
          style="padding: 0; margin-right: 8px"
          @click="handleDelete(row)"
        >
          删除
        </a-button>
      </template>
    </PageWrapper>

    <!-- 角色编辑 -->
    <EditModal
      :visible.sync="editModalVisible"
      :roleId="roleId"
      @ok="queryList"
    />
  </page-layout>
</template>

<script>
import { listRole, delRole, changeRoleStatus } from '@/api/system/role';
import { Modal } from 'ant-design-vue';
import EditModal from './EditModal.vue';
import { filterOptions, tableColumns, statusEnum } from './constant';
import moment from 'moment';
export default {
  components: { EditModal },
  data() {
    return {
      loading: true,
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      filterOptions,
      tableColumns,
      tableData: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 新增/编辑弹窗是否开启
      editModalVisible: false,
      roleId: '',
    };
  },
  created() {
    this.queryList();
  },
  methods: {
    /** 表单数据请求 */
    async queryList() {
      this.loading = true;
      const { dateRange, ...params } = this.filterOptions.params;
      let timeData = {};
      // 如果存在数据
      if (dateRange && dateRange[0] && dateRange[1]) {
        timeData = {
          beginTime: moment(dateRange[0]).valueOf(),
          endTime: moment(dateRange[1]).valueOf(),
        };
      }
      const [result, error] = await listRole({
        ...params,
        ...timeData,
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
      });
      this.loading = false;
      if (error) return;
      this.tableData = result.data;
      this.tablePage.total = result.count;
    },
    async handleReset() {
      this.filterOptions.params = {
        roleName: '',
        status: '',
        dateRange: ['', ''],
      };
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      this.queryList();
    },
    /** 角色状态修改 */
    handleStatusChange(row) {
      const text = row.status === statusEnum.NORMAL ? '停用' : '启用';
      Modal.confirm({
        title: '警告',
        content: '确认要' + text + '"' + row.roleName + '"角色吗?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          const [, error] = await changeRoleStatus(
            row.roleId,
            row.status === statusEnum.NORMAL
              ? statusEnum.ABNORMAL
              : statusEnum.NORMAL
          );
          if (error) return;
          this.$message.success(`${text}成功`);
          this.queryList();
        },
      });
    },
    /** 新增按钮操作 */
    handleCreate() {
      this.roleId = '';
      this.editModalVisible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.roleId = row.roleId;
      this.editModalVisible = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleId = row.roleId;
      Modal.confirm({
        title: '警告',
        width: '500px',
        content: (
          <div>
            是否确认删除以下角色的数据项?
            <ul>
              <li>{row.roleName}</li>
            </ul>
          </div>
        ),
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          const [, error] = await delRole({ roleId });
          if (error) return;
          this.$message.success('删除成功');
          this.queryList();
        },
      });
    },
  },
};
</script>
