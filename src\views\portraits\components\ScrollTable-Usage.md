# 无限循环滚动表格 (ScrollTable) 使用指南

## 概述

新的 ScrollTable 组件实现了无限向下循环滚动功能，提供持续流动的视觉效果，支持自动滚动、用户交互暂停、数据循环使用等特性。

## 核心特性

### 1. 无限循环滚动
- 始终保持指定数量的可见行（默认10行）
- 数据循环使用，无缝衔接
- 平滑的滚动动画效果

### 2. 智能缓冲机制
- 动态数据缓冲区管理
- 队列更新机制
- 内存优化和回收

### 3. 用户交互控制
- 鼠标悬停自动暂停滚动
- 支持手动滚动干预
- 自然的自动恢复逻辑

### 4. 高性能实现
- CSS Transform 优化
- 事件节流处理
- 虚拟滚动技术

## 基本用法

```vue
<template>
  <ScrollTable
    :height="'400px'"
    :tableTitle="'税收排名TOP10'"
    :columns="columns"
    :tableData="tableData"
    :scrollSpeed="1"
    :scrollInterval="50"
    :visibleRowCount="10"
    :autoScroll="true"
  />
</template>

<script>
export default {
  data() {
    return {
      columns: [
        {
          title: '排名',
          field: 'rank',
          width: 80,
          type: 'html',
          formatter: ({ cellValue }) => `<div class="rank-badge">TOP${cellValue}</div>`
        },
        {
          title: '公司名称',
          field: 'companyName',
          minWidth: 120,
          align: 'left'
        },
        {
          title: '税收(万元)',
          field: 'tax',
          width: 100,
          align: 'right',
          formatter: ({ cellValue }) => Number(cellValue).toLocaleString()
        }
      ],
      tableData: [
        { rank: 1, companyName: '阿里巴巴', tax: 1250.5 },
        { rank: 2, companyName: '腾讯科技', tax: 980.3 },
        { rank: 3, companyName: '百度公司', tax: 756.8 },
        // ... 更多数据
      ]
    }
  }
}
</script>
```

## 属性配置

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| height | String | '150px' | 表格高度 |
| tableTitle | String | '' | 表格标题 |
| columns | Array | [] | 列配置 |
| tableData | Array | [] | 数据源 |

### 滚动配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| scrollSpeed | Number | 1 | 滚动速度(像素/次) |
| scrollInterval | Number | 50 | 滚动间隔(毫秒) |
| visibleRowCount | Number | 10 | 可见行数 |
| bufferSize | Number | 5 | 缓冲区大小 |
| rowHeight | Number | 40 | 行高(像素) |
| autoScroll | Boolean | true | 是否自动滚动 |

## 列配置

### 基本列配置

```javascript
{
  title: '列标题',        // 表头显示文本
  field: 'dataField',    // 数据字段名
  width: 100,            // 固定宽度(像素)
  minWidth: 80,          // 最小宽度(像素)
  align: 'left',         // 内容对齐方式: left/center/right
  headerAlign: 'center', // 表头对齐方式
  type: 'text'           // 列类型: text/html
}
```

### HTML 列配置

```javascript
{
  title: '排名',
  field: 'rank',
  type: 'html',
  formatter: ({ cellValue, row }) => {
    return `<div class="rank-badge rank-${cellValue}">TOP${cellValue}</div>`;
  }
}
```

## 高级用法

### 1. 自定义滚动参数

```vue
<ScrollTable
  :scrollSpeed="2"
  :scrollInterval="30"
  :visibleRowCount="8"
  :bufferSize="3"
  :rowHeight="45"
/>
```

### 2. 动态控制滚动

```vue
<template>
  <div>
    <button @click="toggleAutoScroll">
      {{ autoScrollEnabled ? '暂停' : '开始' }}滚动
    </button>
    <ScrollTable
      :autoScroll="autoScrollEnabled"
      :tableData="dynamicData"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      autoScrollEnabled: true,
      dynamicData: []
    }
  },
  methods: {
    toggleAutoScroll() {
      this.autoScrollEnabled = !this.autoScrollEnabled;
    },
    updateData() {
      // 动态更新数据
      this.dynamicData = this.fetchNewData();
    }
  }
}
</script>
```

### 3. 响应式配置

```vue
<ScrollTable
  :height="isMobile ? '300px' : '500px'"
  :visibleRowCount="isMobile ? 6 : 10"
  :scrollSpeed="isMobile ? 0.5 : 1"
/>
```

## 性能优化建议

### 1. 数据量控制
- 建议单次数据量控制在100条以内
- 对于大数据量，考虑分页或虚拟化

### 2. 列配置优化
- 避免过多的HTML类型列
- 合理设置列宽，避免频繁重排

### 3. 滚动参数调优
- 根据设备性能调整scrollSpeed和scrollInterval
- 移动端建议降低滚动速度

## 常见问题

### Q: 如何实现不同的滚动效果？
A: 通过调整scrollSpeed和scrollInterval参数：
- 平滑滚动：scrollSpeed=1, scrollInterval=50
- 快速滚动：scrollSpeed=2, scrollInterval=30
- 慢速滚动：scrollSpeed=0.5, scrollInterval=100

### Q: 如何处理数据更新？
A: 组件会自动监听tableData变化并重新初始化，无需手动处理。

### Q: 如何自定义样式？
A: 可以通过CSS覆盖组件样式类，或使用深度选择器修改内部样式。

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 更新日志

### v2.0.0
- 重构为无限循环滚动实现
- 移除vxe-grid依赖
- 添加虚拟滚动支持
- 优化性能和内存使用
