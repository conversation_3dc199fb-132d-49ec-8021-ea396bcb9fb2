import request from '@/utils/request';

// 查询角色列表
export function listRole(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/role/list',
    method: 'get',
    params: query,
  });
}

// 查询角色详细
export function getRole(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/role/detail',
    method: 'get',
    params: query,
  });
}

// 新增角色
export function addRole(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/role/add',
    method: 'post',
    data: data,
  });
}

// 修改角色
export function updateRole(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/role/edit',
    method: 'post',
    data: data,
  });
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
  const data = {
    roleId,
    status,
  };
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/role/changeStatus',
    method: 'post',
    data: data,
  });
}

// 删除角色
export function delRole(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/role/delete',
    method: 'get',
    params: query,
  });
}

// 角色应用树
export function roleAppTree(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/role/roleTree',
    method: 'get',
    params: query,
  });
}
// 角色应用菜单树
export function roleMenuTree(query) {
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/role/roleMenuTree',
    method: 'get',
    params: query,
  });
}
