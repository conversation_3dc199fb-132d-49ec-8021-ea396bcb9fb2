import { ref, toRefs, computed } from 'vue';
import { useStore } from './vueApi';
import { getDict } from '@/api/system/dict';
export default function (...args) {
  const res = ref({});
  const store = useStore();
  const dictStore = computed(() => store.state.dict);
  // 工具
  const changeData = () => {
    args.forEach((dictType) => {
      res.value[dictType] = ref([]);
      const dicts = getDictFromStore(dictStore.value, dictType);
      if (dicts) {
        res.value[dictType] = dicts;
      } else {
        getDict(dictType).then(([resp]) => {
          res.value[dictType].value = resp?.data?.map((p) => ({
            label: p.dictLabel,
            value: p.dictValue,
            elTagType: p.listClass,
            elTagClass: p.cssClass,
          }));
          store.dispatch('dict/setDict', {
            key: dictType,
            value: res.value[dictType],
          });
        });
      }
    });
  };
  const getDictFromStore = (state, dictKeys) => {
    if (dictKeys == null && dictKeys == '') {
      return null;
    }
    try {
      for (let i = 0; i < state.dict.length; i++) {
        if (state.dict[i].key == dictKeys) {
          return state.dict[i].value;
        }
      }
    } catch (e) {
      return null;
    }
  };
  changeData();
  return {
    ...toRefs(res.value),
  };
}
