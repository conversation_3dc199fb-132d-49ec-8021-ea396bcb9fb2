import { request } from '@/utils/request/requestTkb';

// 基本信息列表分页查询
export function getListInfo(query, pageName) {
  const map = {
    businessPortraits: '/base/enterprise/pageEnterprise',
    parkPortraits: '/base/sciencePark/parkPages',
  };
  const url = map[pageName];
  return request({
    url: url,
    method: 'POST',
    data: query,
  });
}
// 太科城信息管理指标信息
export function codeByType(query) {
  return request({
    url: '/base/code/codeByType',
    method: 'GET',
    params: query,
  });
}

// 查询企业画像详情
export function queryCompanyPicture(query) {
  return request({
    url: `/company/picture/queryCompanyPicture`,
    method: 'POST',
    params: query,
  });
}

// 查询园区画像详情
export function queryParkPicture(query) {
  return request({
    url: `/park/picture/queryParkPicture`,
    method: 'POST',
    params: query,
  });
}

// 查询太科办页面详情
export function queryTaikePicture(query) {
  return request({
    url: `/taike/picture/queryTaikePicture`,
    method: 'GET',
    params: query,
  });
}

// 查询太科办页面详情
export function queryIndustryPie(query) {
  return request({
    url: `/taike/picture/queryIndustryPie`,
    method: 'GET',
    params: query,
  });
}
