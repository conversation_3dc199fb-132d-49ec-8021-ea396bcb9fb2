<template>
  <page-layout>
    <a-button @click="goBack">返回</a-button>
    <div class="article">
      <div class="article-left">
        <h4>{{ item.title }}</h4>
        <div class="article-left-remark">
          <div>
            <img :src="item.imgUrl" alt="" />
            <span class="name">{{ item.name }}</span>
            <span class="time">最新保存时间：{{ item.saveTime }}</span>
            <span
              >分类:
              <span class="type"
                >{{ item.firstClassName }}/{{ item.secondClassName }}</span
              >
            </span>
          </div>
          <div class="nums">
            <div>
              <a-icon type="eye" /><span>{{ item.totalRead || 0 }}</span>
            </div>
            <div>
              <a-icon type="download" /><span>{{ item.totalRecive || 0 }}</span>
            </div>
            <div>
              发布时间： <span>{{ item.releaseTime }}</span>
            </div>
          </div>
        </div>
        <div
          class="article-left-con"
          v-html="item.richText"
          v-if="item.articleType === '0'"
        ></div>
        <div v-if="item.articleType === '1'">
          <div class="article-left-con">
            <img
              v-for="(img, index) in item.pictures"
              :key="index"
              class="img-item"
              :src="baseImgUrl + img.fileUrl"
              alt=""
              @click="handlePreview(img)"
            />
          </div>
        </div>
        <div
          v-if="item.articleType === '2'"
          class="article-left-con article-left-con-annex"
        >
          <div class="article-left-con-annex-box">
            <div
              v-for="(item1, index1) in item.documents"
              :key="index1"
              class="article-right-main-item"
            >
              <fileTypeItem :item="item1" />
            </div>
          </div>
        </div>
      </div>
      <div
        class="article-right"
        v-if="item && item.attachments && item.attachments.length"
      >
        <div class="article-right-box">
          <div class="article-right-top">
            <img src="@/assets/icons/link.png" alt="" /><span class="text"
              >附件</span
            ><i
              >({{ item.attachmentsCount || item.attachments?.length || 0 }})</i
            >
          </div>
          <div class="article-right-main">
            <div
              v-for="(item1, index) in item.attachments"
              :key="index"
              class="article-right-main-item"
            >
              <fileTypeItem :item="item1" />
            </div>
          </div>
        </div>
      </div>
      <div v-else class="article-right policy-dynamics-content-right">
        <div v-if="list && list.length > 0">
          <div class="policy-dynamics-content-right-top">
            <img src="@/assets/icons/suggest.png" alt="" />
            <span>推荐</span>
          </div>
          <div class="policy-dynamics-content-right-detail">
            <div class="item-con">
              <div
                v-for="(item, index) in list"
                :key="index"
                class="item"
                @click="itemClick(item)"
              >
                <h4>{{ item.title }}</h4>
                <p>{{ item.displayContent }}</p>
                <div class="item-bottom">
                  <div>
                    <img :src="item.imgUrl" alt="" />
                    <span class="item-name">{{ item.author }}</span>
                  </div>
                  <div class="item-nums">
                    <div>{{ item.releaseTime }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </page-layout>
</template>

<script>
import FileImgs from '@/assets/config/art_detail.js';
import fileTypeItem from './components/fileTypeItem/index.vue';
import { articleDetail } from '@/api/digitalOperation/policyDynamics/index.js';
import { articleList } from '@/api/digitalOperation/policyDynamics/index.js';
export default {
  name: 'articleDetail',
  components: { fileTypeItem },

  data() {
    return {
      FileImgs, //文件类型图片们
      item: {},
      list: [],
      params: {
        title: '', //标题
        articleType: '0', //文章类型
        firstClassId: '', //一级类别
        secondClassId: '', //二级类别
        secondClassName: '',
        pageNum: 1,
        limit: 4,
        state: '1', //当前状态（0保存，1上架，，2下架）
      },
      previewVisible: false,
      previewImage: '',
    };
  },

  mounted() {
    if (this.$route.query.id) {
      this.articleDetail();
    } else {
      this.item = JSON.parse(localStorage.getItem('publishForm'));
    }
    this.articleList();
  },

  methods: {
    handlePreview(item) {
      console.log(item, '0000');
      this.previewVisible = true;
      this.previewImage = this.baseImgUrl + item.fileUrl;
    },
    handleCancel() {
      this.previewVisible = false;
    },
    imgClick(e) {
      console.log(e, 'eeee');
    },
    async articleDetail() {
      const [res, err] = await articleDetail({ id: this.$route.query.id });
      if (err) return;
      this.item = res.data;
    },
    async articleList() {
      const [res, err] = await articleList(this.params);
      if (err) return;
      this.list = res.data;
    },
    goBack() {
      if (this.$route.query.type == 'publishKnowledgeView') {
        this.$router.push({
          name: 'publishKnowledge',
          query: {
            type: 'viewBack',
          },
        });
      } else {
        this.$router.go(-1);
      }
    },
    itemClick(item) {
      this.$router.push({
        name: 'articleDetail',
        query: {
          id: item.articleId,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.article {
  background-color: #fff;
  // height: 80vh;
  margin: 24px;
  padding: 32px;
  display: flex;
  .article-left {
    width: 64%;
    margin-right: 40px;
    h4 {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      height: 24px;
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #333333;
      margin-bottom: 22px;
    }
    .article-left-con {
      margin-top: 40px;
      height: 63vh;
      overflow: scroll;
    }
    .article-left-con-annex {
      padding: 16px;
    }
    .article-left-con-annex-box {
      padding: 16px;
      background: #f7f8fa;
      border-radius: 12px;
    }
    .article-left-remark {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: AlibabaPuHuiTi;
      font-size: 12px;
      font-weight: normal;
      line-height: 12px;
      color: #999999;
      img {
        width: 20px;
        height: 20px;
        border-radius: 10px;
        margin-right: 4px;
      }
      .time {
        margin: 0 16px;
      }
      .type {
        margin-left: 16px;
        color: #1890ff;
      }
      .nums {
        display: flex;
        div {
          margin-left: 16px;
          /deep/i {
            margin-right: 4px;
          }
        }
      }
    }
  }
  .article-right {
    width: 35%;

    .article-right-box {
      padding: 12px;
      border-radius: 12px;
      background: linear-gradient(149deg, #e4f4ff 1%, #f4f5ff 98%);
    }
    .article-right-top {
      display: flex;
      align-items: center;
      height: 29px;
      margin-bottom: 12px;
      img {
        margin-right: 6.5px;
        width: 18.43px;
        height: 20.31px;
      }
      .text {
        font-family: Alimama ShuHeiTi;
        font-size: 20px;
        font-weight: bold;
        line-height: 29px;
        letter-spacing: 0px;
        color: #333333;
      }
      i {
        margin: 9px 0 0 10px;
        font-style: normal;
        font-family: AlibabaPuHuiTi;
        font-size: 12px;
        font-weight: normal;
        line-height: 18px;
        color: rgba(0, 0, 0, 0.4);
      }
    }
    .article-right-main {
      background-color: #fff;
      border-radius: 8px;
      padding: 18px 16px;
      .article-right-main-item {
        display: flex;
      }
    }
    .empty {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .article-right-main-item:not(:last-child) {
    margin-bottom: 18px;
  }
  .img-item {
    width: 32%;
    height: 300px;
    margin-right: 10px;
  }
}
.policy-dynamics-content-right {
  // position: relative;
  // width: 993px;
  background: url('@/assets/images/suggest-bg.png') no-repeat;
  background-size: 100% 452px;
  // min-height: 65vh;
  background-color: #f7f8fa;
  padding: 12px;

  .policy-dynamics-content-right-top {
    display: flex;
    align-items: center;
    height: 29px;
    img {
      margin-right: 6.5px;
      width: 17.71px;
      height: 20.31px;
    }
    span {
      font-family: Alimama ShuHeiTi;
      font-size: 20px;
      font-weight: bold;
      line-height: 29px;
      letter-spacing: 0px;
      color: #333333;
    }
    .policy-dynamics-content-right-top-tabs {
      /deep/.ant-tabs-bar {
        background: #ffffff00;
        margin-bottom: 0;
        border: 0;
      }
      /deep/.ant-tabs-nav-wrap {
        // width: 116px;
        .ant-tabs-nav .ant-tabs-tab {
          padding: 4px;
          margin: 0 8px;
          color: #767676;
        }
        .ant-tabs-nav .ant-tabs-tab-active {
          color: #333;
        }
      }
      /deep/ .ant-tabs-ink-bar {
        background-color: #1890ff;
        width: 16px !important;
        height: 3px;
        border-radius: 2px;
        margin-left: 8px;
      }
    }
  }
  .policy-dynamics-content-right-detail {
    margin-top: 12px;
    width: 100%;
    // height: 60vh;
    overflow: scroll;
    border-radius: 8px;
    height: 520px;
    .item-con {
      border-radius: 8px;
      background: #ffffff;
      padding: 12px;
      height: 520px;
      .item {
        width: 100%;
        padding-top: 4px;

        border-bottom: 1px #e9ebee solid;
        height: 128px;
        // border-radius: 8px;

        h4 {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          height: 24px;
          font-family: HarmonyOS Sans SC;
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
          color: #333333;
        }
        p {
          height: 44px;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          font-family: HarmonyOS Sans SC;
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          color: #666666;
        }
        .item-bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-family: AlibabaPuHuiTi;
          font-size: 12px;
          font-weight: normal;
          line-height: 12px;
          color: #999999;

          img {
            width: 20px;
            height: 20px;
            border-radius: 10px;
            margin-right: 4px;
          }
          .item-nums {
            display: flex;
            div {
              margin-left: 16px;
              /deep/i {
                margin-right: 4px;
              }
            }
          }
        }
      }
    }
  }
  /deep/.ant-modal-close {
    top: -12px;
    right: -12px;
  }
}
</style>
