import request from '@/utils/request';

// 查询字典类型列表
export function listTypeRoot() {
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/type/listRoot',
    method: 'get',
  });
}

export function listType(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/type/list',
    method: 'get',
    params: query,
  });
}

// 查询字典类型详细
export function getType({ dictId }) {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/dict/type/detail/' +
      dictId,
    method: 'get',
  });
}

// 新增字典类型
export function addType(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/type/add',
    method: 'post',
    data: data,
  });
}

// 修改字典类型
export function updateType(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/type/edit',
    method: 'post',
    data: data,
  });
}

// 删除字典类型
export function delType(dictId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/type/' + dictId,
    method: 'get',
  });
}

// 导出字典类型
export function exportType(query) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/type/export',
    method: 'get',
    params: query,
  });
}

// 获取字典选择框列表
export function optionselect() {
  return request({
    url:
      process.env.VUE_APP_BASE_API +
      '/api/authority/admin/dict/type/optionselect',
    method: 'get',
  });
}

// 新增字典数据
export function addData(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/data',
    method: 'post',
    data: data,
  });
}

// 删除字典数据
export function delData(dataId) {
  return request({
    url:
      process.env.VUE_APP_BASE_API + '/api/authority/admin/dict/data/' + dataId,
    method: 'get',
  });
}
