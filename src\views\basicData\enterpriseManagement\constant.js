// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    field: 'enterpriseName',
    title: '企业名称',
    minWidth: 180,
  },
  {
    field: 'enterpriseAddress',
    title: '企业地址',
    minWidth: 180,
  },
  {
    field: 'enterpriseProperty',
    title: '企业性质',
    width: 180,
    formatter: ({ cellValue }) => {
      return getStatus(cellValue);
    },
  },
  {
    field: 'affiliatedPark',
    title: '所属园区',
    width: 180,
  },
  {
    field: 'floor',
    title: '楼栋',
    width: 180,
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 120,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'enterpriseName',
    title: '企业名称',
    element: 'a-input',
    // element: 'slot',
    // slotName: 'nameSlot',
  },
  {
    field: 'parkId',
    title: '所属园区',
    element: 'slot',
    slotName: 'enterSlot',
  },
  {
    field: 'enterpriseProperty',
    title: '企业性质',
    element: 'a-select',
    props: {
      options: [],
      showSearch: true,
      optionFilterProp: 'children',
    },
  },
];

export const initFormValue = () => {
  return {
    enterpriseName: '',
    enterpriseAddress: '',
    enterpriseProperty: '',
    affiliatedPark: '',
    floor: '',
    parkId: '',
  };
};

export const initFormLabelValue = () => {
  return {
    labelId: [],
  };
};

export const getStatus = (code) => {
  switch (code) {
    case '1':
      return '科技企业';
    case '2':
      return '工业企业';
    case '3':
      return '后勤管理';
    case '4':
      return '商铺';
    case '5':
      return '其它';
  }
};
