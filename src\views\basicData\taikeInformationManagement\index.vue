<template>
  <page-layout>
    <a-tabs default-active-key="1" @change="tabChange" v-model="tabKey">
      <a-tab-pane key="1" tab="基础信息">
        <PageWrapper
          :loading="loading"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :config="{ noMargin: true }"
          @loadData="loadData"
        >
          <!-- table插槽 -->
          <template #operate="{ row }">
            <span class="operate-button" @click="onClickEdit(row)">编辑</span>
          </template>
          <!-- 基础信息弹窗 -->
          <ListBasicModal
            :visible="visibleBasic"
            :dict="dict?.type || []"
            :detail="modalBasicData"
            @loadData="loadData"
            @handleCancel="handleCancel"
          />
        </PageWrapper>
      </a-tab-pane>
      <a-tab-pane key="2" tab="指标信息">
        <PageWrapper
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="targetTableColumn"
          :tableData="tragetTableData"
          :config="{ noMargin: true }"
          @loadData="targetLoadData"
          :tableOn="{
            'checkbox-change': selectChangeEvent,
            'checkbox-all': selectChangeEvent,
          }"
          :tableProps="{
            'checkbox-config': {
              labelField: 'name',
              checkMethod: checCheckboxkMethod,
            },
          }"
        >
          <!-- 创建按钮区域插槽 -->
          <template #defaultHeader>
            <a-button
              type="primary"
              style="margin-right: 8px"
              @click="handleCreate"
            >
              新增
            </a-button>
            <a-button
              type="danger"
              style="margin-right: 8px"
              :disabled="!checkItems.length"
              @click="handelDelete"
            >
              删除
            </a-button>
          </template>
          <!-- filter插槽 -->
          <template #identity="{ item }">
            <a-input
              v-model="filterOptions.params[item.field]"
              placeholder="AutoFilter插槽"
            />
          </template>
          <template #workingType="{ item }">
            <a-select
              @change="timeChange"
              v-model="filterOptions.params[item.field]"
            >
              <a-select-option
                v-for="item in typeSelect"
                :key="item.value"
                :value="item.value"
                >{{ item.label }}</a-select-option
              >
            </a-select>
          </template>
          <!-- 月份插槽 -->
          <template #month="{ item }">
            <BuseRangePicker
              type="month"
              format="YYYY-MM"
              v-model="filterOptions.params[item.field]"
              :disableDateFunc="disableDateFunc"
            />
          </template>
          <!-- table插槽 -->
          <template #operate="{ row }">
            <span class="operate-button" @click="onClickEdit(row)">编辑</span>
          </template>
          <!-- 指标信息弹窗 -->
          <ListModal
            :visible="visible"
            :detail="modalData"
            :modelTitle="modelTitle"
            :indicator="indicator"
            @targetLoadData="targetLoadData"
            @handleCancel="handleCancel"
          />
        </PageWrapper>
      </a-tab-pane>
      <a-tab-pane key="3" tab="人才企业数量">
        <PageWrapper
          :loading="loading"
          :tablePage="tablePage"
          :tableColumn="targetTableColumn2"
          :tableData="tragetTableData"
          :config="{ noMargin: true }"
          @loadData="targetLoadData"
        >
          <!-- table插槽 -->
          <template #operate="{ row }">
            <span class="operate-button" @click="onClickEdit1(row)">编辑</span>
          </template>
          <!-- 指标信息弹窗 -->
        </PageWrapper>
      </a-tab-pane>
      <a-tab-pane key="4" tab="社区常住人口数量">
        <PageWrapper
          :loading="loading"
          :tablePage="tablePage"
          :tableColumn="targetTableColumn2"
          :tableData="tragetTableData"
          :config="{ noMargin: true }"
          @loadData="targetLoadData"
        >
          <!-- table插槽 -->
          <template #operate="{ row }">
            <span class="operate-button" @click="onClickEdit1(row)">编辑</span>
          </template>
        </PageWrapper>
      </a-tab-pane>
    </a-tabs>
    <BuseModal
      ref="modal"
      :modalConfig="modal.modalConfig"
      :formConfig="modal.formConfig"
      :submit="handleModalSubmit"
      v-model="modal.formParams"
    />
  </page-layout>
</template>

<script>
import BuseModal from '@/components/BuseModal/index.vue';
import moment from 'moment';
import { defaultTableColumn } from './constant';
import ListModal from './components/ListModal.vue';
import ListBasicModal from './components/ListBasicModal.vue';
import {
  baseInformation,
  metricInformation,
  codeByType,
  deleteInformation,
  getInformationByTape,
  editInformation,
} from '@/api/basicData';
import { tableColumn } from '@/views/documentCenter/bucketList/constant';
export default {
  components: { ListModal, ListBasicModal, BuseModal },
  dicts: ['my_notify_rule', 'tkc_basic'],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tragetTableData: [],
      currentTab: 1,
      visible: false,
      visibleBasic: false,
      modalBasicData: null,
      modalData: null,
      checkItems: [],
      modelTitle: 'add',
      indicator: [],
      timeType: '1',
      typeSelect: [
        {
          value: 1,
          label: '年',
        },
        {
          value: 2,
          label: '月',
        },
      ],
      // 指标表格列配置
      targetTableColumn: [
        {
          type: 'checkbox',
          width: 60,
          fixed: 'left',
        },
        {
          field: 'workingType',
          title: '统计类型',
          width: 250,
          formatter: ({ cellValue }) => {
            return cellValue === '1' ? '年' : '月';
          },
        },
        {
          field: 'rateTime',
          title: '时间',
          width: 250,
        },
        {
          field: 'indicator',
          title: '指标',
          minWidth: 100,
          formatter: ({ cellValue }) => {
            return this.filterIndicator(cellValue);
          },
        },
        {
          field: 'value',
          title: '值',
          width: 200,
        },
        {
          field: 'unit',
          title: '单位',
          width: 200,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 180,
        },
      ],
      targetTableColumn2: [
        {
          field: 'indicator',
          title: '基础信息',
          minWidth: 100,
          formatter: ({ cellValue }) => {
            return this.filterIndicator(cellValue);
          },
        },
        {
          field: 'value',
          title: '值',
          width: 200,
        },
        {
          field: 'unit',
          title: '单位',
          width: 200,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 180,
        },
      ],

      modal: {
        modalConfig: {
          props: {
            title: '编辑',
            width: '400px',
          },
          formProps: {
            layout: 'vertical',
            defaultColSpan: 24,
          },
        },
        formParams: {},
        formConfig: [
          {
            field: 'value',
            title: '值',
            element: 'a-input',
          },
        ],
      },
      tabKey: '1',
      selectRow: {},
      talent_enterprie: [],
      community: [],
    };
  },
  created() {
    this.loadData();
    this.codeByType();
    this.codeByType('talent_enterprie');
    this.codeByType('community');
  },
  computed: {
    tableColumn() {
      return [
        {
          field: 'indicator',
          title: '基础信息',
          minWidth: 180,
          formatter: ({ cellValue }) => {
            return this.dict?.type?.tkc_basic?.find((q) => q.value == cellValue)
              ?.label;
          },
        },
        {
          field: 'value',
          title: '值',
          minWidth: 180,
        },
        {
          field: 'unit',
          title: '单位',
          width: 180,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 180,
        },
      ];
    },
    // 指标数据筛选器
    filterOptions() {
      return {
        showCount: undefined, // 初始展示几个筛选项 非必填
        params: {
          workingType: 1,
          rateTime: { startValue: '', endValue: '' },
          indicatorList: [],
        }, // 筛选器结果数据
        config: [
          {
            field: 'rateTime',
            title: '时间',
            element: 'slot',
            slotName: 'month',
            defaultValue: undefined,
          },
          {
            field: 'indicatorList',
            title: '指标信息',
            element: 'a-select',
            props: {
              options: this.indicator,
              mode: 'multiple',
            },
            defaultValue: [],
          },
        ],
      };
    },
  },
  methods: {
    onClickEdit1(row) {
      this.selectRow = row;
      this.$refs.modal.open({ params: row });
    },
    async handleModalSubmit() {
      const [res, err] = await editInformation({
        ...this.selectRow,
        idemType: this.tabKey,
        ...this.modal.formParams,
      });
      if (err) return;
      this.$message.success('保存成功');
      this.tabChange(this.tabKey);
    },
    // 字典加载完成
    // onDictReady() {
    //   this.filterOptions.config[1].props.options =
    //     this.dict.type.my_notify_rule;
    // },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const [res, err] = await baseInformation({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
      });
      this.loading = false;

      if (err) return;
      this.tablePage.total = res.total;
      this.tableData = res.data;
    },
    // 创建按钮点击事件
    handleCreate() {
      this.modelTitle = 'add';
      if (this.currentTab === 1) {
        this.visibleBasic = true;
      } else {
        this.visible = true;
      }
    },

    // 编辑按钮点击事件
    onClickEdit(row) {
      this.modelTitle = 'edit';
      if (this.currentTab == 1) {
        this.visibleBasic = true;
        this.visible = false;
        this.modalBasicData = row;
      } else {
        this.visible = true;
        this.visibleBasic = false;
        this.modalData = row;
      }
    },
    // 关闭弹窗
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.modalData = null;
      this.visibleBasic = false;
      this.visible = false;
    },
    // 查看详情
    onClickDetail() {
      // this.$router.push('./second/detail');
    },
    // 单选
    selectChangeEvent({ records }) {
      console.log('单选事件', records);
      this.checkItems = records;
    },
    // 指标信息数据请求
    async targetLoadData({ otherParams = {} } = {}) {
      const method = !this.currentTab
        ? metricInformation
        : getInformationByTape;

      this.loading = true;
      const params = this.filterOptions.params;
      const [res, err] = await method({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        startTime: params.rateTime.startValue
          ? moment(params.rateTime.startValue).format('yyyy-MM')
          : '',
        endTime: params.rateTime.endValue
          ? moment(params.rateTime.endValue).format('yyyy-MM')
          : '',
        indicatorList: params.indicatorList || [],
        ...otherParams,
        idemType: this.currentTab,
      });
      this.loading = false;

      if (err) return;
      // 设置数据
      res.data.records.map((item, index) => {
        item.index = index;
        return { item };
      });
      this.tablePage.total = res.data.total;
      this.tragetTableData = res.data.records;
    },
    // tab切换
    tabChange(key) {
      this.currentTab = key;
      if (key === '1') {
        this.loadData();
      } else if (key === '2') {
        this.targetLoadData();
      } else if (key === '3') {
        this.targetLoadData({ type: '1', otherParams: { idemType: '3' } });
      } else if (key === '4') {
        this.targetLoadData({ type: '1', otherParams: { idemType: '4' } });
      }
    },
    // 月份选择
    monthChange(date, dateString) {
      console.log('月份选择回调', date, dateString);
    },
    // 删除
    handelDelete() {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          that.deleteInformation();
        },
      });
    },
    // 复选框禁用前三项
    checCheckboxkMethod({ row }) {
      return row.index > -1;
    },
    // 指标类型枚举
    async codeByType(codeType = 'indicator') {
      const [res, err] = await codeByType({
        codeType,
      });
      if (err) return;
      this[codeType] = res.data.map((item) => {
        item.label = item.name;
        item.source = item;
        return item;
      });
    },
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    timeChange(val) {
      console.log('时间类型', val);
      this.timeType = val;
    },
    // 删除
    async deleteInformation() {
      const ids = this.checkItems.map((item) => {
        return item.id;
      });
      console.log('当前id', ids);
      const [, err] = await deleteInformation({
        list: ids,
      });
      if (err) return;
      // 刷新数据
      this.targetLoadData();
    },
    // format指标
    filterIndicator(val) {
      const dict = ['1', '2'].includes(this.tabKey)
        ? this.indicator
        : this.tabKey === '3'
        ? this.talent_enterprie
        : this.community;
      return dict
        .filter((item) => {
          return val === item.value;
        })
        .map((item) => item.label);
    },
  },
};
</script>
<style scoped>
::v-deep .page-wrapper-container {
  margin: 0 !important;
}
</style>
