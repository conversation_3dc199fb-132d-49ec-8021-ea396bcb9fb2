<template>
  <div :class="[tableTitle ? 'table-container' : '']">
    <h3 v-if="tableTitle">{{ tableTitle }}</h3>
    <div 
      class="vxe-scroll-wrapper"
      :style="{ height: height }"
      @mouseenter="pauseAutoScroll"
      @mouseleave="resumeAutoScroll"
    >
      <vxe-grid
        ref="vxeTable"
        :columns="vxeColumns"
        :data="visibleData"
        :height="height"
        border="inner"
        stripe
        header-align="center"
        align="center"
        size="mini"
        :scroll-y="{ enabled: false }"
        :scroll-x="{ enabled: false }"
        show-overflow="tooltip"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScrollTableVxe',
  props: {
    height: {
      type: String,
      default: '400px',
    },
    tableTitle: {
      type: String,
      default: '',
    },
    columns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    scrollSpeed: {
      type: Number,
      default: 1,
    },
    scrollInterval: {
      type: Number,
      default: 50,
    },
    visibleRowCount: {
      type: Number,
      default: 10,
    },
    bufferSize: {
      type: Number,
      default: 5,
    },
    autoScroll: {
      type: Boolean,
      default: true,
    },
  },
  
  data() {
    return {
      scrollTimer: null,
      isScrollPaused: false,
      isUserScrolling: false,
      userScrollTimer: null,
      visibleData: [],
      dataBuffer: [],
      currentStartIndex: 0,
      isDestroyed: false,
    };
  },
  
  computed: {
    // 转换列配置为 vxe-table 格式
    vxeColumns() {
      return this.columns.map(col => ({
        field: col.field,
        title: col.title,
        width: col.width,
        minWidth: col.minWidth || 80,
        align: col.align || 'left',
        headerAlign: col.headerAlign || 'center',
        formatter: col.formatter ? ({ cellValue, row }) => {
          if (col.type === 'html') {
            // vxe-table 不直接支持 HTML，需要特殊处理
            return col.formatter({ cellValue, row });
          }
          return col.formatter({ cellValue, row });
        } : null,
        slots: col.type === 'html' ? {
          default: ({ row }) => {
            const value = col.formatter ? 
              col.formatter({ cellValue: row[col.field], row }) : 
              row[col.field];
            return [<span domPropsInnerHTML={value}></span>];
          }
        } : null,
      }));
    },
    
    totalBufferSize() {
      return this.visibleRowCount + this.bufferSize * 2;
    },
  },
  
  watch: {
    tableData: {
      handler(newData) {
        if (newData && newData.length > 0) {
          this.initializeData();
          this.startAutoScroll();
        } else {
          this.stopAutoScroll();
        }
      },
      immediate: true,
    },
    
    autoScroll(newVal) {
      if (newVal) {
        this.startAutoScroll();
      } else {
        this.stopAutoScroll();
      }
    },
  },
  
  mounted() {
    this.$nextTick(() => {
      this.initializeData();
      if (this.autoScroll && this.tableData.length > 0) {
        this.startAutoScroll();
      }
    });
  },
  
  beforeDestroy() {
    this.isDestroyed = true;
    this.stopAutoScroll();
    this.clearAllTimers();
  },
  
  methods: {
    initializeData() {
      if (!this.tableData || this.tableData.length === 0) {
        this.visibleData = [];
        this.dataBuffer = [];
        return;
      }
      
      this.createDataBuffer();
      this.updateVisibleData();
      this.currentStartIndex = 0;
    },
    
    createDataBuffer() {
      const sourceData = this.tableData;
      const bufferSize = Math.max(this.totalBufferSize, sourceData.length);
      
      this.dataBuffer = [];
      for (let i = 0; i < bufferSize; i++) {
        const sourceIndex = i % sourceData.length;
        this.dataBuffer.push({
          ...sourceData[sourceIndex],
          _originalIndex: sourceIndex,
          _bufferIndex: i,
        });
      }
    },
    
    updateVisibleData() {
      const startIndex = this.currentStartIndex;
      const endIndex = startIndex + this.visibleRowCount;
      
      this.visibleData = this.dataBuffer.slice(startIndex, endIndex);
      
      if (this.visibleData.length < this.visibleRowCount) {
        const remaining = this.visibleRowCount - this.visibleData.length;
        const additionalData = this.dataBuffer.slice(0, remaining);
        this.visibleData = [...this.visibleData, ...additionalData];
      }
    },
    
    startAutoScroll() {
      if (!this.autoScroll || this.isScrollPaused || this.isUserScrolling || this.isDestroyed) {
        return;
      }
      
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }
      
      this.scrollTimer = setInterval(() => {
        if (this.isDestroyed || this.isScrollPaused || this.isUserScrolling) {
          return;
        }
        this.performAutoScroll();
      }, this.scrollInterval);
    },
    
    stopAutoScroll() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
    },
    
    performAutoScroll() {
      if (!this.dataBuffer.length) return;
      
      // 移动到下一行
      this.currentStartIndex = (this.currentStartIndex + 1) % this.dataBuffer.length;
      
      // 更新可见数据
      this.updateVisibleData();
      
      // 扩展数据缓冲区（如果需要）
      this.expandBufferIfNeeded();
    },
    
    expandBufferIfNeeded() {
      const currentBufferSize = this.dataBuffer.length;
      const requiredSize = this.currentStartIndex + this.visibleRowCount + this.bufferSize;
      
      if (requiredSize > currentBufferSize) {
        const additionalSize = requiredSize - currentBufferSize;
        for (let i = 0; i < additionalSize; i++) {
          const sourceIndex = (currentBufferSize + i) % this.tableData.length;
          this.dataBuffer.push({
            ...this.tableData[sourceIndex],
            _originalIndex: sourceIndex,
            _bufferIndex: currentBufferSize + i,
          });
        }
      }
    },
    
    pauseAutoScroll() {
      this.isScrollPaused = true;
    },
    
    resumeAutoScroll() {
      this.isScrollPaused = false;
      if (this.autoScroll && !this.isUserScrolling) {
        this.startAutoScroll();
      }
    },
    
    clearAllTimers() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
      
      if (this.userScrollTimer) {
        clearTimeout(this.userScrollTimer);
        this.userScrollTimer = null;
      }
    },
  },
};
</script>

<style scoped lang="less">
.table-container {
  width: 100%;
  background: rgba(249, 249, 249, 0.6);
  padding: 16px;
  border-radius: 8px;
  
  h3 {
    padding-left: 19px;
    font-family: PingFang SC;
    font-size: 12px;
    color: #333333;
    background-image: url('@/assets/images/portraits/1-0041.png');
    background-size: 14px 13px;
    background-repeat: no-repeat;
    background-position: 0 2px;
    margin-bottom: 12px;
  }
}

.vxe-scroll-wrapper {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
  
  :deep(.vxe-table) {
    border-radius: 4px;
  }
  
  :deep(.vxe-header--row) {
    background-color: #f5f5f5;
  }
  
  :deep(.vxe-body--row) {
    transition: all 0.3s ease;
  }
  
  :deep(.vxe-body--row:hover) {
    background-color: #f0f9ff;
  }
}
</style>
