<template>
  <div class="data-board">
    <div class="analysis-all">
      <h2>太湖湾科创城碳排放综合分析</h2>
      <BaseChart
        v-if="showCarbonEmission"
        class="chart-box"
        :axisData="carbonEmission.axisData"
        :seriesData="carbonEmission.seriesData"
        lineType="bar"
        unit="单位：吨"
      />
      <BaseChart
        v-if="showCarbonEmissionReduction"
        :axisData="carbonEmissionReduction.axisData"
        :seriesData="carbonEmissionReduction.seriesData"
        lineType="line"
        unit="单位：吨/万元"
      />
    </div>
    <div class="analysis-energy">
      <h2>能耗综合分析</h2>
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="能耗（吨标煤）">
          <div class="tab-content">
            <BaseChart
              class="chart-box"
              :axisData="energy.axisData"
              :seriesData="energy.seriesData"
              lineType="bar"
              unit="单位：吨标煤"
            />
            <div class="ranking">
              <h2>能源消耗园区排名</h2>
              <div
                class="rank-item"
                v-for="(item, index) in energyConsumptionRanking"
                :key="index"
              >
                <span class="seq">{{ index + 1 }}</span>
                <span class="park-name">{{ item.name }}</span>
                <span class="number">{{ item.energyConsumption }}</span>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="单位产值能耗（吨标煤/万元）" force-render>
          <div class="tab-content">
            <BaseChart
              class="chart-box"
              :axisData="energyUnit.axisData"
              :seriesData="energyUnit.seriesData"
              lineType="bar"
              unit="单位：吨标煤/万元"
            />
            <div class="ranking">
              <h2>单位产值能耗园区排名</h2>
              <div
                class="rank-item"
                v-for="(item, index) in energyIntensityRanking"
                :key="index"
              >
                <span class="seq">{{ index + 1 }}</span>
                <span class="park-name">{{ item.name }}</span>
                <span class="number">{{ item.energyIntensity }}</span>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
    <HeatAnalysis />
  </div>
</template>

<script>
import BaseChart from '@/components/chart/lineChart.vue';
import HeatAnalysis from './heatAnalysis.vue';
import { comprehensive } from '@/api/digitalOperation/greenDevelopment/dataBoard/index.js';

export default {
  name: 'ManagementTkIndex',
  components: {
    BaseChart,
    HeatAnalysis,
  },

  data() {
    return {
      //碳排量信息
      carbonEmission: {
        seriesData: [
          {
            name: '月度碳排放',
            //随机添加12条数据
            data: [],
          },
          {
            name: '月度碳减排',
            data: [],
          },
        ],
        axisData: [],
      },
      //碳减排信息
      carbonEmissionReduction: {
        seriesData: [
          {
            name: '平均碳强度',
            //随机添加12条数据 1-15
            data: [],
          },
          {
            name: '月度碳强度',
            data: [],
          },
        ],
        axisData: [],
      },
      //能耗综合分析--能耗（吨标煤）
      energy: {
        seriesData: [
          {
            name: '能耗（吨标煤）',
            //随机添加12条数据
            data: [],
          },
        ],
        axisData: [],
      },
      //能耗综合分析--单位产值能耗（吨标煤/万元）
      energyUnit: {
        seriesData: [
          {
            name: '单位产值能耗（吨标煤/万元）',
            //随机添加12条数据
            data: [],
          },
        ],
        axisData: [],
      },
      //能耗排名
      energyConsumptionRanking: [],
      //单位产值能耗排名
      energyIntensityRanking: [],
    };
  },
  computed: {
    showCarbonEmission() {
      const resOfAvg = this.carbonEmission.seriesData[0].data.find(
        (item) => item > 0
      );
      const resOfCarbonIntensity = this.carbonEmission.seriesData[1].data.find(
        (item) => item > 0
      );
      return resOfAvg !== undefined || resOfCarbonIntensity !== undefined;
    },
    showCarbonEmissionReduction() {
      const resOfAvg = this.carbonEmissionReduction.seriesData[0].data.find(
        (item) => item > 0
      );
      const resOfCarbonIntensity =
        this.carbonEmissionReduction.seriesData[1].data.find(
          (item) => item > 0
        );
      return resOfAvg !== undefined || resOfCarbonIntensity !== undefined;
    },
  },

  mounted() {
    this.comprehensive();
  },
  methods: {
    callback(val) {
      console.log(val);
    },
    async comprehensive() {
      const [res, err] = await comprehensive();
      if (err) return;
      this.dataHandle(res.data);
    },

    dataHandle(data) {
      let axisData = [];
      let carbonEmission = []; //碳排放
      let carbonReduction = []; //碳减排
      let carbonIntensity = []; //碳强度
      let carbonIntensityAvg = []; //平均碳强度
      let energyConsumption = []; //能耗
      let energyIntensity = []; //单位产值能耗
      console.log(data.monthlyReports, '绿色发展');
      data.monthlyReports.forEach((item) => {
        axisData.push(item.month);
        carbonEmission.push(item.carbonEmission);
        carbonReduction.push(item.carbonReduction);
        carbonIntensity.push(item.carbonIntensity);
        carbonIntensityAvg.push(data.carbonIntensityAvg);
        energyConsumption.push(item.energyConsumption);
        energyIntensity.push(item.energyIntensity);
      });
      this.carbonEmission.axisData = axisData;
      this.carbonEmission.seriesData[0].data = carbonEmission;
      this.carbonEmission.seriesData[1].data = carbonReduction;
      this.carbonEmissionReduction.axisData = axisData;
      this.carbonEmissionReduction.seriesData[0].data = carbonIntensityAvg;
      this.carbonEmissionReduction.seriesData[1].data = carbonIntensity;

      this.energy.axisData = axisData;
      this.energy.seriesData[0].data = energyConsumption;
      this.energyUnit.axisData = axisData;
      this.energyUnit.seriesData[0].data = energyIntensity;
      this.energyConsumptionRanking = data.energyConsumptionRanking;
      this.energyIntensityRanking = data.energyIntensityRanking;
    },
  },
};
</script>

<style lang="less" scoped>
.data-board {
  width: 100%;
  margin: 24px;
  .analysis-all {
    width: 100%;
    background: #fff;
    padding: 24px;
  }
  .chart-box {
    width: 100%;
    padding: 40px 0;
  }
  .analysis-energy {
    background: #fff;
    padding: 24px;
    margin-top: 40px;
    .tab-content {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      .chart-box {
        flex: 7;
      }
      .ranking {
        flex: 3;
        margin-left: 40px;
        .rank-item {
          display: flex;
          flex-direction: row;
          align-items: flex-start;
          padding: 8px 0;
          .seq {
            flex: 1%;
            font-size: 18px;
            color: #333;
            font-weight: bold;
          }
          .park-name {
            flex: 70%;
            font-size: 16px;
            color: #333;
          }
          .number {
            flex: 20%;
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>
