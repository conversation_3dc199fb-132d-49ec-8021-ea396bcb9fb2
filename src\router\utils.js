// 根据菜单配置，找到第一个可访问的菜单路径
export const getFirstSiteMenuPath = (siteMenuData) => {
  if (!siteMenuData) return '';
  const firstMenu = siteMenuData[0];
  if (firstMenu && firstMenu.children && firstMenu.children.length > 0) {
    return getFirstSiteMenuPath(firstMenu.children);
  } else {
    return firstMenu?.fullPath || '';
  }
};
/**
 * 格式化菜单栏，增加fullPath值
 * @param {*} options
 * @param {*} parentPath
 * @returns
 */
export const formatMenuData = (options, parentPath = '') => {
  options.forEach((route) => {
    const { meta, type, extra } = route;
    let extraJSon = {};
    try {
      extraJSon = JSON.parse(extra);
    } catch (err) {
      extraJSon = {};
    }
    // 将路由菜单类型塞入meta字段中
    route.meta = {
      ...meta,
      ...extraJSon,
      type: meta.type || type,
      ...extraJSon,
    };
    let isFullPath = route.path ? route.path.substring(0, 1) === '/' : false;
    route.fullPath = isFullPath
      ? route.path
      : `${parentPath}${route.path ? `/${route.path}` : ''}`;
    if (!route.redirect || route.redirect === 'noRedirect') {
      route.redirect = '';
    }
    if (route.children) {
      route.children = formatMenuData(route.children, route.fullPath);
    }
  });
  return options;
};
