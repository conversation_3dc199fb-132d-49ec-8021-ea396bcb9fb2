import moment from 'moment';
// 表格列配置
export const defaultTableColumn = () => [
  {
    field: 'state',
    title: '现状',
    minWidth: 180,
  },
  {
    field: 'name',
    title: '姓名',
    minWidth: 180,
  },
  {
    field: 'desc',
    title: '描述',
    minWidth: 180,
    showOverflow: true,
    formatter: ({ cellValue }) => {
      return cellValue + 'aaa';
    },
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 180,
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'name',
    title: '自定义名称',
    props: {
      placeholder: '请输入名称',
    },
  },
  {
    field: 'state',
    title: '字典：my_notify_rule',
    element: 'a-select',
    props: {
      options: [],
    },
  },
  {
    field: 'birthday',
    title: '生日',
    element: 'a-date-picker',
  },
  {
    field: 'birthdayRange',
    title: '出生时长',
    element: 'a-range-picker',
    defaultValue: ['', ''],
    style: {},
  },
];

export const initFormValue = () => {
  return {
    name: '',
    type: '',
    desc: '',
    workTimes: ['', ''],
    imgUrl: '',
    xmlUrl: '',
  };
};

export const formConfig = [
  {
    field: 'name',
    title: '人物名称',
    itemProps: {
      help: '请输入真实姓名',
    },
    props: {
      placeholder: '请输入人物名称',
    },
    rules: [{ required: true, message: '命中校验' }],
  },
  {
    field: 'type',
    title: '职业类型',
    element: 'a-select',
    props: {
      options: [
        { value: '1', label: '工地搬砖' },
        { value: '2', label: '公司搬砖' },
      ],
    },
  },
  {
    field: 'workTimes',
    title: '工作时长',
    element: 'a-range-picker',
    props: {
      showTime: {
        defaultValue: [
          moment('00:00:00', 'HH:mm:ss'),
          moment('23:59:59', 'HH:mm:ss'),
        ],
      },
    },
  },
  {
    field: 'desc',
    title: '描述',
    props: {
      placeholder: '请输入人物描述',
      type: 'textarea',
    },
  },
  {
    field: 'imgUrl',
    title: '图片提交',
    element: 'slot',
    slotName: 'imgUpload',
  },
  {
    field: 'xmlUrl',
    title: '图片提交',
    element: 'slot',
    slotName: 'xmlUpload',
  },
];
