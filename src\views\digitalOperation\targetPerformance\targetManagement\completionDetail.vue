<template>
  <div class="completion-detail-content">
    <div class="objective-overview">
      <h2>目标总览</h2>
      <a-row type="flex">
        <a-col :span="6">
          <p>
            <span class="title">分配园区：</span>{{ completionDetail.parkName }}
          </p>
        </a-col>
        <a-col :span="6">
          <p>
            <span class="title">目标分配量：</span
            >{{ completionDetail.assignedAmount }}
          </p>
        </a-col>
        <a-col :span="6">
          <p>
            <span class="title">当前完成情况：</span
            >{{ completionDetail.completeSchedule }}
          </p>
        </a-col>
        <a-col :span="6">
          <p>
            <span class="title">完成进度：</span
            >{{ completionDetail.finishingRateUnit }}
          </p>
        </a-col>
        <a-col :span="6">
          <p>
            <span class="title">目标年份：</span>{{ completionDetail.year }}
          </p>
        </a-col>
        <a-col :span="6">
          <p>
            <span class="title">分配时间：</span
            >{{ completionDetail.createTime }}
          </p>
        </a-col>
        <a-col :span="6">
          <p>
            <span class="title">一级指标：</span
            >{{ completionDetail.oneTargetName }}
          </p>
        </a-col>
        <a-col :span="6">
          <p>
            <span class="title">二级指标：</span
            >{{ completionDetail.twoTargetName }}
          </p>
        </a-col>
      </a-row>
    </div>
    <BuseCrud
      ref="crud"
      title="详情列表"
      :loading="loading"
      :filterOptions="filterOptions"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :modalConfig="modalConfig"
      @loadData="loadData"
    >
      <template slot="attachment" slot-scope="{ row }">
        <div v-if="row.attachments && row.attachments.length > 0">
          <downLoadTemplate
            v-for="(item, index) in row.attachments"
            :key="index"
            :attachment="item"
          />
        </div>
        <span v-else> 无</span>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import moment from 'moment';
import downLoadTemplate from '@/components/downLoadTemplate/index.vue';
import { reportingAll } from '@/api/digitalOperation/targetPerformance/targetManagement/completionDetail.js';
export default {
  name: 'ManagementTkCompletionDetail',
  components: { downLoadTemplate },
  data() {
    return {
      tableData: [],
      params: { stipulateTime: undefined },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      completionDetail: {},
    };
  },
  created() {
    this.tableColumn = [
      {
        title: '阶段性反馈规定日期',
        field: 'stipulateTime',
      },
      {
        title: '进度提交日期',
        field: 'submitTime',
      },
      {
        title: ' 当前完成情况',
        field: 'completeSchedule',
      },
      {
        title: ' 补充说明',
        field: 'remark',
      },
      {
        title: '附件',
        field: 'attachment',
        slots: {
          default: 'attachment',
        },
      },
    ];
  },
  computed: {
    filterOptions() {
      return {
        params: this.params,
        config: [
          {
            field: 'stipulateTime',
            title: '阶段性反馈规定日期',
            element: 'a-month-picker',
            itemProps: {
              labelCol: { span: 10 },
              wrapperCol: { span: 14 },
            },
          },
        ],
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
      };
    },
  },

  mounted() {
    this.loadData();
    this.completionDetail = JSON.parse(
      localStorage.getItem('completionDetail')
    );
    console.log('completionDetail', this.completionDetail);
  },

  methods: {
    async loadData() {
      // this.loading = true;
      console.log(this.params, 'this.params');
      const [res, err] = await reportingAll({
        stipulateTime: this.params.stipulateTime
          ? moment(this.params.stipulateTime).format('YYYY-MM')
          : '',
        cenaInfoId: this.$route.query.id,
      });
      if (err) return;
      this.loading = false;
      this.tableData = res.data;
    },
  },
};
</script>

<style lang="less" scoped></style>
