<template>
  <cardItem title="减排潜力" width="50%">
    <div class="card-group">
      <cardSingle v-for="(item, index) in list" :key="index" :item="item">
      </cardSingle>
    </div>
    <div class="chart">
      <pieChart
        title="分区潜力"
        :list="pieList"
        v-if="pieList.length > 0"
      ></pieChart>
      <a-empty v-else></a-empty>
    </div>
  </cardItem>
</template>

<script>
import pieChart from './components/pieChart.vue';
import cardItem from './components/cardItem.vue';
import cardSingle from './components/cardSingle.vue';
export default {
  props: {
    pieList: {
      type: Array,
      default: () => [],
    },
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: { cardSingle, cardItem, pieChart },
  data() {
    return {};
  },
  computed: {
    list() {
      return [
        {
          label: '燃气',
          num: this.info?.gasProportion || '-',
          unit: '%',
          icon: require('@/assets/images/materialScreen/icon-gas.png'),
        },
        {
          label: '电',
          num: this.info?.electricityProportion || '-',
          unit: '%',
          icon: require('@/assets/images/materialScreen/icon-electricity.png'),
        },
        {
          label: '水',
          num: this.info?.waterProportion || '-',
          unit: '%',
          icon: require('@/assets/images/materialScreen/icon-water.png'),
        },
      ];
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="less">
.card-group {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.chart {
  width: 100%;
}
</style>
