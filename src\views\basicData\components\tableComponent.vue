<template>
  <div>
    <BuseCrud
      :title="tableTitle[pageName]"
      ref="crud"
      :loading="loading"
      :tablePage="tablePage"
      :tableData="tableData"
      :tableColumn="tableColumn"
      :modalConfig="modalConfig"
      :filterOptions="filterOptions"
      :tableOn="tableCheckbox"
      @modalCancel="modalCancelHandler"
      @modalSubmit="modalSubmit"
      @rowDel="deleteRowHandler"
      @loadData="loadData"
      @handleCreate="rowAdd"
      @rowEdit="rowEdit"
      @rowView="rowView"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
    >
      <template slot="defaultTitle">
        <span></span>
      </template>
      <!--    左侧按钮插槽-->
      <template slot="defaultHeader" v-if="buttonShow">
        <div>
          <a-button type="primary" class="mr-8" @click="rowAdd">新增</a-button>
          <a-button class="mr-8" @click="onClickImport">导入</a-button>
          <a-button
            v-if="!notExportAll"
            type="button"
            class="mr-8"
            @click="exportAllTable"
            >全部导出
          </a-button>
          <a-button
            :disabled="!checkItems.length && !notExportAll"
            type="button"
            class="mr-8"
            @click="exportTable"
            :loading="exportLoading"
            >导出
          </a-button>
          <a-button
            :disabled="!checkItems.length"
            type="danger"
            class="mr-8"
            @click="deleteData"
            >删除
          </a-button>
        </div>
      </template>
      <!--    日期筛选器（年）-->
      <template slot="dateYear">
        <BuseRangePicker
          type="year"
          placeholder="请选择年份"
          v-model="getTime.rateTime"
          :needShowSecondPicker="() => false"
          format="YYYY"
          :disableDateFunc="disableDateFunc"
        />
      </template>
      <!--    日期筛选器（年）-->
      <template slot="year">
        <BuseRangePicker
          type="year"
          placeholder="请选择年份"
          v-model="getTime.year"
          :needShowSecondPicker="() => false"
          format="YYYY"
          :disableDateFunc="disableDateFunc"
        />
      </template>
      <!--        弹框日期-->
      <template slot="dateYearForPop" slot-scope="{ params }">
        <BuseRangePicker
          type="year"
          placeholder="请选择年份"
          v-model="params.rateTime"
          :needShowSecondPicker="() => false"
          format="YYYY"
          :disableDateFunc="disableDateFunc"
        />
      </template>
      <!--        认定日期-->
      <template slot="particularYearForPop" slot-scope="{ params }">
        <BuseRangePicker
          type="year"
          placeholder="请选择年份"
          v-model="params.particularYear"
          :needShowSecondPicker="() => false"
          format="YYYY"
          :disableDateFunc="disableDateFunc"
        />
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import moment from 'moment';
import * as api from '@/api/basicData/index';
import { institutionsMixin } from '../mixins/institutionsMixin';
import { resolveBlob } from '@/utils/common/fileDownload';

export default {
  name: 'tableComponent',
  mixins: [institutionsMixin],
  props: {
    tableColumn: {
      type: Array,
      default: function () {
        return [];
      },
    },
    modalConfig: {
      type: Object,
      default: function () {
        return {};
      },
    },
    filterOptions: {
      type: Object,
      default: function () {
        return {};
      },
    },
    pageName: {
      type: String,
      default: function () {
        return '';
      },
    },
    parentData: {
      type: Object,
      default: function () {
        return {};
      },
    },
    buttonShow: {
      type: Boolean,
      default: function () {
        return true;
      },
    },
    notExportAll: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      loading: false,
      checkItems: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableTitle: {
        cityPlanInstitutions: '',
        incubatorsInstitutions: '',
        innovationPlatformInstitutions: '',
        majorProgramInstitutions: '',
        ledgerManage: '',
        overseasManage: '',
        riskInvesmentManage: '',
        majorInnovationManage: '',
      },
      exportLoading: false,
    };
  },
  computed: {
    // vxe-table多选方法
    tableCheckbox() {
      return {
        'checkbox-change': this.selectChangeEvent,
        'checkbox-all': this.selectChangeEvent,
      };
    },
    // 获取时间
    getTime() {
      return this.parentData;
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 请求表格数据
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      let startTime = '';
      if (
        params.rateTime &&
        Object.prototype.hasOwnProperty.call(params.rateTime, 'startValue')
      ) {
        if (params.rateTime?.startValue) {
          startTime = params.rateTime.startValue.format('YYYY');
        } else {
          startTime = '';
        }
      } else {
        startTime = '';
      }
      this.checkItems = [];
      const [res, err] = await api.getListInfo(
        {
          limit: this.tablePage.pageSize,
          pageNum: this.tablePage.currentPage,
          ...params,
          rateTime: startTime,
          year: params?.year?.startValue
            ? moment(params?.year?.startValue).format('YYYY')
            : '',
        },
        this.pageName
      );
      this.loading = false;

      if (err) return;

      // 设置数据
      this.tablePage.total = res.data?.total || res.total || 0;
      this.tableData = res.data?.records || res.data?.data || res?.data;
    },
    // 关闭弹框
    modalCancelHandler() {},
    // 提交弹框
    async modalSubmit(value) {
      let obj = {
        rateTime: value?.rateTime?.startValue?.format('YYYY'),
        particularYear: value.particularYear
          ? value.particularYear.startValue?.format('YYYY')
          : undefined,
        makeTime: value.makeTime
          ? value.makeTime?.format('YYYY-MM-DD')
          : undefined,
        serviceTime: value.serviceTime
          ? value.serviceTime?.format('YYYY-MM-DD')
          : undefined,
        enterpriseName: value.enterprise?.label,
        enterpriseId: value.enterprise?.key,
        year: value.year?.startValue
          ? moment(value.year?.startValue).format('YYYY')
          : undefined,
      };
      const params = { ...value, ...obj };
      if (value.crudOperationType === 'add') {
        const [, err] = await api.addInfo(params, this.pageName);
        if (err) {
          return false;
        }
        this.$message.success('新增成功!');
      } else {
        const [, err] = await api.editInfo(params, this.pageName);
        if (err) {
          return false;
        }
        this.$message.success('编辑成功!');
      }
      this.loadData();
    },
    // 删除一条数据
    async deleteRowHandler() {
      // const params = this.params
      // const [res, err] = await api.addInfo(params, this.pageName)
      // if (err) return
      // this.loadData()
    },
    // 删除多条数据
    async deleteData() {
      const list = this.checkItems.map((item) => {
        return item.id;
      });
      const params = {
        list: list,
        ids: list,
      };
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [, err] = await api.deleteInfo(params, that.pageName);
          if (!err) {
            that.$message.success('删除成功');
            // 刷新数据
            that.loadData();
            // 删除完数据后删除选中数组中内容，删除按钮恢复禁用状态
            that.checkItems = [];
            return;
          }
        },
      });
    },
    // 日期格式化
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: this.pageName,
          // url: '', // 导出传参下载模板的url
          // apiUrl: 'ajaxUrl', // 导出传参上传请求url
        },
      });
    },
    // 导出
    async exportTable() {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
      const list = this.checkItems.map((item) => {
        return item.id;
      });
      const params = {
        list: list,
        ids: list,
        ...this.filterOptions.params,
        year: this.filterOptions?.params?.year?.startValue
          ? moment(this.filterOptions?.params.year.startValue).format('YYYY')
          : '',
      };
      this.exportLoading = true;
      const [res] = await api.downloadInformation(params, this.pageName);
      this.exportLoading = false;
      resolveBlob(res, mimeMap.xlsx, '导出数据', '.xls');
    },
    // 导出全部数据
    async exportAllTable() {
      const mimeMap = {
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
      const [res] = await api.downloadAllInformation(
        {
          ...this.filterOptions.params,
          year: this.filterOptions?.params?.year?.startValue
            ? moment(this.filterOptions.params.year.startValue).format('YYYY')
            : '',
        },
        this.pageName
      );
      resolveBlob(res, mimeMap.xlsx, '导出数据', '.xls');
    },
    // 多选
    selectChangeEvent({ records }) {
      this.checkItems = records;
    },
    // 编辑格式化
    rowEdit(row) {
      const rateTime = {
        startValue: row.rateTime ? moment(row.rateTime) : null,
        endValue: null,
      };
      const enterprise = {
        key: row.enterpriseId,
        label: row.enterpriseName,
      };
      let list = [
        {
          label: row.enterpriseName,
          value: row.enterpriseId,
        },
      ];
      this.$emit('getEnterprise', list);
      this.$refs.crud.switchModalView(true, 'UPDATE', {
        ...row,
        rateTime,
        enterprise,
        enterpriseName: row.enterpriseName,
        year: { startValue: row.year },
      });
    },
    // 查看格式化
    rowView(row) {
      let list = [
        {
          label: row.enterpriseName,
          value: row.enterpriseId,
        },
      ];
      const enterprise = {
        key: row.enterpriseId,
        label: row.enterpriseName,
      };
      this.$emit('getEnterprise', list);
      this.$refs.crud.switchModalView(true, 'VIEW', {
        ...row,
        enterprise,
      });
    },
    // 新增数据
    rowAdd() {
      let list = [];
      this.$emit('getEnterprise', list);
      this.$refs.crud.switchModalView(true, 'ADD');
    },
    setForm(val) {
      this.$refs.crud && this.$refs.crud.setFormFields(val);
    },
  },
};
</script>

<style scoped lang="scss">
.mr-8 {
  margin-right: 8px;
}
</style>
