<template>
  <div class="add-record-container">
    <DynamicForm
      ref="dynamicForm"
      :defaultColSpan="8"
      :config="formConfig"
      :params="params"
    >
      <template slot="uploadFile" slot-scope="{ params }">
        <div class="file">
          <uploadFiles
            :disabled="disabled"
            v-model="params.attachment"
            :accept="'.png, .pdf, .jpg, .word, .excel'"
            @setAnnexList="setAnnexList"
            :showDefaultUploadList="true"
            :maxCount="5"
            :fileListTemp="params.attachment"
            :maxSize="4"
          ></uploadFiles>
        </div>
      </template>
    </DynamicForm>
    <vxe-grid
      ref="vxeRef"
      :autoResize="true"
      :columns="tableColumns"
      :data="tableData"
    >
      <template slot="checkResult" slot-scope="{ row }">
        <a-radio-group
          :disabled="isViewAndTag"
          v-model="row.checkSituation"
          :default-value="row.checkSituation"
        >
          <a-radio value="1"> 符合 </a-radio>
          <a-radio value="0"> 不符合 </a-radio>
          <a-radio
            value="2"
            v-if="row.standardReference.indexOf('洁净厂房、高层厂房') != '-1'"
          >
            不需要设置
          </a-radio>
        </a-radio-group>
      </template>
      <template slot="findTheProblem" slot-scope="{ row }">
        <a-textarea
          :disabled="isViewAndTag"
          v-model="row.findTheProblem"
        ></a-textarea>
      </template>
      <template slot="rectifyComments" slot-scope="{ row }">
        <a-textarea
          :disabled="isViewAndTag"
          v-model="row.rectifyComments"
        ></a-textarea>
      </template>
      <template
        slot="attachments"
        slot-scope="{ row }"
        v-if="row.checkSituation == '0'"
      >
        <uploadPics
          :disabled="isViewAndTag"
          v-model="row.attachments"
          :accept="'.png, .jpg'"
          :maxCount="1"
          @setNewsImgs="(fileLists) => setNewsImg(fileLists, row)"
          :fileListTemp="row.attachments"
          :maxSize="10"
        />
      </template>
      <template
        slot="hiddenDanger"
        slot-scope="{ row }"
        v-if="row.checkSituation == '0'"
      >
        <a-radio-group
          :disabled="isViewAndTag"
          v-model="row.hiddenDanger"
          :default-value="row.hiddenDanger"
        >
          <a-radio value="0"> 一般隐患 </a-radio>
          <a-radio value="1"> 重大隐患 </a-radio>
        </a-radio-group>
      </template>
      <template slot="operation" slot-scope="{ row }">
        <span
          class="edit"
          @click="editHandle(row)"
          v-if="row.tag !== '0' && !isViewAndTag"
          >编辑</span
        >
      </template>
    </vxe-grid>
    <a-row type="flex" justify="end">
      <a-col :span="6">
        <a-button :disabled="isViewAndTag" @click="addHandle"
          >列表维护</a-button
        >
        <a-button
          :disabled="isViewAndTag"
          type="primary"
          class="submit-btn"
          @click="submitHandler"
          >保存</a-button
        >
        <a-button @click="goBack" style="margin-left: 10px"> 返回 </a-button>
      </a-col>
    </a-row>
    <addListForIE
      v-if="isVisible"
      :isVisible="isVisible"
      @cancelModal="cancelModal"
      :rowData="editRowData"
      :addOrEdit="addOrEdit"
      :serialNumber="serialNumber"
    />
  </div>
</template>

<script>
import uploadPics from '@/components/Uploads/uploadPics.vue';
import uploadFiles from '@/components/Uploads/uploadFilesNew.vue';
import addListForIE from '../addList/addListForIE.vue';
import {
  randerImgs,
  imgToStringHandle,
  oneImgsHandle,
  oneImgsRander,
} from '@/utils/index';
import { getTableColumn } from './addForIE.js';
// import moment from 'moment';
import {
  enterpriseInfo,
  addOrUpdate,
} from '@/api/digitalOperation/securityManagement/parkSafety/industrialEnterprise.js';
import { getExcelData } from '@/api/digitalOperation/securityManagement/parkSafety/industrialEnterprise';
export default {
  name: 'ManagementTkAddForFS',
  components: {
    uploadPics,
    uploadFiles,
    addListForIE,
  },

  data() {
    return {
      checkResult: true,
      tableData: null,
      params: {
        reviewedOrganizeId: undefined,
        reviewedOrganizeName: undefined,
        inspectTime: undefined,
        address: undefined,
        fireSafetyName: undefined,
        inspectName: undefined,
        checkResult: undefined,
        reviewTime: undefined,
        reviewResult: undefined,
        attachment: [],
      },
      isViewAndTag: false,
      userAccountFilterList: [],
      row: '',
      disabled: false,
      isVisible: false,
      editRowData: {},
      addOrEdit: '',
      serialNumber: '', //新增时入参序号
    };
  },

  computed: {
    formConfig() {
      return [
        {
          title: '被检查单位',
          field: 'reviewedOrganizeName',
          // defaultValue: this.params.reviewedOrganizeName,
          rules: [
            {
              required: true,
              message: '请输入被检查单位',
            },
          ],
          props: {
            disabled: true,
          },
        },
        {
          title: '检查时间',
          field: 'inspectTime',
          element: 'a-date-picker',
          rules: [
            {
              required: true,
              message: '请输入检查时间',
            },
          ],
          props: {
            disabled: this.disabled,
          },
        },
        {
          title: '检查人员',
          field: 'inspectName',
          rules: [
            {
              required: true,
              message: '请输入检查人员',
            },
          ],
          props: {
            disabled: this.disabled,
          },
        },
        {
          title: '单位消防安全负责人',
          field: 'fireSafetyName',
          rules: [
            {
              required: true,
              message: '请输入检查单位负责人',
            },
          ],
          props: {
            disabled: this.disabled,
          },
          colProps: {
            span: 12,
          },
        },
        {
          title: '上传附件',
          field: 'attachment',
          element: 'slot',
          slotName: 'uploadFile',
          props: {
            disabled: this.disabled,
          },
          colProps: {
            span: 24,
          },
          itemProps: {
            labelCol: {
              span: 2,
            },
            wrapperCol: { span: 10 },
          },
        },
      ];
    },
  },
  created() {
    this.tableColumns = getTableColumn.call(this);
    this.row = JSON.parse(localStorage.getItem('addForIEInfo'));
  },

  mounted() {
    this.getBaseInfo();
    this.rowHandel();
  },

  methods: {
    //查看详情
    async enterpriseInfo() {
      const [res, err] = await enterpriseInfo(this.$route.query.id);
      if (err) return;

      let type = this.$route.query.type;
      var mergedArray = this.tableData.map(function (item1) {
        var item2 = res.data.find(function (item2) {
          return item1.id == item2.industrialEnterpriseExcelId;
        });
        //将查询到的两个表拼接在一起
        if (item2) {
          //查看详情时展示右半边数据
          if (type == 'view') {
            return Object.assign({}, item1, item2);
          } else {
            //新增时只取对应的id值
            return Object.assign({}, item1, {
              checkSituation: null,
              findTheProblem: null,
              industrialEnterpriseExcelId: item2.industrialEnterpriseExcelId,
              industrialEnterpriseId: item2.industrialEnterpriseId,
              rectifyComments: null,
            });
          }
        } else {
          return item1;
        }
      });
      this.tableData = mergedArray;
      this.tableData.forEach((item) => {
        item.attachments = oneImgsRander(item.attachments);
      });

      this.serialNumber =
        Number(this.tableData[this.tableData.length - 1].serialNumber) + 1;
    },
    addHandle() {
      this.editRowData = {};
      this.addOrEdit = 'add';
      this.isVisible = true;
    },
    //编辑
    editHandle(row) {
      this.editRowData = { ...row };
      console.log('this.editRowData', this.editRowData);
      this.addOrEdit = 'edit';
      this.isVisible = true;
    },
    //保存
    async addOrUpdate(p) {
      const [, err] = await addOrUpdate(p);
      if (err) return this.$message.error('保存失败');
      this.$message.success('保存成功');
      this.goBack();
    },
    goBack() {
      this.$router.push({
        name: 'parkSecurity',
        query: {
          type: 'IE',
        },
      });
    },
    //单个图片
    setNewsImg(fileList, row) {
      console.log('单个图片', fileList);
      row.attachments = fileList;
    },
    rowHandel() {
      console.log(this.row, 'this.row----');
      const {
        parkId,
        parkName,
        reviewedOrganizeId,
        reviewedOrganizeName,
        inspectTime,
        fireSafetyName,
        inspectName,
        attachments,
      } = this.row;
      this.params.parkId = parkId;
      this.params.parkName = parkName;
      this.params.reviewedOrganizeId = reviewedOrganizeId;
      this.params.reviewedOrganizeName = reviewedOrganizeName;
      if (this.$route.query.type == 'view') {
        this.params.inspectTime = inspectTime;
        this.params.inspectName = inspectName;
        this.params.fireSafetyName = fireSafetyName;
        this.params.attachment = randerImgs(attachments);
        this.disabled = true;
        this.row.tag !== '0'
          ? (this.isViewAndTag = true)
          : (this.isViewAndTag = false); //标识  0:A端录入   1:B端录入
      }
    },
    getBaseInfo() {
      getExcelData({ businessFlag: 1 }).then(([res, err]) => {
        if (err) {
          return;
        }
        this.tableData = res.data;
        this.enterpriseInfo();
      });
    },
    setAnnexList(fileList) {
      this.params.attachment = fileList;
    },

    cancelModal(type) {
      type == 'refresh' ? this.getBaseInfo() : '';
      this.isVisible = false;
    },
    submitHandler() {
      this.$refs.dynamicForm.validate((result) => {
        if (!result) {
          this.$message.error('请先填写完整表单');
          return;
        }
        let flag = true;
        //提交处理
        //获取表格数据和表单数据
        console.log(this.params, 'this.params');
        console.log(this.tableData, 'this.tableData');
        let relevancyList = this.tableData.map((item, index) => {
          if (item.checkSituation == '0') {
            if (!item.attachments.length || !item.hiddenDanger) {
              flag = false;
              this.$message.error(`请完成列表第${index + 1}行的数据`);
              return;
            }
          }
          return {
            checkSituation: item.checkSituation,
            findTheProblem: item.findTheProblem,
            industrialEnterpriseExcelId: item.id,
            rectifyComments: item.rectifyComments,
            attachments: oneImgsHandle(item.attachments),
            hiddenDanger: item.hiddenDanger,
          };
        });
        if (!flag) return;
        const { type, id } = this.$route.query;
        let p = {
          id: type == 'add' && this.row.inspectTime ? undefined : id,
          ...this.params,
          parkId: this.params.parkId,
          parkName: this.params.parkName,
          reviewedOrganizeId: this.params.reviewedOrganizeId,
          reviewedOrganizeName: this.params.reviewedOrganizeName,
          inspectTime:
            type == 'add'
              ? this.params.inspectTime.format('YYYY-MM-DD HH:mm:ss')
              : this.params.inspectTime,
          inspectName: this.params.inspectName,
          fireSafetyName: this.params.fireSafetyName,
          attachment: imgToStringHandle(this.params.attachment),
          relevancyList,
        };
        console.log(p, 'ppppp----');
        this.addOrUpdate(p);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.add-record-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  .submit-btn {
    margin-left: 12px;
    margin-top: 24px;
  }
  .edit {
    color: #165dff;
    cursor: pointer;
  }
}
.file {
  display: flex;
  .item {
    margin-left: 10px;
  }
  /deep/.ant-upload-list {
    display: flex;
    flex-wrap: wrap;
  }
  /deep/.ant-upload-list-picture .ant-upload-list-item,
  /deep/.ant-upload-list-picture-card .ant-upload-list-item {
    height: auto;
    border: none;
  }
  /deep/.ant-upload-list-item-card-actions.picture {
    right: -8px;
  }
  /deep/.ant-form-horizontal > span {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  /deep/ .ant-upload-list-item-thumbnail {
    display: none;
  }
  /deep/.ant-upload-list-picture .ant-upload-list-item-name,
  /deep/.ant-upload-list-picture-card .ant-upload-list-item-name {
    padding-left: 0;
  }
}
</style>
