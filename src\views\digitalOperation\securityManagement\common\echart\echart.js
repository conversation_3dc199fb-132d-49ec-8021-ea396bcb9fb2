import * as d3 from 'd3';
const getTemplateBase = function (marker, name, value) {
  return `<div style="
      background-color: rgba(255, 255, 255, 0.8);
      height: 32px;
      display: flex;
      justify-content: space-between;
      padding: 8px;
      border-radius: 4px;
      margin: 4px 0;"
  >
  <span style="color: #4e5969; font-size: 12px">${marker}${name}</span>
  <span style="color: #1d2129; font-size: 13px;margin-left:10px">${value}</span></div>`;
};

function getColors(len) {
  const linearGradient = d3
    .scaleLinear()
    .domain([0, 0.23, 0.49, 0.76, 1])
    .range(['#FF3D3D', '#FF9861', '#FFC561', '#FFFC61', '#6BD85D']);

  // 提取19个颜色值
  const numberOfColors = len;
  const extractedColors = Array.from({ length: numberOfColors }, (_, index) => {
    const t = index / (numberOfColors - 1);
    return linearGradient(t);
  });
  return extractedColors;
}

export { getTemplateBase, getColors };
