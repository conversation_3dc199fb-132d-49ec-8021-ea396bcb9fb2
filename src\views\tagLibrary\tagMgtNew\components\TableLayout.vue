<template>
  <div class="table-layout">
    <h2 v-if="props.canBack">
      <a href="javascript:;" @click="router.go(-1)" class="form-back">
        <!-- <img :src="require('@/assets/icons/Vector.png')" alt="back" /> -->
        {{ props.title }}
      </a>
    </h2>
    <!-- <h2 v-else>{{ props.title }}</h2> -->
    <slot>
      <BuseCrud
        ref="crud"
        :tabRadioList="tabRadioList"
        :vxeToolBar="{ custom: true }"
        :tableData="table.data"
        :tablePage="table.page"
        :tableColumn="table.column"
        :tableProps="table.props"
        :filterOptions="{
          config: filter.config,
          showCount: 3,
          ...filter,
        }"
        :loading="loading"
        :modalConfig="props.buseConfig"
        v-bind="$attrs"
        @tabRadioChange="tabRadioChangeHandler"
        @loadData="loadData"
        v-on="$listeners"
      >
        <template #defaultHeader>
          <BtnGroup
            :row="{}"
            :btns="props.tableButtons"
            :layout="{
              type: 'flex',
              align: 'middle',
              justify: 'end',
            }"
            :buttonSpan="0"
          />
        </template>
        <template v-for="(_, name) in $scopedSlots" v-slot:[name]="data">
          <slot :name="name" v-bind="data" />
        </template>
      </BuseCrud>
    </slot>
  </div>
</template>
<script setup>
import {
  reactive,
  defineProps,
  defineEmits,
  onMounted,
  computed,
  ref,
  h,
} from 'vue';
import { initParams } from '@/utils';
import BtnGroup from '@/components/BtnGroup';

const loading = ref(true);

const props = defineProps({
  canBack: {
    type: Boolean,
    default: false,
  },
  title: String,
  tableApiList: {
    type: Array,
    default() {
      return [];
    },
  },
  buseConfig: {
    type: Object,
    default() {
      return {
        addBtn: false,
        menu: false,
      };
    },
  },
  tableColumn: {
    type: Array,
    default() {
      return [];
    },
  },
  filterConfig: {
    type: Array,
    default() {
      return [];
    },
  },
  dataHandler: {
    type: Function,
    default() {
      return (x) => x;
    },
  },
  tableButtons: {
    type: Function,
    default() {
      return () => [];
    },
  },
});

const tabRadioList = computed(() => {
  if (props.tableApiList?.length) {
    return props.tableApiList.map((x, i) => ({
      ...x,
      id: i + 1,
      value: i + 1,
    }));
  }
  return [];
});

const listType = ref(0);

/**
 * filter related configuration
 */
const filter = reactive({
  params: {},
  config: props.filterConfig,
});

filter.params = initParams(filter.config);

/**
 * table related variables
 */
const table = reactive({
  props: {
    custom: true,
    columnConfig: { resizable: true },
    showOverflow: false,
  },
  filter: {},
  page: { total: 0, currentPage: 1, pageSize: 10 },
  column: props.tableColumn,
  data: [],
});

function tabRadioChangeHandler(value) {
  table.page.currentPage = 1;
  listType.value = value - 1;
  loadData();
}

const loadData = async () => {
  loading.value = true;
  const { ...resArg } = filter.params;
  const currentListObj = tabRadioList.value?.[listType.value] || {};
  const apiMethod = currentListObj?.api || function () {};
  table.data = [];
  let params = {
    ...resArg,
    pageNum: table.page.currentPage,
    pageSize: table.page.pageSize,
    ...(currentListObj?.params || {}),
  };
  const [res, err] = await apiMethod(params);
  loading.value = false;
  if (err) return;
  table.page.total = res.data?.total;
  table.data = props.dataHandler(res.data?.list || []);
};

onMounted(async () => {
  // await useDict(props.dict);
  tabRadioChangeHandler(1);
});

const emit = defineEmits(['update:params']);
</script>
<style lang="less" scoped>
.table-layout {
  padding: 20px;

  // background-color: #fff;
  .form-back {
    img {
      position: relative;
      top: -2px;
      width: 12px;
      height: 12px;
      margin-right: 10px;
    }
    color: #000;
  }
}
::v-deep .public-list-page {
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e7edf2;
  border-radius: 12px;
  box-shadow: 0 4px 4px 0 #9eaec11a;
}
</style>
