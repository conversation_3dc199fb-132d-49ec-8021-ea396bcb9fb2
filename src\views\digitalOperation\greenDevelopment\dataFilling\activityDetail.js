const getTableData = (type, number = 0) => {
  let total = 0;
  if (type === '1') {
    total = number * 2.162;
  } else if (type === '2') {
    total = number * 0.5703;
  } else if (type === '3') {
    total = number * 0.5703;
  }
  //天然气--1
  const naturalGas = [
    {
      id: 1,
      name: '天然气.低位发热值',
      number: '389.31',
      unit: 'GJ/万立方米',
    },
    {
      id: 2,
      name: '天然气.单位热值含碳量',
      number: '0.0153',
      unit: 'tC/GJ',
    },
    {
      id: 3,
      name: '天然气.碳氧化率',
      number: number,
      unit: '%',
      edit: true,
    },
    {
      id: 4,
      name: '消耗量',
      number: total.toFixed(4),
      unit: '立方米',
    },
  ];
  //净购入电力---2
  const purchaseElectricity = [
    {
      id: 1,
      name: '电力消耗排放因子',
      number: '0.5703',
      unit: 'kgCO2/kWh',
    },
    {
      id: 2,
      name: '净购入电量',
      number: number,
      unit: '千瓦时',
      edit: true,
    },
    {
      id: 3,
      name: '总计',
      number: total.toFixed(4),
      unit: 'kg',
    },
  ];
  //光伏发电---3
  const photovoltaicPower = [
    {
      id: 1,
      name: '电力消耗排放因子',
      number: '0.5703',
      unit: 'kgCO2/kWh',
    },
    {
      id: 2,
      name: '光伏发电量',
      number: number,
      unit: '千瓦时',
      edit: true,
    },
    {
      id: 3,
      name: '总计',
      number: total.toFixed(4),
      unit: 'kg',
    },
  ];

  switch (type) {
    case '1':
      return naturalGas;
    case '2':
      return purchaseElectricity;
    case '3':
      return photovoltaicPower;
    default:
      return [];
  }
};

export { getTableData };
