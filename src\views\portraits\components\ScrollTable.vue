<template>
  <div :class="[tableTitle ? 'table-container' : '']">
    <h3 v-if="tableTitle">{{ tableTitle }}</h3>
    <vxe-grid
      ref="tableRef"
      headerAlign="left"
      size="mini"
      showOverflow="tooltip"
      align="left"
      :columns="columns"
      :data="tableData"
      :column-config="{ useKey: true }"
      :row-config="{ useKey: true }"
      :height="height"
      border="none"
      @scroll="scrollMethod"
    >
    </vxe-grid>
  </div>
</template>

<script>
export default {
  props: {
    height: {
      type: String,
      default: '150px',
    },
    tableTitle: {
      type: String,
      default: '',
    },
    columns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loadDataCallback: {
      type: Function,
      default: () => async () => [],
    },
    dataCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      tablePage: {
        pageNum: 1,
        limit: 20,
        total: 0,
      },
      // tableData: [],
      loading: false,
      tableInfo: {},
      timer: null,
      scrollTimer: null,
      isBottom: false,
    };
  },
  watch: {
    tableData(newVal) {
      this.initScroll();
    },
  },
  methods: {
    initScroll() {
      // if (this.$attrs?.noScroll) {
      //   this.stopScrolling();
      //   return;
      // }
      const scrollElement = this.$refs.tableRef?.$el?.querySelector?.(
        '.vxe-table--body-wrapper'
      ); // 获取表格的内容部分
      if (this.scrollTimer) return; // 防止多次调用
      this.scrollTimer = setInterval(() => {
        // 检查是否滚动到达底部
        if (this.isBottom) {
          // 如果滚动到达底部，则重置到顶部
          scrollElement.scrollTop = 0;
          this.isBottom = false;
        } else {
          // 否则继续向下滚动
          // if (!bind || !bind.value.loading) {
          scrollElement.scrollTop += 2; // 可以根据需要调整滚动速度
          // }
        }
      }, 100);
    },

    stopScrolling() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer); // 清除定时器
        this.scrollTimer = null; // 重置定时器变量
      }
    },
    async scrollMethod({
      scrollTop,
      scrollHeight,
      $event,
      isY,
      isX,
      isBottom,
      ...arg
    }) {
      if (isBottom) {
        this.isBottom = true;
      }
      // console.log(
      //   '🚀 ~ scrollMethod ~ this.loading:',
      //   this.loading || this.timer
      // );
      if (!isY || isX) return;
      if (this.loading || this.timer) return;

      // console.log(
      //   '🚀 ~ scrollMethod ~ arguments',
      //   scrollTop,
      //   scrollHeight,
      //   arguments
      // );
      if (scrollHeight / scrollTop < 4 || scrollHeight - scrollTop < 100) {
        if (
          this.dataCount > this.tableData.length &&
          this.dataCount >= this.tablePage.pageNum * this.tablePage.limit
        ) {
          // console.log('🚀 ~ this.dataCount:', this.dataCount);
          this.tablePage.pageNum += 1;
          await this.loadData();
        }
      }
    },
    async loadData() {
      this.loading = true;
      this.timer = setTimeout(() => {
        clearTimeout(this.timer);
        this.timer = null;
      }, 2000);
      await this.loadDataCallback({
        ...this.tablePage,
      });
      this.loading = false;
    },
  },
  directives: {
    'auto-scroll': {
      bind(el, binding) {
        const scrollElement = el.querySelector('.vxe-table--body-wrapper'); // 获取表格的内容部分
        let scrollTimeout; // 用于控制滚动的定时器

        const startScrolling = () => {
          if (scrollTimeout) return; // 防止多次调用

          const scroll = (bind) => {
            // 检查是否滚动到达底部
            if (
              scrollElement.scrollTop + scrollElement.clientHeight + 20 >=
              scrollElement.scrollHeight
            ) {
              // 如果滚动到达底部，则重置到顶部
              scrollElement.scrollTop = 0;
            } else {
              // 否则继续向下滚动
              if (!bind || !bind.value.loading) {
                scrollElement.scrollTop += 2; // 可以根据需要调整滚动速度
              }
            }
            // 使用 setTimeout 递归调用滚动函数
            scrollTimeout = setTimeout(scroll, 100); // 调整滚动的间隔时间
          };

          scroll(); // 启动滚动
        };

        const stopScrolling = () => {
          if (scrollTimeout) {
            clearTimeout(scrollTimeout); // 清除定时器
            scrollTimeout = null; // 重置定时器变量
          }
        };

        // 监听绑定的值变化
        el.__scrollingEnabled__ = (bind) => {
          if (bind && bind.value.loading) {
            stopScrolling();
            return;
          }
          // 检查 tableData 的长度
          if (
            (bind && bind.value.tableData.length > 3) || // 如果是更新的 binding
            binding.value.tableData.length > 3 // 初始绑定
          ) {
            startScrolling(bind || binding); // 开始滚动
          } else {
            stopScrolling(); // 停止滚动
          }
        };

        // 初始判断
        el.__scrollingEnabled__(); // 检查初始状态
      },

      update(el, binding) {
        // 监听外部传入的值变化
        el.__scrollingEnabled__(binding); // 根据新 binding 判断是否开始或停止滚动
      },

      unbind(el) {
        // 清理工作
        clearTimeout(el.__scrollTimeout__); // 清空定时器
        delete el.__scrollTimeout__; // 删除定时器变量
      },
    },
  },
};
</script>

<style scoped lang="less">
.table-container {
  width: 100%;
  background: rgba(249, 249, 249, 0.6);
  padding: 16px;
  border-radius: 8px;
  h3 {
    padding-left: 19px;
    font-family: PingFang SC;
    font-size: 12px;
    color: #333333;
    background-image: url('@/assets/images/portraits/1-0041.png');
    background-size: 14px 13px;
    background-repeat: no-repeat;
    background-position: 0 2px;
  }
}
::v-deep .vxe-header--row {
  background-color: #f5f5f5;
  position: relative;
}
</style>
