<template>
  <div :class="[tableTitle ? 'table-container' : '']">
    <h3 v-if="tableTitle">{{ tableTitle }}</h3>
    <div
      class="infinite-scroll-table"
      :style="{ height: height }"
      ref="tableContainer"
      @mouseenter="pauseAutoScroll"
      @mouseleave="resumeAutoScroll"
    >
      <!-- 表头容器（固定，隐藏滚动条） -->
      <div class="table-header-container" ref="headerContainer">
        <table
          class="scroll-table header-table"
          :style="{ width: tableMinWidth }"
        >
          <thead>
            <tr>
              <th
                v-for="(column, index) in calculatedColumns"
                :key="index"
                class="header-cell"
                :style="getColumnStyle(column, 'header')"
              >
                {{ column.title }}
              </th>
            </tr>
          </thead>
        </table>
      </div>

      <!-- 内容容器（禁用纵向滚动，只保留横向滚动） -->
      <div class="table-body-wrapper">
        <div
          class="table-body-container"
          ref="bodyContainer"
          @scroll="handleBodyScroll"
        >
          <!-- 虚拟滚动容器：动态高度 -->
          <div
            class="virtual-scroll-container"
            :style="{ height: virtualScrollHeight + 'px' }"
          >
            <!-- 上方占位区域 -->
            <div
              class="scroll-spacer-top"
              :style="{ height: topSpacerHeight + 'px' }"
            ></div>

            <!-- 可见内容区域 -->
            <table
              class="scroll-table body-table"
              :style="{ width: tableMinWidth }"
            >
              <tbody class="table-body" ref="tableBody">
                <tr
                  v-for="(row, index) in visibleData"
                  :key="getRowKey(row, index)"
                  class="table-row"
                  :class="{
                    'even-row': shouldEnableScroll
                      ? (currentStartIndex + index) % 2 === 0
                      : index % 2 === 0,
                    'odd-row': shouldEnableScroll
                      ? (currentStartIndex + index) % 2 === 1
                      : index % 2 === 1,
                  }"
                >
                  <td
                    v-for="(column, colIndex) in calculatedColumns"
                    :key="colIndex"
                    class="table-cell"
                    :style="getColumnStyle(column)"
                  >
                    <!-- HTML 类型的单元格 -->
                    <div
                      v-if="column.type === 'html'"
                      class="cell-content"
                      v-html="formatCellValue(row, column)"
                    ></div>
                    <!-- 普通文本单元格，带 tooltip -->
                    <a-tooltip
                      v-else
                      :title="formatCellValue(row, column)"
                      placement="top"
                    >
                      <div class="cell-content">
                        {{ formatCellValue(row, column) }}
                      </div>
                    </a-tooltip>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- 下方占位区域 -->
            <div
              class="scroll-spacer-bottom"
              :style="{ height: bottomSpacerHeight + 'px' }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    height: {
      type: String,
      default: '150px',
    },
    tableTitle: {
      type: String,
      default: '',
    },
    columns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    // 滚动配置
    scrollSpeed: {
      type: Number,
      default: 0.5, // 每次滚动的像素数（稍微增加，减少计算频率）
    },
    scrollInterval: {
      type: Number,
      default: 16, // 滚动间隔（毫秒，60fps，稳定帧率）
    },
    visibleRowCount: {
      type: Number,
      default: 10, // 可见行数
    },
    bufferSize: {
      type: Number,
      default: 5, // 缓冲区大小
    },
    rowHeight: {
      type: Number,
      default: 40, // 行高（像素）
    },
    autoScroll: {
      type: Boolean,
      default: true, // 是否自动滚动
    },
  },
  data() {
    return {
      // 滚动相关状态
      scrollTimer: null,
      isScrollPaused: false,
      isUserScrolling: false,
      userScrollTimer: null,

      // 数据管理
      visibleData: [], // 当前可见的数据
      dataBuffer: [], // 数据缓冲区
      currentStartIndex: 0, // 当前显示数据的起始索引

      // 虚拟滚动相关
      scrollCounter: 0,
      virtualScrollOffset: 0, // 虚拟滚动偏移量（像素）

      // 性能优化
      throttleTimer: null,

      // 组件状态
      isDestroyed: false,

      // 容器宽度（用于响应式计算）
      containerWidth: 0,
      resizeObserver: null,
    };
  },
  computed: {
    // 计算总的缓冲区大小
    totalBufferSize() {
      return this.visibleRowCount + this.bufferSize * 2;
    },

    // 计算容器高度
    containerHeight() {
      return parseInt(this.height) || 150;
    },

    // 判断是否需要滚动
    shouldEnableScroll() {
      // 当数据条数大于可见行数时，启用滚动
      // 或者当数据条数大于等于可见行数且启用了自动滚动时，也启用滚动
      return (
        this.tableData &&
        (this.tableData.length > this.visibleRowCount ||
          (this.tableData.length >= this.visibleRowCount && this.autoScroll))
      );
    },

    // 计算表格宽度（占满容器）
    tableMinWidth() {
      return '100%'; // 表格始终占满容器宽度
    },

    // 计算实际的列宽分配
    calculatedColumns() {
      if (!this.containerWidth || this.columns.length === 0) {
        return this.columns;
      }

      const totalWidth = this.containerWidth;
      let fixedWidth = 0; // 固定宽度列的总宽度
      let flexColumns = []; // 需要自适应的列

      // 第一遍：计算固定宽度列的总宽度
      this.columns.forEach((column, index) => {
        if (column.width) {
          fixedWidth += column.width;
        } else {
          flexColumns.push({ ...column, index });
        }
      });

      // 剩余宽度分配给自适应列
      const remainingWidth = totalWidth - fixedWidth;
      const flexColumnCount = flexColumns.length;

      // 计算每个自适应列的宽度
      const result = [...this.columns];
      if (flexColumnCount > 0) {
        flexColumns.forEach((column) => {
          const minWidth = column.minWidth || 80;
          const calculatedWidth = Math.max(
            minWidth,
            Math.floor(remainingWidth / flexColumnCount)
          );
          result[column.index] = {
            ...result[column.index],
            calculatedWidth,
          };
        });
      }

      return result;
    },

    // 虚拟滚动相关计算属性

    // 总的虚拟高度（所有数据的总高度）
    virtualScrollHeight() {
      if (!this.shouldEnableScroll || !this.tableData) {
        return this.containerHeight;
      }
      return this.tableData.length * this.rowHeight;
    },

    // 上方占位区域高度
    topSpacerHeight() {
      if (!this.shouldEnableScroll) {
        return 0;
      }
      return this.currentStartIndex * this.rowHeight + this.virtualScrollOffset;
    },

    // 下方占位区域高度
    bottomSpacerHeight() {
      if (!this.shouldEnableScroll || !this.tableData) {
        return 0;
      }
      const remainingRows =
        this.tableData.length -
        this.currentStartIndex -
        this.visibleData.length;
      return (
        Math.max(0, remainingRows * this.rowHeight) - this.virtualScrollOffset
      );
    },
  },

  watch: {
    tableData: {
      handler(newData, oldData) {
        // 先停止当前滚动
        this.stopAutoScroll();

        if (newData && newData.length > 0) {
          // 检测数据变化类型并调整滚动位置
          this.handleDataChange(newData, oldData);

          // 延迟启动滚动，确保DOM更新完成
          this.$nextTick(() => {
            if (this.shouldEnableScroll && this.autoScroll) {
              this.startAutoScroll();
            }
          });
        } else {
          // 清空数据
          this.visibleData = [];
          this.dataBuffer = [];
        }
      },
      immediate: true,
    },

    autoScroll(newVal) {
      if (newVal && this.shouldEnableScroll) {
        this.startAutoScroll();
      } else {
        this.stopAutoScroll();
      }
    },

    // 监听是否需要滚动的变化
    shouldEnableScroll(newVal) {
      if (newVal && this.autoScroll) {
        this.$nextTick(() => {
          this.startAutoScroll();
        });
      } else {
        this.stopAutoScroll();
      }
    },
  },

  mounted() {
    this.$nextTick(() => {
      this.initializeData();
      this.initResizeObserver();
      // 只有在需要滚动且启用自动滚动时才开始
      if (this.autoScroll && this.shouldEnableScroll) {
        this.startAutoScroll();
      }
    });
  },

  beforeDestroy() {
    this.isDestroyed = true;
    this.stopAutoScroll();
    this.clearAllTimers();
    this.destroyResizeObserver();
  },
  methods: {
    // 处理数据变化 - 简化版本，基于索引滚动
    handleDataChange(newData, oldData) {
      const oldLength = oldData ? oldData.length : 0;
      const newLength = newData ? newData.length : 0;

      // 保存当前的滚动状态
      const oldCurrentIndex = this.currentStartIndex || 0;

      // 重新初始化数据
      this.initializeData();

      // 如果有旧数据，尝试保持相对位置
      if (oldLength > 0 && newLength > 0) {
        // 确保当前索引在有效范围内
        if (oldCurrentIndex < newLength) {
          this.currentStartIndex = oldCurrentIndex;
        } else {
          // 如果超出范围，调整到末尾
          this.currentStartIndex = Math.max(
            0,
            newLength - this.visibleRowCount
          );
        }

        // 重置虚拟滚动偏移
        this.virtualScrollOffset = 0;

        // 更新可见数据
        this.updateVisibleData();
      }
    },

    // 初始化数据缓冲区
    initializeData() {
      if (!this.tableData || this.tableData.length === 0) {
        this.visibleData = [];
        this.dataBuffer = [];
        return;
      }

      // 创建循环数据缓冲区
      this.createDataBuffer();

      // 初始化可见数据
      this.updateVisibleData();

      // 只在首次初始化时重置滚动状态
      if (this.currentStartIndex === undefined) {
        this.scrollCounter = 0;
        this.virtualScrollOffset = 0;
        this.currentStartIndex = 0;
      }
    },

    // 创建数据缓冲区（循环使用原始数据）
    createDataBuffer() {
      const sourceData = this.tableData;

      if (!this.shouldEnableScroll) {
        // 如果不需要滚动，直接使用原始数据
        this.dataBuffer = sourceData.map((item, index) => ({
          ...item,
          _originalIndex: index,
          _bufferIndex: index,
        }));
        return;
      }

      // 只有在需要滚动时才创建循环缓冲区
      const bufferSize = Math.max(
        this.totalBufferSize * 2,
        sourceData.length * 2
      );

      this.dataBuffer = [];
      for (let i = 0; i < bufferSize; i++) {
        const sourceIndex = i % sourceData.length;
        this.dataBuffer.push({
          ...sourceData[sourceIndex],
          _originalIndex: sourceIndex,
          _bufferIndex: i,
        });
      }
    },

    // 更新可见数据
    updateVisibleData() {
      if (!this.shouldEnableScroll) {
        // 如果不需要滚动，直接显示原始数据
        this.visibleData = [...this.tableData];
        return;
      }

      const startIndex = this.currentStartIndex;
      // 只有在需要滚动时才增加缓冲行
      const endIndex = startIndex + this.visibleRowCount + 1;

      this.visibleData = this.dataBuffer.slice(startIndex, endIndex);

      // 如果数据不足，从头开始补充
      if (this.visibleData.length < this.visibleRowCount + 1) {
        const remaining = this.visibleRowCount + 1 - this.visibleData.length;
        const additionalData = this.dataBuffer.slice(0, remaining);
        this.visibleData = [...this.visibleData, ...additionalData];
      }
    },
    // 开始自动滚动
    startAutoScroll() {
      if (
        !this.autoScroll ||
        this.isScrollPaused ||
        this.isUserScrolling ||
        this.isDestroyed
      ) {
        return;
      }

      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }

      this.scrollTimer = setInterval(() => {
        if (this.isDestroyed || this.isScrollPaused || this.isUserScrolling) {
          return;
        }

        this.performAutoScroll();
      }, this.scrollInterval);
    },

    // 停止自动滚动
    stopAutoScroll() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
    },

    // 执行自动滚动 - 使用虚拟滚动实现平滑效果
    performAutoScroll() {
      if (!this.dataBuffer.length) {
        return;
      }

      // 更新虚拟滚动偏移量
      this.virtualScrollOffset -= this.scrollSpeed;

      // 当偏移量达到一行高度时，更新数据索引
      if (Math.abs(this.virtualScrollOffset) >= this.rowHeight) {
        // 计算滚动了多少行
        const scrolledRows = Math.floor(
          Math.abs(this.virtualScrollOffset) / this.rowHeight
        );

        // 更新数据索引
        this.currentStartIndex =
          (this.currentStartIndex + scrolledRows) % this.dataBuffer.length;

        // 重置偏移量，保持剩余的滚动距离
        this.virtualScrollOffset =
          this.virtualScrollOffset + scrolledRows * this.rowHeight;

        // 更新可见数据
        this.updateVisibleData();

        // 扩展数据缓冲区（如果需要）
        this.expandBufferIfNeeded();
      }
    },

    // 扩展缓冲区（循环添加数据）
    expandBufferIfNeeded() {
      const currentBufferSize = this.dataBuffer.length;
      const requiredSize =
        this.currentStartIndex + this.visibleRowCount + this.bufferSize;

      if (requiredSize > currentBufferSize) {
        const additionalSize = requiredSize - currentBufferSize;
        for (let i = 0; i < additionalSize; i++) {
          const sourceIndex = (currentBufferSize + i) % this.tableData.length;
          this.dataBuffer.push({
            ...this.tableData[sourceIndex],
            _originalIndex: sourceIndex,
            _bufferIndex: currentBufferSize + i,
          });
        }
      }
    },

    // 用户交互方法
    pauseAutoScroll() {
      this.isScrollPaused = true;
    },

    resumeAutoScroll() {
      this.isScrollPaused = false;
      if (this.autoScroll && !this.isUserScrolling && this.shouldEnableScroll) {
        this.startAutoScroll();
      }
    },

    // 处理内容区域滚动（只处理横向滚动同步）
    handleBodyScroll(e) {
      const scrollLeft = e.target.scrollLeft;

      // 同步表头横向滚动位置
      if (this.$refs.headerContainer) {
        this.$refs.headerContainer.scrollLeft = scrollLeft;
      }

      // 禁用纵向滚动，重置 scrollTop
      if (e.target.scrollTop !== 0) {
        e.target.scrollTop = 0;
      }
    },

    // 处理用户手动滚动
    handleUserScroll() {
      if (this.throttleTimer) return;

      this.throttleTimer = setTimeout(() => {
        this.throttleTimer = null;
      }, 16); // 60fps

      // 标记用户正在滚动
      this.isUserScrolling = true;

      // 清除用户滚动定时器
      if (this.userScrollTimer) {
        clearTimeout(this.userScrollTimer);
      }

      // 设置延迟恢复自动滚动
      this.userScrollTimer = setTimeout(() => {
        this.isUserScrolling = false;
        if (
          this.autoScroll &&
          !this.isScrollPaused &&
          this.shouldEnableScroll
        ) {
          this.startAutoScroll();
        }
      }, 2000); // 2秒后恢复自动滚动
    },

    // 获取列样式（类似 a-table 的列宽控制）
    getColumnStyle(column, type = 'cell') {
      const style = {};

      // 处理宽度设置 - 优先使用计算后的宽度
      if (column.calculatedWidth) {
        // 使用计算后的宽度（自适应列）
        style.width = `${column.calculatedWidth}px`;
      } else if (column.width) {
        // 固定宽度列
        style.width = `${column.width}px`;
      } else if (column.minWidth) {
        // 最小宽度列
        style.width = `${column.minWidth}px`;
      } else {
        // 默认宽度
        style.width = '80px';
      }

      // 处理文本对齐
      if (type === 'header') {
        // 表头对齐：headerAlign 优先，否则使用 align，最后默认 left
        style.textAlign = column.headerAlign || column.align || 'left';
      } else {
        // 数据单元格对齐：使用 align，默认 left
        style.textAlign = column.align || 'left';
      }

      return style;
    },

    // 格式化单元格值
    formatCellValue(row, column) {
      if (column.formatter && typeof column.formatter === 'function') {
        return column.formatter({ cellValue: row[column.field], row });
      }
      return row[column.field] || '';
    },

    // 获取行的唯一键
    getRowKey(row, index) {
      return row._bufferIndex !== undefined
        ? `row_${row._bufferIndex}`
        : `row_${index}`;
    },

    // 初始化容器宽度监听
    initResizeObserver() {
      if (typeof ResizeObserver !== 'undefined' && this.$refs.tableContainer) {
        this.resizeObserver = new ResizeObserver((entries) => {
          for (let entry of entries) {
            this.containerWidth = entry.contentRect.width;
          }
        });
        this.resizeObserver.observe(this.$refs.tableContainer);

        // 初始化容器宽度
        this.containerWidth = this.$refs.tableContainer.offsetWidth;
      }
    },

    // 销毁容器宽度监听
    destroyResizeObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }
    },

    // 清理所有定时器
    clearAllTimers() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }

      if (this.userScrollTimer) {
        clearTimeout(this.userScrollTimer);
        this.userScrollTimer = null;
      }

      if (this.throttleTimer) {
        clearTimeout(this.throttleTimer);
        this.throttleTimer = null;
      }
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .top-title {
  font-family: YouSheBiaoTiHei;
  font-size: 14px;
  color: #666666;
  border-radius: 0px 16px 16px 0px;
  width: 48px;
  height: 18px;
  text-align: center;
  line-height: 18px;
  opacity: 1;
  &-1 {
    color: #ff6f3f;
    background: linear-gradient(
      270deg,
      rgba(255, 123, 0, 0.2) 0%,
      rgba(255, 123, 0, 0) 100%
    );
  }
  &-2 {
    color: #ffba00;
    background: linear-gradient(
      270deg,
      rgba(255, 178, 0, 0.2) 0%,
      rgba(255, 161, 0, 0) 100%
    );
  }
  &-3 {
    color: #00cd52;
    background: linear-gradient(
      270deg,
      rgba(63, 211, 122, 0.2) 0%,
      rgba(81, 231, 153, 0) 100%
    );
  }
}
.table-container {
  width: 100%;
  background: rgba(249, 249, 249, 0.6);
  padding: 16px;
  border-radius: 8px;

  h3 {
    padding-left: 19px;
    font-family: PingFang SC;
    font-size: 12px;
    color: #333333;
    background-image: url('@/assets/images/portraits/1-0041.png');
    background-size: 14px 13px;
    background-repeat: no-repeat;
    background-position: 0 2px;
    margin-bottom: 12px;
  }
}

.infinite-scroll-table {
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden; /* 隐藏外层溢出 */
  background: #fff;
  position: relative;
  height: 100%;
}

/* 表头容器（固定，隐藏滚动条） */
.table-header-container {
  overflow: hidden; /* 隐藏表头自身滚动条 */
  height: 40px; /* 固定表头高度 */
  border-bottom: 1px solid #e8e8e8;
}

/* 内容包装器 */
.table-body-wrapper {
  height: calc(100% - 40px); /* 减去表头高度 */
  overflow: hidden; /* 隐藏所有滚动条 */
  position: relative;
}

/* 内容容器（只允许横向滚动） */
.table-body-container {
  width: 100%;
  height: 100%;
  overflow-x: auto; /* 横向滚动 */
  overflow-y: hidden; /* 完全禁用纵向滚动 */

  /* 隐藏纵向滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  -webkit-overflow-scrolling: touch; /* iOS */
}

/* 虚拟滚动容器 */
.virtual-scroll-container {
  width: 100%;
  position: relative;
}

/* 占位区域 */
.scroll-spacer-top,
.scroll-spacer-bottom {
  width: 100%;
  pointer-events: none; /* 不响应鼠标事件 */
}

/* 统一两个 table 的样式 */
.scroll-table {
  width: auto; /* 让表格根据内容宽度自适应 */
  border-collapse: collapse;
  table-layout: fixed; /* 固定列宽算法 */
}

.header-table {
  margin-bottom: 0; /* 移除表头表格的下边距 */
}

.body-table {
  margin-top: 0; /* 移除内容表格的上边距 */
}

/* 移除原有的 table-header 样式，因为现在使用分离的表头 */

.header-cell {
  padding: 0 8px; /* 与表格单元格保持一致 */
  border-right: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 600;
  font-size: 12px;
  color: #333;
  background: #f5f5f5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 40px !important; /* 强制表头高度为40px */
  max-height: 40px; /* 防止被拉伸 */
  min-height: 40px; /* 防止被压缩 */
  line-height: 40px; /* 文本垂直居中 */

  &:last-child {
    border-right: none;
  }
}

.table-body {
  position: relative;
  /* 移除过渡效果，让 translateY 直接控制滚动 */
  /* transition: all 0.3s ease-out; */
  /* 确保表体不会被拉伸 */
  height: auto;
}

.table-row {
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  height: 40px !important; /* 强制固定行高为40px */
  max-height: 40px; /* 防止被拉伸 */
  min-height: 40px; /* 防止被压缩 */
  line-height: 40px; /* 确保文本垂直居中 */

  &:hover {
    background-color: #f5f5f5;
  }

  &.even-row {
    background-color: #fafafa;
  }

  &.odd-row {
    background-color: #fff;
  }
}

.table-cell {
  padding: 0 8px; /* 移除上下padding，让行高控制高度 */
  border-right: 1px solid #f0f0f0;
  font-size: 12px;
  color: #666;
  vertical-align: middle;
  height: 40px !important; /* 强制单元格高度为40px */
  max-height: 40px; /* 防止被拉伸 */
  min-height: 40px; /* 防止被压缩 */
  line-height: 40px; /* 文本垂直居中 */
  position: relative; /* 为内容定位 */

  &:last-child {
    border-right: none;
  }

  /* 单元格内容容器 */
  .cell-content {
    width: 100%;
    height: 100%;
    line-height: 40px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }

  /* 支持HTML内容的样式 */
  :deep(.top-title) {
    display: inline-block;
    line-height: 18px;
    margin: 11px 0; /* 垂直居中：(40-18)/2 = 11px */
  }
}

// 滚动条样式 - 应用到内容容器
.table-body-container::-webkit-scrollbar {
  width: 0px; /* 完全隐藏纵向滚动条 */
  height: 6px; /* 保留横向滚动条 */
}

// 只为横向滚动条设置样式
.table-body-container::-webkit-scrollbar:horizontal {
  height: 6px;
}

.table-body-container::-webkit-scrollbar-track:horizontal {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-body-container::-webkit-scrollbar-thumb:horizontal {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// 确保纵向滚动条完全隐藏
.table-body-container::-webkit-scrollbar:vertical {
  width: 0px;
  display: none;
}

// 响应式设计
@media (max-width: 768px) {
  .table-cell,
  .header-cell {
    padding: 0 6px; /* 保持无上下padding */
    font-size: 11px;
    height: 36px; /* 移动端稍微减小高度 */
    line-height: 36px;
  }

  .table-row {
    height: 36px; /* 移动端行高 */
    line-height: 36px;
  }
}

// 简单的淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 加载状态
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

// 空数据状态
.empty-data {
  padding: 40px 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>
