<template>
  <page-layout>
    <a-row
      type="flex"
      justify="space-between"
      class="page-wrapper-row-flex"
      :gutter="[16, 0]"
    >
      <a-col :span="3">
        <a-card
          title="字典表类型"
          :bordered="false"
          style="height: 100%"
          :bodyStyle="{
            'padding-top': '12px',
            'padding-left': '0',

            overflow: 'auto',
          }"
        >
          <a-tree
            ref="tree"
            default-expand-all
            :treeData="dictTypeOptions"
            :replaceFields="replaceFields"
            :selectedKeys="selectedKeys"
            switcherIcon="switcherIcon"
            @select="handleNodeClick"
          >
          </a-tree>
        </a-card>
      </a-col>
      <a-col :span="21">
        <PageWrapper
          style="margin: 0"
          title="字典列表"
          createText=""
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="tableColumns"
          :tableData="tableData"
          @loadData="loadData"
          @handleCreate="handleAdd"
        >
          <template #defaultHeader>
            <a-button
              v-hasPermi="['system:dict:addType']"
              style="margin-right: 8px"
              type="primary"
              :loading="loading"
              @click="handleAdd()"
            >
              添加字典
            </a-button>
          </template>
          <template #dictCode="{ row }">
            <span @click="handleDictData(row)" class="dict-code-click">
              {{ row.dictCode }}
            </span>
          </template>
          <template #operation="{ row }">
            <a-button
              v-hasPermi="['system:dict:editType']"
              icon="edit"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleUpdate(row)"
            >
              编辑
            </a-button>
            <a-button
              v-if="row.userId !== 1"
              v-hasPermi="['system:dict:deleteType']"
              icon="delete"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleDelete(row)"
            >
              删除
            </a-button>
          </template>
        </PageWrapper>
      </a-col>
    </a-row>
    <!-- 编辑字典表 -->
    <EditModal
      :visible.sync="editModalVisible"
      :dictId="dictId"
      :dictType="selectedKeys[0]"
      @ok="loadData"
    />
    <!-- 字典表详情 -->
    <DetailModal
      :visible.sync="detailModalVisible"
      :dictCode="dictCode"
      :dictId="dictId"
    />
  </page-layout>
</template>
<script>
import { listType, delType } from '@/api/system/dict/type';
import EditModal from './EditModal.vue';
import DetailModal from './DetailModal.vue';
import { Modal } from 'ant-design-vue';
import { filterOptions, tableColumns, dictTypeOptions } from './constant';
export default {
  components: {
    EditModal,
    DetailModal,
  },
  data() {
    return {
      dictTypeOptions,
      selectedKeys: [dictTypeOptions[0].value],
      replaceFields: {
        children: 'children',
        title: 'label',
        key: 'value',
      },
      // 表格表单搜索数据
      loading: false,
      filterOptions,
      tableColumns,
      tableData: [],
      // 分页器配置
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      // 点击操作数据
      dictCode: '',
      dictId: '',
      // 弹窗展示标识
      editModalVisible: false,
      detailModalVisible: false,
    };
  },
  created() {
    this.loadData();
  },
  methods: {
    /**
     * 列表查询
     */
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [result, error] = await listType({
        ...params,
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
        dictType:
          (this.selectedKeys &&
            Array.isArray(this.selectedKeys) &&
            this.selectedKeys.length > 0 &&
            this.selectedKeys[0]) ||
          '',
      });
      this.loading = false;
      if (error) return;
      this.tableData = result.data;
      this.tablePage.total = result.count;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.dictId = '';
      this.editModalVisible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.dictId = row.dictId;
      this.editModalVisible = true;
    },
    handleDictData(record) {
      this.dictCode = record.dictCode;
      this.dictId = record.dictId;
      this.detailModalVisible = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.loading) return;
      Modal.confirm({
        title: '警告',
        content: '是否确认删除字典名称"' + row.dictName + '"的数据项?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.loading = true;
          const [, error] = await delType(row.dictId);
          this.loading = false;
          if (error) return;
          this.$message.success('删除成功');
          this.loadData();
        },
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      if (data.length === 0) return;
      this.selectedKeys = data;
      this.loadData();
    },
  },
};
</script>
<style lang="less" scoped>
.page-wrapper-row-flex {
  display: flex;
  align-items: stretch;
}
.dict-code-click {
  cursor: pointer;
  color: #1890ff;
}
/deep/ .ant-form-item {
  margin-bottom: 0;
}
</style>
