<template>
  <div class="login-container">
    <img :style="{ height: '80px' }" :src="'@/assets/images/logo/logo.png'" />
    <div class="login-title">重置密码</div>
    <a-form-model
      style="width: 360px"
      ref="resetPasswordForm"
      :model="form"
      :rules="rules"
    >
      <a-form-model-item prop="userName">
        <a-input
          size="large"
          v-model.trim="form.userName"
          :max-length="30"
          placeholder="请输入账户名"
          @pressEnter="onSubmit"
          autocomplete="autocomplete"
        >
          <a-icon slot="prefix" type="user" />
        </a-input>
      </a-form-model-item>
      <a-form-model-item prop="originalPassword">
        <a-input-password
          size="large"
          v-model.trim="form.originalPassword"
          placeholder="请输入原始密码"
          autocomplete="autocomplete"
          type="password"
        >
          <a-icon slot="prefix" type="lock" />
        </a-input-password>
      </a-form-model-item>
      <a-form-model-item prop="password">
        <a-input-password
          size="large"
          v-model.trim="form.password"
          placeholder="请输入新密码"
          autocomplete="autocomplete"
          type="password"
          @blur="
            () => {
              if (form.passwordSecond) {
                $refs.resetPasswordForm.validateField('passwordSecond');
              }
            }
          "
        >
          <a-icon slot="prefix" type="lock" />
        </a-input-password>
      </a-form-model-item>
      <a-form-model-item prop="passwordSecond">
        <a-input-password
          size="large"
          v-model.trim="form.passwordSecond"
          placeholder="请再次输入新密码"
          autocomplete="autocomplete"
          type="password"
          @pressEnter="onSubmit"
        >
          <a-icon slot="prefix" type="lock" />
        </a-input-password>
      </a-form-model-item>
      <div class="reset-password" @click="goToLogin">去登录</div>
      <a-row type="flex">
        <a-button
          class="bd-form-btn"
          :loading="loading"
          size="large"
          type="primary"
          @click="onSubmit"
        >
          重置密码
        </a-button>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
import { rsaCode } from '@/utils/common/auth';
import { changeFirstPassword } from '@/api/system/base.js';
// 密码格式验证正则
export const PASSWORD_REGEX =
  /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,}/;
export const PASSWORD_REGEX_DESC =
  '必须是包含大小写字母、数字和特殊符号的8位以上组合';

export default {
  name: 'ChangePassword',
  components: {},
  computed: {
    platformLogo() {
      return require('@/assets/images/logo/userLogo.png');
      // `${window.location.origin}${window.location.pathname}logo-platform.png`
    },
    copyright() {
      return process.env.VUE_APP_COPY_RIGHT;
    },
  },
  data() {
    return {
      loading: false,
      form: {
        userName: '', //用户名称
        password: '', //新密码
        originalPassword: '', //原始密码
        passwordSecond: '', //重复密码
      },
      rules: {
        userName: [
          { required: true, message: '请输入账户名', trigger: 'change' },
          { max: 30, message: '账户名不得超过30个字符' },
        ],
        originalPassword: [
          { required: true, message: '请输入原始密码', trigger: 'change' },
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            whitespace: true,
          },
          {
            validator: (rule, value, callback) => {
              if (PASSWORD_REGEX.test(value)) {
                if (value && value === this.form.originalPassword) {
                  callback(new Error('新密码不能与旧密码一致'));
                } else {
                  callback();
                }
              } else {
                callback(new Error(PASSWORD_REGEX_DESC));
              }
            },
            trigger: 'blur',
          },
        ],
        passwordSecond: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'change',
            whitespace: true,
          },
          {
            validator: (rule, value, callback) => {
              if (value && value !== this.form.password) {
                callback(new Error('两次密码不同!'));
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
      },
    };
  },
  methods: {
    goToLogin() {
      this.$router.replace({ name: 'LOGIN' });
    },
    onSubmit() {
      this.$refs.resetPasswordForm.validate(async (valid) => {
        if (valid) {
          if (this.loading) return;
          this.loading = true;
          const userName = this.form.userName;
          const password = rsaCode(this.form.password);
          const originalPassword = rsaCode(this.form.originalPassword);
          const [, error] = await changeFirstPassword({
            userName,
            password,
            originalPassword,
          });
          this.loading = false;
          if (error) return;
          this.$message.success('修改成功！');
          this.$router.replace({ name: 'LOGIN' });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.login-container {
  background-color: @g-gray-bright;
  background-image: url('https://gw.alipayobjects.com/zos/rmsportal/TVYTbAXWheQpRcWDaDMu.svg');
  background-repeat: no-repeat;
  background-position-x: center;
  background-position-y: 110px;
  background-size: 100%;

  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .title {
    margin-left: 52px;
    height: 44px;
    line-height: 44px;
    font-size: 34px;
    color: @g-title;
    font-family: 'Myriad Pro', 'Helvetica Neue', Arial, Helvetica, sans-serif;
    font-weight: 600;
    position: relative;
    &::before {
      position: absolute;
      content: '';
      background: url('../../assets/images/logo.png') no-repeat;
      background-size: 44px 44px;
      width: 44px;
      height: 44px;
      left: -52px;
    }
  }

  .desc {
    font-size: 14px;
    color: @g-desc;
    margin-top: 16px;
  }

  .login-title {
    margin: 24px 0;
    height: 18px;
    line-height: 18px;
    font-size: 18px;
    font-weight: 600;
    color: @g-gray-dark;
  }

  .copyright {
    position: absolute;
    bottom: 24px;
    color: @g-desc;
    font-size: 14px;
    .icon {
      margin: 0 4px;
    }
  }
}
.common-layout {
  .top {
    text-align: center;
    .header {
      height: 44px;
      line-height: 44px;
      a {
        text-decoration: none;
      }
      .logo {
        height: 44px;
        vertical-align: top;
        margin-right: 16px;
      }
      .title {
        font-size: 33px;
        color: @title-color;
        font-family: 'Myriad Pro', 'Helvetica Neue', Arial, Helvetica,
          sans-serif;
        font-weight: 600;
        position: relative;
        top: 2px;
      }
    }
    .desc {
      font-size: 14px;
      color: @text-color-second;
      margin: 16px 0;
    }
  }
  .login {
    width: 368px;
    margin: 0 auto;
    @media screen and (max-width: 576px) {
      width: 95%;
    }
    @media screen and (max-width: 320px) {
      .captcha-button {
        font-size: 14px;
      }
    }
    .title {
      padding: 24px 0;
      display: flex;
      justify-content: center;
      align-content: center;
      span {
        height: 18px;
        line-height: 18px;
        color: @gray-7;
        font-size: 18px;
        font-weight: 600;
      }
    }
    .icon {
      font-size: 24px;
      color: @text-color-second;
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }
    }
  }
}
.bd-code-box {
  width: 100px;
  height: 40px;
  margin-left: 16px;
  .bd-code-img {
    width: 100px;
    height: 40px;
  }
}
.form-item-code {
  display: flex;
}
.reset-password {
  text-align: right;
  cursor: pointer;
  margin-bottom: 12px;
  color: #1677ff;
}
/deep/.ant-form-extra {
  margin-top: 1px;
}
/deep/.ant-upload.ant-upload-select-picture-card {
  margin-bottom: 0;
}
/deep/ .ant-form-item {
  margin-bottom: 12px;
  // max-width: 426px;
}

/deep/ .ant-form-item-label,
/deep/ .ant-col-24.ant-form-item-label {
  // z-index: 1;
  // position: relative;
  user-select: text;
  font-size: 14px;
  line-height: 22px;
  font-weight: normal;
  padding-bottom: 8px;

  .tip {
    font-size: 12px;
    color: #bfbfbf;
  }
}
/deep/.ant-form-explain {
  margin-top: 3px;
}
/deep/ .ant-form-item-control {
  line-height: 32px;
}
.bd-form-btn {
  width: 100%;
}
</style>
