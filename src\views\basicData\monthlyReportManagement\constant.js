import moment from 'moment';
// 表格列配置
export const defaultTableColumn = () => [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    field: 'rateTime',
    title: '日期',
    width: 110,
    // formatter: ({ cellValue }) => {
    //   return moment(cellValue).format('YYYY-MM');
    // },
  },
  {
    field: 'scc',
    title: '统一社会信用代码',
    minWidth: 150,
  },
  {
    field: 'enterpriseName',
    title: '企业名称',
    width: 250,
  },
  {
    field: 'incomeCurrPer',
    title: '营收（本年累计）',
    width: 200,
  },
  {
    field: 'incomeCorrPer',
    title: '营收（上年同期累计）',
    width: 200,
  },
  {
    field: 'growRate',
    title: '增长率',
    width: 120,
    formatter: ({ cellValue }) => {
      return cellValue || cellValue == 0 ? cellValue + '%' : '-';
    },
  },
  {
    field: 'depositAmount',
    title: '净入库金额',
    width: 200,
  },
  {
    field: 'tax',
    title: '税收',
    width: 120,
  },
  {
    field: 'updateBy',
    title: '更新人',
    width: 120,
  },
  {
    field: 'updateTime',
    title: '更新时间',
    width: 180,
  },
  {
    title: '操作',
    slots: { default: 'operate' },
    width: 160,
    fixed: 'right',
  },
];
// 筛选器配置
export const defaultFilterConfig = () => [
  {
    field: 'rateTime',
    title: '日期',
    element: 'slot',
    slotName: 'year',
    itemProps: {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 20,
      },
    },
  },
  {
    field: 'enterpriseName',
    title: '企业名称',
    element: 'a-input',
    // slotName: 'enterSlot',
  },
  {
    field: 'scc',
    title: '统一社会信用代码',
    element: 'a-input',
  },
];

export const initFormValue = () => {
  return {
    rateTime: undefined,
    scc: '',
    enterprise: {},
    enterpriseName: '',
    enterpriseId: '',
    incomeCurrPer: '',
    incomeCorrPer: '',
    depositAmount: '',
    tax: '',
  };
};
