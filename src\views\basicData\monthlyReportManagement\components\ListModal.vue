<template>
  <a-modal
    width="600px"
    :title="
      modelTitle === 'add' ? '新增' : modelTitle === 'see' ? '详情' : '编辑'
    "
    :visible="visible"
    :destroyOnClose="true"
    cancelText="取消"
    @ok="onClickSubmit"
    @cancel="handleCancel()"
  >
    <template slot="footer">
      <a-button @click="handleCancel()">取消</a-button>
      <a-button
        v-if="modelTitle !== 'see'"
        type="primary"
        @click="onClickSubmit"
        :loading="loading"
        >确认</a-button
      >
    </template>
    <a-spin tip="加载中..." :spinning="loading">
      <DynamicForm
        ref="ruleForm"
        :config="formConfig"
        :params="formValue"
        :preview="preview"
        :labelCol="{ span: 8 }"
        :wrapperCol="{ span: 16 }"
      >
      </DynamicForm>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from 'moment';
import { initFormValue } from '../constant';
import { saveTaxable, editTaxable } from '@/api/basicData';
import { institutionsMixin } from '../../mixins/institutionsMixin';
import FuzzySelect from '@/components/FuzzySelect/index.vue';

export default {
  props: ['visible', 'detail', 'preview', 'isLook', 'modelTitle'],
  mixins: [institutionsMixin],
  components: {},
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (!this.detail) return;
          this.formValue.enterpriseName = this.detail.enterpriseName;
          this.formValue.enterpriseId = this.detail.enterpriseId;
          this.formValue = {
            ...initFormValue(),
            ...this.detail,
            ...{ enterprise: this.formValue.enterprise },
            rateTime: moment(this.detail.rateTime).format('YYYY-MM'),
          };
        } else {
          this.formValue = initFormValue();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      formValue: initFormValue(),
      formConfig: [
        {
          field: 'rateTime',
          title: '日期',
          element: 'a-month-picker',
          props: {
            placeholder: '请选择日期',
          },
          rules: [{ required: true, message: '请选择日期' }],
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          element: (h, item, params) => {
            return (
              <FuzzySelect
                value={params?.enterpriseName}
                onChangeSelect={(val) => {
                  if (val) {
                    this.formValue = {
                      ...this.formValue,
                      enterpriseName: val.name,
                      enterpriseId: val.unifiedCreditCode,
                      scc: val.unifiedCreditCode,
                    };
                  } else {
                    this.formValue = {
                      ...this.formValue,
                      enterpriseName: '',
                      enterpriseId: '',
                      scc: '',
                    };
                  }
                }}
                disabled={this.modelTitle == 'see'}
              />
            );
          },
          rules: [{ required: true, message: '请输入' }],
        },
        {
          field: 'scc',
          title: '统一社会信用代码',
          element: 'a-input',
          props: {
            placeholder: '请输入统一社会信用代码',
            disabled: true,
          },
          // rules: [
          //   { required: true, validator: this.checkCode, trigger: 'change' },
          // ],
        },
        {
          field: 'incomeCurrPer',
          title: '营收（本年累计）',
          // element: 'a-input-number',
          element: (h, item, params) => {
            return (
              <a-input-group>
                <a-input-number
                  value={params.incomeCurrPer}
                  placeholder="请输入营收（本年累计）"
                  style="width:calc(100% - 30px)"
                  step={0.01}
                  min={-999999999999}
                  max={999999999999}
                  onChange={(val) => {
                    params.incomeCurrPer = val;
                  }}
                ></a-input-number>
                <span class="input-unit">元</span>
              </a-input-group>
            );
          },
          rules: [
            { required: true, message: '请输入营收（本年累计）' },
            {
              pattern: /^-?(0|[1-9]\d*)(\.\d{1,2})?$|^-?0\.\d{1,2}$/,
              trigger: 'change',
              message: '请输数字，最多保留两位小数。',
            },
          ],
        },
        {
          field: 'incomeCorrPer',
          title: '营收（上年同期累计）',
          element: (h, item, params) => {
            return (
              <a-input-group>
                <a-input-number
                  value={params.incomeCorrPer}
                  placeholder="请输入营收（上年同期累计）"
                  style="width:calc(100% - 30px)"
                  step={0.11}
                  min={-999999999999}
                  max={999999999999}
                  onChange={(val) => {
                    params.incomeCorrPer = val;
                  }}
                ></a-input-number>
                <span class="input-unit">元</span>
              </a-input-group>
            );
          },
          rules: [
            { required: true, message: '请输入营收（上年同期累计）' },
            {
              pattern: /^-?(0|[1-9]\d*)(\.\d{1,2})?$|^-?0\.\d{1,2}$/,
              trigger: 'change',
              message: '请输数字，最多保留两位小数。',
            },
          ],
        },
        {
          field: 'depositAmount',
          title: '净入库金额',
          element: (h, item, params) => {
            return (
              <a-input-group>
                <a-input-number
                  value={params.depositAmount}
                  placeholder="请输入净入库金额"
                  style="width:calc(100% - 30px)"
                  step={0.11}
                  min={-999999999999}
                  max={999999999999}
                  onChange={(val) => {
                    params.depositAmount = val;
                  }}
                ></a-input-number>
                <span class="input-unit">元</span>
              </a-input-group>
            );
          },
          rules: [
            {
              pattern: /^-?(0|[1-9]\d*)(\.\d{1,2})?$|^-?0\.\d{1,2}$/,
              trigger: 'change',
              message: '请输数字，最多保留两位小数。',
            },
          ],
        },
        {
          field: 'tax',
          title: '税收',
          element: (h, item, params) => {
            return (
              <a-input-group>
                <a-input-number
                  value={params.tax}
                  placeholder="请输入税收"
                  style="width:calc(100% - 30px)"
                  step={0.11}
                  min={-999999999999}
                  max={999999999999}
                  onChange={(val) => {
                    params.tax = val;
                  }}
                ></a-input-number>
                <span class="input-unit">元</span>
              </a-input-group>
            );
          },
          rules: [
            { required: true, message: '请输入税收' },
            {
              pattern: /^-?(0|[1-9]\d*)(\.\d{1,2})?$|^-?0\.\d{1,2}$/,
              trigger: 'change',
              message: '请输数字，最多保留两位小数。',
            },
          ],
        },
      ],
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
    };
  },
  methods: {
    handlerChange(val) {
      if (val) {
        this.formValue.scc = val.key;
      } else {
        this.formValue.scc = '';
      }
    },
    // 下拉字典加载完成
    onDictReady() {
      this.formConfig[0].props.options = this.stateSelect;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    onClickSubmit() {
      if (this.modelTitle === 'see') {
        this.handleCancel();
        return;
      }
      // 表单校验
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.modelTitle === 'add') {
            this.saveTaxable();
          } else {
            this.editTaxable();
          }
        }
      });
    },
    onClickReset() {
      // 清空校验报错
      this.$refs.ruleForm.clearValidate();
    },
    // 关闭弹窗
    handleCancel(update) {
      this.$emit('handleCancel', update);
    },
    // 新增
    async saveTaxable() {
      const [, err] = await saveTaxable({
        ...this.formValue,
        rateTime: moment(this.formValue.rateTime).format('YYYY-MM'),
      });
      if (err) return;
      this.$message.success('新增成功!');
      this.handleCancel();
      this.$emit('loadData');
    },
    // 编辑
    async editTaxable() {
      const [, err] = await editTaxable({
        ...this.formValue,
        rateTime: moment(this.formValue.rateTime).format('YYYY-MM'),
      });
      if (err) return;
      this.$message.success('编辑成功!');
      this.handleCancel();
      this.$emit('loadData');
    },
    // 数字规则校验
    async checkNum(rule, value) {
      if (!value) {
        return Promise.reject(`请输入内容`);
      }
      if (isNaN(Number(value))) {
        return Promise.reject('请输入数字');
      } else {
        if (value.indexOf('.') !== -1) {
          let len = value.toString().split('.')[1].length;
          if (len !== 0) {
            if (len > 2) {
              return Promise.reject('只能保留两位小数');
            } else {
              return Promise.resolve();
            }
          } else {
            return Promise.reject('请输入正确数字格式');
          }
        } else {
          return Promise.resolve();
        }
      }
    },
    // 信用代码规则校验
    async checkCode(rule, value) {
      if (!value) {
        return Promise.reject(`请输入内容`);
      }
      if (!this.validateInput(value)) {
        return Promise.reject('请输入18位数字和字母组合');
      } else {
        if (value.length > 18) {
          return Promise.reject('不能多于18位数字');
        } else if (value.length < 18) {
          return Promise.reject('不能少于18位数字');
        } else {
          return Promise.resolve();
        }
      }
    },
    // 输入框只能输入字母和数字
    validateInput(input) {
      return /^[a-zA-Z0-9]+$/.test(input);
    },
  },
};
</script>
<style scoped>
.w100 {
  width: 100%;
}
.unit {
  position: absolute;
  width: 40px;
  text-align: center;
  right: 1px;
  height: 29px;
  line-height: 29px;
  background: #e8eaec;
  top: -6px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>
