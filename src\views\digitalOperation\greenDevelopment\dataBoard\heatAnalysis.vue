<template>
  <div class="heat-analysis-wrapper">
    <h2>碳排放分析</h2>
    <div class="heat-content">
      <div class="heat-analysis">
        <div class="heat-map-box">
          <HeatMap :emissionData="EmissionData" ref="heatMap" />
        </div>
        <div class="btn-wrap">
          <a-row type="flex">
            <a-col :span="6" v-for="item in parkList" :key="item.id">
              <a-button
                class="park-btn"
                type="link"
                @click="parkBtnClick(item.id)"
                >{{ `${item.id}、${item.name}` }}</a-button
              >
            </a-col>
          </a-row>
        </div>
      </div>
      <div class="heat-right">
        <div class="filter">
          <a-radio-group
            default-value="1"
            button-style="solid"
            @change="radioChange"
          >
            <a-radio-button value="1"> 本月 </a-radio-button>
            <a-radio-button value="2"> 上月 </a-radio-button>
          </a-radio-group>
          <a-month-picker v-model="date" class="date" @change="dateChange" />
        </div>
        <a-progress
          class="process"
          type="circle"
          :percent="analysisData.ratio"
          :strokeWidth="8"
          :width="200"
        >
          <template #format="percent">
            <p>
              <span style="color: #333; font-size: 28px">{{ percent }}</span
              >%
            </p>
            <p>绿色电力占比</p>
          </template>
        </a-progress>
        <p>
          光伏面积总量：<span class="number-text"
            >{{ analysisData.photovoltaicArea }}㎡</span
          >
        </p>
        <p>
          发电量：<span class="number-text"
            >{{ analysisData.powerGeneration }}wh</span
          >
        </p>
        <p>
          减排量：<span class="number-text"
            >{{ analysisData.carbonReduction }}t</span
          >
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import HeatMap from './heatmap.vue';
import moment from 'moment';
import { parkList, parkIdLinkId } from './heatmap.js';
import { monthlyComprehensive } from '@/api/digitalOperation/greenDevelopment/dataBoard/index.js';

export default {
  name: 'ManagementTkHeatAnalysis',
  components: {
    HeatMap,
  },
  data() {
    return {
      parkIdLinkId,
      parkList,
      analysisData: {},
      EmissionData: [],
      parkReports: [],
      date: moment().format('YYYY-MM'),
    };
  },
  created() {},

  mounted() {
    this.monthlyComprehensive(this.date);
  },

  methods: {
    async monthlyComprehensive(date) {
      const [res, err] = await monthlyComprehensive({ date });
      if (err) return;
      console.log('热力图', res);
      this.analysisData = res.data;
      let arr = res.data.parkReports;
      this.parkReports = arr;
      let arr1 = [];
      this.parkReports.forEach((item) => {
        arr1.push({
          emission: item.carbonEmission,
          id: this.parkIdLinkId.get(item.id),
          name: item.name,
        });
      });
      this.EmissionData = arr1;
      // console.log(this.EmissionData, 'this.EmissionData0000000.0');
      this.emissionDataLoadEnd = true;
    },
    parkBtnClick(id) {
      this.$refs.heatMap.selectedHandler(id);
    },
    radioChange(e) {
      this.emissionDataLoadEnd = false;
      //当前年月
      const curMonth = moment().format('YYYY-MM');
      // 转到上个月
      const previousMonth = moment().subtract(1, 'month');
      // 提取年份和月份
      const year = previousMonth.format('YYYY');
      const month = previousMonth.format('MM');
      const preMonth = `${year}-${month}`;
      const { value } = e.target;
      value === '1' ? (this.date = curMonth) : (this.date = preMonth);
      this.monthlyComprehensive(this.date);
    },
    dateChange(val) {
      this.emissionDataLoadEnd = false;
      this.date = val ? val.format('YYYY-MM') : '';
      this.monthlyComprehensive(this.date);
    },
  },
};
</script>

<style lang="less" scoped>
.heat-analysis-wrapper {
  background: #fff;
  padding: 24px 24px;
  margin-top: 40px;
  .heat-analysis {
    flex: 7;
  }
  .btn-wrap {
    padding-top: 8px;
    .park-btn {
      color: #767676;
      &:hover {
        color: @primary-color;
      }
    }
  }

  .heat-right {
    flex: 3;
    padding-left: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    .filter {
      display: flex;
      .date {
        margin-left: 10px;
      }
    }
    .process {
      margin: 40px 0;
    }
    .number-text {
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-weight: 500;
    }
  }
  .heat-content {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    .heat-map-box {
      aspect-ratio: 1825 / 461;
      background: #fff;
      background: url('@/assets/heatmap_new/map.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
      .tip-text {
        position: absolute;
        z-index: 2;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        // background: url('@/assets/heatmap_new/text.png') no-repeat;
        // background-size: 100% 100%;
      }
      /deep/.cls-1 {
        fill: #5996ef;
      }
    }
  }
}
</style>
