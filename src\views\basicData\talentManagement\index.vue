<template>
  <page-layout>
    <BuseCrud
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :config="{ noMargin: true }"
      @loadData="loadData"
      :modalConfig="modalConfig"
      :tableProps="{
        headerAlign: 'left',
        border: 'none',
        columnConfig: { resizable: true },
        showOverflow: 'tooltip',
        align: 'left',
      }"
      :tableOn="{
        'checkbox-change': selectChangeEvent,
        'checkbox-all': selectChangeEvent,
      }"
    >
      <template #defaultTitle>
        <span></span>
      </template>
      <!-- 创建按钮区域插槽 -->
      <template #defaultHeader>
        <a-button
          type="primary"
          style="margin-right: 8px"
          @click="handleCreate"
        >
          新增
        </a-button>
        <a-button style="margin-right: 8px" @click="onClickImport">
          导入
        </a-button>
        <ExportButton
          :notExportAll="true"
          :checkItems="checkItems"
          :pageName="pageName"
          :params="filterOptions.params"
        />
        <a-button
          type="danger"
          style="margin-right: 8px"
          :disabled="!checkItems.length"
          @click="handelDelete"
        >
          删除
        </a-button>
      </template>
      <!-- 姓名插槽 -->
      <template #nameSearch>
        <a-input-search placeholder="请输入" @search="nameSearch" />
      </template>
      <!-- 年份插槽 -->
      <template #year>
        <BuseRangePicker
          type="year"
          :needShowSecondPicker="() => false"
          v-model="filterOptions.params.year"
          format="YYYY"
          placeholder="请选择年份"
          :disableDateFunc="disableDateFunc"
        />
      </template>
      <!-- 企业 -->
      <template #enterSlot>
        <a-select
          :allowClear="true"
          :filterOption="filterOption"
          :showSearch="true"
          placeholder="请选择"
          @change="onEnterChange"
        >
          <a-select-option
            v-for="item in stateSelect"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </template>
      <!-- filter插槽 -->
      <template #identity="{ item }">
        <a-input
          v-model="filterOptions.params[item.field]"
          placeholder="AutoFilter插槽"
        />
      </template>
      <!-- table插槽 -->
      <template #operate="{ row }">
        <span class="operate-button" @click="onClickEdit(row)">编辑</span>
      </template>
      <!-- 编辑弹窗 -->
      <ListModal
        :visible="visible"
        :detail="modalData"
        :modelTitle="modelTitle"
        :preview="preview"
        :carrierCategory="carrierCategory"
        @loadData="loadData"
        :dictData="dict.type || []"
        @handleCancel="handleCancel"
      />
    </BuseCrud>
  </page-layout>
</template>

<script>
import moment from 'moment';
import { institutionsMixin } from '../mixins/institutionsMixin';
import { defaultTableColumn, defaultFilterConfig } from './constant';
import ListModal from './components/ListModal.vue';
import ExportButton from '@/views/basicData/components/ExportButton.vue';
import { talentPage, deleteTalentId, codeByType } from '@/api/basicData';
export default {
  components: { ListModal, ExportButton },
  dicts: ['level_type', 'project_man_type'],
  mixins: [institutionsMixin],
  data() {
    return {
      num: '',
      pageName: 'talent',
      loading: false,
      filterOptions: {
        config: defaultFilterConfig(), // 筛选器配置
        showCount: undefined, // 初始展示几个筛选项 非必填
        params: {
          talentName: '',
          rateTime: undefined,
          enterpriseName: '',
          projectManType: undefined,
          unifiedCreditCode: this.$route.query.pictureId,
        }, // 筛选器结果数据
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: defaultTableColumn(),
      tableData: [],
      visible: false,
      modalData: null,
      checkItems: [],
      modelTitle: 'add',
      stateSelect: [
        {
          value: 1,
          label: '国有园区',
        },
        {
          value: 2,
          label: '民营园区',
        },
        {
          value: 3,
          label: '龙头企业园区',
        },
      ],
      // 载体类别
      carrierCategory: [],
      detail: {},
      preview: false,
    };
  },
  created() {
    this.loadData();
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        menu: false,
      };
    },
  },
  methods: {
    // 下拉字典加载完成
    async onDictReady() {
      this.filterOptions.config[3].props.options =
        this.dict?.type?.project_man_type || [];
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 下拉联动
    onEnterChange() {
      console.log(222);
    },
    // 请求接口数据
    async loadData() {
      this.loading = true;
      const params = this.filterOptions.params;
      const [res, err] = await talentPage({
        limit: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        ...params,
        year: this.filterOptions?.params.year?.startValue
          ? moment(this.filterOptions?.params.year?.startValue).format('YYYY')
          : '',
      });
      this.loading = false;

      if (err) return;
      // 设置数据
      this.tablePage.total = res.total;
      this.tableData = res.data;
      this.checkItems = [];
    },
    // 创建按钮点击事件
    handleCreate() {
      this.modelTitle = 'add';
      this.visible = true;
    },
    // 编辑按钮点击事件
    onClickEdit(row) {
      this.modelTitle = 'edit';
      this.visible = true;
      this.modalData = row;
      console.log(row, 'row');
    },
    // 关闭弹窗
    handleCancel(update) {
      if (update) {
        this.loadData();
      }
      this.visible = false;
      this.modalData = null;
      this.manageVisible = false;
      this.preview = false;
    },
    // 管理按钮点击事件
    onClickManage() {
      this.manageVisible = true;
    },
    // 删除
    handelDelete() {
      const that = this;
      this.$confirm({
        title: '确认删除',
        content: () => '确认删除当前选中数据？',
        cancelText: '取消',
        okText: '确定',
        async onOk() {
          const [, err] = await deleteTalentId({
            ids: that.checkItems,
          });
          if (!err) {
            that.$message.success('删除成功!');
            that.checkItems = [];
            // 刷新数据
            that.loadData();
            return;
          }
        },
      });
    },
    // 园区类别
    stateChange(value) {
      console.log('选中值', value);
    },
    // 导入
    onClickImport() {
      this.$router.push({
        path: '/basicData/importPage',
        query: {
          pageName: this.pageName,
        },
      });
    },
    // 年份选择
    yearChange(date, dateString) {
      console.log('月份选择回调', date, dateString);
    },
    // 导出
    onClickExport() {
      console.log('导出');
    },
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    // 姓名搜索
    nameSearch() {
      console.log('姓名搜索');
    },
  },
};
</script>
<style scoped>
/deep/.page-wrapper-container {
  margin: 0 !important;
}
</style>
