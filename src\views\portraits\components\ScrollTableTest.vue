<template>
  <div class="scroll-table-test">
    <h2>无限循环滚动表格测试</h2>
    
    <div class="controls">
      <button @click="toggleAutoScroll" class="btn">
        {{ autoScrollEnabled ? '暂停滚动' : '开始滚动' }}
      </button>
      <button @click="changeSpeed" class="btn">
        切换速度 (当前: {{ currentSpeed }}x)
      </button>
      <button @click="addData" class="btn">
        添加数据
      </button>
      <button @click="resetData" class="btn">
        重置数据
      </button>
    </div>

    <div class="table-demos">
      <!-- 税收排名表格 -->
      <div class="demo-section">
        <ScrollTable
          :height="'400px'"
          :tableTitle="'税收排名TOP10'"
          :columns="taxColumns"
          :tableData="taxData"
          :scrollSpeed="scrollSpeed"
          :scrollInterval="50"
          :visibleRowCount="10"
          :autoScroll="autoScrollEnabled"
        />
      </div>

      <!-- 亩均税收表格 -->
      <div class="demo-section">
        <ScrollTable
          :height="'400px'"
          :tableTitle="'亩均税收排名TOP10'"
          :columns="muTaxColumns"
          :tableData="muTaxData"
          :scrollSpeed="scrollSpeed"
          :scrollInterval="60"
          :visibleRowCount="8"
          :autoScroll="autoScrollEnabled"
        />
      </div>
    </div>

    <div class="info-panel">
      <h3>组件状态信息</h3>
      <p>自动滚动: {{ autoScrollEnabled ? '开启' : '关闭' }}</p>
      <p>滚动速度: {{ scrollSpeed }}px/次</p>
      <p>税收数据量: {{ taxData.length }}条</p>
      <p>亩均税收数据量: {{ muTaxData.length }}条</p>
    </div>
  </div>
</template>

<script>
import ScrollTable from './ScrollTable.vue';

export default {
  name: 'ScrollTableTest',
  components: {
    ScrollTable,
  },
  data() {
    return {
      autoScrollEnabled: true,
      scrollSpeed: 1,
      currentSpeed: 1,
      
      // 税收排名列配置
      taxColumns: [
        {
          title: '排名',
          field: 'taxRank',
          width: 90,
          type: 'html',
          formatter: ({ cellValue }) =>
            `<div class='rank-badge rank-${+cellValue}'>TOP${+cellValue}</div>`,
        },
        {
          title: '园区名称',
          field: 'parkName',
          minWidth: 120,
        },
        {
          title: '税收(万元)',
          field: 'tax',
          width: 120,
          type: 'html',
          headerAlign: 'right',
          align: 'right',
          formatter: ({ cellValue }) =>
            `<span class="tax-amount">${cellValue || '-'}</span>`,
        },
      ],
      
      // 亩均税收列配置
      muTaxColumns: [
        {
          title: '排名',
          field: 'muAvgTaxRank',
          width: 90,
          type: 'html',
          formatter: ({ cellValue }) =>
            `<div class='rank-badge rank-${cellValue}'>TOP${cellValue}</div>`,
        },
        {
          title: '园区名称',
          field: 'parkName',
          minWidth: 120,
        },
        {
          title: '亩均税收(万元/亩)',
          field: 'muAvgTax',
          width: 140,
          type: 'html',
          headerAlign: 'right',
          align: 'right',
          formatter: ({ cellValue }) =>
            `<span class="mu-tax-amount">${cellValue || '-'}</span>`,
        },
      ],
      
      // 测试数据
      taxData: [
        { taxRank: 1, parkName: '高新技术产业园', tax: '1,250.50' },
        { taxRank: 2, parkName: '经济技术开发区', tax: '980.30' },
        { taxRank: 3, parkName: '工业园区A区', tax: '756.80' },
        { taxRank: 4, parkName: '科技创新园', tax: '642.15' },
        { taxRank: 5, parkName: '现代服务业园区', tax: '589.90' },
        { taxRank: 6, parkName: '生物医药园', tax: '456.70' },
        { taxRank: 7, parkName: '新材料产业园', tax: '398.25' },
        { taxRank: 8, parkName: '智能制造园区', tax: '345.60' },
        { taxRank: 9, parkName: '文化创意园', tax: '298.40' },
        { taxRank: 10, parkName: '绿色能源园区', tax: '256.80' },
      ],
      
      muTaxData: [
        { muAvgTaxRank: 1, parkName: '高新技术产业园', muAvgTax: '25.8' },
        { muAvgTaxRank: 2, parkName: '科技创新园', muAvgTax: '22.4' },
        { muAvgTaxRank: 3, parkName: '生物医药园', muAvgTax: '19.6' },
        { muAvgTaxRank: 4, parkName: '经济技术开发区', muAvgTax: '18.2' },
        { muAvgTaxRank: 5, parkName: '智能制造园区', muAvgTax: '16.9' },
        { muAvgTaxRank: 6, parkName: '新材料产业园', muAvgTax: '15.3' },
        { muAvgTaxRank: 7, parkName: '现代服务业园区', muAvgTax: '14.7' },
        { muAvgTaxRank: 8, parkName: '工业园区A区', muAvgTax: '13.5' },
        { muAvgTaxRank: 9, parkName: '文化创意园', muAvgTax: '12.8' },
        { muAvgTaxRank: 10, parkName: '绿色能源园区', muAvgTax: '11.9' },
      ],
    };
  },
  methods: {
    toggleAutoScroll() {
      this.autoScrollEnabled = !this.autoScrollEnabled;
    },
    
    changeSpeed() {
      const speeds = [0.5, 1, 2, 3];
      const currentIndex = speeds.indexOf(this.scrollSpeed);
      const nextIndex = (currentIndex + 1) % speeds.length;
      this.scrollSpeed = speeds[nextIndex];
      this.currentSpeed = this.scrollSpeed;
    },
    
    addData() {
      const newTaxItem = {
        taxRank: this.taxData.length + 1,
        parkName: `新增园区${this.taxData.length + 1}`,
        tax: (Math.random() * 1000).toFixed(2),
      };
      
      const newMuTaxItem = {
        muAvgTaxRank: this.muTaxData.length + 1,
        parkName: `新增园区${this.muTaxData.length + 1}`,
        muAvgTax: (Math.random() * 30).toFixed(1),
      };
      
      this.taxData.push(newTaxItem);
      this.muTaxData.push(newMuTaxItem);
    },
    
    resetData() {
      // 重置为初始数据
      this.taxData = this.taxData.slice(0, 10);
      this.muTaxData = this.muTaxData.slice(0, 10);
    },
  },
};
</script>

<style scoped lang="less">
.scroll-table-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  
  &:hover {
    background: #40a9ff;
  }
  
  &:active {
    background: #096dd9;
  }
}

.table-demos {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.demo-section {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.info-panel {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  
  h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #333;
  }
  
  p {
    margin: 8px 0;
    color: #666;
    font-size: 14px;
  }
}

// 排名徽章样式
:deep(.rank-badge) {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  color: white;
  
  &.rank-1 { background: #ff4d4f; }
  &.rank-2 { background: #ff7a45; }
  &.rank-3 { background: #ffa940; }
  &.rank-4, &.rank-5 { background: #52c41a; }
  &.rank-6, &.rank-7, &.rank-8 { background: #1890ff; }
  &.rank-9, &.rank-10 { background: #722ed1; }
}

// 税收金额样式
:deep(.tax-amount) {
  color: #52c41a;
  font-family: 'D-DIN', monospace;
  font-size: 14px;
  font-weight: bold;
}

// 亩均税收样式
:deep(.mu-tax-amount) {
  color: #1890ff;
  font-family: 'D-DIN', monospace;
  font-size: 14px;
  font-weight: bold;
}
</style>
