const _path = require('path');
const fs = require('fs');
const template = require('./template');
const args = require('minimist')(process.argv.slice(2));
const { _ = [], name, path = '' } = args;

function resolvePath(dir) {
  return _path.resolve(__dirname, dir);
}

if (_.length <= 0) {
  throw Error('参数缺失');
}
if (!name) {
  throw Error('缺少文件/文件夹名称参数');
}
// 创建文件夹，即菜单空节点
if (_.includes('dir')) {
  console.log(resolvePath('../src/views/'));
  fs.mkdir(`${resolvePath('../src/views/')}/${name}`, (err) => {
    if (err) throw err;
  });
}
// 创建表格页面
if (_.includes('page')) {
  const defaultPath = path ? '../src/views/' + path : '../src/views';
  const getPath = `${resolvePath(defaultPath)}/${name}`;
  console.log(getPath);
  fs.mkdir(`${resolvePath(getPath)}`, (err) => {
    if (err) throw err;
    console.error(getPath);
    fs.writeFile(`${getPath}/index.vue`, template.vueTemplate, (error) => {
      console.log(error);
    });
    fs.writeFile(`${getPath}/config.js`, template.configTemplate, (error) => {
      console.log(error);
    });
  });
}
