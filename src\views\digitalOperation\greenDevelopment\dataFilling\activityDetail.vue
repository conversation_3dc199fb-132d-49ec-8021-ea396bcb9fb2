<template>
  <div>
    <a-form-model
      ref="ruleForm"
      layout="inline"
      :rules="rules"
      :model="form"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
    >
      <a-form-model-item
        label="活动类型"
        prop="activityType"
        style="width: 30%"
      >
        <a-select
          :disabled="needDisabled"
          v-model="form.activityType"
          @change="activityTypeChangeHandler"
        >
          <a-select-option value="1"> 天然气 </a-select-option>
          <a-select-option value="2"> 净购入电力 </a-select-option>
          <a-select-option value="3"> 光伏发电 </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="活动时间范围" prop="rangeTime" required>
        <a-range-picker
          v-model="form.rangeTime"
          :disabled="needDisabled"
        ></a-range-picker>
      </a-form-model-item>

      <vxe-table
        class="activity-table"
        :data="tableData"
        :edit-config="{
          trigger: 'click',
          mode: 'row',
          activeMethod: activeCellMethod,
        }"
      >
        <vxe-column type="seq" title="序号" width="60"></vxe-column>
        <vxe-column field="name" title="数据项名称"> </vxe-column>
        <vxe-column field="number" title="数值" :edit-render="{}">
          <template #default="{ row }">
            <a-input v-model="row.number" v-if="row.edit === true"></a-input>
            <span v-else>{{ row.number }}</span>
          </template>
          <template #edit="{ row }">
            <a-input
              :disabled="needDisabled"
              v-model="row.number"
              type="number"
              @blur="numberChangeHandler"
            ></a-input>
          </template>
        </vxe-column>
        <vxe-column field="unit" title="单位"> </vxe-column>
      </vxe-table>
      <!-- TODO：上传需要替换组件并实现 -->
      <a-form-model-item>
        <a-upload :disabled="needDisabled" :multiple="true" :file-list="[]">
          <a-button type="primary">
            <a-icon type="upload" /> 上传凭证
          </a-button>
        </a-upload>
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<script>
import { getTableData } from './activityDetail.js';
import moment from 'moment';
export default {
  name: 'ManagementTkActivityDetail',
  props: {
    operationType: {
      type: String,
      default: 'add',
    },
    row: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      tableData: null,
      form: {
        activityType: this.row.activityType || '1',
        rangeTime: this.getRangeTime(),
      },
      rules: {
        rangeTime: [
          {
            required: true,
            message: '请选择活动时间范围',
            trigger: 'change',
          },
        ],
      },
      currentInputValue: 0,
    };
  },
  computed: {
    needDisabled() {
      return this.operationType === 'view' ? true : false;
    },
  },
  mounted() {
    this.tableData = getTableData('1', this.row.number);
  },
  methods: {
    activityTypeChangeHandler(value) {
      this.tableData = getTableData(value);
    },
    activeCellMethod({ row, column }) {
      //判断哪一个单元格可以编辑，判断中的字段名称需要调整；
      if (column.field === 'number' && row.edit === true) {
        return true;
      }
      return false;
    },
    numberChangeHandler(e) {
      this.currentInputValue = e.target.value;
      this.tableData = getTableData(this.form.activityType, e.target.value);
    },
    getFormInstance() {
      return this.$refs.ruleForm;
    },
    getModalData() {
      return {
        activityType: this.form.activityType,
        rangeTime: this.form.rangeTime,
        inputValue: this.currentInputValue,
        fileList: [],
      };
    },
    getRangeTime() {
      const startTime = this.row.startTime
        ? moment(this.row.startTime)
        : undefined;
      const endTime = this.row.endTime ? moment(this.row.endTime) : undefined;

      return [startTime, endTime];
    },
  },
};
</script>

<style lang="less" scoped>
.activity-table {
  margin-top: 30px;
}
</style>
