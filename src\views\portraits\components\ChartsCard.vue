<template>
  <div class="charts-container">
    <h3 v-if="chartTitle">{{ chartTitle }}</h3>
    <div
      v-show="showCharts"
      ref="chart"
      class="charts"
      :style="{ height: height, width: '100%' }"
    ></div>

    <a-empty
      :style="{
        height: height,
        margin: '0',
        paddingTop: 'calc((' + height + ' / 2) - 30px)',
      }"
      v-show="!showCharts"
      :image="simpleImage"
    />
  </div>
</template>
<script>
import * as echarts from 'echarts';

import { Empty } from 'ant-design-vue';
/**
 * 简单Echarts
 */
export default {
  props: {
    options: {
      type: Object,
      default: () => {},
    },
    chartTitle: {
      type: String,
    },
    height: {
      type: String,
      default: '160px',
    },
  },
  data() {
    return {
      charts: null,
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE, //空图片
    };
  },
  watch: {
    options: {
      handler(val) {
        this.charts.setOption(val);
      },
      deep: true,
    },
  },
  computed: {
    showCharts() {
      return !!this.options?.series?.[0]?.data?.length;
    },
  },
  mounted() {
    // this.options?.series?.[0]?.data?.length &&
    this.initChart();
  },
  updated() {
    this?.charts?.resize();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.charts?.resize);
  },
  methods: {
    setOption() {
      this.charts.setOption(this.options);
    },
    initChart() {
      this.charts = echarts.init(this.$refs.chart);
      window.addEventListener('resize', this.charts.resize);
    },
  },
};
</script>

<style lang="scss" scoped>
.charts-container {
  width: 100%;
  background: rgba(249, 249, 249, 0.6);
  padding: 15px;
  border-radius: 8px;
  overflow: unset;
  h3 {
    padding-left: 19px;
    font-family: PingFang SC;
    font-size: 12px;
    color: #333333;
    background-image: url('@/assets/images/portraits/1-004.png');
    background-size: 14px 13px;
    background-repeat: no-repeat;
    background-position: 0 2px;
  }
}
</style>
