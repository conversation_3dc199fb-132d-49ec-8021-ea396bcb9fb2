// import Layout from '@/layouts/AdminLayout.vue';

/**
 * 登录白名单：不需要登录拦截的路由配置
 * @names 根据路由名称匹配
 * @paths 根据路由fullPath匹配
 * @includes 判断路由是否包含在该配置中
 */
export const loginWhiteList = {
  names: ['404', '401'],
  paths: ['/login', '/resetPassword', '/401', '/portraits/portraitsDetail'],
  includes(route) {
    return this.names.includes(route.name) || this.paths.includes(route.path);
  },
};
// 公共路由-本地化配置
export const constantRoutes = [
  {
    path: '/portraits/portraitsDetail',
    component: () => import('@/views/portraits/components/portraitsDetail.vue'),
    hidden: true,
  },
  {
    path: '/resetPassword',
    component: () => import('@/views/login/resetPassword'),
    hidden: true,
  },
  {
    path: '/login',
    name: 'LOGIN',
    component: () => import('@/views/login/login'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
];

// 业务路由
export const businessRoutes = [
  {
    path: '*',
    redirect: '/401',
    hidden: true,
  },
  // {
  //   path: '/basicData',
  //   component: Layout,
  //   meta: { title: '基础数据' },
  //   hidden: true,
  //   children: [
  //     {
  //       path: 'taikeInformationManagement',
  //       component: () => import('@/views/basicData/taikeInformationManagement'),
  //       meta: { title: '太科城信息管理' },
  //     },
  //   ],
  // },
];
