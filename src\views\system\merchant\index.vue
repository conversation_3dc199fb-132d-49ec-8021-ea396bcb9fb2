<template>
  <page-layout-pro>
    <a-row class="merchant-tree">
      <a-col :span="4" class="merchant-tree-left">
        <MerchantTree
          ref="merchanTree"
          :merchantId.sync="merchantId"
          :merchantName.sync="merchantName"
          @afterChooseMerchant="afterChooseMerchant"
          @getTreeDone="getTreeDone"
        ></MerchantTree>
      </a-col>
      <a-col :span="20">
        <PageWrapper
          :title="`租户列表 - ${merchantName}`"
          createText=""
          :loading="loading"
          :tablePage="tablePage"
          :filterOptions="filterOption"
          :tableColumn="tableColumns"
          :tableData="tableData"
          :style="{ margin: '0  24px 0 16px' }"
          @loadData="loadData"
          @handleReset="handleReset"
          @handleCreate="handleAdd"
        >
          <template #defaultHeader>
            <a-button
              v-hasPermi="['system:merchant:add']"
              v-if="showAddMerchant"
              type="primary"
              :loading="loading"
              @click="handleAdd()"
            >
              新增租户
            </a-button>
          </template>
          <template #status="{ row }">
            <a-badge
              :status="getStatusBadgeColor(row.status)"
              :text="getStatusLabel(row.status)"
            />
          </template>
          <template #merchantName="{ row }">
            {{ row.merchantName }}
            <a-tag v-if="row.merchantId === baseMerchant" color="#2db7f5">
              当前租户
            </a-tag>
          </template>
          <template #operation="{ row }">
            <a-button
              icon="security-scan"
              v-hasPermi="['system:merchant:seePrivateKey']"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="seeSecret(row)"
            >
              查看密钥
            </a-button>
            <a-button
              v-hasPermi="['system:merchant:edit']"
              v-if="row.merchantId !== baseMerchant"
              icon="edit"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleEdit(row)"
            >
              编辑
            </a-button>
            <a-button
              v-hasPermi="['system:merchant:delete']"
              v-if="row.merchantId !== baseMerchant"
              icon="delete"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleDelete(row)"
            >
              删除
            </a-button>
            <a-button
              v-hasPermi="['system:merchant:resetPassword']"
              v-if="row.merchantId !== baseMerchant && row.userId"
              icon="edit"
              type="link"
              style="padding: 0; margin-right: 8px"
              @click="handleChangePassword(row)"
            >
              重置管理员账号密码
            </a-button>
          </template>
        </PageWrapper>
      </a-col>
    </a-row>
    <!-- 编辑租户 -->
    <EditModal
      :visible.sync="editModalVisible"
      :merchantId="chooseId"
      :parentId="merchantId"
      :merchantName="merchantName"
      :merchantListOptions="merchantTreeList"
      @ok="refreshData"
    />
    <!--查看租户详情 -->
    <SecretDetail
      :visible.sync="seeSecretVisible"
      :merchantDetail="merchantDetail"
    />
    <!-- 修改密码 -->
    <ChangePassword :visible.sync="showReset" :userId="editUserId" />
  </page-layout-pro>
</template>

<script>
import { getMerchantList, delMerchant } from '@/api/system/merchant';
import EditModal from './EditModal.vue';
import { Modal } from 'ant-design-vue';
import {
  filterOption,
  tableColumns,
  getStatusLabel,
  getStatusBadgeColor,
} from './constant';
import SecretDetail from './SecretDetail';
import MerchantTree from '@/components/SystemTreeSelect/MerchantTree.vue';
import ChangePassword from '@/views/system/user/components/ChangePassword.vue';

export default {
  components: { EditModal, SecretDetail, MerchantTree, ChangePassword },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 筛选条件
      filterOption,
      // 表格数据
      pageSizeOptions: ['12', '24', '36'],
      tableColumns,
      tableData: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },

      // 是否显示弹出层
      editModalVisible: false,
      chooseId: '', //选择编辑的ID
      seeSecretVisible: false,
      merchantDetail: {},
      showReset: false,
      editUserId: '',

      // 树结构参数
      merchantId: '', //当前选中的租户ID
      merchantName: '',
      merchantTreeList: [],
      merchantArrayList: [],
    };
  },
  computed: {
    showAddMerchant() {
      return (
        this.merchantArrayList.find((item) => item.id === this.merchantId)
          ?.type === 'ISV'
      );
    },
    baseMerchant() {
      return this.$store?.state.base?.user?.merchantId;
    },
  },
  methods: {
    getStatusLabel,
    getStatusBadgeColor,
    refreshData() {
      this.$refs?.merchanTree?.refresh();
    },
    /** 重置请求接口数据 */
    async handleReset() {
      this.merchantId =
        this.merchantTreeList.length > 0 ? this.merchantTreeList[0]?.id : '';
      this.merchantName =
        this.merchantTreeList.length > 0 ? this.merchantTreeList[0]?.label : '';
      this.afterChooseMerchant();
    },
    /**
     * 获取树完成之后保存数据
     */
    getTreeDone(data) {
      this.merchantTreeList = data?.tree || [];
      this.merchantArrayList = data?.array || [];
      this.loadData();
    },
    // /切换点击选择租户之后
    async afterChooseMerchant() {
      this.filterOption.params = {
        merchantName: '',
        merchantType: '',
        status: '',
      };
      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 };
      await this.loadData();
    },
    /** 请求接口数据 */
    async loadData() {
      this.loading = true;
      const params = this.filterOption.params;
      const [result, error] = await getMerchantList({
        limit: this.tablePage.pageSize,
        page: this.tablePage.currentPage,
        parentId: this.merchantId,
        ...params,
      });
      this.loading = false;
      if (error) return;
      this.tableData = result.data;
      this.tablePage.total = result.count;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.chooseId = '';
      this.editModalVisible = true;
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      this.chooseId = row.merchantId;
      this.editModalVisible = true;
    },
    /** 修改按钮操作 */
    handleChangePassword(row) {
      if (!row.userId) {
        this.$message.warning('该租户无关联的管理员账户');
        return;
      }
      this.editUserId = row.userId;
      this.showReset = true;
    },
    /** 修改按钮操作 */
    seeSecret(row) {
      this.seeSecretVisible = true;
      this.merchantDetail = row;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.loading) return;
      Modal.confirm({
        title: '警告',
        content: '是否确认删除名称为"' + row.merchantName + '"的租户?',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          this.loading = true;
          const [, error] = await delMerchant(row.merchantId);
          this.loading = false;
          if (error) return;
          this.$message.success('删除成功');
          if (row.merchantId === this.merchantId) {
            this.merchantId = this.merchantTreeList[0].id;
          }
          this.refreshData();
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .ant-form-item {
  margin-bottom: 8px;
}
.merchant-tree {
  padding-left: 24px;
  display: flex;
  align-items: stretch;
}
/deep/.vxe-grid--pager-wrapper {
  display: none;
}
</style>
