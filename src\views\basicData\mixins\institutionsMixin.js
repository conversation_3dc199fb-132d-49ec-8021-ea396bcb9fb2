import * as api from '@/api/basicData/index';
import { getToken } from '@/utils/common/auth';
import { getParkList } from '@/api/basicData/index.js';

export const institutionsMixin = {
  data() {
    return {
      checkItems: [],
      companyList: [],
      parkList: [],
    };
  },
  created() {
    this.getParkList();
  },
  methods: {
    // 对数据映射处理
    translateValue(cellValue, list) {
      return list
        ?.filter((obj) => obj.value === cellValue)
        ?.map((obj) => obj.label)
        ?.join(',');
    },
    // 单选
    selectChangeEvent({ records }) {
      this.checkItems = records.map((item) => {
        return item.id;
      });
    },
    // 获取枚举列表
    async getCodeByType(codeType) {
      const [res, err] = await api.codeByType({
        codeType: codeType,
      });
      if (err) return;
      const list = res.data.map((item) => {
        return {
          codeType: item.codeType,
          codeTypeName: item.codeTypeName,
          label: item.name,
          value: item.value,
        };
      });
      return list;
    },
    async getParkList() {
      const [res, err] = await getParkList({
        limit: 1000,
        pageNum: 1,
      });
      if (res && res.data) {
        this.parkList = res.data;
      }
    },
    // 远程搜索公司名称
    async handleSearch(value) {
      const requestOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          Authorization: getToken(),
        },
        body: JSON.stringify({
          pageNum: 1,
          limit: 100,
        }),
      };
      await fetch(
        // process.env.VUE_APP_BASE_API_TKB + '/base/enterprise/companyNameList',
        process.env.VUE_APP_BASE_API_TKB + '/common/dropdown/enterprise/list',
        requestOptions
      )
        .then((response) => {
          if (response.ok) {
            return response.json();
          }
          throw new Error('Network response was not ok.');
        })
        .then((data) => {
          console.log(data, data);
          let list = [];
          if (data?.data) {
            data.data.forEach((item) => {
              if (item.label?.indexOf(value) !== -1 && value) {
                let obj = {
                  label: item.label,
                  value: item.value,
                };
                list.push(obj);
              }
            });
          }
          // 处理获取到的数据
          this.companyList = list.slice(0, 100);
        })
        .catch((error) => {
          // 处理错误
          console.error('Error:', error);
        });
    },

    getEnterprise(data) {
      this.companyList = data;
    },
    // 长度校验
    async checkLen(rule, value) {
      if (!value) {
        return Promise.reject(`请输入内容`);
      }
      if (value.length > 120) {
        return Promise.reject('输入内容过多，不能超过255个字');
      } else {
        return Promise.resolve();
      }
    },
    // 整数规则校验
    async checkNumber(rule, value) {
      if (!value) {
        return Promise.reject(`请输入`);
      }
      if (isNaN(Number(value))) {
        return Promise.reject('请输入数字');
      } else {
        if (value > 9999) {
          return Promise.reject('请输入整数');
        }
        if (value.indexOf('.') !== -1) {
          return Promise.reject('请输入整数');
        } else {
          return Promise.resolve();
        }
      }
    },
    // 数字规则校验
    async checkNum(rule, value) {
      if (!value) {
        return Promise.reject(`请输入内容`);
      }
      if (isNaN(Number(value))) {
        return Promise.reject('请输入数字');
      } else {
        if (value.indexOf('.') !== -1) {
          let len = value.toString().split('.')[1].length;
          if (len !== 0) {
            if (len > 2) {
              return Promise.reject('只能保留两位小数');
            } else {
              return Promise.resolve();
            }
          } else {
            return Promise.reject('请输入正确数字格式');
          }
        } else {
          return Promise.resolve();
        }
      }
    },
    // 信用代码规则校验
    async checkCode(rule, value) {
      if (!value) {
        return Promise.reject(`请输入统一社会信用代码`);
      }
      if (!this.validateInput(value)) {
        return Promise.reject('请输入18位数字和字母组合');
      } else {
        if (value.length > 18) {
          return Promise.reject('不能多于18位数字');
        } else if (value.length < 18) {
          return Promise.reject('不能少于18位数字');
        } else {
          return Promise.resolve();
        }
      }
    },
    // 输入框只能输入字母和数字
    validateInput(input) {
      return /^[a-zA-Z0-9]+$/.test(input);
    },
  },
};
