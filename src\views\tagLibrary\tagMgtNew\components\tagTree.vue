<template>
  <div class="tag-manage-left">
    <!--        目录-->
    <a-tree
      :tree-data="treeData"
      class="custom-tree"
      :defaultExpandAll="true"
      @select="getNodeInfo"
    >
      <template slot="icon" slot-scope="item">
        <div class="node-container">
          <span class="node-title">{{ item.title }}</span>
          <a-icon
            class="icon"
            id="plus"
            type="plus-circle"
            @click="editCatalog(item, 'add')"
            v-if="getNodeLevel(item) !== 3"
          ></a-icon>
          <a-icon
            class="icon"
            id="edit"
            type="form"
            @click="editCatalog(item, 'edit')"
          ></a-icon>
          <a-icon
            class="icon"
            id="delete"
            type="delete"
            @click="deleteCatalog(item)"
            v-if="getNodeLevel(item) !== 1"
          ></a-icon>
        </div>
      </template>
    </a-tree>
    <!--        弹框-->
    <a-modal
      :visible="visible"
      :title="visibleTitle"
      @ok="submitCatalog"
      @cancel="visible = false"
    >
      <a-form-model ref="formRef" :model="formState" :rules="rules">
        <a-form-model-item prop="catalogName">
          <a-input v-model="formState.catalogName" placeholder="请填写目录名" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { getUUID } from '@/utils/index';
import { Modal } from 'ant-design-vue';
import * as api from '@/api/tagLibrary/index';

export default {
  name: 'tagTree',
  props: {
    tagList: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  data() {
    return {
      formState: {
        catalogName: '',
      },
      visibleTitle: '',
      nodedetail: {},
      operate: '',
      visible: false,
      treeData: [
        {
          title: '科创园区画像目录体系',
          key: '1',
          children: [
            // {
            //     title: '基本属性',
            //     key: '1-1',
            //     children: [
            //         {
            //             title: '自然属性',
            //             key: '1-1-1',
            //             children: []
            //         }
            //     ]
            // },
            // {
            //     title: '创新生态和人力聚集力',
            //     key: '1-2',
            //     children: []
            // }
          ],
        },
      ],
      rules: {
        catalogName: [
          {
            required: true,
            message: '请填写目录名!',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  mounted() {
    this.queryLabelDir();
    // this.getNodeInfo(['8869279361692584036518'])
  },
  methods: {
    // 标签库目录查询
    async queryLabelDir() {
      // 初始化时为根目录添加操作按钮
      this.addAttrToTree(this.treeData);
      const [res, err] = await api.queryLabelDir({});
      if (err) return;
      let list = JSON.parse(res.data[0].dirData);
      if (Array.isArray(list)) {
        this.treeData = list;
        // 为整棵树添加操作按钮
        this.addAttrToTree(this.treeData);
      }
    },
    //处理数据
    addAttrToTree(jsonData) {
      for (let i = 0; i < jsonData.length; i++) {
        this.$set(jsonData[i], 'scopedSlots', { title: 'icon' });
        if (Object.prototype.hasOwnProperty.call(jsonData[i], 'key')) {
          this.addAttrToTree(jsonData[i].children);
        }
      }
      return jsonData;
    },
    // 编辑目录
    editCatalog(item, type) {
      this.visible = true;
      this.operate = type;
      if (type === 'add') {
        this.visibleTitle = '新增目录';
        this.formState.catalogName = '';
      } else {
        this.visibleTitle = '编辑目录';
        this.formState.catalogName = item.title;
      }
    },
    // 删除目录
    deleteCatalog(item) {
      let level = this.getNodeLevel(item);
      // 目录下有子目录，不可删除
      if (item.children.length !== 0) {
        this.$message.error(
          '该目录下有文件，不可直接删除，请先删除里面的文件！'
        );
        return;
      }
      // 如果三级目录下有标签，不可删除
      if (level === 3 && this.tagList.length !== 0) {
        this.$message.error(
          '该目录下有标签，不可直接删除，请先删除里面的标签！'
        );
        return;
      }
      let that = this;
      Modal.confirm({
        title: '确定删除该目录?',
        icon: 'warning',
        onOk() {
          return new Promise((resolve, reject) => {
            setTimeout(Math.random() > 0.5 ? resolve : reject, 1000);
            let node = that.findParentNode(that.treeData, item.key);
            node.children = node.children.filter((a) => a.key !== item.key);
            // 调用保存接口保存删除后的目录结构
            that.saveLabelDir(that.treeData);
          }).catch(() => console.log('Oops errors!'));
        },
        onCancel() {},
      });
    },
    // 新增、编辑提交
    submitCatalog() {
      console.log('this.$refs.formRef', this.$refs.formRef.validate);
      this.$refs.formRef.validate((valid) => {
        console.log(valid);
        if (valid) {
          if (this.operate === 'add') {
            const key = getUUID();
            let obj = {
              title: this.formState.catalogName,
              key: key,
              children: [],
            };
            this.nodedetail.children.push(obj);
            // 更新插槽
          } else {
            this.nodedetail.title = this.formState.catalogName;
          }
          this.saveLabelDir(this.treeData);
          this.visible = false;
        }
      });
    },
    // 标签库目录保存/更新接口
    async saveLabelDir(list) {
      const dirData = JSON.stringify(list);
      const params = {
        dirData: dirData,
      };
      const [, err] = await api.saveLabelDir(params);
      if (err) return;
      this.queryLabelDir();
    },
    // 点击节点获取节点信息
    getNodeInfo(selectedKeys) {
      if (selectedKeys.length === 0) {
        return;
      }
      let clickedNode = this.findNodeByKey(this.treeData, selectedKeys[0]);
      let parent = this.findParentNode(this.treeData, selectedKeys[0]);
      let level = this.getNodeLevel(parent);
      // 该节点为叶子节点时传参给父组件更新页面标签信息
      if (level === 2) {
        let nodeInfo = {
          id: clickedNode.key,
          name: clickedNode.title,
          treeData: this.treeData,
        };
        this.$emit('getNodeInfo', nodeInfo);
      }
      this.nodedetail = clickedNode;
    },
    // 获取当前节点信息
    findNodeByKey(nodes, key) {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].key === key) {
          return nodes[i];
        }
        if (nodes[i].children) {
          const foundNode = this.findNodeByKey(nodes[i].children, key);
          if (foundNode) {
            return foundNode;
          }
        }
      }
      return null;
    },
    // 获取父亲节点信息
    findParentNode(nodes, key) {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].children) {
          if (nodes[i].children.some((child) => child.key === key)) {
            return nodes[i];
          }
          const foundNode = this.findParentNode(nodes[i].children, key);
          if (foundNode) {
            return foundNode;
          }
        }
      }
      return null;
    },
    // 判断当前节点层级
    getNodeLevel(node, level = 0) {
      if (node && node.key) {
        return this.getNodeLevel(
          this.findParentNode(this.treeData, node.key),
          level + 1
        );
      } else {
        return level;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.tag-manage-left {
  overflow: auto;

  .custom-tree .icon {
    margin-left: 5px;
  }

  #plus {
    color: #409eff;
  }

  #edit {
    color: #67c23a;
  }

  #delete {
    color: #f56c6c;
  }
}
</style>
