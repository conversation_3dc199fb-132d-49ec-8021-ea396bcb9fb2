<template>
  <div class="ranking-chart-box">
    <h2>历史检查不符合项排名</h2>
    <p class="pass-tip">
      总不符合项<b>{{ total }}个</b>
    </p>
    <BaseChart
      class="chart-box"
      v-if="rankingDataForEchart.yAxis.length > 0"
      :yAxis="rankingDataForEchart.yAxis"
      :seriesData="rankingDataForEchart.seriesData"
      :tooltipFormatter="tooltipFormatter"
    />
    <echartEmptyBox v-else />
  </div>
</template>

<script>
import echartEmptyBox from '@/components/echartEmptyBox/index.vue';
import BaseChart from '@/components/chart/lineChartForIERanking.vue';
import { getColors, getTemplateBase } from '../echart';
// import { mockDataForRanking } from '../mockData';
import { industryCoincidenceRate } from '@/api/digitalOperation/securityManagement/parkSafety/echart/IE.js';
export default {
  name: 'ManagementTkRanking',
  components: {
    BaseChart,
    echartEmptyBox,
  },

  data() {
    return {
      mockDataForRanking: [],
      total: '',
    };
  },
  computed: {
    //根据接口数据获取图表所需数据
    rankingDataForEchart() {
      const resolvedData = {
        data: this.mockDataForRanking
          .map((item) => {
            return item.checkNotPass;
          })
          .reverse(),
        yAxis: this.mockDataForRanking
          .map((item) => {
            return item.name;
          })
          .reverse(),
      };
      const barColors = getColors(resolvedData.data.length);
      const seriesData = [
        {
          name: '总不符合项目数量',
          type: 'bar',
          barMaxWidth: '80%',
          itemStyle: {
            color: ({ dataIndex }) => {
              return barColors[resolvedData.data.length - dataIndex - 1];
            },
          },
          data: resolvedData.data,
        },
      ];
      const yAxis = resolvedData.yAxis;
      return {
        seriesData,
        yAxis,
      };
    },
  },
  create() {},

  mounted() {
    this.getRankingData();
  },

  methods: {
    tooltipFormatter(info) {
      let str = `<div style="text-align: left; color:#1D2129;" >${info[0].name}</div>`;
      const itemInfo =
        this.mockDataForRanking.filter(
          (item) => item.name === info[0].name
        )[0] || {};
      str += getTemplateBase('', '检查单位数量', itemInfo.unitNumber);
      // str += getTemplateBase('', '总检查项', itemInfo.checkTotal);
      // str += getTemplateBase('', '总不符合项目数量', itemInfo.checkNotPass);
      info.forEach((item) => {
        str += getTemplateBase(item.marker, item.seriesName, item.value + '个');
      });
      return str;
    },
    //获取排名数据
    async getRankingData() {
      const [res, err] = await industryCoincidenceRate();
      if (err) return;

      let arr = [];
      res.data.list.forEach((item) => {
        arr.push({
          name: item.parkName,
          unitNumber: item.unitNums,
          checkTotal: item.inspectionItems,
          checkNotPass: item.notCompliantItems,
          checkRate:
            (item.notCompliantItems / item.inspectionItems).toFixed(4) * 100,
        });
      });
      this.mockDataForRanking = arr;
      this.total = res.data.total;
    },
  },
};
</script>

<style lang="less" scoped>
.ranking-chart-box {
  margin: 16px;
  height: auto;
  height: 800px;
  .pass-tip {
    font-size: 16px;
  }
}
</style>
