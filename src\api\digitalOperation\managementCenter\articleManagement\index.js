import { request } from '@/utils/request/requestTkb';

// 文章上架
export function articleOnShelf(data) {
  return request({
    url: '/gov/article/articleOnShelf',
    method: 'post',
    data,
  });
}

// 文章下架
export function articleOffShelf(data) {
  return request({
    url: '/gov/article/articleOffShelf',
    method: 'post',
    data,
  });
}

// 文章删除
export function articleDelete(data) {
  return request({
    url: '/gov/article/articleDelete',
    method: 'post',
    data,
  });
}

// 推送
export function push(data) {
  return request({
    url: '/gov/article/push',
    method: 'post',
    data,
  });
}

// 公开发布
export function publicPush(data) {
  return request({
    url: '/gov/article/publicPush',
    method: 'post',
    data,
  });
}

// 推送详情
export function pushDetail(data) {
  return request({
    url: '/gov/article/pushDetail',
    method: 'post',
    data,
  });
}

// 推送详情统计
export function pushDetailStatistics(data) {
  return request({
    url: '/gov/article/pushDetailStatistics',
    method: 'get',
    params: data,
  });
}

export function allOrgs(data) {
  return request({
    url: '/organize/allOrgs',
    method: 'post',
    data,
  });
}

export function pushPublicBatch(data) {
  return request({
    url: '/gov/article/pushPublicBatch',
    method: 'post',
    data,
  });
}

export function batchPush(data) {
  return request({
    url: '/gov/article/batchPush',
    method: 'post',
    data,
  });
}
export function pushArticleByLabels(data) {
  return request({
    url: '/gov/article/pushArticleByLabels',
    method: 'post',
    data,
  });
}

export function exportArticlePushInfo(data) {
  return request({
    url: '/gov/article/exportArticlePushInfo',
    method: 'post',
    data,
    responseType: 'blob',
  });
}
